# 设计文档

## 概述

本设计文档详细描述了为 Universe Bag（乾坤袋）扩展的设置页面添加"关于我们"和"帮助中心"页面的技术实现方案。该功能将在现有的设置页面框架内集成两个新的标签页，提供扩展信息展示和用户帮助功能。

## 架构

### 整体架构
- **现有架构扩展**：在当前的 OptionsApp.tsx 组件基础上添加新的标签页
- **组件层次结构**：
  ```
  OptionsApp
  ├── 现有标签页 (bookmarks, categories, tags, import-export, settings)
  ├── AboutTab (新增)
  └── HelpCenterTab (新增)
  ```

### 导航结构更新
- 在现有的侧边栏导航中添加两个新选项
- 将新选项放置在导航列表的底部，符合用户习惯
- 保持现有的导航交互模式和样式一致性

## 组件和接口

### 1. AboutTab 组件

**功能职责**：
- 展示扩展的基本信息
- 显示版本号、开发者信息
- 展示扩展图标和品牌元素
- 提供版权和许可信息

**组件接口**：
```typescript
interface AboutTabProps {}

interface ExtensionInfo {
  name: string
  version: string
  description: string
  developer: string
  website?: string
  email?: string
  license?: string
  buildDate?: string
}
```

**主要功能**：
- 从 manifest.json 自动获取扩展信息
- 响应式布局适配不同屏幕尺寸
- 支持深色/浅色主题（如果扩展支持）

### 2. HelpCenterTab 组件

**功能职责**：
- 提供功能使用指南
- 展示常见问题解答
- 提供搜索功能
- 展示联系方式和反馈渠道

**组件接口**：
```typescript
interface HelpCenterTabProps {}

interface HelpSection {
  id: string
  title: string
  content: string
  category: 'guide' | 'faq' | 'troubleshooting'
  keywords: string[]
}

interface HelpSearchResult {
  section: HelpSection
  relevance: number
  matchedKeywords: string[]
}
```

**主要功能**：
- 分类展示帮助内容（使用指南、FAQ、故障排除）
- 实现本地搜索功能
- 支持内容折叠/展开
- 提供快速导航锚点

### 3. 导航更新

**修改现有的 tabs 配置**：
```typescript
const tabs = [
  { id: 'bookmarks', name: '收藏管理', icon: Bookmark },
  { id: 'categories', name: '分类管理', icon: Folder },
  { id: 'tags', name: '标签管理', icon: Tag },
  { id: 'import-export', name: '导入导出', icon: Download },
  { id: 'settings', name: '设置', icon: Settings },
  // 新增的标签页
  { id: 'about', name: '关于我们', icon: Info },
  { id: 'help', name: '帮助中心', icon: HelpCircle },
]
```

## 数据模型

### 1. 扩展信息数据模型

```typescript
interface ExtensionManifest {
  name: string
  version: string
  description: string
  manifest_version: number
  permissions: string[]
  author?: string
}

interface AboutPageData {
  extensionInfo: ExtensionManifest
  buildInfo: {
    buildDate: string
    buildVersion: string
  }
  developerInfo: {
    name: string
    website?: string
    email?: string
  }
  licenseInfo: {
    type: string
    text?: string
    url?: string
  }
}
```

### 2. 帮助内容数据模型

```typescript
interface HelpContent {
  sections: HelpSection[]
  categories: HelpCategory[]
  searchIndex: SearchIndex
}

interface HelpCategory {
  id: string
  name: string
  description: string
  icon: string
  order: number
}

interface SearchIndex {
  keywords: Map<string, string[]> // keyword -> section ids
  content: Map<string, string>    // section id -> searchable content
}
```

## 错误处理

### 1. 数据加载错误
- **manifest.json 读取失败**：显示默认的扩展信息，记录错误日志
- **帮助内容加载失败**：显示基础帮助信息和错误提示
- **搜索功能异常**：降级到简单的文本匹配搜索

### 2. 用户交互错误
- **搜索无结果**：显示友好的无结果提示和建议
- **外部链接访问失败**：提供备用联系方式
- **页面渲染异常**：使用错误边界组件捕获并显示错误信息

### 3. 错误恢复策略
```typescript
interface ErrorBoundaryState {
  hasError: boolean
  errorMessage: string
  errorStack?: string
}

// 错误恢复组件
const PageErrorBoundary: React.FC<{children: React.ReactNode}> = ({children}) => {
  // 实现错误边界逻辑
  // 提供重试机制
  // 显示友好的错误信息
}
```

## 测试策略

### 1. 单元测试
- **AboutTab 组件测试**：
  - 扩展信息正确显示
  - manifest.json 数据解析
  - 响应式布局测试
  - 主题切换测试

- **HelpCenterTab 组件测试**：
  - 帮助内容渲染
  - 搜索功能测试
  - 分类筛选测试
  - 内容折叠/展开测试

### 2. 集成测试
- **导航集成测试**：
  - 新标签页正确添加到导航
  - 标签页切换功能正常
  - URL 路由正确处理
  - 导航状态保持

### 3. 用户体验测试
- **响应式测试**：不同屏幕尺寸下的显示效果
- **性能测试**：页面加载速度和搜索响应时间
- **无障碍测试**：键盘导航和屏幕阅读器支持
- **兼容性测试**：不同浏览器下的功能正常性

### 4. 测试数据准备
```typescript
// 测试用的帮助内容数据
const mockHelpContent: HelpContent = {
  sections: [
    {
      id: 'getting-started',
      title: '快速开始',
      content: '如何开始使用 Universe Bag...',
      category: 'guide',
      keywords: ['开始', '入门', '使用']
    },
    // 更多测试数据...
  ],
  categories: [
    {
      id: 'guide',
      name: '使用指南',
      description: '详细的功能使用说明',
      icon: 'book',
      order: 1
    },
    // 更多分类...
  ],
  searchIndex: new Map()
}
```

## 实现细节

### 1. 文件结构
```
src/
├── options/
│   ├── components/
│   │   ├── AboutTab.tsx          # 关于我们页面组件
│   │   ├── HelpCenterTab.tsx     # 帮助中心页面组件
│   │   └── HelpSearchBox.tsx     # 帮助搜索组件
│   ├── data/
│   │   ├── helpContent.ts        # 帮助内容数据
│   │   └── aboutInfo.ts          # 关于页面数据
│   └── utils/
│       ├── manifestReader.ts     # manifest.json 读取工具
│       └── helpSearch.ts         # 帮助内容搜索工具
```

### 2. 样式设计
- **设计原则**：与现有设置页面保持一致的视觉风格
- **颜色方案**：使用现有的主题色彩系统
- **布局模式**：采用卡片式布局，支持响应式设计
- **图标系统**：使用 lucide-react 图标库保持一致性

### 3. 性能优化
- **懒加载**：帮助内容按需加载，减少初始加载时间
- **搜索优化**：使用防抖技术优化搜索性能
- **缓存策略**：缓存帮助内容和搜索结果
- **虚拟滚动**：如果帮助内容过多，使用虚拟滚动优化性能

### 4. 国际化支持
```typescript
interface I18nContent {
  zh: HelpContent
  en?: HelpContent
  // 其他语言...
}

// 根据浏览器语言或用户设置选择内容
const getLocalizedContent = (locale: string): HelpContent => {
  // 实现语言选择逻辑
}
```

### 5. 配置管理
```typescript
// 帮助内容配置
const helpConfig = {
  searchDebounceDelay: 300,
  maxSearchResults: 20,
  enableSearchHighlight: true,
  defaultCategory: 'guide',
  contactInfo: {
    email: '<EMAIL>',
    website: 'https://universebag.com',
    github: 'https://github.com/universebag/extension'
  }
}
```

这个设计确保了新功能与现有系统的无缝集成，同时提供了良好的用户体验和可维护性。