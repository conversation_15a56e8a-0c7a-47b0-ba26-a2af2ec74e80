# 导入导出管理功能设计文档

## 概述

本设计文档基于需求文档，定义了一个完整的导入导出管理系统的技术架构和实现方案。该系统将扩展现有的BookmarkImportExportService，增加对分类数据、标签数据的支持，并提供智能的重复数据处理和冲突解决机制。

## 架构设计

### 系统架构图

```mermaid
graph TB
    UI[导入导出界面] --> Controller[导入导出控制器]
    Controller --> DataService[数据导入导出服务]
    DataService --> BookmarkService[收藏服务]
    DataService --> CategoryService[分类服务]
    DataService --> TagService[标签服务]
    DataService --> ConflictResolver[冲突解决器]
    DataService --> Validator[数据验证器]
    DataService --> FileHandler[文件处理器]
    
    ConflictResolver --> MergeEngine[合并引擎]
    ConflictResolver --> ConflictUI[冲突解决界面]
    
    FileHandler --> JSONHandler[JSON处理器]
    FileHandler --> CSVHandler[CSV处理器]
    FileHandler --> HTMLHandler[HTML处理器]
```

### 数据流图

```mermaid
sequenceDiagram
    participant User as 用户
    participant UI as 导入导出界面
    participant Service as 数据服务
    participant Resolver as 冲突解决器
    participant Storage as 数据存储
    
    User->>UI: 选择导入文件
    UI->>Service: 解析文件数据
    Service->>Service: 验证数据格式
    Service->>Resolver: 检测重复数据
    Resolver->>UI: 显示冲突解决选项
    User->>UI: 选择处理方式
    UI->>Resolver: 执行冲突解决
    Resolver->>Service: 返回处理结果
    Service->>Storage: 保存数据
    Storage->>UI: 返回导入结果
    UI->>User: 显示完成状态
```

## 组件和接口

### 1. 核心服务类

#### ImportExportManagerService

```typescript
export class ImportExportManagerService {
  // 导出功能
  exportAllData(options: ExportAllOptions): Promise<ExportResult>
  exportBookmarks(options: ExportBookmarksOptions): Promise<ExportResult>
  exportCategories(options: ExportCategoriesOptions): Promise<ExportResult>
  exportTags(options: ExportTagsOptions): Promise<ExportResult>
  
  // 导入功能
  importData(file: File, options: ImportOptions): Promise<ImportResult>
  
  // 冲突处理
  detectConflicts(importData: ImportData): Promise<ConflictDetectionResult>
  resolveConflicts(conflicts: ConflictItem[], resolutions: ConflictResolution[]): Promise<ResolvedData>
}
```

#### ConflictResolverService

```typescript
export class ConflictResolverService {
  // 冲突检测
  detectBookmarkConflicts(importBookmarks: BookmarkInput[], existingBookmarks: Bookmark[]): ConflictItem[]
  detectCategoryConflicts(importCategories: CategoryInput[], existingCategories: Category[]): ConflictItem[]
  detectTagConflicts(importTags: TagInput[], existingTags: Tag[]): ConflictItem[]
  
  // 冲突解决
  resolveBookmarkConflict(conflict: ConflictItem, resolution: ConflictResolution): BookmarkInput
  resolveCategoryConflict(conflict: ConflictItem, resolution: ConflictResolution): CategoryInput
  resolveTagConflict(conflict: ConflictItem, resolution: ConflictResolution): TagInput
  
  // 智能合并
  mergeBookmarkData(existing: Bookmark, imported: BookmarkInput): BookmarkInput
  mergeCategoryData(existing: Category, imported: CategoryInput): CategoryInput
  mergeTagData(existing: Tag, imported: TagInput): TagInput
}
```

### 2. 数据类型定义

#### 导出选项类型

```typescript
// 全部数据导出选项
export interface ExportAllOptions {
  format: 'json'
  includeBookmarks: boolean
  includeCategories: boolean
  includeTags: boolean
  includeMetadata: boolean
  dateRange?: DateRange
}

// 收藏夹导出选项
export interface ExportBookmarksOptions extends ExportOptions {
  includeRelatedCategories: boolean
  includeRelatedTags: boolean
}

// 分类导出选项
export interface ExportCategoriesOptions {
  format: 'json'
  includeHierarchy: boolean
  includeStatistics: boolean
  categoryIds?: string[]
}

// 标签导出选项
export interface ExportTagsOptions {
  format: 'json'
  includeUsageStats: boolean
  includeRelatedBookmarks: boolean
  tagIds?: string[]
}
```

#### 导入数据类型

```typescript
// 完整导入数据结构
export interface ImportData {
  version: string
  exportDate: string
  metadata: ImportMetadata
  bookmarks?: BookmarkInput[]
  categories?: CategoryInput[]
  tags?: TagInput[]
}

// 导入元数据
export interface ImportMetadata {
  source: string
  totalBookmarks: number
  totalCategories: number
  totalTags: number
  exportOptions: Record<string, any>
}
```

#### 冲突处理类型

```typescript
// 冲突项
export interface ConflictItem {
  id: string
  type: 'bookmark' | 'category' | 'tag'
  conflictType: 'duplicate' | 'name_conflict' | 'data_mismatch'
  existingData: any
  importData: any
  conflictFields: string[]
  similarity: number
}

// 冲突解决方案
export interface ConflictResolution {
  conflictId: string
  action: 'keep_existing' | 'use_imported' | 'merge' | 'manual_edit'
  mergedData?: any
  manualData?: any
}

// 冲突检测结果
export interface ConflictDetectionResult {
  hasConflicts: boolean
  conflicts: ConflictItem[]
  summary: {
    bookmarkConflicts: number
    categoryConflicts: number
    tagConflicts: number
  }
}
```

### 3. 用户界面组件

#### ImportExportManagerTab

```typescript
export interface ImportExportManagerTabProps {}

export const ImportExportManagerTab: React.FC<ImportExportManagerTabProps> = () => {
  // 导出功能状态
  const [exportType, setExportType] = useState<'all' | 'bookmarks' | 'categories' | 'tags'>('all')
  const [exportOptions, setExportOptions] = useState<ExportAllOptions>()
  
  // 导入功能状态
  const [importFile, setImportFile] = useState<File | null>(null)
  const [importOptions, setImportOptions] = useState<ImportOptions>()
  
  // 冲突处理状态
  const [conflicts, setConflicts] = useState<ConflictItem[]>([])
  const [showConflictDialog, setShowConflictDialog] = useState(false)
  
  // 处理函数
  const handleExport = async () => { /* 导出逻辑 */ }
  const handleImport = async () => { /* 导入逻辑 */ }
  const handleConflictResolution = async () => { /* 冲突解决逻辑 */ }
}
```

#### ConflictResolutionDialog

```typescript
export interface ConflictResolutionDialogProps {
  conflicts: ConflictItem[]
  onResolve: (resolutions: ConflictResolution[]) => void
  onCancel: () => void
}

export const ConflictResolutionDialog: React.FC<ConflictResolutionDialogProps> = ({
  conflicts,
  onResolve,
  onCancel
}) => {
  // 冲突解决状态
  const [resolutions, setResolutions] = useState<ConflictResolution[]>([])
  const [currentConflictIndex, setCurrentConflictIndex] = useState(0)
  
  // 批量操作状态
  const [batchAction, setBatchAction] = useState<'keep_existing' | 'use_imported' | null>(null)
  
  // 处理函数
  const handleSingleResolution = (conflictId: string, resolution: ConflictResolution) => { /* 单个冲突解决 */ }
  const handleBatchResolution = (action: 'keep_existing' | 'use_imported') => { /* 批量冲突解决 */ }
  const handleManualEdit = (conflictId: string, editedData: any) => { /* 手动编辑 */ }
}
```

## 数据模型

### 导出数据结构

#### 全部数据导出格式

```json
{
  "version": "2.0",
  "exportDate": "2024-01-01T00:00:00.000Z",
  "exportType": "all",
  "metadata": {
    "source": "Universe Bag Extension",
    "totalBookmarks": 150,
    "totalCategories": 12,
    "totalTags": 45,
    "exportOptions": {
      "includeBookmarks": true,
      "includeCategories": true,
      "includeTags": true,
      "includeMetadata": true
    }
  },
  "bookmarks": [
    {
      "id": "bookmark_1",
      "type": "url",
      "title": "示例收藏",
      "url": "https://example.com",
      "description": "这是一个示例收藏",
      "content": "页面内容摘录",
      "category": "技术",
      "tags": ["JavaScript", "前端"],
      "favicon": "https://example.com/favicon.ico",
      "createdAt": "2024-01-01T00:00:00.000Z",
      "updatedAt": "2024-01-01T00:00:00.000Z",
      "metadata": {
        "pageTitle": "示例页面",
        "siteName": "example.com",
        "wordCount": 100,
        "aiGenerated": false
      }
    }
  ],
  "categories": [
    {
      "id": "category_1",
      "name": "技术",
      "description": "技术相关收藏",
      "color": "#3B82F6",
      "bookmarkCount": 25,
      "createdAt": "2024-01-01T00:00:00.000Z",
      "updatedAt": "2024-01-01T00:00:00.000Z"
    }
  ],
  "tags": [
    {
      "id": "tag_1",
      "name": "JavaScript",
      "color": "#F59E0B",
      "usageCount": 15,
      "createdAt": "2024-01-01T00:00:00.000Z",
      "updatedAt": "2024-01-01T00:00:00.000Z"
    }
  ]
}
```

#### 分类数据导出格式

```json
{
  "version": "2.0",
  "exportDate": "2024-01-01T00:00:00.000Z",
  "exportType": "categories",
  "metadata": {
    "source": "Universe Bag Extension",
    "totalCategories": 12,
    "exportOptions": {
      "includeHierarchy": true,
      "includeStatistics": true
    }
  },
  "categories": [
    {
      "id": "category_1",
      "name": "技术",
      "description": "技术相关收藏",
      "color": "#3B82F6",
      "parentId": null,
      "bookmarkCount": 25,
      "children": [
        {
          "id": "category_2",
          "name": "前端开发",
          "description": "前端开发相关",
          "color": "#10B981",
          "parentId": "category_1",
          "bookmarkCount": 15
        }
      ],
      "createdAt": "2024-01-01T00:00:00.000Z",
      "updatedAt": "2024-01-01T00:00:00.000Z"
    }
  ]
}
```

### 冲突检测算法

#### 收藏夹冲突检测

```typescript
function detectBookmarkConflicts(
  importBookmarks: BookmarkInput[], 
  existingBookmarks: Bookmark[]
): ConflictItem[] {
  const conflicts: ConflictItem[] = []
  
  for (const importBookmark of importBookmarks) {
    // URL重复检测
    if (importBookmark.url) {
      const existingByUrl = existingBookmarks.find(b => b.url === importBookmark.url)
      if (existingByUrl) {
        conflicts.push({
          id: generateConflictId(),
          type: 'bookmark',
          conflictType: 'duplicate',
          existingData: existingByUrl,
          importData: importBookmark,
          conflictFields: ['url'],
          similarity: 1.0
        })
        continue
      }
    }
    
    // 标题和内容相似度检测
    for (const existing of existingBookmarks) {
      const similarity = calculateBookmarkSimilarity(importBookmark, existing)
      if (similarity > 0.8) {
        conflicts.push({
          id: generateConflictId(),
          type: 'bookmark',
          conflictType: 'data_mismatch',
          existingData: existing,
          importData: importBookmark,
          conflictFields: determineConflictFields(importBookmark, existing),
          similarity
        })
      }
    }
  }
  
  return conflicts
}
```

#### 智能合并算法

```typescript
function mergeBookmarkData(
  existing: Bookmark, 
  imported: BookmarkInput
): BookmarkInput {
  return {
    ...imported,
    // 合并标签（去重）
    tags: Array.from(new Set([...existing.tags, ...(imported.tags || [])])),
    
    // 合并描述（优先使用更长的描述）
    description: selectBetterDescription(existing.description, imported.description),
    
    // 合并内容（优先使用更长的内容）
    content: selectBetterContent(existing.content, imported.content),
    
    // 合并元数据
    metadata: {
      ...existing.metadata,
      ...imported.metadata,
      // 保持AI生成标记的准确性
      aiGenerated: existing.metadata.aiGenerated || imported.metadata?.aiGenerated || false
    }
  }
}
```

## 错误处理

### 错误类型定义

```typescript
export enum ImportExportErrorType {
  FILE_FORMAT_ERROR = 'FILE_FORMAT_ERROR',
  DATA_VALIDATION_ERROR = 'DATA_VALIDATION_ERROR',
  CONFLICT_RESOLUTION_ERROR = 'CONFLICT_RESOLUTION_ERROR',
  STORAGE_ERROR = 'STORAGE_ERROR',
  NETWORK_ERROR = 'NETWORK_ERROR',
  PERMISSION_ERROR = 'PERMISSION_ERROR'
}

export interface ImportExportError {
  type: ImportExportErrorType
  message: string
  details?: any
  recoverable: boolean
  suggestions: string[]
}
```

### 错误处理策略

1. **文件格式错误**：提供详细的格式说明和示例
2. **数据验证错误**：列出所有验证失败的项目和原因
3. **冲突解决错误**：允许用户重新选择解决方案
4. **存储错误**：提供重试机制和数据恢复选项
5. **网络错误**：支持离线模式和断点续传
6. **权限错误**：引导用户授予必要权限

### 错误恢复机制

```typescript
export class ErrorRecoveryService {
  // 自动重试机制
  async retryWithBackoff<T>(
    operation: () => Promise<T>,
    maxRetries: number = 3,
    baseDelay: number = 1000
  ): Promise<T> {
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await operation()
      } catch (error) {
        if (attempt === maxRetries) throw error
        
        const delay = baseDelay * Math.pow(2, attempt - 1)
        await new Promise(resolve => setTimeout(resolve, delay))
      }
    }
    throw new Error('Max retries exceeded')
  }
  
  // 部分失败处理
  async handlePartialFailure(
    results: Array<{success: boolean, data?: any, error?: Error}>,
    onPartialSuccess: (successfulItems: any[]) => void,
    onErrors: (errors: Error[]) => void
  ): Promise<void> {
    const successful = results.filter(r => r.success).map(r => r.data)
    const errors = results.filter(r => !r.success).map(r => r.error!)
    
    if (successful.length > 0) {
      await onPartialSuccess(successful)
    }
    
    if (errors.length > 0) {
      await onErrors(errors)
    }
  }
}
```

## 测试策略

### 单元测试覆盖

1. **服务层测试**
   - ImportExportManagerService的所有方法
   - ConflictResolverService的冲突检测和解决逻辑
   - 数据验证和转换函数

2. **组件层测试**
   - ImportExportManagerTab的用户交互
   - ConflictResolutionDialog的冲突处理界面
   - 文件上传和下载功能

3. **集成测试**
   - 完整的导入导出流程
   - 冲突检测和解决的端到端测试
   - 大数据量的性能测试

### 测试数据准备

```typescript
// 测试数据工厂
export class TestDataFactory {
  static createTestBookmarks(count: number): BookmarkInput[] {
    return Array.from({ length: count }, (_, i) => ({
      type: 'url',
      title: `测试收藏 ${i + 1}`,
      url: `https://example${i + 1}.com`,
      description: `这是第 ${i + 1} 个测试收藏`,
      category: `分类${(i % 3) + 1}`,
      tags: [`标签${i + 1}`, `标签${(i % 5) + 1}`]
    }))
  }
  
  static createConflictScenarios(): Array<{
    existing: Bookmark[]
    imported: BookmarkInput[]
    expectedConflicts: number
  }> {
    return [
      // URL重复场景
      {
        existing: [this.createBookmark({ url: 'https://example.com' })],
        imported: [this.createBookmarkInput({ url: 'https://example.com' })],
        expectedConflicts: 1
      },
      // 内容相似场景
      {
        existing: [this.createBookmark({ title: '相似标题', content: '相似内容' })],
        imported: [this.createBookmarkInput({ title: '相似标题', content: '相似内容' })],
        expectedConflicts: 1
      }
    ]
  }
}
```

## 性能优化

### 大数据量处理

1. **分批处理**：将大量数据分批处理，避免内存溢出
2. **流式处理**：使用流式API处理大文件
3. **Web Workers**：在后台线程处理数据转换和验证
4. **虚拟滚动**：在冲突解决界面使用虚拟滚动显示大量冲突项

### 内存管理

```typescript
export class MemoryOptimizedProcessor {
  private readonly BATCH_SIZE = 100
  
  async processBatches<T, R>(
    items: T[],
    processor: (batch: T[]) => Promise<R[]>,
    onProgress?: (processed: number, total: number) => void
  ): Promise<R[]> {
    const results: R[] = []
    
    for (let i = 0; i < items.length; i += this.BATCH_SIZE) {
      const batch = items.slice(i, i + this.BATCH_SIZE)
      const batchResults = await processor(batch)
      results.push(...batchResults)
      
      // 触发垃圾回收
      if (i % (this.BATCH_SIZE * 10) === 0) {
        await new Promise(resolve => setTimeout(resolve, 0))
      }
      
      onProgress?.(i + batch.length, items.length)
    }
    
    return results
  }
}
```

### 缓存策略

1. **解析结果缓存**：缓存文件解析结果，避免重复解析
2. **冲突检测缓存**：缓存相似度计算结果
3. **UI状态缓存**：保存用户的选择和配置

## 安全考虑

### 数据验证

1. **文件类型验证**：严格验证上传文件的类型和大小
2. **数据结构验证**：验证导入数据的结构完整性
3. **内容安全验证**：检查导入内容是否包含恶意代码

### 隐私保护

1. **敏感信息过滤**：在导出时过滤敏感信息
2. **数据脱敏**：对个人信息进行脱敏处理
3. **访问控制**：确保只有授权用户可以执行导入导出操作

### 安全实现

```typescript
export class SecurityValidator {
  // 文件安全检查
  validateFile(file: File): ValidationResult {
    const errors: ValidationError[] = []
    
    // 检查文件类型
    if (!this.isAllowedFileType(file.type)) {
      errors.push({
        field: 'fileType',
        message: '不支持的文件类型',
        code: 'INVALID_FILE_TYPE'
      })
    }
    
    // 检查文件大小
    if (file.size > this.MAX_FILE_SIZE) {
      errors.push({
        field: 'fileSize',
        message: '文件大小超出限制',
        code: 'FILE_TOO_LARGE'
      })
    }
    
    return {
      isValid: errors.length === 0,
      errors
    }
  }
  
  // 数据内容安全检查
  sanitizeImportData(data: ImportData): ImportData {
    return {
      ...data,
      bookmarks: data.bookmarks?.map(bookmark => ({
        ...bookmark,
        // 清理潜在的XSS内容
        title: this.sanitizeHtml(bookmark.title),
        description: bookmark.description ? this.sanitizeHtml(bookmark.description) : undefined,
        content: bookmark.content ? this.sanitizeHtml(bookmark.content) : undefined
      }))
    }
  }
}
```

## 总结

本设计文档定义了一个完整的导入导出管理系统，具有以下特点：

1. **功能完整**：支持全部数据、收藏夹、分类、标签的分别导出
2. **智能处理**：提供智能的重复检测和冲突解决机制
3. **用户友好**：直观的界面和详细的操作反馈
4. **性能优化**：支持大数据量处理和内存优化
5. **安全可靠**：完善的数据验证和错误处理机制
6. **扩展性强**：模块化设计，便于未来功能扩展

该设计将为用户提供强大而易用的数据管理功能，满足各种数据备份、迁移和同步需求。