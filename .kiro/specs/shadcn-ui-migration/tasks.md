# 实施计划

## 核心重构（已完成）

- [x] 1-18. shadcn/ui组件迁移和集成
  - ✅ 环境配置和组件安装完成
  - ✅ 所有核心组件已重构使用shadcn组件
  - ✅ 主题系统优化完成
  - ✅ 基础测试覆盖完成
  - ✅ 构建验证通过（12/12项检查）
  - _需求: 1.1-7.4 全部完成_

## 剩余优化任务

- [x] 19. 修复测试环境配置





  - 修复测试环境中的浏览器API模拟问题
  - 解决window.matchMedia和chrome API的测试环境配置
  - 修复测试中的硬编码样式类检查问题
  - 确保测试能正确验证shadcn组件的使用
  - _需求: 4.4, 5.3_

- [ ] 20. 清理硬编码颜色（可选）
  - 清理代码库中发现的339个硬编码颜色问题
  - 优先处理核心功能组件中的硬编码颜色
  - 将AIConfigPanel等组件中的硬编码颜色替换为shadcn变量
  - _需求: 1.3, 7.1_

- [ ] 21. 创建使用文档（可选）
  - 编写shadcn组件的使用指南
  - 整理现有的验证工具和测试页面文档
  - 记录shadcn组件的自定义扩展规范
  - _需求: 5.1, 5.2_