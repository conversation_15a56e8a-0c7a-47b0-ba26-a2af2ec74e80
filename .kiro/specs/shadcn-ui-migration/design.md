# 设计文档

## 概述

本设计文档描述了将现有浏览器插件重构为主要使用shadcn/ui组件的React架构的技术方案。重构将采用渐进式策略，首先以收藏夹页面作为试点，建立shadcn组件的标准化使用模式，然后逐步扩展到整个插件。

### 设计原则

1. **优先使用shadcn原生组件**：尽量使用shadcn提供的原生组件和变体，确保UI的一致性和可维护性
2. **适度自定义扩展**：在shadcn组件无法满足特定需求时，允许基于shadcn基础进行合理的样式扩展
3. **保持功能完整性**：重构过程中确保现有功能逻辑不受影响
4. **渐进式迁移**：以收藏夹页面为试点，验证成功后再全面推广
5. **标准化开发**：建立基于shadcn的开发规范和最佳实践

## 架构设计

### 组件层次结构

```
插件根组件
├── 收藏夹页面 (试点重构)
│   ├── BookmarkList (使用shadcn Card组件)
│   ├── BookmarkItem (使用shadcn Button、Badge组件)
│   ├── AddBookmarkDialog (使用shadcn Dialog组件)
│   └── BookmarkActions (使用shadcn DropdownMenu组件)
├── 标签页面 (后续重构)
├── 分类页面 (后续重构)
└── 设置页面 (后续重构)
```

### 技术栈更新

- **UI框架**：React + shadcn/ui
- **样式系统**：Tailwind CSS (shadcn依赖)
- **主题系统**：shadcn主题配置
- **表单处理**：react-hook-form + shadcn Form组件
- **状态管理**：保持现有状态管理方案
- **构建工具**：保持现有Vite配置

## 组件设计

### 核心shadcn组件映射

| 现有组件 | shadcn组件 | 使用场景 |
|---------|-----------|---------|
| 自定义按钮 | Button | 所有按钮交互 |
| 自定义输入框 | Input/Textarea | 表单输入 |
| 自定义对话框 | Dialog/AlertDialog | 模态窗口 |
| 自定义表单 | Form | 表单容器 |
| 自定义卡片 | Card | 内容展示 |
| 自定义下拉菜单 | DropdownMenu | 操作菜单 |
| 自定义标签 | Badge | 状态标识 |

### 收藏夹页面组件设计

#### BookmarkList组件
```typescript
// 使用shadcn Card组件作为容器
interface BookmarkListProps {
  bookmarks: Bookmark[];
  onEdit: (bookmark: Bookmark) => void;
  onDelete: (id: string) => void;
}

// 严格使用shadcn原生组件
- Card作为列表容器
- CardHeader显示标题
- CardContent包含书签项目
```

#### BookmarkItem组件
```typescript
// 使用shadcn组件组合
interface BookmarkItemProps {
  bookmark: Bookmark;
  onEdit: () => void;
  onDelete: () => void;
}

// 组件构成：
- Card作为项目容器
- Button用于编辑/删除操作
- Badge显示标签和状态
- Typography显示文本内容
```

#### AddBookmarkDialog组件
```typescript
// 使用shadcn Dialog组件
interface AddBookmarkDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSubmit: (data: BookmarkData) => void;
}

// 组件构成：
- Dialog作为模态容器
- DialogContent包含表单
- Form配合react-hook-form
- Input/Textarea用于数据输入
- Button用于操作按钮
```

## 数据模型

### 保持现有数据结构
```typescript
// 保持现有的数据模型不变
interface Bookmark {
  id: string;
  title: string;
  url: string;
  tags: string[];
  category: string;
  createdAt: Date;
  updatedAt: Date;
}

interface Tag {
  id: string;
  name: string;
  color: string;
  count: number;
}

interface Category {
  id: string;
  name: string;
  description: string;
  bookmarkCount: number;
}
```

## 样式系统设计

### shadcn主题配置
```css
/* 使用shadcn默认主题变量 */
:root {
  --background: 0 0% 100%;
  --foreground: 222.2 84% 4.9%;
  --card: 0 0% 100%;
  --card-foreground: 222.2 84% 4.9%;
  --popover: 0 0% 100%;
  --popover-foreground: 222.2 84% 4.9%;
  /* 其他shadcn主题变量 */
}
```

### 组件样式规范
1. **优先使用shadcn组件**：首选shadcn提供的原生组件和变体
2. **合理自定义扩展**：当shadcn组件无法满足需求时，基于shadcn样式系统进行扩展
3. **保持设计一致性**：自定义样式应遵循shadcn的设计语言和主题变量
4. **使用Tailwind工具类**：利用Tailwind CSS进行样式微调和布局
5. **响应式设计**：使用shadcn和Tailwind的响应式工具类

### 自定义样式指导原则
- **基于shadcn扩展**：自定义组件应继承shadcn的基础样式
- **使用CSS变量**：利用shadcn的CSS变量系统保持主题一致性
- **模块化CSS**：将自定义样式组织为可复用的CSS模块
- **文档化说明**：为自定义样式提供清晰的使用说明和维护指南

## 错误处理

### 组件级错误处理
```typescript
// 使用shadcn Alert组件显示错误
interface ErrorBoundaryProps {
  children: React.ReactNode;
}

// 错误显示组件
- Alert用于错误提示
- AlertDescription显示错误详情
- Button用于重试操作
```

### 表单验证错误
```typescript
// 使用shadcn Form组件的内置验证
- FormMessage显示验证错误
- FormDescription提供输入提示
- 集成react-hook-form的验证机制
```

## 测试策略

### 组件测试
1. **shadcn组件集成测试**：验证shadcn组件的正确使用
2. **交互行为测试**：确保shadcn组件的标准交互
3. **样式一致性测试**：验证shadcn主题的正确应用
4. **功能回归测试**：确保重构后功能完整性

### 测试工具
- **单元测试**：Vitest + React Testing Library
- **组件测试**：测试shadcn组件的渲染和交互
- **集成测试**：验证页面级功能完整性
- **视觉回归测试**：确保UI一致性

## 迁移策略

### 第一阶段：收藏夹页面试点
1. **环境准备**：安装shadcn/ui依赖
2. **组件替换**：逐个替换收藏夹页面组件
3. **样式清理**：移除相关自定义CSS
4. **功能验证**：确保功能完整性
5. **测试覆盖**：编写完整测试用例

### 第二阶段：经验总结
1. **最佳实践文档**：总结shadcn组件使用规范
2. **开发规范**：建立标准化开发流程
3. **组件库**：创建基于shadcn的通用组件
4. **性能评估**：分析重构后的性能表现

### 第三阶段：全面推广
1. **标签页面重构**：应用试点经验
2. **分类页面重构**：继续标准化改造
3. **设置页面重构**：完成整体迁移
4. **全局优化**：统一主题和交互规范

## 性能考虑

### 组件优化
1. **按需加载**：使用shadcn组件的tree-shaking特性
2. **渲染优化**：利用React.memo优化重渲染
3. **状态管理**：保持现有状态管理的性能优势
4. **包大小控制**：仅引入必要的shadcn组件

### 加载性能
1. **代码分割**：按页面分割shadcn组件
2. **懒加载**：对非关键组件实施懒加载
3. **缓存策略**：优化shadcn样式的缓存
4. **构建优化**：配置Vite的shadcn优化选项

## 维护性设计

### 代码组织
```
src/
├── components/
│   ├── ui/           # shadcn组件 (自动生成)
│   ├── bookmarks/    # 收藏夹相关组件
│   ├── tags/         # 标签相关组件
│   └── shared/       # 共享组件
├── lib/
│   └── utils.ts      # shadcn工具函数
├── styles/
│   ├── globals.css   # shadcn全局样式
│   ├── components.css # 自定义组件样式
│   └── utilities.css # 自定义工具类
└── hooks/            # 自定义hooks
```

### 开发规范
1. **组件命名**：遵循shadcn组件命名规范
2. **文件组织**：按功能模块组织组件文件
3. **类型定义**：为所有shadcn组件使用提供类型
4. **文档注释**：使用中文注释说明组件用途
5. **样式扩展规范**：
   - 优先考虑shadcn组件的variant和size属性
   - 必要时使用className进行样式扩展
   - 自定义样式应基于shadcn的设计系统
   - 保持样式的可维护性和一致性

### 版本管理
1. **shadcn版本锁定**：确保组件库版本稳定
2. **升级策略**：制定shadcn组件的升级计划
3. **兼容性测试**：验证新版本的兼容性
4. **回滚机制**：准备组件版本回滚方案