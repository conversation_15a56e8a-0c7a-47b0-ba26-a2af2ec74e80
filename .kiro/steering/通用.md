---
inclusion: always
---
# 通用规范

- 一直使用中文回复我。
- 代码中的注释使用中文。
- 开发要使用模块化开发，注意低耦合开发，能分开的函数尽量最小化。
- 修改代码的时候一定注意不要修改开始正常的代码，尤其是已经验证过正常的功能。
- 保持项目整体的结构和代码不改变，保留以前的功能，不修改任何的文字内容和样式，然后沿用现在的设计风格和样式。我们需要执行指定的任务，当你修改代码时候一定要专注于问题相关的内容，非必要不要修改其他已经验证正确的功能逻辑和 UI 样式

# 单元测试覆盖

- 每完成一个功能或者函数，要进行充分的单元测试，保证测试的覆盖率

# 使用Windows命令

一般来说，没有特殊说明，这个项目我们是在Windows 10 下开发，注意使用Windows下的命令运行命令。

## 构建
```bash
npm run build
```


