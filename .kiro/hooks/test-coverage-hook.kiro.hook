{"enabled": true, "name": "Test Coverage Generator", "description": "Monitors source file changes to identify new/modified functions, check test coverage, generate missing tests, run tests, and update coverage reports", "version": "1", "when": {"type": "fileEdited", "patterns": ["**/*.py", "**/*.js", "**/*.ts", "**/*.java", "**/*.cpp", "**/*.c", "**/*.cs", "**/*.go", "**/*.rb", "**/*.php"]}, "then": {"type": "askAgent", "prompt": "A source file has been modified. Please analyze the changes and:\n\n1. 识别新增或修改的函数和方法\n2. 检查是否存在对应的测试用例并覆盖这些变更\n3. 如果缺少测试覆盖，为新代码生成测试用例\n4. 运行测试以验证它们通过\n5. 更新测试覆盖率报告\n\n请使用模块化开发方式，确保低耦合，并为每个功能编写充分的单元测试以保证测试覆盖率。在Windows环境下使用适当的命令。"}}