{"enabled": true, "name": "SPECS任务重复检测", "description": "当创建或修改tasks.md文件时，自动检查SPECS文件中是否有重复任务，以及代码中是否已实现相关功能，避免重复设计和过度开发", "version": "1", "when": {"type": "fileEdited", "patterns": ["**/tasks.md", "**/SPECS/**/*.md", "docs/**/*.md"]}, "then": {"type": "askAgent", "prompt": "请检查以下内容以避免重复设计：\n1. 分析当前修改的tasks.md文件中的任务\n2. 检查SPECS文件夹中是否已有类似或重复的任务定义\n3. 扫描现有代码库，确认是否已实现相关功能\n4. 检查docs文件夹中的API文档和完成总结，看是否有重复的功能实现\n5. 如发现重复，提供具体的重复项目清单和建议的合并或优化方案\n6. 使用中文回复，重点关注避免过度设计和重复开发"}}