// Background Service Worker 消息处理器

import {
  ExtensionMessage,
  ExtensionMessageResponse,
  MessageResponse,
  MessageHandler,
  GetPageInfoMessage,
  GetLinkInfoMessage,
  QuickBookmarkMessage,
  BookmarkSelectedTextMessage,
  SaveDetailedBookmarkMessage,
  UpdateBookmarkMessage,
  DeleteBookmarkMessage,
  CheckBookmarkStatusMessage,
  UpdateIconStatusMessage,
  AIGenerateSuggestionsMessage,
  GetSettingsMessage,
  UpdateSettingsMessage,
  ManualSyncMessage,
  PingMessage
} from '../types/messages'
import { bookmarkService } from '../services/bookmarkService'
import { indexedDBService } from '../utils/indexedDB'
import { aiService } from '../services/aiService'
import { aiConfigService } from '../services/aiConfigService'
import { aiChatService } from '../services/aiChatService'
import { aiRecommendationService } from '../services/aiRecommendationService'
import { tabStatusManager } from '../services/tabStatusManager'
import { bookmarkStatusService } from '../services/bookmarkStatusService'

// 消息处理器映射
type MessageHandlerMap = {
  [K in ExtensionMessage['type']]: MessageHandler<
    Extract<ExtensionMessage, { type: K }>,
    ExtensionMessageResponse
  >
}

/**
 * 消息处理器类
 * 负责处理来自各个组件的消息通信
 */
export class BackgroundMessageHandler {
  private handlers: Partial<MessageHandlerMap> = {}

  constructor() {
    this.initializeHandlers()
    this.initializeContextMenus()
  }

  /**
   * 初始化所有消息处理器
   */
  private initializeHandlers() {
    this.handlers = {
      'PING': this.handlePing.bind(this),
      'GET_PAGE_INFO': this.handleGetPageInfo.bind(this),
      'GET_LINK_INFO': this.handleGetLinkInfo.bind(this),
      'QUICK_BOOKMARK': this.handleQuickBookmark.bind(this),
      'BOOKMARK_SELECTED_TEXT': this.handleBookmarkSelectedText.bind(this),
      'SAVE_DETAILED_BOOKMARK': this.handleSaveDetailedBookmark.bind(this),
      'UPDATE_BOOKMARK': this.handleUpdateBookmark.bind(this),
      'DELETE_BOOKMARK': this.handleDeleteBookmark.bind(this),
      'CHECK_BOOKMARK_STATUS': this.handleCheckBookmarkStatus.bind(this),
      'UPDATE_ICON_STATUS': this.handleUpdateIconStatus.bind(this),
      'AI_GENERATE_SUGGESTIONS': this.handleAIGenerateSuggestions.bind(this),
      'AI_GENERATE_TEXT': this.handleAIGenerateText.bind(this),
      'AI_GENERATE_TAGS': this.handleAIGenerateTags.bind(this),
      'AI_GENERATE_CATEGORY': this.handleAIGenerateCategory.bind(this),
      'AI_GENERATE_DESCRIPTION': this.handleAIGenerateDescription.bind(this),
      'AI_RECOMMEND_TAGS': this.handleAIRecommendTags.bind(this),
      'AI_RECOMMEND_FOLDERS': this.handleAIRecommendFolders.bind(this),
      'AI_RECOMMEND_BOTH': this.handleAIRecommendBoth.bind(this),
      'AI_TEST_CONNECTION': this.handleAITestConnection.bind(this),
      'AI_UPDATE_CONFIG': this.handleAIUpdateConfig.bind(this),
      'AI_GET_CONFIG': this.handleAIGetConfig.bind(this),
      'AI_GET_STATS': this.handleAIGetStats.bind(this),
      'AI_RESET_STATS': this.handleAIResetStats.bind(this),
      'GET_SETTINGS': this.handleGetSettings.bind(this),
      'UPDATE_SETTINGS': this.handleUpdateSettings.bind(this),
      'MANUAL_SYNC': this.handleManualSync.bind(this),
      'GET_BOOKMARKS': this.handleGetBookmarks.bind(this),
      'GET_BOOKMARK': this.handleGetBookmark.bind(this)
    }
  }

  /**
   * 处理消息的主入口
   */
  async handleMessage(
    message: ExtensionMessage,
    sender: chrome.runtime.MessageSender
  ): Promise<ExtensionMessageResponse> {
    try {
      // 验证消息格式
      const validationResult = this.validateMessage(message)
      if (!validationResult.isValid) {
        return {
          success: false,
          error: `消息验证失败: ${validationResult.errors.map(e => e.message).join(', ')}`,
          requestId: message.requestId
        }
      }

      // 获取对应的处理器
      const handler = this.handlers[message.type]
      if (!handler) {
        return {
          success: false,
          error: `未知的消息类型: ${message.type}`,
          requestId: message.requestId
        }
      }

      // 执行处理器
      console.log(`处理消息: ${message.type}`, message.data)
      const response = await handler(message as any, sender)
      
      // 添加请求ID到响应中
      if (message.requestId) {
        response.requestId = message.requestId
      }

      return response
    } catch (error) {
      console.error(`处理消息失败 [${message.type}]:`, error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '未知错误',
        requestId: message.requestId
      }
    }
  }

  /**
   * 验证消息格式
   */
  private validateMessage(message: any): { isValid: boolean; errors: any[] } {
    const errors: any[] = []

    if (!message || typeof message !== 'object') {
      errors.push({ message: '消息必须是对象' })
    }

    if (!message.type || typeof message.type !== 'string') {
      errors.push({ message: '消息类型必须是字符串' })
    }

    return {
      isValid: errors.length === 0,
      errors
    }
  }

  /**
   * Ping处理器 - 用于测试连接
   */
  private async handlePing(
    message: PingMessage,
    sender: chrome.runtime.MessageSender
  ): Promise<MessageResponse> {
    return {
      success: true,
      data: {
        status: 'pong',
        timestamp: Date.now()
      }
    }
  }

  /**
   * 获取页面信息处理器
   */
  private async handleGetPageInfo(
    message: GetPageInfoMessage,
    sender: chrome.runtime.MessageSender
  ): Promise<MessageResponse> {
    try {
      if (!sender.tab?.id) {
        throw new Error('无法获取标签页信息')
      }

      // 向content script请求页面信息
      const response = await chrome.tabs.sendMessage(sender.tab.id, {
        type: 'EXTRACT_PAGE_INFO',
        data: message.data
      })

      return {
        success: true,
        data: response
      }
    } catch (error) {
      throw new Error(`获取页面信息失败: ${error}`)
    }
  }

  /**
   * 获取链接信息处理器
   */
  private async handleGetLinkInfo(
    message: GetLinkInfoMessage,
    sender: chrome.runtime.MessageSender
  ): Promise<MessageResponse> {
    try {
      if (!sender.tab?.id) {
        throw new Error('无法获取标签页信息')
      }

      // 向content script请求链接信息
      const response = await chrome.tabs.sendMessage(sender.tab.id, {
        type: 'EXTRACT_LINK_INFO',
        data: message.data
      })

      return {
        success: true,
        data: response
      }
    } catch (error) {
      throw new Error(`获取链接信息失败: ${error}`)
    }
  }

  /**
   * 快速收藏处理器
   */
  private async handleQuickBookmark(
    message: QuickBookmarkMessage,
    sender: chrome.runtime.MessageSender
  ): Promise<MessageResponse> {
    try {
      // 确保数据库已初始化
      await indexedDBService.init()
      
      // 调用收藏服务保存收藏
      const bookmarkId = await bookmarkService.quickBookmark(
        message.data.title,
        message.data.url,
        message.data.favIconUrl,
        message.data.selectedText
      )
      
      console.log('快速收藏成功:', bookmarkId)
      
      // 更新状态和缓存
      if (message.data.url) {
        // 通知状态服务更新缓存
        await bookmarkStatusService.handleBookmarkAdded(message.data.url, bookmarkId)
        
        // 通知标签页状态管理器更新状态（统一处理图标更新）
        await tabStatusManager.handleBookmarkStatusChange(message.data.url, true)
        
        // 广播状态变化给所有监听者（包括弹窗）
        this.broadcastBookmarkStatusChange(message.data.url, true, bookmarkId)
      }

      return {
        success: true,
        data: { bookmarkId }
      }
    } catch (error) {
      throw new Error(`快速收藏失败: ${error}`)
    }
  }

  /**
   * 收藏选中文字处理器
   */
  private async handleBookmarkSelectedText(
    message: BookmarkSelectedTextMessage,
    sender: chrome.runtime.MessageSender
  ): Promise<MessageResponse> {
    try {
      // 确保数据库已初始化
      await indexedDBService.init()
      
      // 调用收藏服务保存选中文字
      const bookmarkId = await bookmarkService.bookmarkSelectedText(
        message.data.selectedText,
        message.data.url,
        message.data.title,
        message.data.context
      )
      
      console.log('收藏选中文字成功:', bookmarkId)

      return {
        success: true,
        data: { bookmarkId }
      }
    } catch (error) {
      throw new Error(`收藏选中文字失败: ${error}`)
    }
  }

  /**
   * 保存详细收藏处理器
   */
  private async handleSaveDetailedBookmark(
    message: SaveDetailedBookmarkMessage,
    sender: chrome.runtime.MessageSender
  ): Promise<MessageResponse> {
    try {
      // 确保数据库已初始化
      await indexedDBService.init()
      
      // 调用收藏服务保存详细收藏
      const bookmarkId = await bookmarkService.saveBookmark(message.data)
      
      console.log('保存详细收藏成功:', bookmarkId)
      
      // 更新状态和缓存
      if (message.data.url) {
        // 通知状态服务更新缓存
        await bookmarkStatusService.handleBookmarkAdded(message.data.url, bookmarkId)
        
        // 通知标签页状态管理器更新状态（统一处理图标更新）
        await tabStatusManager.handleBookmarkStatusChange(message.data.url, true)
        
        // 广播状态变化给所有监听者（包括弹窗）
        this.broadcastBookmarkStatusChange(message.data.url, true, bookmarkId)
      }

      return {
        success: true,
        data: { bookmarkId }
      }
    } catch (error) {
      throw new Error(`保存详细收藏失败: ${error}`)
    }
  }

  /**
   * 更新收藏处理器
   */
  private async handleUpdateBookmark(
    message: UpdateBookmarkMessage,
    sender: chrome.runtime.MessageSender
  ): Promise<MessageResponse> {
    try {
      // 确保数据库已初始化
      await indexedDBService.init()
      
      // 调用收藏服务更新收藏
      await bookmarkService.updateBookmark(message.data.id, message.data.updates)
      
      console.log('更新收藏成功:', message.data.id)

      return {
        success: true,
        data: { bookmarkId: message.data.id }
      }
    } catch (error) {
      throw new Error(`更新收藏失败: ${error}`)
    }
  }

  /**
   * 删除收藏处理器
   */
  private async handleDeleteBookmark(
    message: DeleteBookmarkMessage,
    sender: chrome.runtime.MessageSender
  ): Promise<MessageResponse> {
    try {
      // 确保数据库已初始化
      await indexedDBService.init()
      
      // 调用收藏服务删除收藏
      await bookmarkService.deleteBookmark(message.data.id)
      
      console.log('删除收藏成功:', message.data.id)
      
      // 获取被删除的收藏信息以更新状态
      const deletedBookmark = await bookmarkService.getBookmark(message.data.id)
      
      // 通知状态服务更新缓存
      if (deletedBookmark?.url) {
        await bookmarkStatusService.handleBookmarkRemoved(deletedBookmark.url)
        
        // 通知标签页状态管理器更新状态（统一处理图标更新）
        await tabStatusManager.handleBookmarkStatusChange(deletedBookmark.url, false)
        
        // 广播状态变化给所有监听者（包括弹窗）
        this.broadcastBookmarkStatusChange(deletedBookmark.url, false)
      }

      return {
        success: true,
        data: { bookmarkId: message.data.id }
      }
    } catch (error) {
      throw new Error(`删除收藏失败: ${error}`)
    }
  }

  /**
   * 检查收藏状态处理器
   */
  private async handleCheckBookmarkStatus(
    message: CheckBookmarkStatusMessage,
    sender: chrome.runtime.MessageSender
  ): Promise<MessageResponse> {
    try {
      // 确保数据库已初始化
      await indexedDBService.init()
      
      // 调用收藏服务检查收藏状态
      const status = await bookmarkService.checkBookmarkStatus(message.data.url)
      
      console.log('检查收藏状态:', message.data.url, status)
      
      return {
        success: true,
        data: status
      }
    } catch (error) {
      throw new Error(`检查收藏状态失败: ${error}`)
    }
  }

  /**
   * 更新图标状态处理器（已废弃，由tabStatusManager统一处理）
   */
  private async handleUpdateIconStatus(
    message: UpdateIconStatusMessage,
    sender: chrome.runtime.MessageSender
  ): Promise<MessageResponse> {
    try {
      // 不再直接更新图标，而是通过tabStatusManager统一处理
      console.log('UPDATE_ICON_STATUS消息已废弃，请使用tabStatusManager统一处理图标状态')
      
      return {
        success: true,
        data: {}
      }
    } catch (error) {
      throw new Error(`更新图标状态失败: ${error}`)
    }
  }

  /**
   * AI生成建议处理器
   */
  private async handleAIGenerateSuggestions(
    message: AIGenerateSuggestionsMessage,
    sender: chrome.runtime.MessageSender
  ): Promise<MessageResponse> {
    try {
      // 调用AI服务生成标签和分类建议
      const [tagsResponse, categoryResponse] = await Promise.all([
        aiService.generateTags({
          content: message.data.content,
          title: message.data.title,
          url: message.data.url,
          maxTags: 5
        }),
        aiService.generateCategory({
          content: message.data.content,
          title: message.data.title,
          url: message.data.url
        })
      ])
      
      const suggestions = {
        tags: tagsResponse.tags,
        category: categoryResponse.category,
        confidence: Math.min(tagsResponse.confidence, categoryResponse.confidence)
      }
      
      return {
        success: true,
        data: suggestions
      }
    } catch (error) {
      console.error('AI生成建议失败:', error)
      
      // 返回默认建议
      const suggestions = {
        tags: ['未分类'],
        category: '默认分类',
        confidence: 0.1
      }
      
      return {
        success: true,
        data: suggestions
      }
    }
  }

  /**
   * AI生成文本处理器
   */
  private async handleAIGenerateText(
    message: any,
    sender: chrome.runtime.MessageSender
  ): Promise<MessageResponse> {
    try {
      const response = await aiChatService.generateText(message.data)
      
      return {
        success: true,
        data: response
      }
    } catch (error) {
      throw new Error(`AI生成文本失败: ${error}`)
    }
  }

  /**
   * AI生成标签处理器
   */
  private async handleAIGenerateTags(
    message: any,
    sender: chrome.runtime.MessageSender
  ): Promise<MessageResponse> {
    try {
      const response = await aiService.generateTags(message.data)
      
      return {
        success: true,
        data: response
      }
    } catch (error) {
      throw new Error(`AI生成标签失败: ${error}`)
    }
  }

  /**
   * AI生成分类处理器
   */
  private async handleAIGenerateCategory(
    message: any,
    sender: chrome.runtime.MessageSender
  ): Promise<MessageResponse> {
    try {
      const response = await aiService.generateCategory(message.data)
      
      return {
        success: true,
        data: response
      }
    } catch (error) {
      throw new Error(`AI生成分类失败: ${error}`)
    }
  }

  /**
   * AI生成描述处理器
   */
  private async handleAIGenerateDescription(
    message: any,
    sender: chrome.runtime.MessageSender
  ): Promise<MessageResponse> {
    try {
      const response = await aiService.generateDescription(message.data)
      
      return {
        success: true,
        data: response
      }
    } catch (error) {
      throw new Error(`AI生成描述失败: ${error}`)
    }
  }

  /**
   * AI推荐标签处理器
   */
  private async handleAIRecommendTags(
    message: any,
    sender: chrome.runtime.MessageSender
  ): Promise<MessageResponse> {
    try {
      const response = await aiRecommendationService.recommendTags(message.data)
      
      return {
        success: true,
        data: response
      }
    } catch (error) {
      throw new Error(`AI推荐标签失败: ${error}`)
    }
  }

  /**
   * AI推荐文件夹处理器
   */
  private async handleAIRecommendFolders(
    message: any,
    sender: chrome.runtime.MessageSender
  ): Promise<MessageResponse> {
    try {
      const response = await aiRecommendationService.recommendFolders(message.data)
      
      return {
        success: true,
        data: response
      }
    } catch (error) {
      throw new Error(`AI推荐文件夹失败: ${error}`)
    }
  }

  /**
   * AI批量推荐处理器
   */
  private async handleAIRecommendBoth(
    message: any,
    sender: chrome.runtime.MessageSender
  ): Promise<MessageResponse> {
    try {
      const response = await aiRecommendationService.recommendBoth(message.data)
      
      return {
        success: true,
        data: response
      }
    } catch (error) {
      throw new Error(`AI批量推荐失败: ${error}`)
    }
  }

  /**
   * AI测试连接处理器
   */
  private async handleAITestConnection(
    message: any,
    sender: chrome.runtime.MessageSender
  ): Promise<MessageResponse> {
    try {
      const response = await aiConfigService.testConnection(message.data?.config)
      
      return {
        success: true,
        data: response
      }
    } catch (error) {
      throw new Error(`AI连接测试失败: ${error}`)
    }
  }

  /**
   * AI更新配置处理器
   */
  private async handleAIUpdateConfig(
    message: any,
    sender: chrome.runtime.MessageSender
  ): Promise<MessageResponse> {
    try {
      await aiConfigService.saveConfig(message.data)
      const config = await aiConfigService.getConfig()
      
      return {
        success: true,
        data: { config }
      }
    } catch (error) {
      throw new Error(`AI配置更新失败: ${error}`)
    }
  }

  /**
   * AI获取配置处理器
   */
  private async handleAIGetConfig(
    message: any,
    sender: chrome.runtime.MessageSender
  ): Promise<MessageResponse> {
    try {
      const config = await aiConfigService.getConfig()
      
      return {
        success: true,
        data: { config }
      }
    } catch (error) {
      throw new Error(`获取AI配置失败: ${error}`)
    }
  }

  /**
   * AI获取统计信息处理器
   */
  private async handleAIGetStats(
    message: any,
    sender: chrome.runtime.MessageSender
  ): Promise<MessageResponse> {
    try {
      const stats = aiService.getStats()
      
      return {
        success: true,
        data: { stats }
      }
    } catch (error) {
      throw new Error(`获取AI统计信息失败: ${error}`)
    }
  }

  /**
   * AI重置统计信息处理器
   */
  private async handleAIResetStats(
    message: any,
    sender: chrome.runtime.MessageSender
  ): Promise<MessageResponse> {
    try {
      aiService.resetStats()
      
      return {
        success: true,
        data: {}
      }
    } catch (error) {
      throw new Error(`重置AI统计信息失败: ${error}`)
    }
  }

  /**
   * 获取设置处理器
   */
  private async handleGetSettings(
    message: GetSettingsMessage,
    sender: chrome.runtime.MessageSender
  ): Promise<MessageResponse> {
    try {
      const result = await chrome.storage.local.get(['settings'])
      const settings = result.settings || {}
      
      return {
        success: true,
        data: settings
      }
    } catch (error) {
      throw new Error(`获取设置失败: ${error}`)
    }
  }

  /**
   * 更新设置处理器
   */
  private async handleUpdateSettings(
    message: UpdateSettingsMessage,
    sender: chrome.runtime.MessageSender
  ): Promise<MessageResponse> {
    try {
      const result = await chrome.storage.local.get(['settings'])
      const currentSettings = result.settings || {}
      const updatedSettings = { ...currentSettings, ...message.data }
      
      await chrome.storage.local.set({ settings: updatedSettings })
      
      return {
        success: true,
        data: updatedSettings
      }
    } catch (error) {
      throw new Error(`更新设置失败: ${error}`)
    }
  }

  /**
   * 手动同步处理器
   */
  private async handleManualSync(
    message: ManualSyncMessage,
    sender: chrome.runtime.MessageSender
  ): Promise<MessageResponse> {
    try {
      // 这里会执行实际的同步逻辑
      // 暂时模拟同步过程
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      return {
        success: true,
        data: {
          syncedCount: 0,
          lastSync: new Date()
        }
      }
    } catch (error) {
      throw new Error(`手动同步失败: ${error}`)
    }
  }

  /**
   * 获取收藏列表处理器
   */
  private async handleGetBookmarks(
    message: any,
    sender: chrome.runtime.MessageSender
  ): Promise<MessageResponse> {
    try {
      // 确保数据库已初始化
      await indexedDBService.init()
      
      // 调用收藏服务获取收藏列表
      const bookmarks = await bookmarkService.getBookmarks(message.data?.filter, message.data?.sort)
      
      console.log('获取收藏列表成功，数量:', bookmarks.length)
      
      return {
        success: true,
        data: bookmarks
      }
    } catch (error) {
      throw new Error(`获取收藏列表失败: ${error}`)
    }
  }

  /**
   * 获取单个收藏处理器
   */
  private async handleGetBookmark(
    message: any,
    sender: chrome.runtime.MessageSender
  ): Promise<MessageResponse> {
    try {
      // 确保数据库已初始化
      await indexedDBService.init()
      
      // 调用收藏服务获取单个收藏
      const bookmark = await bookmarkService.getBookmark(message.data.id)
      
      if (bookmark) {
        console.log('获取收藏成功:', bookmark.id)
        return {
          success: true,
          data: bookmark
        }
      } else {
        return {
          success: false,
          error: '收藏不存在'
        }
      }
    } catch (error) {
      throw new Error(`获取收藏失败: ${error}`)
    }
  }



  /**
   * 初始化右键菜单
   */
  private initializeContextMenus(): void {
    try {
      // 先清除所有现有菜单项，避免重复创建
      chrome.contextMenus.removeAll(() => {
        // 创建主菜单项
        chrome.contextMenus.create({
          id: 'universe-bag-main',
          title: 'Universe Bag',
          contexts: ['page', 'selection', 'link']
        })

        // 收藏当前页面
        chrome.contextMenus.create({
          id: 'bookmark-page',
          parentId: 'universe-bag-main',
          title: '收藏当前页面',
          contexts: ['page']
        })

        // 收藏选中文字
        chrome.contextMenus.create({
          id: 'bookmark-selection',
          parentId: 'universe-bag-main',
          title: '收藏选中文字',
          contexts: ['selection']
        })

        // 收藏链接
        chrome.contextMenus.create({
          id: 'bookmark-link',
          parentId: 'universe-bag-main',
          title: '收藏链接',
          contexts: ['link']
        })

        console.log('右键菜单初始化完成')
      })

      // 监听右键菜单点击事件
      chrome.contextMenus.onClicked.addListener(this.handleContextMenuClick.bind(this))

    } catch (error) {
      console.error('初始化右键菜单失败:', error)
    }
  }

  /**
   * 处理右键菜单点击事件
   */
  private async handleContextMenuClick(
    info: chrome.contextMenus.OnClickData,
    tab?: chrome.tabs.Tab
  ): Promise<void> {
    try {
      if (!tab?.id) {
        console.error('无法获取当前标签页信息')
        return
      }

      console.log('右键菜单点击:', info.menuItemId, info)

      switch (info.menuItemId) {
        case 'bookmark-page':
          await this.handleBookmarkPageFromContextMenu(tab)
          break

        case 'bookmark-selection':
          if (info.selectionText) {
            await this.handleBookmarkSelectionFromContextMenu(info.selectionText, tab)
          }
          break

        case 'bookmark-link':
          if (info.linkUrl) {
            await this.handleBookmarkLinkFromContextMenu(info.linkUrl, tab)
          }
          break

        default:
          console.warn('未知的右键菜单项:', info.menuItemId)
      }
    } catch (error) {
      console.error('处理右键菜单点击失败:', error)
    }
  }

  /**
   * 从右键菜单收藏页面
   */
  private async handleBookmarkPageFromContextMenu(tab: chrome.tabs.Tab): Promise<void> {
    try {
      // 确保数据库已初始化
      await indexedDBService.init()

      // 获取页面信息
      const pageInfo = await chrome.tabs.sendMessage(tab.id!, {
        type: 'EXTRACT_PAGE_INFO',
        data: { includeMetadata: true }
      })

      // 快速收藏
      const bookmarkId = await bookmarkService.quickBookmark(
        pageInfo.title || tab.title || '无标题',
        pageInfo.url || tab.url || '',
        pageInfo.favicon || tab.favIconUrl
      )

      // 更新图标状态
      await this.updateIconStatus(tab.id!, true)

      // 显示通知
      await this.showNotification('收藏成功', `已收藏页面: ${pageInfo.title}`)

      console.log('从右键菜单收藏页面成功:', bookmarkId)
    } catch (error) {
      console.error('从右键菜单收藏页面失败:', error)
      await this.showNotification('收藏失败', '收藏页面时发生错误')
    }
  }

  /**
   * 从右键菜单收藏选中文字
   */
  private async handleBookmarkSelectionFromContextMenu(
    selectionText: string,
    tab: chrome.tabs.Tab
  ): Promise<void> {
    try {
      // 确保数据库已初始化
      await indexedDBService.init()

      // 收藏选中文字
      const bookmarkId = await bookmarkService.bookmarkSelectedText(
        selectionText,
        tab.url || '',
        tab.title || '无标题'
      )

      // 显示通知
      const shortText = selectionText.length > 30 ? 
        selectionText.substring(0, 30) + '...' : 
        selectionText
      await this.showNotification('收藏成功', `已收藏文字: ${shortText}`)

      console.log('从右键菜单收藏选中文字成功:', bookmarkId)
    } catch (error) {
      console.error('从右键菜单收藏选中文字失败:', error)
      await this.showNotification('收藏失败', '收藏选中文字时发生错误')
    }
  }

  /**
   * 从右键菜单收藏链接
   */
  private async handleBookmarkLinkFromContextMenu(
    linkUrl: string,
    tab: chrome.tabs.Tab
  ): Promise<void> {
    try {
      // 确保数据库已初始化
      await indexedDBService.init()

      // 获取链接信息
      const linkInfo = await chrome.tabs.sendMessage(tab.id!, {
        type: 'EXTRACT_LINK_INFO',
        data: { url: linkUrl }
      })

      // 收藏链接
      const bookmarkId = await bookmarkService.quickBookmark(
        linkInfo.title || linkUrl,
        linkUrl,
        linkInfo.favicon
      )

      // 显示通知
      await this.showNotification('收藏成功', `已收藏链接: ${linkInfo.title || linkUrl}`)

      console.log('从右键菜单收藏链接成功:', bookmarkId)
    } catch (error) {
      console.error('从右键菜单收藏链接失败:', error)
      await this.showNotification('收藏失败', '收藏链接时发生错误')
    }
  }

  /**
   * 显示通知
   */
  private async showNotification(title: string, message: string): Promise<void> {
    try {
      await chrome.notifications.create({
        type: 'basic',
        iconUrl: '/icons/icon-48.png',
        title: title,
        message: message
      })
    } catch (error) {
      console.error('显示通知失败:', error)
    }
  }

  /**
   * 广播收藏状态变化
   * @param url 收藏的URL
   * @param isBookmarked 是否已收藏
   * @param bookmarkId 收藏ID
   */
  private broadcastBookmarkStatusChange(url: string, isBookmarked: boolean, bookmarkId?: string): void {
    try {
      // 向所有扩展页面广播状态变化
      chrome.runtime.sendMessage({
        type: 'BOOKMARK_STATUS_CHANGED',
        data: {
          url,
          isBookmarked,
          bookmarkId,
          timestamp: Date.now()
        }
      }).catch(error => {
        // 忽略没有监听者的错误
        console.log('广播收藏状态变化 - 没有监听者:', error.message)
      })
    } catch (error) {
      console.error('广播收藏状态变化失败:', error)
    }
  }

  /**
   * 生成唯一ID
   */
  private generateId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2)
  }
}

// 创建全局消息处理器实例
export const messageHandler = new BackgroundMessageHandler()