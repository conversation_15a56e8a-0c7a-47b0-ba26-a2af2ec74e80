import React, { useState, useEffect } from 'react'
import { TagColorPicker } from './TagColorPicker'
import { TagUtils } from '../utils/tagUtils'
import { ColorUtils } from '../utils/colorUtils'
import type { ValidationResult, ValidationError } from '../types'

/**
 * 标签表单数据接口
 */
export interface TagFormData {
  name: string
  color?: string
}

/**
 * 标签表单组件属性接口
 */
export interface TagFormProps {
  /** 编辑时传入的现有标签数据 */
  tag?: {
    id?: string
    name: string
    color?: string
  }
  /** 保存回调函数 */
  onSave: (tagData: TagFormData) => Promise<void>
  /** 取消回调函数 */
  onCancel: () => void
  /** 是否处于加载状态 */
  loading?: boolean
  /** 组件样式类名 */
  className?: string
  /** 现有标签列表（用于重复检查） */
  existingTags?: Array<{ id?: string; name: string }>
}

/**
 * 表单验证错误接口
 */
interface FormErrors {
  name?: string
  color?: string
  general?: string
}

/**
 * 标签表单组件
 * 提供标签创建和编辑的表单界面，包含名称输入、颜色选择和表单验证
 */
export const TagForm: React.FC<TagFormProps> = ({
  tag,
  onSave,
  onCancel,
  loading = false,
  className = '',
  existingTags = []
}) => {
  // 表单数据状态
  const [formData, setFormData] = useState<TagFormData>({
    name: tag?.name || '',
    color: tag?.color || ''
  })

  // 表单验证错误状态
  const [errors, setErrors] = useState<FormErrors>({})

  // 表单是否已修改状态
  const [isDirty, setIsDirty] = useState(false)

  // 是否正在验证状态
  const [isValidating, setIsValidating] = useState(false)

  // 是否为编辑模式
  const isEditMode = Boolean(tag?.id)

  // 当传入的标签数据变化时，更新表单数据
  useEffect(() => {
    if (tag) {
      setFormData({
        name: tag.name || '',
        color: tag.color || ''
      })
      setIsDirty(false)
      setErrors({})
    }
  }, [tag])

  /**
   * 验证表单数据
   */
  const validateForm = async (data: TagFormData): Promise<FormErrors> => {
    const newErrors: FormErrors = {}

    // 验证标签名称
    if (!data.name.trim()) {
      newErrors.name = '标签名称不能为空'
    } else {
      // 使用TagUtils验证标签名称格式
      const nameValidation = TagUtils.validateTagName(data.name.trim())
      if (!nameValidation || !nameValidation.isValid) {
        newErrors.name = nameValidation?.errors?.[0]?.message || '标签名称格式无效'
      } else {
        // 检查名称重复
        const normalizedName = TagUtils.normalizeTagName(data.name.trim())
        const isDuplicate = existingTags.some(existingTag => {
          // 编辑模式下排除当前标签
          if (isEditMode && existingTag.id === tag?.id) {
            return false
          }
          return TagUtils.normalizeTagName(existingTag.name) === normalizedName
        })

        if (isDuplicate) {
          newErrors.name = '标签名称已存在，请使用其他名称'
        }
      }
    }

    // 验证颜色格式
    if (data.color && !ColorUtils.isValidColor(data.color)) {
      newErrors.color = '颜色格式无效'
    }

    return newErrors
  }

  /**
   * 处理表单字段变化
   */
  const handleFieldChange = (field: keyof TagFormData, value: string) => {
    const newFormData = { ...formData, [field]: value }
    setFormData(newFormData)
    setIsDirty(true)

    // 清除相关字段的错误
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }))
    }

    // 清除general错误（当用户开始修改时）
    if (errors.general) {
      setErrors(prev => ({ ...prev, general: undefined }))
    }

    // 对于名称字段，进行简单的即时验证
    if (field === 'name') {
      const trimmedValue = value.trim()
      if (trimmedValue.length === 0) {
        setErrors(prev => ({ ...prev, name: '标签名称不能为空' }))
      } else if (trimmedValue.length > 50) {
        setErrors(prev => ({ ...prev, name: '标签名称长度不能超过50个字符' }))
      } else {
        // 清除名称错误
        setErrors(prev => ({ ...prev, name: undefined }))
      }
    }
  }

  /**
   * 处理表单提交
   */
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (loading) return

    // 验证表单
    const formErrors = await validateForm(formData)
    setErrors(formErrors)

    // 如果有错误，不提交
    if (Object.keys(formErrors).length > 0) {
      return
    }

    try {
      // 准备提交数据
      const submitData: TagFormData = {
        name: TagUtils.normalizeTagName(formData.name.trim()),
        color: formData.color || ColorUtils.generateColorFromString(formData.name.trim())
      }

      await onSave(submitData)
    } catch (error) {
      console.error('保存标签失败:', error)
      setErrors({
        general: error instanceof Error ? error.message : '保存失败，请重试'
      })
    }
  }

  /**
   * 处理取消操作
   */
  const handleCancel = () => {
    if (isDirty) {
      const confirmed = window.confirm('您有未保存的更改，确定要取消吗？')
      if (!confirmed) return
    }
    onCancel()
  }

  /**
   * 生成颜色预览
   */
  const getColorPreview = () => {
    const color = formData.color || ColorUtils.generateColorFromString(formData.name || 'default')
    return color
  }

  /**
   * 检查表单是否有效
   */
  const isFormValid = () => {
    // 基本检查：标签名称不能为空且长度合理
    const hasValidName = formData.name.trim().length > 0 && formData.name.trim().length <= 50
    
    // 检查是否有名称相关的错误
    const hasNameError = Boolean(errors.name)
    
    // 调试日志（仅在开发环境）
    if (typeof window !== 'undefined' && window.location.hostname === 'localhost') {
      console.log('TagForm isFormValid 检查:', {
        hasValidName,
        hasNameError,
        formDataName: formData.name,
        formDataNameLength: formData.name.length,
        formDataNameTrimmed: formData.name.trim(),
        errors
      })
    }
    
    return hasValidName && !hasNameError
  }

  return (
    <form onSubmit={handleSubmit} className={`tag-form ${className}`}>
      {/* 表单标题 */}
      <div className="mb-6">
        <h3 className="text-lg font-semibold text-gray-900">
          {isEditMode ? '编辑标签' : '创建新标签'}
        </h3>
        <p className="text-sm text-gray-600 mt-1">
          {isEditMode ? '修改标签信息' : '填写标签信息以创建新标签'}
        </p>
      </div>

      {/* 全局错误提示 */}
      {errors.general && (
        <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md">
          <div className="flex">
            <svg className="w-5 h-5 text-red-400 mr-2 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
            </svg>
            <span className="text-sm text-red-700">{errors.general}</span>
          </div>
        </div>
      )}

      {/* 标签名称字段 */}
      <div className="mb-6">
        <label htmlFor="tag-name" className="block text-sm font-medium text-gray-700 mb-2">
          标签名称 <span className="text-red-500">*</span>
        </label>
        <div className="relative">
          <input
            id="tag-name"
            type="text"
            value={formData.name}
            onChange={(e) => handleFieldChange('name', e.target.value)}
            disabled={loading}
            placeholder="输入标签名称"
            maxLength={50}
            className={`
              w-full px-3 py-2 border rounded-md text-sm
              focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent
              ${errors.name 
                ? 'border-red-300 bg-red-50' 
                : 'border-gray-300 bg-white'
              }
              ${loading ? 'opacity-50 cursor-not-allowed' : ''}
            `}
          />
          
          {/* 验证状态指示器 */}
          {isValidating && (
            <div className="absolute right-3 top-2.5">
              <svg className="w-4 h-4 text-gray-400 animate-spin" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" />
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" />
              </svg>
            </div>
          )}
        </div>
        
        {/* 字符计数 */}
        <div className="flex justify-between items-center mt-1">
          <div>
            {errors.name && (
              <span className="text-sm text-red-600">{errors.name}</span>
            )}
          </div>
          <span className="text-xs text-gray-500">
            {formData.name.length}/50
          </span>
        </div>
      </div>

      {/* 标签颜色字段 */}
      <div className="mb-6">
        <TagColorPicker
          value={formData.color}
          onChange={(color) => handleFieldChange('color', color)}
          placeholder="选择标签颜色"
          disabled={loading}
        />
        {errors.color && (
          <div className="mt-2 text-sm text-red-600">{errors.color}</div>
        )}
      </div>

      {/* 标签预览 */}
      {formData.name && (
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            标签预览
          </label>
          <div className="p-3 bg-gray-50 rounded-md">
            <div className="flex items-center space-x-2">
              <span 
                className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium"
                style={{
                  backgroundColor: getColorPreview(),
                  color: ColorUtils.getContrastColor(getColorPreview())
                }}
              >
                {formData.name || '标签名称'}
              </span>
              <span className="text-xs text-gray-500">
                这是标签在书签中的显示效果
              </span>
            </div>
          </div>
        </div>
      )}

      {/* 表单操作按钮 */}
      <div className="flex justify-end space-x-3 pt-4 border-t">
        <button
          type="button"
          onClick={handleCancel}
          disabled={loading}
          className={`
            px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md
            hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2
            ${loading ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
          `}
        >
          取消
        </button>
        
        <button
          type="submit"
          disabled={loading || !isFormValid()}
          className={`
            px-4 py-2 text-sm font-medium text-white rounded-md
            focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2
            ${loading || !isFormValid()
              ? 'bg-gray-400 cursor-not-allowed'
              : 'bg-blue-600 hover:bg-blue-700 cursor-pointer'
            }
          `}
        >
          {loading ? (
            <div className="flex items-center">
              <svg className="w-4 h-4 mr-2 animate-spin" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" />
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" />
              </svg>
              保存中...
            </div>
          ) : (
            isEditMode ? '保存修改' : '创建标签'
          )}
        </button>
      </div>
    </form>
  )
}

export default TagForm