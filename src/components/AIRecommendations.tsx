// AI推荐组件 - 显示AI推荐的标签和文件夹

import React, { useState, useEffect } from 'react'
import { 
  Sparkles, 
  Tag, 
  Folder, 
  Plus, 
  Check, 
  Loader2,
  RefreshCw,
  AlertCircle
} from 'lucide-react'

// shadcn组件导入
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Separator } from '@/components/ui/separator'

/**
 * AI推荐请求接口
 */
interface AIRecommendationRequest {
  title?: string
  url?: string
  content?: string
  description?: string
  maxRecommendations?: number
}

/**
 * 标签推荐响应接口
 */
interface TagRecommendationResponse {
  existingTags: string[]
  newTags: string[]
  confidence: number
  reasoning?: string
}

/**
 * 文件夹推荐响应接口
 */
interface FolderRecommendationResponse {
  recommendedFolders: Array<{
    name: string
    confidence: number
    reason?: string
  }>
  reasoning?: string
}

/**
 * AI推荐组件属性接口
 */
interface AIRecommendationsProps {
  /** 推荐请求数据 */
  request: AIRecommendationRequest
  /** 当前已选择的标签 */
  selectedTags?: string[]
  /** 当前选择的文件夹 */
  selectedFolder?: string
  /** 标签选择回调 */
  onTagSelect?: (tag: string) => void
  /** 标签取消选择回调 */
  onTagDeselect?: (tag: string) => void
  /** 文件夹选择回调 */
  onFolderSelect?: (folder: string) => void
  /** 是否禁用 */
  disabled?: boolean
  /** 是否显示标签推荐 */
  showTagRecommendations?: boolean
  /** 是否显示文件夹推荐 */
  showFolderRecommendations?: boolean
  /** 自定义样式类名 */
  className?: string
}

/**
 * AI推荐组件
 * 提供基于AI的标签和文件夹推荐功能
 */
const AIRecommendations: React.FC<AIRecommendationsProps> = ({
  request,
  selectedTags = [],
  selectedFolder,
  onTagSelect,
  onTagDeselect,
  onFolderSelect,
  disabled = false,
  showTagRecommendations = true,
  showFolderRecommendations = true,
  className = ''
}) => {
  const [loading, setLoading] = useState(false)
  const [tagRecommendations, setTagRecommendations] = useState<TagRecommendationResponse | null>(null)
  const [folderRecommendations, setFolderRecommendations] = useState<FolderRecommendationResponse | null>(null)
  const [error, setError] = useState<string | null>(null)

  /**
   * 获取AI推荐
   */
  const fetchRecommendations = async () => {
    if (!request.title && !request.content && !request.description) {
      return
    }

    try {
      setLoading(true)
      setError(null)

      console.log('获取AI推荐:', request)

      // 发送消息到background script
      const response = await chrome.runtime.sendMessage({
        type: 'AI_RECOMMEND_BOTH',
        data: request
      })

      if (response?.success) {
        setTagRecommendations(response.data.tags)
        setFolderRecommendations(response.data.folders)
        console.log('AI推荐获取成功:', response.data)
      } else {
        throw new Error(response?.error || 'AI推荐失败')
      }
    } catch (error) {
      console.error('获取AI推荐失败:', error)
      setError(error instanceof Error ? error.message : '获取推荐失败')
    } finally {
      setLoading(false)
    }
  }

  /**
   * 刷新推荐
   */
  const handleRefresh = () => {
    fetchRecommendations()
  }

  /**
   * 处理标签点击
   */
  const handleTagClick = (tag: string) => {
    if (disabled) return

    if (selectedTags.includes(tag)) {
      onTagDeselect?.(tag)
    } else {
      onTagSelect?.(tag)
    }
  }

  /**
   * 处理文件夹点击
   */
  const handleFolderClick = (folder: string) => {
    if (disabled) return
    onFolderSelect?.(folder)
  }

  /**
   * 获取置信度颜色
   */
  const getConfidenceColor = (confidence: number): string => {
    if (confidence >= 0.8) return 'text-green-600'
    if (confidence >= 0.6) return 'text-yellow-600'
    return 'text-gray-600'
  }

  /**
   * 获取置信度文本
   */
  const getConfidenceText = (confidence: number): string => {
    if (confidence >= 0.8) return '高'
    if (confidence >= 0.6) return '中'
    return '低'
  }

  // 当请求数据变化时自动获取推荐
  useEffect(() => {
    const timer = setTimeout(() => {
      fetchRecommendations()
    }, 500) // 防抖处理

    return () => clearTimeout(timer)
  }, [request.title, request.content, request.description, request.url])

  return (
    <div className={`space-y-4 ${className}`}>
      {/* 标题和刷新按钮 */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Sparkles className="w-4 h-4 text-primary" />
          <span className="text-sm font-medium text-foreground">AI智能推荐</span>
        </div>
        <Button
          variant="ghost"
          size="sm"
          onClick={handleRefresh}
          disabled={loading || disabled}
          className="h-8 w-8 p-0"
        >
          <RefreshCw className={`w-3 h-3 ${loading ? 'animate-spin' : ''}`} />
        </Button>
      </div>

      {/* 错误提示 */}
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* 加载状态 */}
      {loading && (
        <div className="flex items-center justify-center py-8">
          <Loader2 className="w-6 h-6 animate-spin text-primary" />
          <span className="ml-2 text-sm text-muted-foreground">AI正在分析内容...</span>
        </div>
      )}

      {/* 标签推荐 */}
      {showTagRecommendations && tagRecommendations && !loading && (
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center text-sm">
              <Tag className="w-4 h-4 mr-2" />
              推荐标签
              <Badge 
                variant="outline" 
                className={`ml-2 text-xs ${getConfidenceColor(tagRecommendations.confidence)}`}
              >
                置信度: {getConfidenceText(tagRecommendations.confidence)}
              </Badge>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            {/* 现有标签推荐 */}
            {tagRecommendations.existingTags.length > 0 && (
              <div>
                <div className="text-xs text-muted-foreground mb-2">现有标签</div>
                <div className="flex flex-wrap gap-2">
                  {tagRecommendations.existingTags.map((tag, index) => {
                    const isSelected = selectedTags.includes(tag)
                    return (
                      <Badge
                        key={index}
                        variant={isSelected ? "default" : "secondary"}
                        className={`cursor-pointer transition-colors ${
                          disabled ? 'opacity-50 cursor-not-allowed' : 'hover:bg-accent'
                        }`}
                        onClick={() => handleTagClick(tag)}
                      >
                        {isSelected ? (
                          <Check className="w-3 h-3 mr-1" />
                        ) : (
                          <Plus className="w-3 h-3 mr-1" />
                        )}
                        {tag}
                      </Badge>
                    )
                  })}
                </div>
              </div>
            )}

            {/* 新标签建议 */}
            {tagRecommendations.newTags.length > 0 && (
              <div>
                {tagRecommendations.existingTags.length > 0 && <Separator />}
                <div className="text-xs text-muted-foreground mb-2">新标签建议</div>
                <div className="flex flex-wrap gap-2">
                  {tagRecommendations.newTags.map((tag, index) => {
                    const isSelected = selectedTags.includes(tag)
                    return (
                      <Badge
                        key={index}
                        variant={isSelected ? "default" : "outline"}
                        className={`cursor-pointer transition-colors ${
                          disabled ? 'opacity-50 cursor-not-allowed' : 'hover:bg-accent'
                        }`}
                        onClick={() => handleTagClick(tag)}
                      >
                        {isSelected ? (
                          <Check className="w-3 h-3 mr-1" />
                        ) : (
                          <Plus className="w-3 h-3 mr-1" />
                        )}
                        {tag}
                      </Badge>
                    )
                  })}
                </div>
              </div>
            )}

            {/* 推荐理由 */}
            {tagRecommendations.reasoning && (
              <div className="text-xs text-muted-foreground pt-2 border-t">
                {tagRecommendations.reasoning}
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* 文件夹推荐 */}
      {showFolderRecommendations && folderRecommendations && !loading && (
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center text-sm">
              <Folder className="w-4 h-4 mr-2" />
              推荐文件夹
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            {folderRecommendations.recommendedFolders.map((folder, index) => {
              const isSelected = selectedFolder === folder.name
              return (
                <div
                  key={index}
                  className={`flex items-center justify-between p-2 rounded-md border cursor-pointer transition-colors ${
                    isSelected 
                      ? 'bg-primary/10 border-primary' 
                      : 'bg-background hover:bg-accent'
                  } ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}
                  onClick={() => handleFolderClick(folder.name)}
                >
                  <div className="flex items-center space-x-2">
                    {isSelected ? (
                      <Check className="w-4 h-4 text-primary" />
                    ) : (
                      <Folder className="w-4 h-4 text-muted-foreground" />
                    )}
                    <span className="text-sm font-medium">{folder.name}</span>
                    <Badge 
                      variant="outline" 
                      className={`text-xs ${getConfidenceColor(folder.confidence)}`}
                    >
                      {getConfidenceText(folder.confidence)}
                    </Badge>
                  </div>
                  {folder.reason && (
                    <span className="text-xs text-muted-foreground max-w-32 truncate">
                      {folder.reason}
                    </span>
                  )}
                </div>
              )
            })}

            {/* 推荐理由 */}
            {folderRecommendations.reasoning && (
              <div className="text-xs text-muted-foreground pt-2 border-t">
                {folderRecommendations.reasoning}
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* 无推荐结果 */}
      {!loading && !error && 
       (!tagRecommendations || (tagRecommendations.existingTags.length === 0 && tagRecommendations.newTags.length === 0)) &&
       (!folderRecommendations || folderRecommendations.recommendedFolders.length === 0) && (
        <div className="text-center py-8 text-muted-foreground">
          <Sparkles className="w-8 h-8 mx-auto mb-2 opacity-50" />
          <p className="text-sm">请输入标题或内容以获取AI推荐</p>
        </div>
      )}
    </div>
  )
}

export default AIRecommendations