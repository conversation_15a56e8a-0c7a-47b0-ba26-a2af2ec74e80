// 自定义Hook - 管理tooltip的显示状态和交互逻辑

import { useState, useCallback, useRef, useEffect } from 'react'

export interface UseTooltipOptions {
  delay?: number
  hideDelay?: number
  disabled?: boolean
}

export interface UseTooltipReturn {
  isVisible: boolean
  show: () => void
  hide: () => void
  toggle: () => void
  triggerProps: {
    onMouseEnter: () => void
    onMouseLeave: () => void
    onFocus: () => void
    onBlur: () => void
    'aria-describedby'?: string
  }
}

/**
 * 自定义Hook：管理tooltip的显示逻辑
 * @param options 配置选项
 * @returns tooltip状态和控制方法
 */
export const useTooltip = (options: UseTooltipOptions = {}): UseTooltipReturn => {
  const { delay = 0, hideDelay = 0, disabled = false } = options
  
  const [isVisible, setIsVisible] = useState(false)
  const showTimeoutRef = useRef<NodeJS.Timeout | null>(null)
  const hideTimeoutRef = useRef<NodeJS.Timeout | null>(null)

  // 清理定时器
  const clearTimeouts = useCallback(() => {
    if (showTimeoutRef.current) {
      clearTimeout(showTimeoutRef.current)
      showTimeoutRef.current = null
    }
    if (hideTimeoutRef.current) {
      clearTimeout(hideTimeoutRef.current)
      hideTimeoutRef.current = null
    }
  }, [])

  // 显示tooltip
  const show = useCallback(() => {
    if (disabled) return
    
    clearTimeouts()
    
    if (delay > 0) {
      showTimeoutRef.current = setTimeout(() => {
        setIsVisible(true)
      }, delay)
    } else {
      setIsVisible(true)
    }
  }, [delay, disabled, clearTimeouts])

  // 隐藏tooltip
  const hide = useCallback(() => {
    clearTimeouts()
    
    if (hideDelay > 0) {
      hideTimeoutRef.current = setTimeout(() => {
        setIsVisible(false)
      }, hideDelay)
    } else {
      setIsVisible(false)
    }
  }, [hideDelay, clearTimeouts])

  // 切换显示状态
  const toggle = useCallback(() => {
    if (isVisible) {
      hide()
    } else {
      show()
    }
  }, [isVisible, show, hide])

  // 处理鼠标进入
  const handleMouseEnter = useCallback(() => {
    show()
  }, [show])

  // 处理鼠标离开
  const handleMouseLeave = useCallback(() => {
    hide()
  }, [hide])

  // 处理焦点获得
  const handleFocus = useCallback(() => {
    show()
  }, [show])

  // 处理焦点失去
  const handleBlur = useCallback(() => {
    hide()
  }, [hide])

  // 组件卸载时清理定时器
  useEffect(() => {
    return () => {
      clearTimeouts()
    }
  }, [clearTimeouts])

  // 返回状态和控制方法
  return {
    isVisible,
    show,
    hide,
    toggle,
    triggerProps: {
      onMouseEnter: handleMouseEnter,
      onMouseLeave: handleMouseLeave,
      onFocus: handleFocus,
      onBlur: handleBlur,
      'aria-describedby': isVisible ? 'tooltip-content' : undefined
    }
  }
}