import React from 'react'

// 视图模式类型
export type ViewMode = 'card' | 'row' | 'compact'

// 视图模式配置接口
interface ViewModeConfig {
  id: ViewMode
  name: string
  icon: React.ReactNode
  description: string
}

// 组件属性接口
interface ViewModeSelectorProps {
  currentMode: ViewMode
  onModeChange: (mode: ViewMode) => void
  className?: string
}

// 视图模式配置数据
const VIEW_MODE_CONFIGS: ViewModeConfig[] = [
  {
    id: 'card',
    name: '卡片视图',
    icon: (
      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <rect x="3" y="3" width="7" height="7" rx="1" />
        <rect x="14" y="3" width="7" height="7" rx="1" />
        <rect x="3" y="14" width="7" height="7" rx="1" />
        <rect x="14" y="14" width="7" height="7" rx="1" />
      </svg>
    ),
    description: '详细卡片布局，显示完整信息'
  },
  {
    id: 'row',
    name: '行视图',
    icon: (
      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <line x1="3" y1="6" x2="21" y2="6" />
        <line x1="3" y1="12" x2="21" y2="12" />
        <line x1="3" y1="18" x2="21" y2="18" />
      </svg>
    ),
    description: '单行文字视图，仅显示标题和URL'
  },
  {
    id: 'compact',
    name: '紧凑视图',
    icon: (
      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <rect x="3" y="3" width="18" height="4" rx="1" />
        <rect x="3" y="9" width="18" height="4" rx="1" />
        <rect x="3" y="15" width="18" height="4" rx="1" />
      </svg>
    ),
    description: '紧凑多行布局，信息密集显示'
  }
]

/**
 * 视图模式选择器组件 - 下拉框版本
 * 支持三种视图模式切换：卡片、行、紧凑
 * 使用下拉框避免布局问题，确保在所有屏幕尺寸下稳定显示
 */
const ViewModeSelector: React.FC<ViewModeSelectorProps> = ({
  currentMode,
  onModeChange,
  className = ''
}) => {
  // 处理模式切换
  const handleModeChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
    const mode = event.target.value as ViewMode
    if (mode !== currentMode) {
      // 保存到本地存储
      localStorage.setItem('bookmark-view-mode', mode)
      // 触发回调
      onModeChange(mode)
    }
  }

  // 获取当前模式的配置
  const currentConfig = VIEW_MODE_CONFIGS.find(config => config.id === currentMode)

  return (
    <div className={`relative ${className}`}>
      <select
        value={currentMode}
        onChange={handleModeChange}
        className="appearance-none bg-white border border-gray-300 rounded-lg pl-10 pr-8 py-2 text-sm font-medium text-gray-700 hover:border-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors duration-200"
        style={{ minWidth: '140px' }}
        aria-label="选择视图模式"
      >
        {VIEW_MODE_CONFIGS.map((config) => (
          <option key={config.id} value={config.id}>
            {config.name}
          </option>
        ))}
      </select>
      
      {/* 自定义下拉箭头 */}
      <div className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
        <svg 
          className="w-4 h-4 text-gray-400" 
          fill="none" 
          stroke="currentColor" 
          viewBox="0 0 24 24"
        >
          <path 
            strokeLinecap="round" 
            strokeLinejoin="round" 
            strokeWidth={2} 
            d="M19 9l-7 7-7-7" 
          />
        </svg>
      </div>
      
      {/* 当前模式图标显示（可选） */}
      <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
        <span className="text-gray-500">
          {currentConfig?.icon}
        </span>
      </div>
    </div>
  )
}

export default ViewModeSelector