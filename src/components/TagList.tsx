// 标签列表组件 - 显示所有标签

import React, { useState, useMemo, useCallback } from 'react'
import { Tag, Plus, Search, SortAsc, SortDesc, Grid, List, Hash, Cloud, CheckSquare, Square } from 'lucide-react'
import TagCard from './TagCard'
import TagCloud from './TagCloud'
import TagBatchActions from './TagBatchActions'
import { useDebounce, useMediaQuery } from '../utils/performance'
import type { Tag as TagType } from '../types'

interface TagWithStats extends TagType {
  usageCount: number
}

interface TagListProps {
  /** 标签列表数据 */
  tags: TagWithStats[]
  /** 编辑标签回调 */
  onTagEdit: (tag: TagType) => void
  /** 删除标签回调 */
  onTagDelete: (tag: TagType) => void
  /** 点击标签回调 */
  onTagClick?: (tag: TagType) => void
  /** 创建新标签回调 */
  onCreateTag?: () => void
  /** 批量删除回调 */
  onBatchDelete?: (tagIds: string[]) => Promise<void>
  /** 批量合并回调 */
  onBatchMerge?: (sourceTagIds: string[], targetTagId: string) => Promise<void>
  /** 批量设置颜色回调 */
  onBatchSetColor?: (tagIds: string[], color: string) => Promise<void>
  /** 是否正在加载 */
  loading?: boolean
  /** 搜索查询 */
  searchQuery?: string
  /** 搜索查询变化回调 */
  onSearchChange?: (query: string) => void
  /** 排序选项 */
  sortBy?: TagSortOption
  /** 排序选项变化回调 */
  onSortChange?: (sortBy: TagSortOption) => void
  /** 自定义CSS类名 */
  className?: string
}

// 排序选项类型
export type TagSortOption = 
  | 'name-asc' | 'name-desc'
  | 'usage-asc' | 'usage-desc'
  | 'created-asc' | 'created-desc'
  | 'updated-asc' | 'updated-desc'

// 视图模式类型
type ViewMode = 'grid' | 'list' | 'cloud'

/**
 * 标签列表组件
 * 以网格布局显示所有标签，支持搜索、排序、空状态和加载状态
 */
const TagList: React.FC<TagListProps> = React.memo(({
  tags,
  onTagEdit,
  onTagDelete,
  onTagClick,
  onCreateTag,
  onBatchDelete,
  onBatchMerge,
  onBatchSetColor,
  loading = false,
  searchQuery = '',
  onSearchChange,
  sortBy = 'name-asc',
  onSortChange,
  className = ''
}) => {
  // 本地状态
  const [viewMode, setViewMode] = useState<ViewMode>('grid')
  const [localSearchQuery, setLocalSearchQuery] = useState(searchQuery)
  const [selectedTags, setSelectedTags] = useState<TagType[]>([])
  const [isSelectionMode, setIsSelectionMode] = useState(false)

  // 检测移动设备
  const isMobile = useMediaQuery('(max-width: 640px)')

  // 防抖搜索处理
  const debouncedSearchChange = useDebounce((query: string) => {
    onSearchChange?.(query)
  }, 300)

  // 处理搜索输入变化
  const handleSearchChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const query = e.target.value
    setLocalSearchQuery(query)
    debouncedSearchChange(query)
  }, [debouncedSearchChange])

  // 处理排序变化
  const handleSortChange = useCallback((e: React.ChangeEvent<HTMLSelectElement>) => {
    const newSortBy = e.target.value as TagSortOption
    onSortChange?.(newSortBy)
  }, [onSortChange])

  // 过滤和排序标签
  const filteredAndSortedTags = useMemo(() => {
    let result = [...tags]

    // 搜索过滤
    const query = onSearchChange ? searchQuery : localSearchQuery
    if (query.trim()) {
      const lowerQuery = query.toLowerCase()
      result = result.filter(tag => 
        tag.name.toLowerCase().includes(lowerQuery)
      )
    }

    // 排序
    result.sort((a, b) => {
      switch (sortBy) {
        case 'name-asc':
          return a.name.localeCompare(b.name, 'zh-CN')
        case 'name-desc':
          return b.name.localeCompare(a.name, 'zh-CN')
        case 'usage-asc':
          return a.usageCount - b.usageCount
        case 'usage-desc':
          return b.usageCount - a.usageCount
        case 'created-asc':
          return new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
        case 'created-desc':
          return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
        case 'updated-asc':
          return new Date(a.updatedAt).getTime() - new Date(b.updatedAt).getTime()
        case 'updated-desc':
          return new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime()
        default:
          return 0
      }
    })

    return result
  }, [tags, searchQuery, localSearchQuery, sortBy, onSearchChange])

  // 处理标签选择
  const handleTagSelect = useCallback((tag: TagType, selected: boolean) => {
    if (selected) {
      setSelectedTags(prev => [...prev, tag])
    } else {
      setSelectedTags(prev => prev.filter(t => t.id !== tag.id))
    }
  }, [])

  // 处理全选/取消全选
  const handleSelectAll = useCallback(() => {
    if (selectedTags.length === filteredAndSortedTags.length) {
      setSelectedTags([])
    } else {
      setSelectedTags([...filteredAndSortedTags])
    }
  }, [selectedTags.length, filteredAndSortedTags])

  // 清除选择
  const handleClearSelection = useCallback(() => {
    setSelectedTags([])
    setIsSelectionMode(false)
  }, [])

  // 切换选择模式
  const toggleSelectionMode = useCallback(() => {
    setIsSelectionMode(!isSelectionMode)
    if (isSelectionMode) {
      setSelectedTags([])
    }
  }, [isSelectionMode])

  // 检查标签是否被选中
  const isTagSelected = useCallback((tag: TagType) => {
    return selectedTags.some(selected => selected.id === tag.id)
  }, [selectedTags])

  // 渲染加载状态
  const renderLoadingState = () => (
    <div className="text-center py-12">
      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto mb-4"></div>
      <p className="text-gray-500">加载标签数据中...</p>
    </div>
  )

  // 渲染空状态
  const renderEmptyState = () => {
    const hasSearchQuery = (onSearchChange ? searchQuery : localSearchQuery).trim()
    
    if (hasSearchQuery) {
      // 搜索无结果状态
      return (
        <div className="text-center py-12">
          <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <Search className="w-8 h-8 text-gray-400" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            未找到匹配的标签
          </h3>
          <p className="text-gray-500 mb-6">
            尝试使用不同的关键词搜索，或创建新的标签
          </p>
          {onCreateTag && (
            <button
              onClick={onCreateTag}
              className="inline-flex items-center px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors"
            >
              <Plus className="w-4 h-4 mr-2" />
              创建标签
            </button>
          )}
        </div>
      )
    }

    // 完全空状态
    return (
      <div className="text-center py-12">
        <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <Tag className="w-8 h-8 text-gray-400" />
        </div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">
          暂无标签
        </h3>
        <p className="text-gray-500 mb-6">
          创建您的第一个标签来更好地组织书签
        </p>
        {onCreateTag && (
          <button
            onClick={onCreateTag}
            className="inline-flex items-center px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors"
          >
            <Plus className="w-4 h-4 mr-2" />
            创建标签
          </button>
        )}
      </div>
    )
  }

  // 渲染标签网格
  const renderTagGrid = () => {
    if (viewMode === 'cloud') {
      return (
        <TagCloud
          tags={filteredAndSortedTags}
          onTagClick={onTagClick}
          onTagEdit={onTagEdit}
          onTagDelete={onTagDelete}
        />
      )
    }

    return (
      <div className={`
        ${viewMode === 'grid' 
          ? 'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6' 
          : 'space-y-4'
        }
      `}>
        {filteredAndSortedTags.map((tag) => (
          <div key={tag.id} className="relative">
            {/* 选择模式下的复选框 */}
            {isSelectionMode && (
              <div className="absolute top-2 left-2 z-10">
                <button
                  onClick={() => handleTagSelect(tag, !isTagSelected(tag))}
                  className="p-1 bg-white rounded-full shadow-md hover:bg-gray-50 transition-colors"
                >
                  {isTagSelected(tag) ? (
                    <CheckSquare className="w-4 h-4 text-primary-600" />
                  ) : (
                    <Square className="w-4 h-4 text-gray-400" />
                  )}
                </button>
              </div>
            )}
            
            <TagCard
              tag={tag}
              onEdit={() => onTagEdit(tag)}
              onDelete={() => onTagDelete(tag)}
              onClick={onTagClick ? () => onTagClick(tag) : undefined}
              className={`
                ${viewMode === 'list' ? 'max-w-none' : 'h-full'}
                ${isSelectionMode && isTagSelected(tag) ? 'ring-2 ring-primary-500 ring-opacity-50' : ''}
              `}
            />
          </div>
        ))}
      </div>
    )
  }

  // 渲染标签统计信息
  const renderTagStats = () => {
    if (tags.length === 0) return null

    const totalUsage = tags.reduce((sum, tag) => sum + tag.usageCount, 0)
    const activeTags = tags.filter(tag => tag.usageCount > 0).length
    const unusedTags = tags.length - activeTags
    const averageUsage = totalUsage > 0 ? Math.round(totalUsage / activeTags) : 0

    return (
      <div className="bg-gray-50 rounded-lg p-4 mb-6">
        <div className="grid grid-cols-1 sm:grid-cols-4 gap-4 text-center">
          <div>
            <div className="text-2xl font-bold text-gray-900">{tags.length}</div>
            <div className="text-sm text-gray-600">总标签数</div>
          </div>
          <div>
            <div className="text-2xl font-bold text-green-600">{activeTags}</div>
            <div className="text-sm text-gray-600">活跃标签</div>
          </div>
          <div>
            <div className="text-2xl font-bold text-gray-400">{unusedTags}</div>
            <div className="text-sm text-gray-600">未使用标签</div>
          </div>
          <div>
            <div className="text-2xl font-bold text-primary-600">{averageUsage}</div>
            <div className="text-sm text-gray-600">平均使用次数</div>
          </div>
        </div>
      </div>
    )
  }

  // 渲染工具栏
  const renderToolbar = () => (
    <div className="flex flex-col sm:flex-row gap-4 mb-6">
      {/* 搜索框 */}
      <div className="flex-1 relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
        <input
          type="text"
          placeholder="搜索标签..."
          value={onSearchChange ? searchQuery : localSearchQuery}
          onChange={handleSearchChange}
          className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
        />
      </div>

      {/* 排序选择器 */}
      <div className="flex items-center space-x-2">
        <label htmlFor="sort-select" className="text-sm font-medium text-gray-700 whitespace-nowrap">
          排序方式:
        </label>
        <select
          id="sort-select"
          value={sortBy}
          onChange={handleSortChange}
          className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
        >
          <option value="name-asc">名称 A-Z</option>
          <option value="name-desc">名称 Z-A</option>
          <option value="usage-desc">使用次数 ↓</option>
          <option value="usage-asc">使用次数 ↑</option>
          <option value="created-desc">创建时间 ↓</option>
          <option value="created-asc">创建时间 ↑</option>
          <option value="updated-desc">更新时间 ↓</option>
          <option value="updated-asc">更新时间 ↑</option>
        </select>
      </div>

      {/* 批量操作按钮 */}
      {(onBatchDelete || onBatchMerge || onBatchSetColor) && (
        <button
          onClick={toggleSelectionMode}
          className={`flex items-center px-3 py-2 rounded-lg transition-colors ${
            isSelectionMode
              ? 'bg-primary-600 text-white'
              : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
          }`}
          title={isSelectionMode ? '退出选择模式' : '进入选择模式'}
        >
          {isSelectionMode ? (
            <CheckSquare className="w-4 h-4 mr-2" />
          ) : (
            <Square className="w-4 h-4 mr-2" />
          )}
          {isSelectionMode ? '退出选择' : '批量操作'}
        </button>
      )}

      {/* 全选按钮（仅在选择模式下显示） */}
      {isSelectionMode && (
        <button
          onClick={handleSelectAll}
          className="flex items-center px-3 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
          title={selectedTags.length === filteredAndSortedTags.length ? '取消全选' : '全选'}
        >
          {selectedTags.length === filteredAndSortedTags.length ? '取消全选' : '全选'}
        </button>
      )}

      {/* 视图模式切换 */}
      <div className="flex items-center space-x-1 bg-gray-100 rounded-lg p-1">
        <button
          onClick={() => setViewMode('grid')}
          className={`p-2 rounded-md transition-colors ${
            viewMode === 'grid' 
              ? 'bg-white text-primary-600 shadow-sm' 
              : 'text-gray-600 hover:text-gray-900'
          }`}
          title="网格视图"
        >
          <Grid className="w-4 h-4" />
        </button>
        <button
          onClick={() => setViewMode('list')}
          className={`p-2 rounded-md transition-colors ${
            viewMode === 'list' 
              ? 'bg-white text-primary-600 shadow-sm' 
              : 'text-gray-600 hover:text-gray-900'
          }`}
          title="列表视图"
        >
          <List className="w-4 h-4" />
        </button>
        <button
          onClick={() => setViewMode('cloud')}
          className={`p-2 rounded-md transition-colors ${
            viewMode === 'cloud' 
              ? 'bg-white text-primary-600 shadow-sm' 
              : 'text-gray-600 hover:text-gray-900'
          }`}
          title="标签云视图"
        >
          <Cloud className="w-4 h-4" />
        </button>
      </div>
    </div>
  )

  // 渲染结果计数
  const renderResultCount = () => {
    if (loading || tags.length === 0) return null

    const query = onSearchChange ? searchQuery : localSearchQuery
    const isFiltered = query.trim()
    const count = filteredAndSortedTags.length

    return (
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-2 text-sm text-gray-600">
          <Hash className="w-4 h-4" />
          <span>
            {isFiltered ? (
              <>显示 {count} 个标签，共 {tags.length} 个</>
            ) : (
              <>共 {count} 个标签</>
            )}
          </span>
        </div>
        
        {isFiltered && (
          <button
            onClick={() => {
              setLocalSearchQuery('')
              onSearchChange?.('')
            }}
            className="text-sm text-primary-600 hover:text-primary-700 font-medium"
          >
            清除筛选
          </button>
        )}
      </div>
    )
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* 标签统计信息 */}
      {!loading && renderTagStats()}

      {/* 工具栏 */}
      {!loading && tags.length > 0 && renderToolbar()}

      {/* 批量操作组件 */}
      {isSelectionMode && selectedTags.length > 0 && (onBatchDelete || onBatchMerge || onBatchSetColor) && (
        <TagBatchActions
          selectedTags={selectedTags}
          allTags={tags}
          onBatchDelete={onBatchDelete!}
          onBatchMerge={onBatchMerge!}
          onBatchSetColor={onBatchSetColor!}
          onClearSelection={handleClearSelection}
        />
      )}

      {/* 结果计数 */}
      {renderResultCount()}

      {/* 主要内容区域 */}
      <div>
        {loading ? (
          renderLoadingState()
        ) : filteredAndSortedTags.length === 0 ? (
          renderEmptyState()
        ) : (
          renderTagGrid()
        )}
      </div>
    </div>
  )
})

// 设置显示名称便于调试
TagList.displayName = 'TagList'

export default TagList

// 导出类型定义
export type { TagListProps, TagWithStats, TagSortOption }