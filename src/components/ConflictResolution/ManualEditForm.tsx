// 手动编辑表单组件

import React from 'react'
import { Check } from 'lucide-react'
import { getFieldLabel } from './utils'

export interface ManualEditFormProps {
  editedData: Record<string, any>
  onDataChange: (data: Record<string, any> | ((prev: Record<string, any>) => Record<string, any>)) => void
  onSave: () => void
  onCancel: () => void
}

const ManualEditForm: React.FC<ManualEditFormProps> = ({
  editedData,
  onDataChange,
  onSave,
  onCancel
}) => {
  return (
    <div className="space-y-4">
      <h3 className="text-lg font-medium text-gray-900">手动编辑数据</h3>
      
      <div className="space-y-3">
        {Object.entries(editedData).map(([key, value]) => (
          <div key={key}>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              {getFieldLabel(key)}
            </label>
            
            {key === 'tags' && Array.isArray(value) ? (
              <input
                type="text"
                value={value.join(', ')}
                onChange={(e) => onDataChange((prev: Record<string, any>) => ({
                  ...prev,
                  [key]: e.target.value.split(',').map(t => t.trim()).filter(Boolean)
                }))}
                className="w-full border border-gray-300 rounded-lg px-3 py-2 text-sm"
                placeholder="用逗号分隔多个标签"
              />
            ) : key === 'description' || key === 'content' ? (
              <textarea
                value={String(value || '')}
                onChange={(e) => onDataChange((prev: Record<string, any>) => ({ ...prev, [key]: e.target.value }))}
                className="w-full border border-gray-300 rounded-lg px-3 py-2 text-sm"
                rows={3}
              />
            ) : (
              <input
                type="text"
                value={String(value || '')}
                onChange={(e) => onDataChange((prev: Record<string, any>) => ({ ...prev, [key]: e.target.value }))}
                className="w-full border border-gray-300 rounded-lg px-3 py-2 text-sm"
              />
            )}
          </div>
        ))}
      </div>
      
      <div className="flex space-x-2 pt-4">
        <button
          onClick={onSave}
          className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 flex items-center"
        >
          <Check className="w-4 h-4 mr-2" />
          保存
        </button>
        <button
          onClick={onCancel}
          className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50"
        >
          取消
        </button>
      </div>
    </div>
  )
}

export default ManualEditForm