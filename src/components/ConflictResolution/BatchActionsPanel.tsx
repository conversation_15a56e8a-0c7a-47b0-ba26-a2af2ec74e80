// 批量操作面板组件

import React from 'react'

export interface BatchActionsPanelProps {
  remainingConflicts: number
  onBatchResolution: (action: 'keep_existing' | 'use_imported') => void
}

const BatchActionsPanel: React.FC<BatchActionsPanelProps> = ({
  remainingConflicts,
  onBatchResolution
}) => {
  return (
    <div className="p-4 bg-blue-50 border-b border-blue-200">
      <div className="flex items-center justify-between">
        <span className="text-sm font-medium text-blue-900">
          批量处理剩余 {remainingConflicts} 个冲突
        </span>
        <div className="flex space-x-2">
          <button
            onClick={() => onBatchResolution('keep_existing')}
            className="px-3 py-1 text-sm bg-gray-600 text-white rounded hover:bg-gray-700"
          >
            全部保留现有
          </button>
          <button
            onClick={() => onBatchResolution('use_imported')}
            className="px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700"
          >
            全部使用导入
          </button>
        </div>
      </div>
    </div>
  )
}

export default BatchActionsPanel