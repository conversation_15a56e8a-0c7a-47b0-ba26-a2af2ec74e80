// 冲突解决对话框组件 - 处理导入数据冲突的用户界面

import React from 'react'
import { 
  X, 
  AlertTriangle, 
  ArrowRight, 
  Check, 
  Edit3, 
  RotateCcw,
  ChevronLeft,
  ChevronRight
} from 'lucide-react'
import { ConflictItem, ConflictResolution } from '../types'
import { useConflictResolution } from '../hooks/useConflictResolution'
import ConflictListPanel from './ConflictResolution/ConflictListPanel'
import ManualEditForm from './ConflictResolution/ManualEditForm'
import BatchActionsPanel from './ConflictResolution/BatchActionsPanel'
import { getConflictTypeLabel } from './ConflictResolution/utils'

export interface ConflictResolutionDialogProps {
  conflicts: ConflictItem[]
  onResolve: (resolutions: ConflictResolution[]) => void
  onCancel: () => void
  isOpen: boolean
}

const ConflictResolutionDialog: React.FC<ConflictResolutionDialogProps> = ({
  conflicts,
  onResolve,
  onCancel,
  isOpen
}) => {
  // 使用自定义 Hook 管理冲突解决逻辑
  const {
    resolutions,
    currentConflictIndex,
    editingConflictId,
    editedData,
    showBatchActions,
    currentConflict,
    currentResolution,
    stats,
    handleSingleResolution,
    handleBatchResolution,
    handleManualEdit,
    handleSaveManualEdit,
    handleCancelManualEdit,
    navigateToConflict,
    handleComplete,
    handleResetAll,
    setShowBatchActions,
    setEditedData
  } = useConflictResolution(conflicts, onResolve)

  if (!isOpen || !currentConflict) {
    return null
  }

  return (
    <div className="fixed inset-0 bg-background/80 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-6xl w-full max-h-[90vh] overflow-hidden flex flex-col">
        {/* 头部 */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            <AlertTriangle className="w-6 h-6 text-orange-600" />
            <div>
              <h2 className="text-xl font-semibold text-gray-900">解决数据冲突</h2>
              <p className="text-sm text-gray-600">
                发现 {conflicts.length} 个冲突项，已解决 {stats.resolved} 个
              </p>
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            {/* 批量操作按钮 */}
            <button
              onClick={() => setShowBatchActions(!showBatchActions)}
              className="px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-50"
            >
              批量操作
            </button>
            
            {/* 重置按钮 */}
            <button
              onClick={handleResetAll}
              className="p-2 text-gray-400 hover:text-gray-600"
              title="重置所有解决方案"
            >
              <RotateCcw className="w-4 h-4" />
            </button>
            
            {/* 关闭按钮 */}
            <button
              onClick={onCancel}
              className="p-2 text-gray-400 hover:text-gray-600"
            >
              <X className="w-5 h-5" />
            </button>
          </div>
        </div>

        {/* 批量操作面板 */}
        {showBatchActions && (
          <BatchActionsPanel
            remainingConflicts={conflicts.length - resolutions.length}
            onBatchResolution={handleBatchResolution}
          />
        )}

        {/* 主要内容区域 */}
        <div className="flex-1 flex overflow-hidden">
          {/* 左侧：冲突列表 */}
          <ConflictListPanel
            conflicts={conflicts}
            resolutions={resolutions}
            currentConflictIndex={currentConflictIndex}
            stats={stats}
            onNavigateToConflict={navigateToConflict}
          />

          {/* 右侧：冲突详情和解决选项 */}
          <div className="flex-1 flex flex-col">
            {/* 导航栏 */}
            <div className="flex items-center justify-between p-4 border-b border-gray-200">
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => navigateToConflict(currentConflictIndex - 1)}
                  disabled={currentConflictIndex === 0}
                  className="p-1 text-gray-400 hover:text-gray-600 disabled:opacity-50"
                >
                  <ChevronLeft className="w-4 h-4" />
                </button>
                
                <span className="text-sm text-gray-600">
                  {currentConflictIndex + 1} / {conflicts.length}
                </span>
                
                <button
                  onClick={() => navigateToConflict(currentConflictIndex + 1)}
                  disabled={currentConflictIndex === conflicts.length - 1}
                  className="p-1 text-gray-400 hover:text-gray-600 disabled:opacity-50"
                >
                  <ChevronRight className="w-4 h-4" />
                </button>
              </div>
              
              <div className="text-sm text-gray-600">
                {getConflictTypeLabel(currentConflict.conflictType)}冲突
              </div>
            </div>

            {/* 冲突详情 */}
            <div className="flex-1 overflow-y-auto p-6">
              {editingConflictId === currentConflict.id ? (
                // 手动编辑模式
                <ManualEditForm
                  editedData={editedData}
                  onDataChange={setEditedData}
                  onSave={handleSaveManualEdit}
                  onCancel={handleCancelManualEdit}
                />
              ) : (
                // 对比模式
                <div className="space-y-6">
                  <div className="text-center">
                    <h3 className="text-lg font-medium text-gray-900 mb-2">
                      {currentConflict.importData.title || currentConflict.importData.name}
                    </h3>
                    <div className="text-sm text-gray-600">
                      相似度: {Math.round(currentConflict.similarity * 100)}% · 
                      冲突字段: {currentConflict.conflictFields.join(', ')}
                    </div>
                  </div>

                  {/* 数据对比 */}
                  <div className="grid grid-cols-2 gap-6">
                    {/* 现有数据 */}
                    <div className="border border-gray-200 rounded-lg p-4">
                      <h4 className="font-medium text-gray-900 mb-3 flex items-center">
                        <span className="w-3 h-3 bg-gray-500 rounded-full mr-2"></span>
                        现有数据
                      </h4>
                      <div className="space-y-2 text-sm">
                        {Object.entries(currentConflict.existingData).map(([key, value]) => (
                          <div key={key} className={`${
                            currentConflict.conflictFields.includes(key) ? 'bg-red-50 p-2 rounded' : ''
                          }`}>
                            <span className="font-medium text-gray-700">{key}:</span>
                            <span className="ml-2 text-gray-900">
                              {Array.isArray(value) ? value.join(', ') : String(value || '(空)')}
                            </span>
                          </div>
                        ))}
                      </div>
                    </div>

                    {/* 导入数据 */}
                    <div className="border border-gray-200 rounded-lg p-4">
                      <h4 className="font-medium text-gray-900 mb-3 flex items-center">
                        <span className="w-3 h-3 bg-blue-500 rounded-full mr-2"></span>
                        导入数据
                      </h4>
                      <div className="space-y-2 text-sm">
                        {Object.entries(currentConflict.importData).map(([key, value]) => (
                          <div key={key} className={`${
                            currentConflict.conflictFields.includes(key) ? 'bg-blue-50 p-2 rounded' : ''
                          }`}>
                            <span className="font-medium text-gray-700">{key}:</span>
                            <span className="ml-2 text-gray-900">
                              {Array.isArray(value) ? value.join(', ') : String(value || '(空)')}
                            </span>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>

                  {/* 解决选项 */}
                  <div className="border-t border-gray-200 pt-6">
                    <h4 className="font-medium text-gray-900 mb-4">选择解决方案</h4>
                    
                    <div className="grid grid-cols-2 gap-4">
                      <button
                        onClick={() => handleSingleResolution(currentConflict.id, 'keep_existing')}
                        className={`p-4 border rounded-lg text-left hover:bg-gray-50 ${
                          currentResolution?.action === 'keep_existing' 
                            ? 'border-gray-500 bg-gray-50' 
                            : 'border-gray-200'
                        }`}
                      >
                        <div className="flex items-center mb-2">
                          <span className="w-3 h-3 bg-gray-500 rounded-full mr-2"></span>
                          <span className="font-medium">保留现有</span>
                        </div>
                        <p className="text-sm text-gray-600">
                          保留当前系统中的数据，忽略导入的数据
                        </p>
                      </button>

                      <button
                        onClick={() => handleSingleResolution(currentConflict.id, 'use_imported')}
                        className={`p-4 border rounded-lg text-left hover:bg-gray-50 ${
                          currentResolution?.action === 'use_imported' 
                            ? 'border-blue-500 bg-blue-50' 
                            : 'border-gray-200'
                        }`}
                      >
                        <div className="flex items-center mb-2">
                          <span className="w-3 h-3 bg-blue-500 rounded-full mr-2"></span>
                          <span className="font-medium">使用导入</span>
                        </div>
                        <p className="text-sm text-gray-600">
                          使用导入的数据替换现有数据
                        </p>
                      </button>

                      <button
                        onClick={() => handleSingleResolution(currentConflict.id, 'merge')}
                        className={`p-4 border rounded-lg text-left hover:bg-gray-50 ${
                          currentResolution?.action === 'merge' 
                            ? 'border-green-500 bg-green-50' 
                            : 'border-gray-200'
                        }`}
                      >
                        <div className="flex items-center mb-2">
                          <ArrowRight className="w-4 h-4 text-green-600 mr-2" />
                          <span className="font-medium">智能合并</span>
                        </div>
                        <p className="text-sm text-gray-600">
                          自动合并两个数据源的最佳内容
                        </p>
                      </button>

                      <button
                        onClick={() => handleManualEdit(currentConflict.id)}
                        className={`p-4 border rounded-lg text-left hover:bg-gray-50 ${
                          currentResolution?.action === 'manual_edit' 
                            ? 'border-purple-500 bg-purple-50' 
                            : 'border-gray-200'
                        }`}
                      >
                        <div className="flex items-center mb-2">
                          <Edit3 className="w-4 h-4 text-purple-600 mr-2" />
                          <span className="font-medium">手动编辑</span>
                        </div>
                        <p className="text-sm text-gray-600">
                          手动编辑数据内容后保存
                        </p>
                      </button>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* 底部操作栏 */}
        <div className="flex items-center justify-between p-6 border-t border-gray-200 bg-gray-50">
          <div className="text-sm text-gray-600">
            进度: {stats.resolved} / {stats.total} 个冲突已解决
          </div>
          
          <div className="flex space-x-3">
            <button
              onClick={onCancel}
              className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50"
            >
              取消导入
            </button>
            
            <button
              onClick={() => handleComplete()}
              disabled={resolutions.length !== conflicts.length}
              className="px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
            >
              <Check className="w-4 h-4 mr-2" />
              完成解决并导入
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

export default ConflictResolutionDialog