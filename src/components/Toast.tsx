// 通知组件 - 提供操作成功和失败的用户反馈提示

import React, { useEffect, useState } from 'react'
import { CheckCircle, XCircle, AlertCircle, Info, X } from 'lucide-react'

export type ToastType = 'success' | 'error' | 'warning' | 'info'

export interface ToastProps {
  /** 通知类型 */
  type: ToastType
  /** 通知标题 */
  title: string
  /** 通知消息 */
  message?: string
  /** 是否显示 */
  isVisible: boolean
  /** 自动关闭时间（毫秒），0表示不自动关闭 */
  autoClose?: number
  /** 关闭回调 */
  onClose: () => void
  /** 自定义CSS类名 */
  className?: string
}

/**
 * 通知组件
 * 提供成功、错误、警告、信息四种类型的通知提示
 */
const Toast: React.FC<ToastProps> = ({
  type,
  title,
  message,
  isVisible,
  autoClose = 5000,
  onClose,
  className = ''
}) => {
  const [isAnimating, setIsAnimating] = useState(false)

  // 自动关闭逻辑
  useEffect(() => {
    if (isVisible && autoClose > 0) {
      const timer = setTimeout(() => {
        handleClose()
      }, autoClose)

      return () => clearTimeout(timer)
    }
  }, [isVisible, autoClose])

  // 显示动画
  useEffect(() => {
    if (isVisible) {
      setIsAnimating(true)
    }
  }, [isVisible])

  // 处理关闭
  const handleClose = () => {
    setIsAnimating(false)
    setTimeout(() => {
      onClose()
    }, 300) // 等待动画完成
  }

  // 获取图标
  const getIcon = () => {
    switch (type) {
      case 'success':
        return <CheckCircle className="w-5 h-5" />
      case 'error':
        return <XCircle className="w-5 h-5" />
      case 'warning':
        return <AlertCircle className="w-5 h-5" />
      case 'info':
        return <Info className="w-5 h-5" />
      default:
        return <Info className="w-5 h-5" />
    }
  }

  // 获取样式类
  const getStyleClasses = () => {
    const baseClasses = 'flex items-start p-4 rounded-lg shadow-lg border'
    
    switch (type) {
      case 'success':
        return `${baseClasses} bg-green-50 border-green-200 text-green-800`
      case 'error':
        return `${baseClasses} bg-red-50 border-red-200 text-red-800`
      case 'warning':
        return `${baseClasses} bg-yellow-50 border-yellow-200 text-yellow-800`
      case 'info':
        return `${baseClasses} bg-blue-50 border-blue-200 text-blue-800`
      default:
        return `${baseClasses} bg-gray-50 border-gray-200 text-gray-800`
    }
  }

  // 获取图标颜色类
  const getIconColorClass = () => {
    switch (type) {
      case 'success':
        return 'text-green-600'
      case 'error':
        return 'text-red-600'
      case 'warning':
        return 'text-yellow-600'
      case 'info':
        return 'text-blue-600'
      default:
        return 'text-gray-600'
    }
  }

  if (!isVisible) return null

  return (
    <div
      className={`
        fixed top-4 right-4 z-50 max-w-sm w-full transform transition-all duration-300 ease-in-out
        ${isAnimating ? 'translate-x-0 opacity-100' : 'translate-x-full opacity-0'}
        ${className}
      `}
    >
      <div className={getStyleClasses()}>
        {/* 图标 */}
        <div className={`flex-shrink-0 ${getIconColorClass()}`}>
          {getIcon()}
        </div>

        {/* 内容 */}
        <div className="ml-3 flex-1">
          <h4 className="text-sm font-semibold">
            {title}
          </h4>
          {message && (
            <p className="mt-1 text-sm opacity-90">
              {message}
            </p>
          )}
        </div>

        {/* 关闭按钮 */}
        <button
          onClick={handleClose}
          className="ml-4 flex-shrink-0 text-gray-400 hover:text-gray-600 transition-colors"
          aria-label="关闭通知"
        >
          <X className="w-4 h-4" />
        </button>
      </div>
    </div>
  )
}

export default Toast