// 通知容器组件 - 管理多个通知的显示

import React from 'react'
import Toast from './Toast'
import type { ToastMessage } from '../hooks/useToast'

interface ToastContainerProps {
  /** 通知列表 */
  toasts: ToastMessage[]
  /** 关闭通知回调 */
  onCloseToast: (id: string) => void
  /** 自定义CSS类名 */
  className?: string
}

/**
 * 通知容器组件
 * 在页面右上角显示多个通知
 */
const ToastContainer: React.FC<ToastContainerProps> = ({
  toasts,
  onCloseToast,
  className = ''
}) => {
  if (toasts.length === 0) return null

  return (
    <div className={`fixed top-4 right-4 z-50 space-y-2 ${className}`}>
      {toasts.map((toast, index) => (
        <div
          key={toast.id}
          style={{
            transform: `translateY(${index * 4}px)`,
            zIndex: 50 - index
          }}
        >
          <Toast
            type={toast.type}
            title={toast.title}
            message={toast.message}
            isVisible={true}
            autoClose={toast.autoClose}
            onClose={() => onCloseToast(toast.id)}
          />
        </div>
      ))}
    </div>
  )
}

export default ToastContainer