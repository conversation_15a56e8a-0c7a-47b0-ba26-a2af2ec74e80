import React, { useState, useCallback, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card'
import { Button } from '../ui/button'
import { Input } from '../ui/input'
import { Label } from '../ui/label'
import { Badge } from '../ui/badge'
import { Separator } from '../ui/separator'
import { Alert, AlertDescription } from '../ui/alert'
import { <PERSON><PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from '../ui/tabs'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select'
import { 
  Cloud, 
  Key, 
  Server, 
  Wifi, 
  WifiOff, 
  Clock, 
  Database, 
  Settings, 
  RefreshCw,
  CheckCircle,
  XCircle,
  AlertCircle,
  Loader2,
  MessageCircle,
  Plus,
  Edit,
  Trash2,
  TestTube,
  Zap
} from 'lucide-react'

// 导入AI服务
import { aiIntegrationService } from '../../services/aiIntegrationService'
import { aiChatService, ChatMessage } from '../../services/aiChatService'
import { AIProviderConfig, AIProviderInfo, AIConnectionResult, AIModel } from '../../types/ai'

interface ProviderStatus {
  config: AIProviderConfig
  status?: AIConnectionResult
  models?: AIModel[]
  isLoading: boolean
  isTestingConnection: boolean
  isLoadingModels: boolean
}

interface TestResult {
  providerId: string
  providerName: string
  modelId: string
  modelName: string
  success: boolean
  responseTime: number
  response?: string
  error?: string
  timestamp: Date
}

const CloudAIServiceTest: React.FC = () => {
  // 状态管理
  const [supportedProviders, setSupportedProviders] = useState<AIProviderInfo[]>([])
  const [configuredProviders, setConfiguredProviders] = useState<ProviderStatus[]>([])
  const [selectedProvider, setSelectedProvider] = useState<string>('')
  const [selectedModel, setSelectedModel] = useState<string>('')
  const [testResults, setTestResults] = useState<TestResult[]>([])
  const [logs, setLogs] = useState<string[]>([])
  const [isLoading, setIsLoading] = useState(false)
  
  // 新增提供商表单状态
  const [showAddForm, setShowAddForm] = useState(false)
  const [newProviderForm, setNewProviderForm] = useState({
    type: '',
    name: '',
    baseUrl: '',
    apiKey: '',
    timeout: 30000,
    enabled: true
  })

  // 测试消息
  const [testMessage, setTestMessage] = useState('你好，这是一个连接测试。请简单回复"连接成功"。')

  // 添加日志
  const addLog = useCallback((message: string) => {
    const timestamp = new Date().toLocaleTimeString()
    setLogs(prev => [`[${timestamp}] ${message}`, ...prev.slice(0, 49)])
  }, [])

  // 加载支持的提供商
  const loadSupportedProviders = useCallback(async () => {
    try {
      const providers = aiIntegrationService.getSupportedProviders()
      setSupportedProviders(providers)
      addLog(`加载了 ${providers.length} 个支持的AI提供商`)
    } catch (error) {
      addLog(`加载支持的提供商失败: ${(error as Error).message}`)
    }
  }, [addLog])

  // 加载已配置的提供商
  const loadConfiguredProviders = useCallback(async () => {
    setIsLoading(true)
    try {
      const providers = await aiIntegrationService.getConfiguredProviders()
      const providerStatuses: ProviderStatus[] = providers.map(provider => ({
        config: provider,
        isLoading: false,
        isTestingConnection: false,
        isLoadingModels: false
      }))
      
      setConfiguredProviders(providerStatuses)
      addLog(`加载了 ${providers.length} 个已配置的AI提供商`)
    } catch (error) {
      addLog(`加载已配置提供商失败: ${(error as Error).message}`)
    } finally {
      setIsLoading(false)
    }
  }, [addLog])

  // 测试提供商连接
  const testProviderConnection = useCallback(async (providerId: string) => {
    const providerIndex = configuredProviders.findIndex(p => p.config.id === providerId)
    if (providerIndex === -1) return

    // 更新状态为测试中
    setConfiguredProviders(prev => {
      const updated = [...prev]
      updated[providerIndex] = { ...updated[providerIndex], isTestingConnection: true }
      return updated
    })

    addLog(`开始测试 ${configuredProviders[providerIndex].config.name} 连接...`)

    try {
      const result = await aiIntegrationService.testConnection(providerId)
      
      // 更新连接状态
      setConfiguredProviders(prev => {
        const updated = [...prev]
        updated[providerIndex] = { 
          ...updated[providerIndex], 
          status: result,
          isTestingConnection: false
        }
        return updated
      })

      if (result.success) {
        addLog(`${configuredProviders[providerIndex].config.name} 连接成功 (${result.responseTime}ms)`)
      } else {
        addLog(`${configuredProviders[providerIndex].config.name} 连接失败: ${result.error}`)
      }
    } catch (error) {
      addLog(`测试连接异常: ${(error as Error).message}`)
      
      // 更新为失败状态
      setConfiguredProviders(prev => {
        const updated = [...prev]
        updated[providerIndex] = { 
          ...updated[providerIndex], 
          status: {
            providerId,
            success: false,
            error: (error as Error).message,
            testedAt: new Date()
          },
          isTestingConnection: false
        }
        return updated
      })
    }
  }, [configuredProviders, addLog])

  // 获取提供商模型列表
  const loadProviderModels = useCallback(async (providerId: string) => {
    const providerIndex = configuredProviders.findIndex(p => p.config.id === providerId)
    if (providerIndex === -1) return

    // 更新状态为加载中
    setConfiguredProviders(prev => {
      const updated = [...prev]
      updated[providerIndex] = { ...updated[providerIndex], isLoadingModels: true }
      return updated
    })

    addLog(`获取 ${configuredProviders[providerIndex].config.name} 模型列表...`)

    try {
      const models = await aiIntegrationService.getAvailableModels(providerId, true)
      
      // 更新模型列表
      setConfiguredProviders(prev => {
        const updated = [...prev]
        updated[providerIndex] = { 
          ...updated[providerIndex], 
          models,
          isLoadingModels: false
        }
        return updated
      })

      addLog(`${configuredProviders[providerIndex].config.name} 找到 ${models.length} 个模型`)
    } catch (error) {
      addLog(`获取模型列表失败: ${(error as Error).message}`)
      
      // 更新为失败状态
      setConfiguredProviders(prev => {
        const updated = [...prev]
        updated[providerIndex] = { 
          ...updated[providerIndex], 
          models: [],
          isLoadingModels: false
        }
        return updated
      })
    }
  }, [configuredProviders, addLog])

  // 测试所有提供商连接
  const testAllConnections = useCallback(async () => {
    addLog('开始测试所有提供商连接...')
    
    for (const provider of configuredProviders) {
      if (provider.config.enabled) {
        await testProviderConnection(provider.config.id)
      }
    }
    
    addLog('所有提供商连接测试完成')
  }, [configuredProviders, testProviderConnection, addLog])

  // 添加新提供商
  const addProvider = useCallback(async () => {
    if (!newProviderForm.type || !newProviderForm.name || !newProviderForm.baseUrl) {
      addLog('请填写完整的提供商信息')
      return
    }

    try {
      await aiIntegrationService.configureProvider({
        type: newProviderForm.type as any,
        name: newProviderForm.name,
        baseUrl: newProviderForm.baseUrl,
        apiKey: newProviderForm.apiKey || undefined,
        timeout: newProviderForm.timeout,
        enabled: newProviderForm.enabled
      })

      addLog(`添加提供商成功: ${newProviderForm.name}`)
      
      // 重置表单
      setNewProviderForm({
        type: '',
        name: '',
        baseUrl: '',
        apiKey: '',
        timeout: 30000,
        enabled: true
      })
      setShowAddForm(false)
      
      // 重新加载提供商列表
      await loadConfiguredProviders()
    } catch (error) {
      addLog(`添加提供商失败: ${(error as Error).message}`)
    }
  }, [newProviderForm, addLog, loadConfiguredProviders])

  // 删除提供商
  const removeProvider = useCallback(async (providerId: string) => {
    const provider = configuredProviders.find(p => p.config.id === providerId)
    if (!provider) return

    if (!confirm(`确定要删除提供商 "${provider.config.name}" 吗？`)) {
      return
    }

    try {
      await aiIntegrationService.removeProvider(providerId)
      addLog(`删除提供商成功: ${provider.config.name}`)
      
      // 重新加载提供商列表
      await loadConfiguredProviders()
    } catch (error) {
      addLog(`删除提供商失败: ${(error as Error).message}`)
    }
  }, [configuredProviders, addLog, loadConfiguredProviders])

  // 测试模型对话
  const testModelChat = useCallback(async () => {
    if (!selectedProvider || !selectedModel || !testMessage.trim()) {
      addLog('请选择提供商、模型并输入测试消息')
      return
    }

    const provider = configuredProviders.find(p => p.config.id === selectedProvider)
    const model = provider?.models?.find(m => m.id === selectedModel)
    
    if (!provider || !model) {
      addLog('未找到选中的提供商或模型')
      return
    }

    addLog(`开始测试 ${provider.config.name} - ${model.displayName} 对话...`)
    
    const startTime = Date.now()
    
    try {
      const messages: ChatMessage[] = [
        { role: 'user', content: testMessage }
      ]

      const response = await aiChatService.chatWithCloudService(
        provider.config,
        model.id,
        messages,
        { temperature: 0.7, maxTokens: 100 }
      )

      const responseTime = Date.now() - startTime
      
      const testResult: TestResult = {
        providerId: provider.config.id,
        providerName: provider.config.name,
        modelId: model.id,
        modelName: model.displayName,
        success: true,
        responseTime,
        response: response.content,
        timestamp: new Date()
      }

      setTestResults(prev => [testResult, ...prev.slice(0, 19)]) // 保留最近20条
      addLog(`对话测试成功 (${responseTime}ms): ${response.content.substring(0, 50)}...`)
    } catch (error) {
      const responseTime = Date.now() - startTime
      
      const testResult: TestResult = {
        providerId: provider.config.id,
        providerName: provider.config.name,
        modelId: model.id,
        modelName: model.displayName,
        success: false,
        responseTime,
        error: (error as Error).message,
        timestamp: new Date()
      }

      setTestResults(prev => [testResult, ...prev.slice(0, 19)])
      addLog(`对话测试失败 (${responseTime}ms): ${(error as Error).message}`)
    }
  }, [selectedProvider, selectedModel, testMessage, configuredProviders, addLog])

  // 清空日志
  const clearLogs = useCallback(() => {
    setLogs([])
    addLog('日志已清空')
  }, [addLog])

  // 页面加载时初始化
  useEffect(() => {
    loadSupportedProviders()
    loadConfiguredProviders()
  }, [loadSupportedProviders, loadConfiguredProviders])

  // 渲染提供商状态图标
  const renderProviderStatusIcon = (status?: AIConnectionResult) => {
    if (!status) {
      return <AlertCircle className="h-4 w-4 text-gray-400" />
    }
    
    if (status.success) {
      return <CheckCircle className="h-4 w-4 text-green-500" />
    } else {
      return <XCircle className="h-4 w-4 text-red-500" />
    }
  }

  // 渲染提供商状态徽章
  const renderProviderStatusBadge = (status?: AIConnectionResult) => {
    if (!status) {
      return <Badge variant="secondary">未测试</Badge>
    }
    
    if (status.success) {
      return <Badge variant="default" className="bg-green-500">在线</Badge>
    } else {
      return <Badge variant="destructive">离线</Badge>
    }
  }

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center space-x-2">
        <Cloud className="h-6 w-6 text-primary" />
        <h1 className="text-2xl font-bold">云端AI服务测试</h1>
      </div>

      {/* 标签页导航 */}
      <Tabs defaultValue="providers" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="providers" className="flex items-center space-x-2">
            <Server className="h-4 w-4" />
            <span>提供商管理</span>
          </TabsTrigger>
          <TabsTrigger value="testing" className="flex items-center space-x-2">
            <TestTube className="h-4 w-4" />
            <span>模型测试</span>
          </TabsTrigger>
          <TabsTrigger value="results" className="flex items-center space-x-2">
            <MessageCircle className="h-4 w-4" />
            <span>测试结果</span>
          </TabsTrigger>
        </TabsList>

        {/* 提供商管理标签页 */}
        <TabsContent value="providers" className="space-y-6 mt-6">
          {/* 提供商操作区域 */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="flex items-center space-x-2">
                    <Server className="h-5 w-5" />
                    <span>AI提供商管理</span>
                  </CardTitle>
                  <CardDescription>
                    配置和管理云端AI服务提供商
                  </CardDescription>
                </div>
                
                <div className="flex items-center space-x-2">
                  <Button
                    onClick={() => setShowAddForm(!showAddForm)}
                    className="flex items-center space-x-2"
                  >
                    <Plus className="h-4 w-4" />
                    <span>添加提供商</span>
                  </Button>
                  
                  <Button
                    variant="outline"
                    onClick={testAllConnections}
                    disabled={configuredProviders.length === 0}
                    className="flex items-center space-x-2"
                  >
                    <Wifi className="h-4 w-4" />
                    <span>测试所有</span>
                  </Button>
                  
                  <Button
                    variant="outline"
                    onClick={loadConfiguredProviders}
                    disabled={isLoading}
                    className="flex items-center space-x-2"
                  >
                    {isLoading ? (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    ) : (
                      <RefreshCw className="h-4 w-4" />
                    )}
                    <span>刷新</span>
                  </Button>
                </div>
              </div>
            </CardHeader>
            
            {showAddForm && (
              <CardContent className="border-t">
                <div className="space-y-4">
                  <h3 className="font-semibold">添加新的AI提供商</h3>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="providerType">提供商类型</Label>
                      <Select
                        value={newProviderForm.type}
                        onValueChange={(value) => {
                          const providerInfo = supportedProviders.find(p => p.type === value)
                          setNewProviderForm(prev => ({
                            ...prev,
                            type: value,
                            baseUrl: providerInfo?.defaultBaseUrl || ''
                          }))
                        }}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="选择提供商类型" />
                        </SelectTrigger>
                        <SelectContent>
                          {supportedProviders
                            .filter(p => p.type !== 'ollama' && p.type !== 'lm-studio' && p.type !== 'xinference')
                            .map((provider) => (
                            <SelectItem key={provider.id} value={provider.type}>
                              <div className="flex items-center space-x-2">
                                <span>{provider.icon}</span>
                                <span>{provider.name}</span>
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="providerName">提供商名称</Label>
                      <Input
                        id="providerName"
                        placeholder="例如: My OpenAI"
                        value={newProviderForm.name}
                        onChange={(e) => setNewProviderForm(prev => ({
                          ...prev,
                          name: e.target.value
                        }))}
                      />
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="baseUrl">API基础URL</Label>
                      <Input
                        id="baseUrl"
                        placeholder="例如: https://api.openai.com/v1"
                        value={newProviderForm.baseUrl}
                        onChange={(e) => setNewProviderForm(prev => ({
                          ...prev,
                          baseUrl: e.target.value
                        }))}
                      />
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="apiKey">API密钥</Label>
                      <Input
                        id="apiKey"
                        type="password"
                        placeholder="输入API密钥"
                        value={newProviderForm.apiKey}
                        onChange={(e) => setNewProviderForm(prev => ({
                          ...prev,
                          apiKey: e.target.value
                        }))}
                      />
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <Button onClick={addProvider}>
                      添加提供商
                    </Button>
                    <Button variant="outline" onClick={() => setShowAddForm(false)}>
                      取消
                    </Button>
                  </div>
                </div>
              </CardContent>
            )}
          </Card>

          {/* 已配置的提供商列表 */}
          {configuredProviders.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Database className="h-5 w-5" />
                  <span>已配置的提供商 ({configuredProviders.length})</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {configuredProviders.map((providerStatus) => {
                    const { config, status, models, isTestingConnection, isLoadingModels } = providerStatus
                    
                    return (
                      <Card key={config.id} className="border-l-4 border-l-primary">
                        <CardContent className="pt-4">
                          <div className="flex items-center justify-between mb-3">
                            <div className="flex items-center space-x-3">
                              {renderProviderStatusIcon(status)}
                              <div>
                                <h3 className="font-semibold flex items-center space-x-2">
                                  <span>{config.name}</span>
                                  {!config.enabled && (
                                    <Badge variant="secondary">已禁用</Badge>
                                  )}
                                </h3>
                                <p className="text-sm text-muted-foreground">
                                  {config.type} - {config.baseUrl}
                                </p>
                              </div>
                            </div>
                            
                            <div className="flex items-center space-x-2">
                              {renderProviderStatusBadge(status)}
                              
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => testProviderConnection(config.id)}
                                disabled={isTestingConnection}
                                className="flex items-center space-x-1"
                              >
                                {isTestingConnection ? (
                                  <Loader2 className="h-3 w-3 animate-spin" />
                                ) : (
                                  <RefreshCw className="h-3 w-3" />
                                )}
                                <span>测试</span>
                              </Button>
                              
                              {status?.success && (
                                <Button
                                  size="sm"
                                  variant="outline"
                                  onClick={() => loadProviderModels(config.id)}
                                  disabled={isLoadingModels}
                                  className="flex items-center space-x-1"
                                >
                                  {isLoadingModels ? (
                                    <Loader2 className="h-3 w-3 animate-spin" />
                                  ) : (
                                    <Database className="h-3 w-3" />
                                  )}
                                  <span>模型</span>
                                </Button>
                              )}
                              
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => removeProvider(config.id)}
                                className="flex items-center space-x-1 text-red-600 hover:text-red-700"
                              >
                                <Trash2 className="h-3 w-3" />
                                <span>删除</span>
                              </Button>
                            </div>
                          </div>
                          
                          {status && (
                            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm mb-3">
                              <div>
                                <span className="text-muted-foreground">响应时间:</span>
                                <div className="flex items-center space-x-1">
                                  <Clock className="h-3 w-3" />
                                  <span>{status.responseTime || 0}ms</span>
                                </div>
                              </div>
                              <div>
                                <span className="text-muted-foreground">模型数量:</span>
                                <div className="flex items-center space-x-1">
                                  <Database className="h-3 w-3" />
                                  <span>{models?.length || status.modelCount || 0}</span>
                                </div>
                              </div>
                              <div>
                                <span className="text-muted-foreground">最后检查:</span>
                                <div>{status.testedAt?.toLocaleTimeString()}</div>
                              </div>
                              {status.error && (
                                <div>
                                  <span className="text-muted-foreground">错误:</span>
                                  <div className="text-red-500 text-xs">{status.error}</div>
                                </div>
                              )}
                            </div>
                          )}
                          
                          {models && models.length > 0 && (
                            <div>
                              <Separator className="mb-3" />
                              <h4 className="font-medium mb-2">可用模型 ({models.length})</h4>
                              <div className="space-y-2 max-h-40 overflow-y-auto">
                                {models.map((model) => (
                                  <div key={model.id} className="p-2 bg-muted rounded-md">
                                    <div className="flex items-center justify-between">
                                      <div>
                                        <div className="font-medium text-sm">{model.displayName}</div>
                                        <div className="text-xs text-muted-foreground">{model.description}</div>
                                      </div>
                                      <div className="flex space-x-1">
                                        {model.isRecommended && (
                                          <Badge variant="secondary" className="text-xs">推荐</Badge>
                                        )}
                                        {model.isPopular && (
                                          <Badge variant="outline" className="text-xs">热门</Badge>
                                        )}
                                      </div>
                                    </div>
                                    {model.capabilities && model.capabilities.length > 0 && (
                                      <div className="flex flex-wrap gap-1 mt-2">
                                        {model.capabilities.map((cap) => (
                                          <Badge key={cap} variant="outline" className="text-xs">
                                            {cap}
                                          </Badge>
                                        ))}
                                      </div>
                                    )}
                                  </div>
                                ))}
                              </div>
                            </div>
                          )}
                        </CardContent>
                      </Card>
                    )
                  })}
                </div>
              </CardContent>
            </Card>
          )}

          {configuredProviders.length === 0 && !isLoading && (
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                还没有配置任何AI提供商。点击"添加提供商"开始配置。
              </AlertDescription>
            </Alert>
          )}
        </TabsContent>

        {/* 模型测试标签页 */}
        <TabsContent value="testing" className="space-y-6 mt-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <TestTube className="h-5 w-5" />
                <span>模型对话测试</span>
              </CardTitle>
              <CardDescription>
                选择提供商和模型进行对话测试
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="test-provider">选择提供商</Label>
                  <Select
                    value={selectedProvider}
                    onValueChange={(value) => {
                      setSelectedProvider(value)
                      setSelectedModel('') // 重置模型选择
                    }}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="请选择提供商" />
                    </SelectTrigger>
                    <SelectContent>
                      {configuredProviders
                        .filter(p => p.config.enabled && p.status?.success)
                        .map((providerStatus) => (
                        <SelectItem key={providerStatus.config.id} value={providerStatus.config.id}>
                          {providerStatus.config.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="test-model">选择模型</Label>
                  <Select
                    value={selectedModel}
                    onValueChange={setSelectedModel}
                    disabled={!selectedProvider}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="请选择模型" />
                    </SelectTrigger>
                    <SelectContent>
                      {configuredProviders
                        .find(p => p.config.id === selectedProvider)
                        ?.models?.map((model) => (
                        <SelectItem key={model.id} value={model.id}>
                          <div className="flex items-center space-x-2">
                            <span>{model.displayName}</span>
                            {model.isRecommended && (
                              <Badge variant="secondary" className="text-xs">推荐</Badge>
                            )}
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="test-message">测试消息</Label>
                <Input
                  id="test-message"
                  placeholder="输入要发送给AI的测试消息"
                  value={testMessage}
                  onChange={(e) => setTestMessage(e.target.value)}
                />
              </div>
              
              <Button
                onClick={testModelChat}
                disabled={!selectedProvider || !selectedModel || !testMessage.trim()}
                className="flex items-center space-x-2"
              >
                <MessageCircle className="h-4 w-4" />
                <span>发送测试消息</span>
              </Button>
            </CardContent>
          </Card>
        </TabsContent>

        {/* 测试结果标签页 */}
        <TabsContent value="results" className="space-y-6 mt-6">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center space-x-2">
                  <MessageCircle className="h-5 w-5" />
                  <span>测试结果 ({testResults.length})</span>
                </CardTitle>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setTestResults([])}
                  disabled={testResults.length === 0}
                >
                  清空结果
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              {testResults.length === 0 ? (
                <div className="text-center text-muted-foreground py-8">
                  <TestTube className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>还没有测试结果</p>
                  <p className="text-sm mt-2">在"模型测试"标签页中进行测试</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {testResults.map((result, index) => (
                    <Card key={index} className={`border-l-4 ${
                      result.success ? 'border-l-green-500' : 'border-l-red-500'
                    }`}>
                      <CardContent className="pt-4">
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center space-x-2">
                            {result.success ? (
                              <CheckCircle className="h-4 w-4 text-green-500" />
                            ) : (
                              <XCircle className="h-4 w-4 text-red-500" />
                            )}
                            <span className="font-medium">
                              {result.providerName} - {result.modelName}
                            </span>
                            <Badge variant={result.success ? "default" : "destructive"}>
                              {result.success ? "成功" : "失败"}
                            </Badge>
                          </div>
                          <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                            <span>{result.responseTime}ms</span>
                            <span>{result.timestamp.toLocaleTimeString()}</span>
                          </div>
                        </div>
                        
                        {result.success && result.response && (
                          <div className="mt-3 p-3 bg-muted rounded-md">
                            <div className="text-sm font-medium mb-1">AI回复:</div>
                            <div className="text-sm">{result.response}</div>
                          </div>
                        )}
                        
                        {!result.success && result.error && (
                          <div className="mt-3 p-3 bg-red-50 border border-red-200 rounded-md">
                            <div className="text-sm font-medium text-red-800 mb-1">错误信息:</div>
                            <div className="text-sm text-red-700">{result.error}</div>
                          </div>
                        )}
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* 测试日志 */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center space-x-2">
              <AlertCircle className="h-5 w-5" />
              <span>测试日志</span>
            </CardTitle>
            <Button variant="outline" size="sm" onClick={clearLogs}>
              清空日志
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="bg-muted p-4 rounded-md max-h-60 overflow-y-auto font-mono text-sm">
            {logs.length === 0 ? (
              <div className="text-muted-foreground">暂无日志</div>
            ) : (
              logs.map((log, index) => (
                <div key={index} className="mb-1">
                  {log}
                </div>
              ))
            )}
          </div>
        </CardContent>
      </Card>

      {/* 使用说明 */}
      <Alert>
        <Zap className="h-4 w-4" />
        <AlertDescription>
          <strong>使用说明:</strong>
          <ul className="mt-2 space-y-1 text-sm">
            <li>• 在"提供商管理"中添加和配置云端AI服务提供商</li>
            <li>• 测试提供商连接状态，确保API密钥和配置正确</li>
            <li>• 获取提供商的可用模型列表</li>
            <li>• 在"模型测试"中选择模型进行对话测试</li>
            <li>• 在"测试结果"中查看所有测试记录</li>
          </ul>
        </AlertDescription>
      </Alert>
    </div>
  )
}

export default CloudAIServiceTest