/**
 * VirtualBookmarkList组件测试
 * 用于验证重构后的VirtualBookmarkList组件shadcn集成效果
 */

import React, { useState, useMemo } from 'react'
import VirtualBookmarkList from '../VirtualBookmarkList'
import { Button } from '../ui/button'
import { Badge } from '../ui/badge'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card'
import { Separator } from '../ui/separator'
import { Switch } from '../ui/switch'
import { Label } from '../ui/label'
import { Bookmark, Star, Tag, Calendar, ExternalLink, Settings, Trash2 } from 'lucide-react'
import type { ViewMode } from '../ViewModeSelector'

// 生成大量测试数据
const generateMockBookmarks = (count: number) => {
  const categories = ['前端开发', '后端开发', '设计资源', '工具软件', '学习资料', '技术博客', '开源项目', '文档手册']
  const tags = ['React', 'TypeScript', 'JavaScript', 'CSS', 'Node.js', 'Python', 'Vue', 'Angular', 'Tailwind', 'shadcn']
  const domains = ['github.com', 'stackoverflow.com', 'developer.mozilla.org', 'react.dev', 'tailwindcss.com', 'ui.shadcn.com']
  
  return Array.from({ length: count }, (_, index) => {
    const id = (index + 1).toString()
    const domain = domains[Math.floor(Math.random() * domains.length)]
    const category = categories[Math.floor(Math.random() * categories.length)]
    const bookmarkTags = tags.slice(0, Math.floor(Math.random() * 4) + 1)
    
    return {
      id,
      title: `测试收藏 ${index + 1} - ${category}相关资源`,
      url: `https://${domain}/resource-${id}`,
      description: `这是第${index + 1}个测试收藏，包含${category}相关的优质内容和资源链接。`,
      content: Math.random() > 0.5 ? `详细内容描述：这个收藏包含了关于${category}的深入讲解和实践案例，非常适合学习和参考使用。内容涵盖基础概念、进阶技巧和最佳实践。` : undefined,
      category,
      tags: bookmarkTags,
      createdAt: new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000).toISOString(),
      favicon: Math.random() > 0.3 ? `https://${domain}/favicon.ico` : undefined
    }
  })
}

/**
 * VirtualBookmarkList测试组件
 */
const VirtualBookmarkListTest: React.FC = () => {
  const [viewMode, setViewMode] = useState<ViewMode>('card')
  const [highlightId, setHighlightId] = useState<string | null>(null)
  const [selectedBookmarks, setSelectedBookmarks] = useState<string[]>([])
  const [bookmarkCount, setBookmarkCount] = useState(50)
  const [containerHeight, setContainerHeight] = useState(600)
  const [showStats, setShowStats] = useState(true)
  const [autoHighlight, setAutoHighlight] = useState(false)

  // 生成测试数据
  const mockBookmarks = useMemo(() => generateMockBookmarks(bookmarkCount), [bookmarkCount])

  // 自动高亮演示
  React.useEffect(() => {
    if (!autoHighlight) return
    
    const interval = setInterval(() => {
      const randomId = mockBookmarks[Math.floor(Math.random() * mockBookmarks.length)]?.id
      setHighlightId(randomId || null)
    }, 2000)
    
    return () => clearInterval(interval)
  }, [autoHighlight, mockBookmarks])

  // 处理编辑收藏
  const handleEdit = (bookmark: any) => {
    console.log('编辑收藏:', bookmark)
    alert(`编辑收藏: ${bookmark.title}`)
  }

  // 处理删除收藏
  const handleDelete = (bookmark: any) => {
    console.log('删除收藏:', bookmark)
    if (confirm(`确定要删除收藏 "${bookmark.title}" 吗？`)) {
      alert(`已删除收藏: ${bookmark.title}`)
    }
  }

  // 处理点击收藏
  const handleClick = (bookmark: any) => {
    console.log('点击收藏:', bookmark)
    setHighlightId(bookmark.id === highlightId ? null : bookmark.id)
    
    // 切换选中状态
    setSelectedBookmarks(prev => 
      prev.includes(bookmark.id)
        ? prev.filter(id => id !== bookmark.id)
        : [...prev, bookmark.id]
    )
  }

  // 清除选择
  const clearSelection = () => {
    setHighlightId(null)
    setSelectedBookmarks([])
  }

  // 统计信息
  const stats = useMemo(() => {
    const categories = mockBookmarks.reduce((acc, bookmark) => {
      acc[bookmark.category] = (acc[bookmark.category] || 0) + 1
      return acc
    }, {} as Record<string, number>)
    
    const allTags = mockBookmarks.flatMap(bookmark => bookmark.tags)
    const tagCounts = allTags.reduce((acc, tag) => {
      acc[tag] = (acc[tag] || 0) + 1
      return acc
    }, {} as Record<string, number>)
    
    return {
      total: mockBookmarks.length,
      categories: Object.keys(categories).length,
      tags: Object.keys(tagCounts).length,
      withFavicon: mockBookmarks.filter(b => b.favicon).length,
      withContent: mockBookmarks.filter(b => b.content).length
    }
  }, [mockBookmarks])

  return (
    <div className="p-6 max-w-7xl mx-auto space-y-6">
      {/* 标题 */}
      <div className="space-y-2">
        <h1 className="text-3xl font-bold text-foreground flex items-center gap-2">
          <Bookmark className="h-8 w-8 text-primary" />
          VirtualBookmarkList 组件测试
        </h1>
        <p className="text-muted-foreground">
          测试重构后的VirtualBookmarkList组件，验证shadcn组件集成效果和虚拟滚动性能
        </p>
      </div>

      {/* 统计信息 */}
      {showStats && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Star className="h-5 w-5" />
              数据统计
            </CardTitle>
            <CardDescription>当前测试数据的统计信息</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-primary">{stats.total}</div>
                <div className="text-sm text-muted-foreground">总收藏数</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-primary">{stats.categories}</div>
                <div className="text-sm text-muted-foreground">分类数</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-primary">{stats.tags}</div>
                <div className="text-sm text-muted-foreground">标签数</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-primary">{stats.withFavicon}</div>
                <div className="text-sm text-muted-foreground">有图标</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-primary">{stats.withContent}</div>
                <div className="text-sm text-muted-foreground">有内容</div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* 控制面板 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            控制面板
          </CardTitle>
          <CardDescription>调整组件参数和测试不同配置</CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* 视图模式 */}
          <div className="space-y-2">
            <Label className="text-sm font-medium">视图模式</Label>
            <div className="flex flex-wrap gap-2">
              {(['row', 'compact', 'card'] as ViewMode[]).map(mode => (
                <Button
                  key={mode}
                  variant={viewMode === mode ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setViewMode(mode)}
                >
                  {mode === 'row' ? '行视图' : mode === 'compact' ? '紧凑视图' : '卡片视图'}
                </Button>
              ))}
            </div>
          </div>

          <Separator />

          {/* 数据量控制 */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label className="text-sm font-medium">数据量: {bookmarkCount}</Label>
              <div className="flex flex-wrap gap-2">
                {[10, 50, 100, 500, 1000].map(count => (
                  <Button
                    key={count}
                    variant={bookmarkCount === count ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setBookmarkCount(count)}
                  >
                    {count}
                  </Button>
                ))}
              </div>
            </div>

            <div className="space-y-2">
              <Label className="text-sm font-medium">容器高度: {containerHeight}px</Label>
              <div className="flex flex-wrap gap-2">
                {[400, 600, 800, 1000].map(height => (
                  <Button
                    key={height}
                    variant={containerHeight === height ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setContainerHeight(height)}
                  >
                    {height}px
                  </Button>
                ))}
              </div>
            </div>
          </div>

          <Separator />

          {/* 功能开关 */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="flex items-center space-x-2">
              <Switch
                id="show-stats"
                checked={showStats}
                onCheckedChange={setShowStats}
              />
              <Label htmlFor="show-stats">显示统计</Label>
            </div>

            <div className="flex items-center space-x-2">
              <Switch
                id="auto-highlight"
                checked={autoHighlight}
                onCheckedChange={setAutoHighlight}
              />
              <Label htmlFor="auto-highlight">自动高亮</Label>
            </div>

            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={clearSelection}
                disabled={!highlightId && selectedBookmarks.length === 0}
              >
                清除选择
              </Button>
            </div>
          </div>

          {/* 选择状态 */}
          {(highlightId || selectedBookmarks.length > 0) && (
            <div className="flex flex-wrap gap-2 items-center">
              {highlightId && (
                <Badge variant="default">
                  高亮: {highlightId}
                </Badge>
              )}
              {selectedBookmarks.length > 0 && (
                <Badge variant="secondary">
                  已选择: {selectedBookmarks.length} 个
                </Badge>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {/* VirtualBookmarkList组件 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <ExternalLink className="h-5 w-5" />
            收藏列表 ({mockBookmarks.length} 个收藏)
          </CardTitle>
          <CardDescription>
            当前视图: {viewMode === 'row' ? '行视图' : viewMode === 'compact' ? '紧凑视图' : '卡片视图'} | 
            容器高度: {containerHeight}px | 
            虚拟滚动: 启用
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="border border-border rounded-lg overflow-hidden">
            <VirtualBookmarkList
              bookmarks={mockBookmarks}
              viewMode={viewMode}
              containerHeight={containerHeight}
              highlightBookmarkId={highlightId}
              onEdit={handleEdit}
              onDelete={handleDelete}
              onClick={handleClick}
            />
          </div>
        </CardContent>
      </Card>

      {/* shadcn组件特性说明 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Tag className="h-5 w-5" />
            shadcn组件集成特性
          </CardTitle>
          <CardDescription>重构后的组件使用的shadcn特性</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-3">
              <h3 className="font-semibold text-foreground">Button组件特性</h3>
              <ul className="space-y-1 text-sm text-muted-foreground">
                <li>• 使用ghost变体实现悬停效果</li>
                <li>• 支持icon尺寸 (h-8 w-8)</li>
                <li>• 内置focus和disabled状态</li>
                <li>• 删除按钮使用text-destructive颜色</li>
                <li>• 与主题系统完全集成</li>
              </ul>
            </div>
            
            <div className="space-y-3">
              <h3 className="font-semibold text-foreground">Badge组件特性</h3>
              <ul className="space-y-1 text-sm text-muted-foreground">
                <li>• 使用secondary变体显示标签</li>
                <li>• 圆角边框和内边距</li>
                <li>• 响应式字体大小 (text-xs)</li>
                <li>• 支持悬停和焦点状态</li>
                <li>• 统一的视觉样式</li>
              </ul>
            </div>
            
            <div className="space-y-3">
              <h3 className="font-semibold text-foreground">颜色系统</h3>
              <ul className="space-y-1 text-sm text-muted-foreground">
                <li>• text-foreground: 主要文本颜色</li>
                <li>• text-muted-foreground: 次要文本</li>
                <li>• text-primary: 链接和强调色</li>
                <li>• border-border: 默认边框颜色</li>
                <li>• border-primary: 高亮边框颜色</li>
              </ul>
            </div>
            
            <div className="space-y-3">
              <h3 className="font-semibold text-foreground">主题一致性</h3>
              <ul className="space-y-1 text-sm text-muted-foreground">
                <li>• 支持明暗主题切换</li>
                <li>• CSS变量驱动的颜色系统</li>
                <li>• 统一的间距和圆角</li>
                <li>• 可访问性友好的对比度</li>
                <li>• 使用cn函数合并类名</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 性能信息 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calendar className="h-5 w-5" />
            性能特性
          </CardTitle>
          <CardDescription>虚拟滚动和性能优化特性</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center p-4 border border-border rounded-lg">
              <div className="text-lg font-semibold text-primary">虚拟滚动</div>
              <div className="text-sm text-muted-foreground mt-1">
                只渲染可见区域的项目，支持大量数据
              </div>
            </div>
            <div className="text-center p-4 border border-border rounded-lg">
              <div className="text-lg font-semibold text-primary">动态高度</div>
              <div className="text-sm text-muted-foreground mt-1">
                自动计算项目高度，适应不同内容
              </div>
            </div>
            <div className="text-center p-4 border border-border rounded-lg">
              <div className="text-lg font-semibold text-primary">响应式布局</div>
              <div className="text-sm text-muted-foreground mt-1">
                支持多种视图模式和屏幕尺寸
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

export default VirtualBookmarkListTest