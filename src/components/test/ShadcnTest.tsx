import React from 'react';
import { Button } from '@/components/ui/button';

/**
 * shadcn/ui组件测试组件
 * 用于验证shadcn/ui基础环境是否正常工作
 */
export const ShadcnTest: React.FC = () => {
  return (
    <div className="p-4 space-y-4">
      <h2 className="text-lg font-semibold">shadcn/ui 组件测试</h2>
      
      {/* 测试不同变体的Button组件 */}
      <div className="space-x-2">
        <Button variant="default">默认按钮</Button>
        <Button variant="secondary">次要按钮</Button>
        <Button variant="outline">轮廓按钮</Button>
        <Button variant="ghost">幽灵按钮</Button>
        <Button variant="destructive">危险按钮</Button>
      </div>
      
      {/* 测试不同尺寸的Button组件 */}
      <div className="space-x-2">
        <Button size="sm">小按钮</Button>
        <Button size="default">默认按钮</Button>
        <Button size="lg">大按钮</Button>
      </div>
      
      {/* 测试CSS变量是否正常工作 */}
      <div className="p-4 bg-card border rounded-lg">
        <p className="text-card-foreground">
          这是一个使用shadcn主题变量的卡片组件
        </p>
      </div>
    </div>
  );
};