import React, { useState, useCallback, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card'
import { Button } from '../ui/button'
import { Input } from '../ui/input'
import { Label } from '../ui/label'
import { Badge } from '../ui/badge'
import { Separator } from '../ui/separator'
import { Alert, AlertDescription } from '../ui/alert'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '../ui/tabs'
import { 
  Bot, 
  Search, 
  Server, 
  Wifi, 
  WifiOff, 
  Clock, 
  Database, 
  Settings, 
  RefreshCw,
  CheckCircle,
  XCircle,
  AlertCircle,
  Loader2,
  MessageCircle
} from 'lucide-react'

// 导入对话测试组件
import AIModelChatTest from './AIModelChatTest'
import CloudAIServiceTest from './CloudAIServiceTest'

// 导入我们的AI服务
import { localAIServiceAdapter } from '../../services/localAIServiceAdapter'
import { aiProviderService } from '../../services/aiProviderService'

interface LocalServiceConfig {
  name: string
  baseUrl: string
  port: number
  protocol: 'http' | 'https'
  apiPath?: string
  healthCheckPath?: string
  modelsPath?: string
  timeout?: number
  headers?: Record<string, string>
}

interface ServiceStatus {
  isOnline: boolean
  responseTime?: number
  modelCount?: number
  error?: string
  lastChecked?: Date
}

interface AIModel {
  id: string
  name: string
  displayName: string
  description?: string
  size?: string
  parameters?: string
  capabilities?: string[]
  tags?: string[]
  providerId: string
  isRecommended?: boolean
  isPopular?: boolean
}

const LocalAIServiceTestPage: React.FC = () => {
  // 状态管理
  const [discoveredServices, setDiscoveredServices] = useState<LocalServiceConfig[]>([])
  const [serviceStatuses, setServiceStatuses] = useState<Record<string, ServiceStatus>>({})
  const [serviceModels, setServiceModels] = useState<Record<string, AIModel[]>>({})
  const [isDiscovering, setIsDiscovering] = useState(false)
  const [isTestingAll, setIsTestingAll] = useState(false)
  const [customPorts, setCustomPorts] = useState('')
  const [customServiceName, setCustomServiceName] = useState('My AI Service')
  const [customServiceUrl, setCustomServiceUrl] = useState('http://localhost:8080')
  const [customApiPath, setCustomApiPath] = useState('/v1')
  const [customTimeout, setCustomTimeout] = useState('10000')
  const [logs, setLogs] = useState<string[]>([])

  // 添加日志
  const addLog = useCallback((message: string) => {
    const timestamp = new Date().toLocaleTimeString()
    setLogs(prev => [`[${timestamp}] ${message}`, ...prev.slice(0, 49)]) // 保留最近50条日志
  }, [])

  // 发现本地服务
  const discoverServices = useCallback(async () => {
    setIsDiscovering(true)
    addLog('开始发现本地AI服务...')
    
    try {
      const customPortsList = customPorts
        ? customPorts.split(',').map(p => parseInt(p.trim())).filter(p => !isNaN(p))
        : []
      
      addLog(`扫描端口: ${customPortsList.length > 0 ? customPortsList.join(', ') : '默认端口'}`)
      
      const result = await localAIServiceAdapter.discoverLocalServices(customPortsList)
      
      setDiscoveredServices(result.services)
      addLog(`发现 ${result.services.length} 个本地AI服务`)
      
      if (result.errors.length > 0) {
        addLog(`扫描错误: ${result.errors.length} 个端口无法连接`)
      }
      
      // 清空之前的状态
      setServiceStatuses({})
      setServiceModels({})
      
    } catch (error) {
      addLog(`服务发现失败: ${error.message}`)
    } finally {
      setIsDiscovering(false)
    }
  }, [customPorts, addLog])

  // 测试单个服务连接
  const testServiceConnection = useCallback(async (service: LocalServiceConfig) => {
    const serviceKey = `${service.name}-${service.port}`
    
    setServiceStatuses(prev => ({
      ...prev,
      [serviceKey]: { isOnline: false, lastChecked: new Date() }
    }))
    
    addLog(`测试 ${service.name} 连接...`)
    
    try {
      const result = await localAIServiceAdapter.testLocalServiceConnection(service)
      
      const status: ServiceStatus = {
        isOnline: result.success,
        responseTime: result.responseTime,
        modelCount: result.modelCount,
        error: result.error,
        lastChecked: new Date()
      }
      
      setServiceStatuses(prev => ({
        ...prev,
        [serviceKey]: status
      }))
      
      if (result.success) {
        addLog(`${service.name} 连接成功 (${result.responseTime}ms, ${result.modelCount || 0} 个模型)`)
      } else {
        addLog(`${service.name} 连接失败: ${result.error}`)
      }
      
    } catch (error) {
      addLog(`${service.name} 测试异常: ${error.message}`)
      setServiceStatuses(prev => ({
        ...prev,
        [serviceKey]: {
          isOnline: false,
          error: error.message,
          lastChecked: new Date()
        }
      }))
    }
  }, [addLog])

  // 获取服务模型列表
  const getServiceModels = useCallback(async (service: LocalServiceConfig) => {
    const serviceKey = `${service.name}-${service.port}`
    addLog(`获取 ${service.name} 模型列表...`)
    
    try {
      const models = await localAIServiceAdapter.getLocalServiceModels(service)
      
      setServiceModels(prev => ({
        ...prev,
        [serviceKey]: models
      }))
      
      addLog(`${service.name} 找到 ${models.length} 个模型`)
      
    } catch (error) {
      addLog(`${service.name} 模型获取失败: ${error.message}`)
    }
  }, [addLog])

  // 测试所有服务
  const testAllServices = useCallback(async () => {
    if (discoveredServices.length === 0) {
      addLog('没有发现的服务可供测试')
      return
    }
    
    setIsTestingAll(true)
    addLog(`开始测试所有 ${discoveredServices.length} 个服务...`)
    
    for (const service of discoveredServices) {
      await testServiceConnection(service)
      
      // 如果连接成功，获取模型列表
      const serviceKey = `${service.name}-${service.port}`
      const status = serviceStatuses[serviceKey]
      if (status?.isOnline) {
        await getServiceModels(service)
      }
    }
    
    setIsTestingAll(false)
    addLog('所有服务测试完成')
  }, [discoveredServices, testServiceConnection, getServiceModels, serviceStatuses, addLog])

  // 测试自定义服务
  const testCustomService = useCallback(async () => {
    if (!customServiceName || !customServiceUrl) {
      addLog('请填写服务名称和地址')
      return
    }
    
    try {
      const config = localAIServiceAdapter.createCustomServiceConfig(
        customServiceName,
        customServiceUrl,
        {
          apiPath: customApiPath || '/v1',
          timeout: parseInt(customTimeout) || 10000
        }
      )
      
      addLog(`测试自定义服务: ${customServiceName}`)
      await testServiceConnection(config)
      
      // 如果连接成功，获取模型列表
      const serviceKey = `${config.name}-${config.port}`
      setTimeout(async () => {
        const status = serviceStatuses[serviceKey]
        if (status?.isOnline) {
          await getServiceModels(config)
        }
      }, 100)
      
    } catch (error) {
      addLog(`自定义服务测试失败: ${error.message}`)
    }
  }, [customServiceName, customServiceUrl, customApiPath, customTimeout, testServiceConnection, getServiceModels, serviceStatuses, addLog])

  // 获取默认服务配置
  const getDefaultServices = useCallback(() => {
    const defaultServices = localAIServiceAdapter.getDefaultServices()
    addLog(`获取到 ${defaultServices.length} 个默认服务配置`)
    
    defaultServices.forEach((service, index) => {
      addLog(`${index + 1}. ${service.name} - ${service.baseUrl}`)
    })
  }, [addLog])

  // 清空日志
  const clearLogs = useCallback(() => {
    setLogs([])
    addLog('日志已清空')
  }, [addLog])

  // 页面加载时获取默认配置
  useEffect(() => {
    addLog('本地AI服务测试页面已加载')
    getDefaultServices()
  }, [getDefaultServices, addLog])

  // 渲染服务状态图标
  const renderStatusIcon = (status?: ServiceStatus) => {
    if (!status) {
      return <AlertCircle className="h-4 w-4 text-gray-400" />
    }
    
    if (status.isOnline) {
      return <CheckCircle className="h-4 w-4 text-green-500" />
    } else {
      return <XCircle className="h-4 w-4 text-red-500" />
    }
  }

  // 渲染服务状态徽章
  const renderStatusBadge = (status?: ServiceStatus) => {
    if (!status) {
      return <Badge variant="secondary">未测试</Badge>
    }
    
    if (status.isOnline) {
      return <Badge variant="default" className="bg-green-500">在线</Badge>
    } else {
      return <Badge variant="destructive">离线</Badge>
    }
  }

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center space-x-2">
        <Bot className="h-6 w-6 text-primary" />
        <h1 className="text-2xl font-bold">AI服务集成测试</h1>
      </div>

      {/* 标签页导航 */}
      <Tabs defaultValue="discovery" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="discovery" className="flex items-center space-x-2">
            <Search className="h-4 w-4" />
            <span>本地服务</span>
          </TabsTrigger>
          <TabsTrigger value="cloud" className="flex items-center space-x-2">
            <Bot className="h-4 w-4" />
            <span>云端服务</span>
          </TabsTrigger>
          <TabsTrigger value="chat" className="flex items-center space-x-2">
            <MessageCircle className="h-4 w-4" />
            <span>对话测试</span>
          </TabsTrigger>
        </TabsList>

        {/* 服务发现标签页 */}
        <TabsContent value="discovery" className="space-y-6 mt-6">

      {/* 服务发现区域 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Search className="h-5 w-5" />
            <span>服务发现</span>
          </CardTitle>
          <CardDescription>
            自动扫描本地AI服务，包括Ollama、LM Studio、Xinference等
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex flex-col space-y-2">
            <Label htmlFor="customPorts">自定义端口 (用逗号分隔)</Label>
            <Input
              id="customPorts"
              placeholder="例如: 8888,9999,7777"
              value={customPorts}
              onChange={(e) => setCustomPorts(e.target.value)}
            />
          </div>
          
          <div className="flex space-x-2">
            <Button 
              onClick={discoverServices} 
              disabled={isDiscovering}
              className="flex items-center space-x-2"
            >
              {isDiscovering ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Search className="h-4 w-4" />
              )}
              <span>{isDiscovering ? '扫描中...' : '发现服务'}</span>
            </Button>
            
            <Button 
              variant="outline" 
              onClick={testAllServices}
              disabled={isTestingAll || discoveredServices.length === 0}
              className="flex items-center space-x-2"
            >
              {isTestingAll ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Wifi className="h-4 w-4" />
              )}
              <span>{isTestingAll ? '测试中...' : '测试所有'}</span>
            </Button>
            
            <Button variant="outline" onClick={getDefaultServices}>
              <Settings className="h-4 w-4 mr-2" />
              默认配置
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* 自定义服务测试 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Server className="h-5 w-5" />
            <span>自定义服务测试</span>
          </CardTitle>
          <CardDescription>
            测试自定义本地AI服务的连接和配置
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="serviceName">服务名称</Label>
              <Input
                id="serviceName"
                placeholder="例如: My AI Service"
                value={customServiceName}
                onChange={(e) => setCustomServiceName(e.target.value)}
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="serviceUrl">服务地址</Label>
              <Input
                id="serviceUrl"
                placeholder="例如: http://localhost:8080"
                value={customServiceUrl}
                onChange={(e) => setCustomServiceUrl(e.target.value)}
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="apiPath">API路径 (可选)</Label>
              <Input
                id="apiPath"
                placeholder="例如: /v1"
                value={customApiPath}
                onChange={(e) => setCustomApiPath(e.target.value)}
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="timeout">超时时间 (毫秒)</Label>
              <Input
                id="timeout"
                type="number"
                placeholder="10000"
                value={customTimeout}
                onChange={(e) => setCustomTimeout(e.target.value)}
              />
            </div>
          </div>
          
          <Button onClick={testCustomService} className="flex items-center space-x-2">
            <Server className="h-4 w-4" />
            <span>测试自定义服务</span>
          </Button>
        </CardContent>
      </Card>

      {/* 发现的服务列表 */}
      {discoveredServices.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Database className="h-5 w-5" />
              <span>发现的服务 ({discoveredServices.length})</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {discoveredServices.map((service) => {
                const serviceKey = `${service.name}-${service.port}`
                const status = serviceStatuses[serviceKey]
                const models = serviceModels[serviceKey] || []
                
                return (
                  <Card key={serviceKey} className="border-l-4 border-l-primary">
                    <CardContent className="pt-4">
                      <div className="flex items-center justify-between mb-3">
                        <div className="flex items-center space-x-3">
                          {renderStatusIcon(status)}
                          <div>
                            <h3 className="font-semibold">{service.name}</h3>
                            <p className="text-sm text-muted-foreground">
                              {service.baseUrl} (端口: {service.port})
                            </p>
                          </div>
                        </div>
                        <div className="flex items-center space-x-2">
                          {renderStatusBadge(status)}
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => testServiceConnection(service)}
                            className="flex items-center space-x-1"
                          >
                            <RefreshCw className="h-3 w-3" />
                            <span>测试</span>
                          </Button>
                          {status?.isOnline && (
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => getServiceModels(service)}
                              className="flex items-center space-x-1"
                            >
                              <Database className="h-3 w-3" />
                              <span>模型</span>
                            </Button>
                          )}
                        </div>
                      </div>
                      
                      {status && (
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                          <div>
                            <span className="text-muted-foreground">响应时间:</span>
                            <div className="flex items-center space-x-1">
                              <Clock className="h-3 w-3" />
                              <span>{status.responseTime || 0}ms</span>
                            </div>
                          </div>
                          <div>
                            <span className="text-muted-foreground">模型数量:</span>
                            <div className="flex items-center space-x-1">
                              <Database className="h-3 w-3" />
                              <span>{status.modelCount || 0}</span>
                            </div>
                          </div>
                          <div>
                            <span className="text-muted-foreground">最后检查:</span>
                            <div>{status.lastChecked?.toLocaleTimeString()}</div>
                          </div>
                          {status.error && (
                            <div>
                              <span className="text-muted-foreground">错误:</span>
                              <div className="text-red-500 text-xs">{status.error}</div>
                            </div>
                          )}
                        </div>
                      )}
                      
                      {models.length > 0 && (
                        <div className="mt-4">
                          <Separator className="mb-3" />
                          <h4 className="font-medium mb-2">可用模型 ({models.length})</h4>
                          <div className="space-y-2 max-h-40 overflow-y-auto">
                            {models.map((model) => (
                              <div key={model.id} className="p-2 bg-muted rounded-md">
                                <div className="flex items-center justify-between">
                                  <div>
                                    <div className="font-medium text-sm">{model.displayName}</div>
                                    <div className="text-xs text-muted-foreground">{model.description}</div>
                                  </div>
                                  <div className="flex space-x-1">
                                    {model.isRecommended && (
                                      <Badge variant="secondary" className="text-xs">推荐</Badge>
                                    )}
                                    {model.isPopular && (
                                      <Badge variant="outline" className="text-xs">热门</Badge>
                                    )}
                                  </div>
                                </div>
                                {(model.size || model.parameters) && (
                                  <div className="text-xs text-muted-foreground mt-1">
                                    {model.size && `大小: ${model.size}`}
                                    {model.size && model.parameters && ' | '}
                                    {model.parameters && `参数: ${model.parameters}`}
                                  </div>
                                )}
                                {model.capabilities && model.capabilities.length > 0 && (
                                  <div className="flex flex-wrap gap-1 mt-2">
                                    {model.capabilities.map((cap) => (
                                      <Badge key={cap} variant="outline" className="text-xs">
                                        {cap}
                                      </Badge>
                                    ))}
                                  </div>
                                )}
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                )
              })}
            </div>
          </CardContent>
        </Card>
      )}

      {/* 日志区域 */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center space-x-2">
              <AlertCircle className="h-5 w-5" />
              <span>测试日志</span>
            </CardTitle>
            <Button variant="outline" size="sm" onClick={clearLogs}>
              清空日志
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="bg-muted p-4 rounded-md max-h-60 overflow-y-auto font-mono text-sm">
            {logs.length === 0 ? (
              <div className="text-muted-foreground">暂无日志</div>
            ) : (
              logs.map((log, index) => (
                <div key={index} className="mb-1">
                  {log}
                </div>
              ))
            )}
          </div>
        </CardContent>
      </Card>

        {/* 使用说明 */}
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            <strong>使用说明:</strong>
            <ul className="mt-2 space-y-1 text-sm">
              <li>• 点击"发现服务"自动扫描常见端口的AI服务</li>
              <li>• 可以在自定义端口中添加额外的端口进行扫描</li>
              <li>• 使用"测试自定义服务"功能测试特定的AI服务</li>
              <li>• 绿色图标表示服务在线，红色表示离线</li>
              <li>• 点击"模型"按钮可以获取服务的可用模型列表</li>
            </ul>
          </AlertDescription>
        </Alert>
        </TabsContent>

        {/* 云端服务测试标签页 */}
        <TabsContent value="cloud" className="mt-6">
          <CloudAIServiceTest />
        </TabsContent>

        {/* 对话测试标签页 */}
        <TabsContent value="chat" className="mt-6">
          <AIModelChatTest />
        </TabsContent>
      </Tabs>
    </div>
  )
}

export default LocalAIServiceTestPage