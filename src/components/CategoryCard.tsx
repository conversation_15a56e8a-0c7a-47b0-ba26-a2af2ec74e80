// 分类卡片组件 - 显示单个分类的信息和操作

import React from 'react'
import { Edit, Trash2, Folder, Hash } from 'lucide-react'
import type { Category } from '../types'

interface CategoryCardProps {
  /** 分类数据 */
  category: Category
  /** 书签数量 */
  bookmarkCount: number
  /** 编辑回调 */
  onEdit: () => void
  /** 删除回调 */
  onDelete: () => void
  /** 点击回调 */
  onClick?: () => void
  /** 自定义CSS类名 */
  className?: string
}

/**
 * 分类卡片组件
 * 显示分类的基本信息、统计数据和操作按钮
 * 支持颜色显示、悬停效果和交互反馈
 */
const CategoryCard: React.FC<CategoryCardProps> = React.memo(({
  category,
  bookmarkCount,
  onEdit,
  onDelete,
  onClick,
  className = ''
}) => {
  // 处理卡片点击事件
  const handleCardClick = (e: React.MouseEvent) => {
    // 如果点击的是操作按钮，不触发卡片点击
    if ((e.target as HTMLElement).closest('.category-card-actions')) {
      return
    }
    onClick?.()
  }

  // 处理编辑按钮点击
  const handleEditClick = (e: React.MouseEvent) => {
    e.stopPropagation()
    onEdit()
  }

  // 处理删除按钮点击
  const handleDeleteClick = (e: React.MouseEvent) => {
    e.stopPropagation()
    onDelete()
  }

  // 格式化时间显示
  const formatTime = (date: Date | string) => {
    const dateObj = typeof date === 'string' ? new Date(date) : date
    
    if (isNaN(dateObj.getTime())) {
      return '未知时间'
    }
    
    const now = new Date()
    const diffMs = now.getTime() - dateObj.getTime()
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))
    
    if (diffDays === 0) {
      return '今天'
    } else if (diffDays === 1) {
      return '昨天'
    } else if (diffDays < 7) {
      return `${diffDays}天前`
    } else {
      return dateObj.toLocaleDateString('zh-CN', { 
        month: 'short', 
        day: 'numeric' 
      })
    }
  }

  // 获取分类颜色样式
  const getCategoryColorStyle = () => {
    if (!category.color) {
      return {
        backgroundColor: '#F3F4F6', // 默认灰色背景
        borderColor: '#D1D5DB'
      }
    }

    // 将颜色转换为更浅的背景色和边框色
    const color = category.color
    return {
      backgroundColor: `${color}15`, // 添加透明度
      borderColor: `${color}40`
    }
  }

  // 获取分类颜色指示器样式
  const getColorIndicatorStyle = () => {
    return {
      backgroundColor: category.color || '#6B7280'
    }
  }

  return (
    <div 
      className={`
        group relative bg-white border-2 rounded-lg p-4 hover:shadow-lg transition-all duration-200 cursor-pointer
        ${onClick ? 'hover:border-gray-300' : ''}
        ${className}
      `}
      style={getCategoryColorStyle()}
      onClick={handleCardClick}
    >
      {/* 头部：分类图标 + 名称 + 操作按钮 */}
      <div className="flex items-start justify-between mb-3">
        <div className="flex items-start space-x-3 flex-1 min-w-0">
          {/* 分类颜色指示器和图标 */}
          <div className="flex-shrink-0 relative">
            <div className="w-10 h-10 rounded-lg bg-gray-100 flex items-center justify-center">
              <Folder className="w-5 h-5 text-gray-600" />
            </div>
            {/* 颜色指示器 */}
            <div 
              className="absolute -top-1 -right-1 w-4 h-4 rounded-full border-2 border-white"
              style={getColorIndicatorStyle()}
              title={`分类颜色: ${category.color || '默认'}`}
            />
          </div>

          {/* 分类名称和描述 */}
          <div className="flex-1 min-w-0">
            <h3 className="text-lg font-semibold text-gray-900 group-hover:text-primary-600 transition-colors truncate">
              {category.name}
            </h3>
            {category.description && (
              <p className="text-sm text-gray-600 mt-1 text-truncate-2">
                {category.description}
              </p>
            )}
          </div>
        </div>

        {/* 操作按钮区域 */}
        <div className="category-card-actions flex-shrink-0 flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-opacity duration-150">
          {/* 编辑按钮 */}
          <button
            onClick={handleEditClick}
            className="p-2 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
            title="编辑分类"
            aria-label="编辑分类"
          >
            <Edit className="w-4 h-4" />
          </button>

          {/* 删除按钮 */}
          <button
            onClick={handleDeleteClick}
            className="p-2 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded-lg transition-colors"
            title="删除分类"
            aria-label="删除分类"
          >
            <Trash2 className="w-4 h-4" />
          </button>
        </div>
      </div>

      {/* 统计信息区域 */}
      <div className="space-y-2 mb-4">
        {/* 书签数量 */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2 text-sm text-gray-600">
            <Hash className="w-4 h-4" />
            <span>书签数量</span>
          </div>
          <span className="text-lg font-semibold text-gray-900">
            {bookmarkCount}
          </span>
        </div>

        {/* 分隔线 */}
        <div className="border-t border-gray-200" />

        {/* 创建时间 */}
        <div className="flex items-center justify-between text-xs text-gray-500">
          <span>创建时间</span>
          <span>{formatTime(category.createdAt)}</span>
        </div>

        {/* 更新时间 */}
        {category.updatedAt && new Date(category.updatedAt).getTime() !== new Date(category.createdAt).getTime() && (
          <div className="flex items-center justify-between text-xs text-gray-500">
            <span>更新时间</span>
            <span>{formatTime(category.updatedAt)}</span>
          </div>
        )}
      </div>

      {/* 底部状态指示器 */}
      <div className="flex items-center justify-between">
        {/* 分类状态 */}
        <div className="flex items-center space-x-2">
          {bookmarkCount === 0 ? (
            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-600">
              空分类
            </span>
          ) : (
            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-700">
              活跃
            </span>
          )}
        </div>

        {/* 父分类指示器（如果有） */}
        {category.parentId && (
          <div className="text-xs text-gray-500">
            <span>子分类</span>
          </div>
        )}
      </div>

      {/* 悬停时的阴影效果增强 */}
      <div className="absolute inset-0 rounded-lg bg-gradient-to-r from-transparent to-transparent group-hover:from-white/10 group-hover:to-transparent pointer-events-none transition-all duration-200" />
    </div>
  )
})

// 设置显示名称便于调试
CategoryCard.displayName = 'CategoryCard'

export default CategoryCard

// 导出类型定义
export type { CategoryCardProps }