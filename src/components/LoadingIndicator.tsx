// 加载指示器组件 - 统一的加载状态显示

import React from 'react'
import { Loader2 } from 'lucide-react'

export interface LoadingIndicatorProps {
  /** 是否显示 */
  isVisible: boolean
  /** 加载消息 */
  message?: string
  /** 进度百分比 (0-100) */
  progress?: number
  /** 大小 */
  size?: 'small' | 'medium' | 'large'
  /** 是否为覆盖层模式 */
  overlay?: boolean
  /** 自定义CSS类名 */
  className?: string
}

/**
 * 加载指示器组件
 * 提供统一的加载状态显示，支持进度条和覆盖层模式
 */
const LoadingIndicator: React.FC<LoadingIndicatorProps> = ({
  isVisible,
  message,
  progress,
  size = 'medium',
  overlay = false,
  className = ''
}) => {
  if (!isVisible) return null

  // 获取尺寸类
  const getSizeClasses = () => {
    switch (size) {
      case 'small':
        return {
          spinner: 'w-4 h-4',
          text: 'text-sm',
          container: 'p-2'
        }
      case 'large':
        return {
          spinner: 'w-8 h-8',
          text: 'text-lg',
          container: 'p-6'
        }
      default: // medium
        return {
          spinner: 'w-6 h-6',
          text: 'text-base',
          container: 'p-4'
        }
    }
  }

  const sizeClasses = getSizeClasses()

  // 渲染进度条
  const renderProgressBar = () => {
    if (progress === undefined) return null

    return (
      <div className="w-full bg-gray-200 rounded-full h-2 mt-3">
        <div
          className="bg-primary-600 h-2 rounded-full transition-all duration-300 ease-out"
          style={{ width: `${progress}%` }}
        />
        <div className="text-xs text-gray-600 mt-1 text-center">
          {Math.round(progress)}%
        </div>
      </div>
    )
  }

  // 渲染加载内容
  const renderContent = () => (
    <div className={`flex flex-col items-center justify-center ${sizeClasses.container}`}>
      {/* 旋转图标 */}
      <Loader2 className={`${sizeClasses.spinner} animate-spin text-primary-600`} />
      
      {/* 加载消息 */}
      {message && (
        <p className={`mt-2 text-gray-600 ${sizeClasses.text} text-center`}>
          {message}
        </p>
      )}

      {/* 进度条 */}
      {renderProgressBar()}
    </div>
  )

  // 覆盖层模式
  if (overlay) {
    return (
      <div className={`
        fixed inset-0 bg-background/80 backdrop-blur-sm flex items-center justify-center z-50
        ${className}
      `}>
        <div className="bg-white rounded-lg shadow-lg max-w-sm w-full mx-4">
          {renderContent()}
        </div>
      </div>
    )
  }

  // 内联模式
  return (
    <div className={`flex items-center justify-center ${className}`}>
      {renderContent()}
    </div>
  )
}

export default LoadingIndicator