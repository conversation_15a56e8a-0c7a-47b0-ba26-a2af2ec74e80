import React, { useState } from 'react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Badge } from '@/components/ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

/**
 * 高级交互组件演示
 * 展示DropdownMenu、Badge、Select、Tooltip组件的使用
 */
export const AdvancedComponentsDemo: React.FC = () => {
  const [selectedCategory, setSelectedCategory] = useState<string>('');

  return (
    <TooltipProvider>
      <div className="p-6 space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>高级交互组件演示</CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            
            {/* DropdownMenu 演示 */}
            <div className="space-y-2">
              <h3 className="text-lg font-semibold">DropdownMenu 组件</h3>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline">操作菜单</Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent>
                  <DropdownMenuLabel>收藏夹操作</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem>编辑收藏夹</DropdownMenuItem>
                  <DropdownMenuItem>复制链接</DropdownMenuItem>
                  <DropdownMenuItem>移动到分类</DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem className="text-destructive">
                    删除收藏夹
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>

            {/* Badge 演示 */}
            <div className="space-y-2">
              <h3 className="text-lg font-semibold">Badge 组件</h3>
              <div className="flex gap-2 flex-wrap">
                <Badge variant="default">默认标签</Badge>
                <Badge variant="secondary">次要标签</Badge>
                <Badge variant="destructive">重要标签</Badge>
                <Badge variant="outline">轮廓标签</Badge>
              </div>
              <div className="flex gap-2 flex-wrap">
                <Badge>技术</Badge>
                <Badge>前端</Badge>
                <Badge>React</Badge>
                <Badge>shadcn/ui</Badge>
              </div>
            </div>

            {/* Select 演示 */}
            <div className="space-y-2">
              <h3 className="text-lg font-semibold">Select 组件</h3>
              <div className="w-[200px]">
                <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                  <SelectTrigger>
                    <SelectValue placeholder="选择分类" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="tech">技术</SelectItem>
                    <SelectItem value="design">设计</SelectItem>
                    <SelectItem value="business">商业</SelectItem>
                    <SelectItem value="personal">个人</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              {selectedCategory && (
                <p className="text-sm text-muted-foreground">
                  已选择分类: {selectedCategory}
                </p>
              )}
            </div>

            {/* Tooltip 演示 */}
            <div className="space-y-2">
              <h3 className="text-lg font-semibold">Tooltip 组件</h3>
              <div className="flex gap-4">
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button variant="outline">悬停查看提示</Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>这是一个提示信息</p>
                  </TooltipContent>
                </Tooltip>

                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button variant="ghost">编辑</Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>编辑收藏夹信息</p>
                  </TooltipContent>
                </Tooltip>

                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button variant="destructive" size="sm">删除</Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>删除此收藏夹</p>
                  </TooltipContent>
                </Tooltip>
              </div>
            </div>

            {/* 组合使用演示 */}
            <div className="space-y-2">
              <h3 className="text-lg font-semibold">组合使用演示</h3>
              <Card className="p-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-1">
                    <h4 className="font-medium">示例收藏夹</h4>
                    <div className="flex gap-1">
                      <Badge variant="secondary">React</Badge>
                      <Badge variant="outline">教程</Badge>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button variant="ghost" size="sm">
                          ⭐
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>添加到收藏</p>
                      </TooltipContent>
                    </Tooltip>
                    
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm">
                          ⋮
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem>查看详情</DropdownMenuItem>
                        <DropdownMenuItem>编辑</DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem className="text-destructive">
                          删除
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </div>
              </Card>
            </div>
          </CardContent>
        </Card>
      </div>
    </TooltipProvider>
  );
};

export default AdvancedComponentsDemo;