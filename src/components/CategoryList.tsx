// 分类列表组件 - 显示所有分类

import React from 'react'
import { Folder, Plus } from 'lucide-react'
import CategoryCard from './CategoryCard'
import type { Category } from '../types'

interface CategoryWithStats extends Category {
  bookmarkCount: number
}

interface CategoryListProps {
  /** 分类列表数据 */
  categories: CategoryWithStats[]
  /** 编辑分类回调 */
  onCategoryEdit: (category: Category) => void
  /** 删除分类回调 */
  onCategoryDelete: (category: Category) => void
  /** 点击分类回调 */
  onCategoryClick?: (category: Category) => void
  /** 创建新分类回调 */
  onCreateCategory?: () => void
  /** 是否正在加载 */
  loading?: boolean
  /** 自定义CSS类名 */
  className?: string
}

/**
 * 分类列表组件
 * 以网格布局显示所有分类，支持空状态和加载状态
 */
const CategoryList: React.FC<CategoryListProps> = React.memo(({
  categories,
  onCategoryEdit,
  onCategoryDelete,
  onCategoryClick,
  onCreateCategory,
  loading = false,
  className = ''
}) => {
  // 渲染加载状态
  const renderLoadingState = () => (
    <div className="text-center py-12">
      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto mb-4"></div>
      <p className="text-gray-500">加载分类数据中...</p>
    </div>
  )

  // 渲染空状态
  const renderEmptyState = () => (
    <div className="text-center py-12">
      <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
        <Folder className="w-8 h-8 text-gray-400" />
      </div>
      <h3 className="text-lg font-medium text-gray-900 mb-2">
        暂无分类
      </h3>
      <p className="text-gray-500 mb-6">
        创建您的第一个分类来更好地组织书签
      </p>
      {onCreateCategory && (
        <button
          onClick={onCreateCategory}
          className="inline-flex items-center px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors"
        >
          <Plus className="w-4 h-4 mr-2" />
          创建分类
        </button>
      )}
    </div>
  )

  // 渲染分类网格
  const renderCategoryGrid = () => (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
      {categories.map((category) => (
        <CategoryCard
          key={category.id}
          category={category}
          bookmarkCount={category.bookmarkCount}
          onEdit={() => onCategoryEdit(category)}
          onDelete={() => onCategoryDelete(category)}
          onClick={onCategoryClick ? () => onCategoryClick(category) : undefined}
          className="h-full"
        />
      ))}
    </div>
  )

  // 渲染分类统计信息
  const renderCategoryStats = () => {
    if (categories.length === 0) return null

    const totalBookmarks = categories.reduce((sum, category) => sum + category.bookmarkCount, 0)
    const activeCategories = categories.filter(category => category.bookmarkCount > 0).length
    const emptyCategories = categories.length - activeCategories

    return (
      <div className="bg-gray-50 rounded-lg p-4 mb-6">
        <div className="grid grid-cols-1 sm:grid-cols-4 gap-4 text-center">
          <div>
            <div className="text-2xl font-bold text-gray-900">{categories.length}</div>
            <div className="text-sm text-gray-600">总分类数</div>
          </div>
          <div>
            <div className="text-2xl font-bold text-green-600">{activeCategories}</div>
            <div className="text-sm text-gray-600">活跃分类</div>
          </div>
          <div>
            <div className="text-2xl font-bold text-gray-400">{emptyCategories}</div>
            <div className="text-sm text-gray-600">空分类</div>
          </div>
          <div>
            <div className="text-2xl font-bold text-primary-600">{totalBookmarks}</div>
            <div className="text-sm text-gray-600">总书签数</div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* 分类统计信息 */}
      {!loading && renderCategoryStats()}

      {/* 主要内容区域 */}
      <div>
        {loading ? (
          renderLoadingState()
        ) : categories.length === 0 ? (
          renderEmptyState()
        ) : (
          renderCategoryGrid()
        )}
      </div>
    </div>
  )
})

// 设置显示名称便于调试
CategoryList.displayName = 'CategoryList'

export default CategoryList

// 导出类型定义
export type { CategoryListProps, CategoryWithStats }