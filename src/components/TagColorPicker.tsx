import React, { useState, useRef, useEffect } from 'react'
import { ColorUtils } from '../utils/colorUtils'

/**
 * 标签颜色选择器组件属性接口
 */
export interface TagColorPickerProps {
  /** 当前选中的颜色值 */
  value?: string
  /** 颜色变化回调函数 */
  onChange: (color: string) => void
  /** 预设颜色列表 */
  presetColors?: string[]
  /** 是否允许自定义颜色 */
  allowCustom?: boolean
  /** 组件样式类名 */
  className?: string
  /** 是否禁用 */
  disabled?: boolean
  /** 占位符文本 */
  placeholder?: string
}

/**
 * 标签颜色选择器组件
 * 提供预设颜色选择和自定义颜色输入功能
 */
export const TagColorPicker: React.FC<TagColorPickerProps> = ({
  value = '',
  onChange,
  presetColors = ColorUtils.PRESET_COLORS,
  allowCustom = true,
  className = '',
  disabled = false,
  placeholder = '选择颜色'
}) => {
  const [showCustomInput, setShowCustomInput] = useState(false)
  const [customColor, setCustomColor] = useState('')
  const [validationError, setValidationError] = useState('')
  const colorInputRef = useRef<HTMLInputElement>(null)

  // 当value变化时，更新customColor
  useEffect(() => {
    if (value && !presetColors.includes(value)) {
      setCustomColor(value)
      setShowCustomInput(true)
    }
  }, [value, presetColors])

  /**
   * 处理预设颜色选择
   */
  const handlePresetColorSelect = (color: string) => {
    setValidationError('')
    setShowCustomInput(false)
    setCustomColor('')
    onChange(color)
  }

  /**
   * 处理自定义颜色输入
   */
  const handleCustomColorChange = (inputValue: string) => {
    setCustomColor(inputValue)
    setValidationError('')

    // 实时验证颜色格式
    if (inputValue.trim()) {
      if (ColorUtils.isValidColor(inputValue)) {
        onChange(inputValue)
      } else {
        setValidationError('无效的颜色格式')
      }
    }
  }

  /**
   * 处理自定义颜色输入框失焦
   */
  const handleCustomColorBlur = () => {
    if (customColor.trim() && !ColorUtils.isValidColor(customColor)) {
      setValidationError('请输入有效的颜色格式（如：#ff0000, rgb(255,0,0)）')
    }
  }

  /**
   * 切换自定义颜色输入显示
   */
  const toggleCustomInput = () => {
    if (disabled) return
    
    const newShowCustomInput = !showCustomInput
    setShowCustomInput(newShowCustomInput)
    
    if (newShowCustomInput) {
      // 延迟聚焦，确保输入框已渲染
      setTimeout(() => {
        colorInputRef.current?.focus()
      }, 100)
    } else {
      setCustomColor('')
      setValidationError('')
    }
  }

  /**
   * 获取颜色预览样式
   */
  const getColorPreviewStyle = (color: string) => ({
    backgroundColor: color,
    border: `2px solid ${ColorUtils.getContrastColor(color) === '#000000' ? '#e5e7eb' : '#374151'}`
  })

  /**
   * 检查颜色是否被选中
   */
  const isColorSelected = (color: string) => {
    return value === color
  }

  /**
   * 生成颜色的对比文本颜色
   */
  const getTextColor = (backgroundColor: string) => {
    return ColorUtils.getContrastColor(backgroundColor)
  }

  return (
    <div className={`tag-color-picker ${className}`}>
      {/* 颜色预览区域 */}
      <div className="mb-3">
        <label className="block text-sm font-medium text-gray-700 mb-2">
          {placeholder}
        </label>
        <div className="flex items-center space-x-3">
          {/* 当前颜色预览 */}
          <div 
            className="w-8 h-8 rounded-md border-2 border-gray-300 flex items-center justify-center"
            style={value ? getColorPreviewStyle(value) : { backgroundColor: '#f3f4f6' }}
          >
            {!value && (
              <span className="text-xs text-gray-400">?</span>
            )}
          </div>
          
          {/* 颜色值显示 */}
          <div className="flex-1">
            {value ? (
              <div className="text-sm">
                <span className="font-mono text-gray-600">{value}</span>
                {value !== customColor && customColor && (
                  <span className="ml-2 text-xs text-gray-400">
                    (自定义: {customColor})
                  </span>
                )}
              </div>
            ) : (
              <span className="text-sm text-gray-400">未选择颜色</span>
            )}
          </div>
        </div>
      </div>

      {/* 预设颜色网格 */}
      <div className="mb-4">
        <h4 className="text-sm font-medium text-gray-700 mb-2">预设颜色</h4>
        <div className="grid grid-cols-10 gap-2">
          {presetColors.map((color, index) => (
            <button
              key={`preset-${index}`}
              type="button"
              disabled={disabled}
              className={`
                w-8 h-8 rounded-md border-2 transition-all duration-200 hover:scale-110 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1
                ${isColorSelected(color) 
                  ? 'ring-2 ring-blue-500 ring-offset-2 scale-110' 
                  : 'hover:ring-1 hover:ring-gray-300'
                }
                ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
              `}
              style={getColorPreviewStyle(color)}
              onClick={() => handlePresetColorSelect(color)}
              title={`选择颜色: ${color}`}
              aria-label={`选择预设颜色 ${color}`}
            >
              {isColorSelected(color) && (
                <svg 
                  className="w-4 h-4" 
                  fill="currentColor" 
                  viewBox="0 0 20 20"
                  style={{ color: getTextColor(color) }}
                >
                  <path 
                    fillRule="evenodd" 
                    d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" 
                    clipRule="evenodd" 
                  />
                </svg>
              )}
            </button>
          ))}
        </div>
      </div>

      {/* 自定义颜色区域 */}
      {allowCustom && (
        <div className="border-t pt-4">
          <div className="flex items-center justify-between mb-2">
            <h4 className="text-sm font-medium text-gray-700">自定义颜色</h4>
            <button
              type="button"
              disabled={disabled}
              onClick={toggleCustomInput}
              className={`
                text-sm px-3 py-1 rounded-md transition-colors duration-200
                ${showCustomInput 
                  ? 'bg-red-100 text-red-700 hover:bg-red-200' 
                  : 'bg-blue-100 text-blue-700 hover:bg-blue-200'
                }
                ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
              `}
            >
              {showCustomInput ? '取消' : '自定义'}
            </button>
          </div>

          {showCustomInput && (
            <div className="space-y-2">
              <div className="flex space-x-2">
                {/* HTML颜色选择器 */}
                <input
                  type="color"
                  value={customColor && ColorUtils.isValidColor(customColor) ? customColor : '#000000'}
                  onChange={(e) => handleCustomColorChange(e.target.value)}
                  disabled={disabled}
                  className={`
                    w-10 h-10 rounded-md border border-gray-300 cursor-pointer
                    ${disabled ? 'opacity-50 cursor-not-allowed' : ''}
                  `}
                  title="使用颜色选择器"
                />

                {/* 文本输入框 */}
                <input
                  ref={colorInputRef}
                  type="text"
                  value={customColor}
                  onChange={(e) => handleCustomColorChange(e.target.value)}
                  onBlur={handleCustomColorBlur}
                  disabled={disabled}
                  placeholder="输入颜色值 (如: #ff0000, rgb(255,0,0))"
                  className={`
                    flex-1 px-3 py-2 border border-gray-300 rounded-md text-sm font-mono
                    focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent
                    ${validationError ? 'border-red-300 bg-red-50' : ''}
                    ${disabled ? 'opacity-50 cursor-not-allowed bg-gray-50' : ''}
                  `}
                />
              </div>

              {/* 验证错误提示 */}
              {validationError && (
                <div className="text-sm text-red-600 bg-red-50 px-3 py-2 rounded-md">
                  {validationError}
                </div>
              )}

              {/* 颜色格式提示 */}
              <div className="text-xs text-gray-500">
                支持格式：十六进制 (#ff0000)、RGB (rgb(255,0,0))、HSL (hsl(0,100%,50%))
              </div>
            </div>
          )}
        </div>
      )}

      {/* 清除颜色按钮 */}
      {value && (
        <div className="mt-4 pt-4 border-t">
          <button
            type="button"
            disabled={disabled}
            onClick={() => {
              onChange('')
              setCustomColor('')
              setShowCustomInput(false)
              setValidationError('')
            }}
            className={`
              w-full px-3 py-2 text-sm text-gray-600 bg-gray-100 rounded-md transition-colors duration-200
              hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-1
              ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
            `}
          >
            清除颜色
          </button>
        </div>
      )}
    </div>
  )
}

export default TagColorPicker