// 默认AI模型标签页组件 - 简化版本

import React, { useState, useEffect, useCallback } from 'react'
import { 
  Zap, 
  Bot, 
  MessageSquare, 
  Languages, 
  Tag, 
  Folder, 
  Search, 
  FileText, 
  Settings, 
  CheckCircle, 
  AlertCircle,
  RotateCcw,
  Sparkles,
  Loader2
} from 'lucide-react'
import { Button } from './ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card'
import { Alert, AlertDescription } from './ui/alert'
import { Label } from './ui/label'
import { Badge } from './ui/badge'
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from './ui/select'
import { defaultAIModelService, DefaultAIModelUsage, AvailableAIModel } from '../services/defaultAIModelService'

// 图标映射
const USAGE_ICONS: Record<string, React.ComponentType<{ className?: string }>> = {
  'default-chat': MessageSquare,
  'translation': Languages,
  'tag-naming': Tag,
  'folder-naming': Folder,
  'content-analysis': Search,
  'summary-generation': FileText
}

interface DefaultAIModelsTabProps {}

const DefaultAIModelsTab: React.FC<DefaultAIModelsTabProps> = () => {
  const [usages, setUsages] = useState<DefaultAIModelUsage[]>([])
  const [availableModels, setAvailableModels] = useState<AvailableAIModel[]>([])
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // 加载数据
  const loadData = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)
      
      const [usagesData, modelsData] = await Promise.all([
        defaultAIModelService.getDefaultModelUsages(),
        defaultAIModelService.getAvailableModels()
      ])
      
      setUsages(usagesData)
      setAvailableModels(modelsData)
    } catch (err) {
      console.error('加载默认AI模型配置失败:', err)
      setError('加载配置失败，请稍后重试')
    } finally {
      setLoading(false)
    }
  }, [])

  // 初始化加载
  useEffect(() => {
    loadData()
  }, [loadData])

  // 更新单个使用场景的模型配置
  const handleUpdateUsage = useCallback(async (usageId: string, selectedModelId: string | null, fallbackModelId: string | null) => {
    try {
      setSaving(true)
      await defaultAIModelService.updateUsageModel(usageId, selectedModelId, fallbackModelId)
      
      // 更新本地状态
      setUsages(prev => prev.map(usage => 
        usage.id === usageId 
          ? { ...usage, selectedModelId, fallbackModelId }
          : usage
      ))
    } catch (err) {
      console.error('更新模型配置失败:', err)
      setError('更新配置失败，请稍后重试')
    } finally {
      setSaving(false)
    }
  }, [])

  // 一键设置推荐配置
  const handleSetRecommended = useCallback(async () => {
    try {
      setSaving(true)
      await defaultAIModelService.setRecommendedConfiguration()
      await loadData() // 重新加载数据
    } catch (err) {
      console.error('设置推荐配置失败:', err)
      setError(err instanceof Error ? err.message : '设置推荐配置失败')
    } finally {
      setSaving(false)
    }
  }, [loadData])

  // 重置为默认配置
  const handleResetToDefaults = useCallback(async () => {
    try {
      setSaving(true)
      await defaultAIModelService.resetToDefaults()
      await loadData() // 重新加载数据
    } catch (err) {
      console.error('重置配置失败:', err)
      setError('重置配置失败，请稍后重试')
    } finally {
      setSaving(false)
    }
  }, [loadData])

  // 获取模型信息
  const getModelInfo = (modelId: string | null) => {
    if (!modelId) return null
    return availableModels.find(model => model.id === modelId)
  }

  // 获取分类颜色
  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'chat': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
      case 'translation': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
      case 'analysis': return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200'
      case 'generation': return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200'
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200'
    }
  }

  // 获取分类名称
  const getCategoryName = (category: string) => {
    switch (category) {
      case 'chat': return '对话'
      case 'translation': return '翻译'
      case 'analysis': return '分析'
      case 'generation': return '生成'
      default: return '其他'
    }
  }

  // 按分类分组
  const groupedUsages = usages.reduce((groups, usage) => {
    const category = usage.category
    if (!groups[category]) {
      groups[category] = []
    }
    groups[category].push(usage)
    return groups
  }, {} as Record<string, DefaultAIModelUsage[]>)

  const configuredCount = usages.filter(u => u.selectedModelId).length
  const enabledCount = usages.filter(u => u.enabled).length

  if (loading) {
    return (
      <div className="p-6 space-y-8 bg-background text-foreground">
        <Card>
          <CardContent className="p-8 text-center">
            <Loader2 className="w-8 h-8 animate-spin mx-auto mb-4" />
            <p className="text-muted-foreground">正在加载默认AI模型配置...</p>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="p-6 space-y-8 bg-background text-foreground">
      {/* 页面标题 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Zap className="w-6 h-6 mr-3 text-primary" />
            默认AI模型
          </CardTitle>
          <CardDescription>
            为不同的使用场景配置默认的AI模型，提高工作效率。系统会根据场景自动选择最合适的模型。
          </CardDescription>
        </CardHeader>
      </Card>

      {/* 错误提示 */}
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* 统计信息 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Bot className="w-4 h-4 text-blue-500" />
              <span className="text-sm font-medium">可用模型</span>
            </div>
            <p className="text-2xl font-bold mt-2">{availableModels.length}</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Settings className="w-4 h-4 text-green-500" />
              <span className="text-sm font-medium">使用场景</span>
            </div>
            <p className="text-2xl font-bold mt-2">{usages.length}</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <CheckCircle className="w-4 h-4 text-purple-500" />
              <span className="text-sm font-medium">已配置</span>
            </div>
            <p className="text-2xl font-bold mt-2">{configuredCount}</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <AlertCircle className="w-4 h-4 text-orange-500" />
              <span className="text-sm font-medium">需要配置</span>
            </div>
            <p className="text-2xl font-bold mt-2">{usages.length - configuredCount}</p>
          </CardContent>
        </Card>
      </div>

      {/* 操作按钮 */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h2 className="text-lg font-semibold">模型配置</h2>
          <p className="text-sm text-muted-foreground">
            为每个使用场景选择合适的AI模型
          </p>
        </div>
        <div className="flex flex-col sm:flex-row gap-2">
          <Button 
            variant="default" 
            onClick={handleSetRecommended}
            disabled={saving || availableModels.length === 0}
          >
            {saving ? (
              <Loader2 className="w-4 h-4 mr-2 animate-spin" />
            ) : (
              <Sparkles className="w-4 h-4 mr-2" />
            )}
            一键设置推荐配置
          </Button>
          <Button 
            variant="outline" 
            onClick={handleResetToDefaults}
            disabled={saving}
          >
            <RotateCcw className="w-4 h-4 mr-2" />
            重置为默认
          </Button>
        </div>
      </div>

      {/* 可用模型不足警告 */}
      {availableModels.length === 0 && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            <div>
              <span className="font-medium">没有可用的AI模型</span>
              <p className="text-sm mt-1">
                请先在"AI集成"页面配置并启用至少一个聊天模型
              </p>
            </div>
          </AlertDescription>
        </Alert>
      )}

      {/* 简化的模型配置列表 */}
      <div className="space-y-6">
        {Object.entries(groupedUsages).map(([category, categoryUsages]) => (
          <div key={category}>
            <div className="flex items-center space-x-2 mb-4">
              <Badge className={getCategoryColor(category)}>
                {getCategoryName(category)}
              </Badge>
              <span className="text-sm text-muted-foreground">
                {categoryUsages.length} 个场景
              </span>
            </div>
            
            <div className="grid gap-4">
              {categoryUsages.map((usage) => {
                const selectedModel = getModelInfo(usage.selectedModelId)
                const fallbackModel = getModelInfo(usage.fallbackModelId)
                const IconComponent = USAGE_ICONS[usage.id] || Settings
                
                return (
                  <Card key={usage.id} className="hover:shadow-md transition-shadow">
                    <CardContent className="p-6">
                      <div className="flex items-start space-x-4">
                        <div className="mt-1">
                          <IconComponent className="w-5 h-5 text-primary" />
                        </div>
                        
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center space-x-3 mb-2">
                            <h3 className="font-semibold text-lg">{usage.name}</h3>
                            {!usage.enabled && (
                              <Badge variant="secondary">已禁用</Badge>
                            )}
                          </div>
                          
                          <p className="text-sm text-muted-foreground mb-4">
                            {usage.description}
                          </p>
                          
                          {/* 简化的模型选择 */}
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                              <Label className="text-sm font-medium mb-2">主要模型</Label>
                              <Select
                                value={usage.selectedModelId || 'none'}
                                onValueChange={(value) => 
                                  handleUpdateUsage(usage.id, value === 'none' ? null : value, usage.fallbackModelId)
                                }
                                disabled={saving}
                              >
                                <SelectTrigger>
                                  <SelectValue placeholder="选择主要模型" />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="none">
                                    <span className="text-muted-foreground">未选择模型</span>
                                  </SelectItem>
                                  {availableModels.map(model => (
                                    <SelectItem key={model.id} value={model.id}>
                                      <div className="flex items-center space-x-2">
                                        <div className="w-2 h-2 rounded-full bg-green-500" />
                                        <span>{model.displayName}</span>
                                        <Badge variant="secondary" className="text-xs">
                                          {model.provider}
                                        </Badge>
                                      </div>
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                              
                              {selectedModel && (
                                <div className="mt-2 p-2 bg-muted rounded text-xs">
                                  <div className="flex items-center space-x-2">
                                    <div className="w-2 h-2 rounded-full bg-green-500" />
                                    <span className="font-medium">{selectedModel.displayName}</span>
                                    <Badge variant="secondary">{selectedModel.provider}</Badge>
                                  </div>
                                </div>
                              )}
                            </div>
                            
                            <div>
                              <Label className="text-sm font-medium mb-2">备用模型（可选）</Label>
                              <Select
                                value={usage.fallbackModelId || 'none'}
                                onValueChange={(value) => 
                                  handleUpdateUsage(usage.id, usage.selectedModelId, value === 'none' ? null : value)
                                }
                                disabled={saving}
                              >
                                <SelectTrigger>
                                  <SelectValue placeholder="选择备用模型" />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="none">
                                    <span className="text-muted-foreground">不设置备用模型</span>
                                  </SelectItem>
                                  {availableModels
                                    .filter(model => model.id !== usage.selectedModelId)
                                    .map(model => (
                                      <SelectItem key={model.id} value={model.id}>
                                        <div className="flex items-center space-x-2">
                                          <div className="w-2 h-2 rounded-full bg-yellow-500" />
                                          <span>{model.displayName}</span>
                                          <Badge variant="secondary" className="text-xs">
                                            {model.provider}
                                          </Badge>
                                        </div>
                                      </SelectItem>
                                    ))}
                                </SelectContent>
                              </Select>
                              
                              {fallbackModel && (
                                <div className="mt-2 p-2 bg-muted rounded text-xs">
                                  <div className="flex items-center space-x-2">
                                    <div className="w-2 h-2 rounded-full bg-yellow-500" />
                                    <span className="font-medium">{fallbackModel.displayName}</span>
                                    <Badge variant="secondary">{fallbackModel.provider}</Badge>
                                  </div>
                                </div>
                              )}
                            </div>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                )
              })}
            </div>
          </div>
        ))}
      </div>

      {/* 简化的使用说明 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <AlertCircle className="w-4 h-4 mr-2" />
            使用说明
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-sm space-y-3">
            <div>
              <p className="font-medium mb-2">快速配置：</p>
              <ul className="list-disc list-inside ml-4 space-y-1 text-muted-foreground">
                <li>点击"一键设置推荐配置"自动为所有场景选择最佳模型</li>
                <li>系统会优先选择推荐和热门的模型</li>
                <li>备用模型是可选的，用于主要模型不可用时的自动切换</li>
              </ul>
            </div>
            
            <div>
              <p className="font-medium mb-2">使用场景：</p>
              <ul className="list-disc list-inside ml-4 space-y-1 text-muted-foreground">
                <li><strong>默认聊天：</strong>通用对话和问答</li>
                <li><strong>翻译模型：</strong>多语言文本翻译</li>
                <li><strong>标签命名：</strong>为收藏内容生成标签</li>
                <li><strong>文件夹命名：</strong>为分类生成名称</li>
                <li><strong>内容分析：</strong>分析网页内容</li>
                <li><strong>摘要生成：</strong>生成内容摘要</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

export default DefaultAIModelsTab