// 懒加载包装器组件 - 提供组件和数据的懒加载功能

import React, { useState, useEffect, useRef, useCallback } from 'react'

/**
 * 懒加载配置
 */
export interface LazyLoadConfig {
  threshold: number // 触发加载的距离阈值（像素）
  rootMargin: string // IntersectionObserver的rootMargin
  triggerOnce: boolean // 是否只触发一次
  placeholder?: React.ReactNode // 占位符组件
  errorFallback?: React.ReactNode // 错误回退组件
  loadingComponent?: React.ReactNode // 加载中组件
  delay: number // 延迟加载时间（毫秒）
}

/**
 * 懒加载包装器属性
 */
export interface LazyLoadWrapperProps {
  children: React.ReactNode | (() => Promise<React.ReactNode>)
  config?: Partial<LazyLoadConfig>
  className?: string
  style?: React.CSSProperties
  onLoad?: () => void
  onError?: (error: Error) => void
  onVisible?: () => void
}

/**
 * 懒加载包装器组件
 * 支持组件懒加载和数据懒加载
 */
export const LazyLoadWrapper: React.FC<LazyLoadWrapperProps> = ({
  children,
  config = {},
  className = '',
  style = {},
  onLoad,
  onError,
  onVisible
}) => {
  const finalConfig: LazyLoadConfig = {
    threshold: 100,
    rootMargin: '50px',
    triggerOnce: true,
    delay: 0,
    ...config
  }

  const [isVisible, setIsVisible] = useState(false)
  const [isLoaded, setIsLoaded] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<Error | null>(null)
  const [content, setContent] = useState<React.ReactNode>(null)
  
  const elementRef = useRef<HTMLDivElement>(null)
  const hasTriggered = useRef(false)

  // 处理可见性变化
  const handleIntersection = useCallback((entries: IntersectionObserverEntry[]) => {
    const entry = entries[0]
    
    if (entry.isIntersecting && (!hasTriggered.current || !finalConfig.triggerOnce)) {
      setIsVisible(true)
      hasTriggered.current = true
      onVisible?.()
    }
  }, [finalConfig.triggerOnce, onVisible])

  // 设置IntersectionObserver
  useEffect(() => {
    const element = elementRef.current
    if (!element) return

    const observer = new IntersectionObserver(handleIntersection, {
      rootMargin: finalConfig.rootMargin,
      threshold: 0.1
    })

    observer.observe(element)

    return () => {
      observer.disconnect()
    }
  }, [handleIntersection, finalConfig.rootMargin])

  // 加载内容
  useEffect(() => {
    if (!isVisible || isLoaded || isLoading) return

    const loadContent = async () => {
      setIsLoading(true)
      setError(null)

      try {
        // 延迟加载
        if (finalConfig.delay > 0) {
          await new Promise(resolve => setTimeout(resolve, finalConfig.delay))
        }

        if (typeof children === 'function') {
          // 异步加载组件
          const loadedContent = await children()
          setContent(loadedContent)
        } else {
          // 直接使用子组件
          setContent(children)
        }

        setIsLoaded(true)
        onLoad?.()
      } catch (err) {
        const error = err instanceof Error ? err : new Error('加载失败')
        setError(error)
        onError?.(error)
      } finally {
        setIsLoading(false)
      }
    }

    loadContent()
  }, [isVisible, isLoaded, isLoading, children, finalConfig.delay, onLoad, onError])

  // 渲染内容
  const renderContent = () => {
    if (error) {
      return finalConfig.errorFallback || (
        <div className="lazy-load-error p-4 text-center text-red-600">
          <div className="text-sm">加载失败</div>
          <div className="text-xs text-gray-500 mt-1">{error.message}</div>
        </div>
      )
    }

    if (isLoading) {
      return finalConfig.loadingComponent || (
        <div className="lazy-load-loading p-4 text-center">
          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary-600 mx-auto"></div>
          <div className="text-sm text-gray-600 mt-2">加载中...</div>
        </div>
      )
    }

    if (isLoaded && content) {
      return content
    }

    return finalConfig.placeholder || (
      <div className="lazy-load-placeholder p-4 text-center text-gray-400">
        <div className="text-sm">等待加载...</div>
      </div>
    )
  }

  return (
    <div
      ref={elementRef}
      className={`lazy-load-wrapper ${className}`}
      style={style}
    >
      {renderContent()}
    </div>
  )
}

/**
 * 懒加载数据Hook
 * 用于数据的懒加载
 */
export const useLazyData = <T,>(
  loader: () => Promise<T>,
  dependencies: React.DependencyList = [],
  config: Partial<LazyLoadConfig> = {}
) => {
  const [data, setData] = useState<T | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<Error | null>(null)
  const [hasLoaded, setHasLoaded] = useState(false)

  const finalConfig = {
    delay: 0,
    ...config
  }

  const loadData = useCallback(async () => {
    if (loading || hasLoaded) return

    setLoading(true)
    setError(null)

    try {
      if (finalConfig.delay > 0) {
        await new Promise(resolve => setTimeout(resolve, finalConfig.delay))
      }

      const result = await loader()
      setData(result)
      setHasLoaded(true)
    } catch (err) {
      const error = err instanceof Error ? err : new Error('数据加载失败')
      setError(error)
    } finally {
      setLoading(false)
    }
  }, [loader, loading, hasLoaded, finalConfig.delay])

  const reload = useCallback(() => {
    setHasLoaded(false)
    setData(null)
    setError(null)
    loadData()
  }, [loadData])

  useEffect(() => {
    loadData()
  }, dependencies)

  return {
    data,
    loading,
    error,
    hasLoaded,
    reload
  }
}

/**
 * 懒加载图片组件
 */
export interface LazyImageProps {
  src: string
  alt: string
  placeholder?: string
  className?: string
  style?: React.CSSProperties
  onLoad?: () => void
  onError?: () => void
}

export const LazyImage: React.FC<LazyImageProps> = ({
  src,
  alt,
  placeholder,
  className = '',
  style = {},
  onLoad,
  onError
}) => {
  const [isLoaded, setIsLoaded] = useState(false)
  const [hasError, setHasError] = useState(false)
  const [isVisible, setIsVisible] = useState(false)
  const imgRef = useRef<HTMLImageElement>(null)

  useEffect(() => {
    const img = imgRef.current
    if (!img) return

    const observer = new IntersectionObserver(
      (entries) => {
        const entry = entries[0]
        if (entry.isIntersecting) {
          setIsVisible(true)
          observer.disconnect()
        }
      },
      { rootMargin: '50px' }
    )

    observer.observe(img)

    return () => observer.disconnect()
  }, [])

  const handleLoad = () => {
    setIsLoaded(true)
    onLoad?.()
  }

  const handleError = () => {
    setHasError(true)
    onError?.()
  }

  return (
    <div className={`lazy-image-wrapper ${className}`} style={style}>
      {isVisible && (
        <img
          ref={imgRef}
          src={hasError ? placeholder : src}
          alt={alt}
          onLoad={handleLoad}
          onError={handleError}
          className={`lazy-image ${isLoaded ? 'loaded' : 'loading'}`}
          style={{
            opacity: isLoaded ? 1 : 0,
            transition: 'opacity 0.3s ease-in-out'
          }}
        />
      )}
      
      {!isLoaded && !hasError && (
        <div className="lazy-image-placeholder bg-gray-200 animate-pulse">
          <div className="flex items-center justify-center h-full text-gray-400">
            <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clipRule="evenodd" />
            </svg>
          </div>
        </div>
      )}
    </div>
  )
}

/**
 * 分页懒加载Hook
 * 用于实现无限滚动
 */
export const useInfiniteScroll = <T,>(
  loader: (page: number, pageSize: number) => Promise<{ data: T[], hasMore: boolean }>,
  pageSize: number = 20,
  config: { threshold?: number } = {}
) => {
  const [data, setData] = useState<T[]>([])
  const [loading, setLoading] = useState(false)
  const [hasMore, setHasMore] = useState(true)
  const [error, setError] = useState<Error | null>(null)
  const [page, setPage] = useState(1)

  const { threshold = 100 } = config

  const loadMore = useCallback(async () => {
    if (loading || !hasMore) return

    setLoading(true)
    setError(null)

    try {
      const result = await loader(page, pageSize)
      setData(prev => [...prev, ...result.data])
      setHasMore(result.hasMore)
      setPage(prev => prev + 1)
    } catch (err) {
      const error = err instanceof Error ? err : new Error('加载更多数据失败')
      setError(error)
    } finally {
      setLoading(false)
    }
  }, [loader, page, pageSize, loading, hasMore])

  const reset = useCallback(() => {
    setData([])
    setPage(1)
    setHasMore(true)
    setError(null)
  }, [])

  // 滚动监听
  const handleScroll = useCallback((event: React.UIEvent<HTMLElement>) => {
    const { scrollTop, scrollHeight, clientHeight } = event.currentTarget
    
    if (scrollHeight - scrollTop - clientHeight < threshold) {
      loadMore()
    }
  }, [loadMore, threshold])

  // 初始加载
  useEffect(() => {
    if (data.length === 0 && hasMore) {
      loadMore()
    }
  }, [])

  return {
    data,
    loading,
    hasMore,
    error,
    loadMore,
    reset,
    handleScroll
  }
}

export default LazyLoadWrapper