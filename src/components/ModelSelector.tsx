// 模型选择器组件 - 提供紧凑的模型选择界面

import React, { useState, useCallback } from 'react'
import { 
  ChevronDown, 
  ChevronUp, 
  CheckCircle, 
  Circle, 
  Loader2,
  RefreshCw,
  Search,
  X
} from 'lucide-react'
import { <PERSON><PERSON> } from './ui/button'
import { Input } from './ui/input'
import { Badge } from './ui/badge'
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogHeader, 
  DialogTitle 
} from './ui/dialog'
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from './ui/select'
import { AIModel } from '../types/ai'

interface ModelSelectorProps {
  providerId: string
  providerName: string
  models: AIModel[]
  selectedModelId?: string
  isLoading?: boolean
  onModelSelect: (modelId: string) => void
  onRefreshModels: () => void
}

const ModelSelector: React.FC<ModelSelectorProps> = ({
  providerId,
  providerName,
  models,
  selectedModelId,
  isLoading = false,
  onModelSelect,
  onRefreshModels
}) => {
  const [showDialog, setShowDialog] = useState(false)
  const [searchQuery, setSearchQuery] = useState('')
  const [showDropdown, setShowDropdown] = useState(false)

  // 过滤模型
  const filteredModels = models.filter(model => 
    model.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    model.displayName.toLowerCase().includes(searchQuery.toLowerCase()) ||
    model.description?.toLowerCase().includes(searchQuery.toLowerCase())
  )

  // 获取选中的模型
  const selectedModel = models.find(m => m.id === selectedModelId)

  // 处理模型选择
  const handleModelSelect = useCallback((modelId: string) => {
    onModelSelect(modelId)
    setShowDialog(false)
    setShowDropdown(false)
    setSearchQuery('')
  }, [onModelSelect])

  // 如果没有模型，显示加载或空状态
  if (models.length === 0) {
    return (
      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <h4 className="font-medium text-sm">选择模型</h4>
          <Button
            variant="outline"
            size="sm"
            onClick={onRefreshModels}
            disabled={isLoading}
            aria-label="刷新模型列表"
          >
            {isLoading ? (
              <Loader2 className="w-3 h-3 animate-spin" />
            ) : (
              <RefreshCw className="w-3 h-3" />
            )}
          </Button>
        </div>
        <div className="text-center py-4 text-sm text-muted-foreground">
          {isLoading ? '正在加载模型列表...' : '暂无可用模型'}
        </div>
      </div>
    )
  }

  // 紧凑模式：使用下拉选择器
  if (models.length <= 10) {
    return (
      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <h4 className="font-medium text-sm">选择模型</h4>
          <Button
            variant="outline"
            size="sm"
            onClick={onRefreshModels}
            disabled={isLoading}
            aria-label="刷新模型列表"
          >
            {isLoading ? (
              <Loader2 className="w-3 h-3 animate-spin" />
            ) : (
              <RefreshCw className="w-3 h-3" />
            )}
          </Button>
        </div>
        
        <Select value={selectedModelId || ''} onValueChange={handleModelSelect}>
          <SelectTrigger className="w-full">
            <SelectValue placeholder="请选择模型">
              {selectedModel && (
                <div className="flex items-center space-x-2">
                  <span className="font-mono text-sm">{selectedModel.name}</span>
                  {selectedModel.isRecommended && (
                    <Badge variant="secondary" className="text-xs">推荐</Badge>
                  )}
                </div>
              )}
            </SelectValue>
          </SelectTrigger>
          <SelectContent>
            {models.map((model) => (
              <SelectItem key={model.id} value={model.id}>
                <div className="flex items-center justify-between w-full">
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-2">
                      <span className="font-mono text-sm truncate">{model.name}</span>
                      {model.isRecommended && (
                        <Badge variant="secondary" className="text-xs">推荐</Badge>
                      )}
                      {model.isPopular && (
                        <Badge variant="outline" className="text-xs">热门</Badge>
                      )}
                    </div>
                    {model.description && (
                      <p className="text-xs text-muted-foreground truncate mt-1">
                        {model.description}
                      </p>
                    )}
                  </div>
                </div>
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        {selectedModel && (
          <div className="p-2 bg-muted/30 rounded text-xs">
            <div className="flex items-center justify-between">
              <span className="font-medium">已选择：{selectedModel.displayName}</span>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => handleModelSelect('')}
                aria-label="清除选择"
              >
                <X className="w-3 h-3" />
              </Button>
            </div>
            {selectedModel.description && (
              <p className="text-muted-foreground mt-1">{selectedModel.description}</p>
            )}
          </div>
        )}
      </div>
    )
  }

  // 大量模型：使用弹窗模式
  return (
    <div className="space-y-2">
      <div className="flex items-center justify-between">
        <h4 className="font-medium text-sm">选择模型</h4>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={onRefreshModels}
            disabled={isLoading}
            aria-label="刷新模型列表"
          >
            {isLoading ? (
              <Loader2 className="w-3 h-3 animate-spin" />
            ) : (
              <RefreshCw className="w-3 h-3" />
            )}
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowDialog(true)}
          >
            选择模型 ({models.length})
          </Button>
        </div>
      </div>

      {selectedModel ? (
        <div className="p-3 bg-muted/30 rounded">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <CheckCircle className="w-4 h-4 text-green-500" />
              <span className="font-mono text-sm">{selectedModel.name}</span>
              {selectedModel.isRecommended && (
                <Badge variant="secondary" className="text-xs">推荐</Badge>
              )}
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleModelSelect('')}
              aria-label="清除选择"
            >
              <X className="w-3 h-3" />
            </Button>
          </div>
          {selectedModel.description && (
            <p className="text-xs text-muted-foreground mt-1">
              {selectedModel.description}
            </p>
          )}
        </div>
      ) : (
        <div className="p-3 border-2 border-dashed border-muted rounded text-center">
          <p className="text-sm text-muted-foreground">未选择模型</p>
          <Button
            variant="ghost"
            size="sm"
            className="mt-1"
            onClick={() => setShowDialog(true)}
          >
            点击选择
          </Button>
        </div>
      )}

      {/* 模型选择弹窗 */}
      <Dialog open={showDialog} onOpenChange={setShowDialog}>
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-hidden flex flex-col">
          <DialogHeader>
            <DialogTitle>选择模型 - {providerName}</DialogTitle>
            <DialogDescription>
              从 {models.length} 个可用模型中选择一个
            </DialogDescription>
          </DialogHeader>

          {/* 搜索框 */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
            <Input
              placeholder="搜索模型名称或描述..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>

          {/* 模型列表 */}
          <div className="flex-1 overflow-y-auto space-y-2">
            {filteredModels.length === 0 ? (
              <div className="text-center py-8">
                <p className="text-muted-foreground">没有找到匹配的模型</p>
              </div>
            ) : (
              filteredModels.map((model) => {
                const isSelected = selectedModelId === model.id
                return (
                  <div
                    key={model.id}
                    className={`flex items-center justify-between p-3 rounded-lg border cursor-pointer transition-all hover:bg-muted/50 ${
                      isSelected ? 'border-primary bg-primary/5' : 'border-border'
                    }`}
                    onClick={() => handleModelSelect(model.id)}
                  >
                    <div className="flex items-start space-x-3 flex-1 min-w-0">
                      <div className={`w-4 h-4 rounded-full border-2 mt-0.5 flex-shrink-0 ${
                        isSelected ? 'bg-primary border-primary' : 'border-muted-foreground'
                      }`}>
                        {isSelected && <CheckCircle className="w-4 h-4 text-white" />}
                      </div>
                      
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center space-x-2 mb-1">
                          <span className="font-mono text-sm font-medium truncate">
                            {model.name}
                          </span>
                          {model.isRecommended && (
                            <Badge variant="secondary" className="text-xs">推荐</Badge>
                          )}
                          {model.isPopular && (
                            <Badge variant="outline" className="text-xs">热门</Badge>
                          )}
                        </div>
                        
                        {model.displayName && model.displayName !== model.name && (
                          <p className="text-xs text-muted-foreground truncate">
                            {model.displayName}
                          </p>
                        )}
                        
                        {model.description && (
                          <p className="text-xs text-muted-foreground mt-1 line-clamp-2">
                            {model.description}
                          </p>
                        )}
                        
                        {(model.parameters || model.size) && (
                          <div className="flex items-center space-x-2 mt-1">
                            {model.parameters && (
                              <span className="text-xs text-muted-foreground">
                                {model.parameters}
                              </span>
                            )}
                            {model.size && (
                              <span className="text-xs text-muted-foreground">
                                {model.size}
                              </span>
                            )}
                          </div>
                        )}
                      </div>
                    </div>
                    
                    {model.capabilities && model.capabilities.length > 0 && (
                      <div className="flex flex-wrap gap-1 ml-2">
                        {model.capabilities.slice(0, 3).map((cap) => (
                          <Badge key={cap} variant="outline" className="text-xs">
                            {cap}
                          </Badge>
                        ))}
                        {model.capabilities.length > 3 && (
                          <Badge variant="outline" className="text-xs">
                            +{model.capabilities.length - 3}
                          </Badge>
                        )}
                      </div>
                    )}
                  </div>
                )
              })
            )}
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
}

export default ModelSelector