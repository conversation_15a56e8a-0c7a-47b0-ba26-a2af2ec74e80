// 标题截断组件 - 处理超长标题的显示和截断

import React, { useState, useRef, useEffect, useCallback } from 'react'
import { TextUtils } from '../utils/textUtils'
import { LayoutUtils } from '../utils/layoutUtils'

interface TruncatedTitleProps {
  /** 要显示的标题文本 */
  title: string
  /** 最大显示长度（字符数），默认为50 */
  maxLength?: number
  /** 自定义CSS类名 */
  className?: string
  /** 是否启用悬停提示，默认为true */
  showTooltip?: boolean
  /** 截断位置，默认为'end' */
  truncateAt?: 'start' | 'middle' | 'end'
  /** 自定义省略号文本，默认为'...' */
  ellipsis?: string
  /** 是否基于容器宽度进行截断，默认为true */
  useContainerWidth?: boolean
  /** 最大行数限制，默认为1 */
  maxLines?: number
  /** 字体大小（像素），用于宽度计算，默认为14 */
  fontSize?: number
  /** 字体族，用于宽度计算 */
  fontFamily?: string
}

/**
 * 标题截断组件
 * 自动处理超长标题的截断显示，支持悬停提示和基于容器宽度的动态截断
 */
const TruncatedTitle: React.FC<TruncatedTitleProps> = React.memo(({
  title,
  maxLength = 50,
  className = '',
  showTooltip = true,
  truncateAt = 'end',
  ellipsis = '...',
  useContainerWidth = true,
  maxLines = 1,
  fontSize = 14,
  fontFamily = 'system-ui, -apple-system, sans-serif'
}) => {
  const [displayText, setDisplayText] = useState(title)
  const [isOverflowing, setIsOverflowing] = useState(false)
  const [showFullTitle, setShowFullTitle] = useState(false)
  const [containerWidth, setContainerWidth] = useState(0)
  const titleRef = useRef<HTMLSpanElement>(null)
  const containerRef = useRef<HTMLDivElement>(null)

  // 计算截断后的文本
  const calculateTruncatedText = useCallback(() => {
    if (!title) return ''

    let result = title

    // 优先使用字符长度截断（如果设置了有效的maxLength）
    if (maxLength && maxLength > 0 && title.length > maxLength) {
      const truncateResult = TextUtils.smartTruncate(title, maxLength, {
        position: truncateAt,
        ellipsis,
        wordBoundary: true
      })
      result = truncateResult.text
    } else if (useContainerWidth && containerWidth > 0 && (!maxLength || maxLength <= 0)) {
      // 如果没有设置maxLength且启用了基于容器宽度的截断
      result = TextUtils.truncateByWidth(title, containerWidth, fontSize, fontFamily)
    }

    // 如果有多行限制，应用行数限制
    if (maxLines > 1) {
      result = TextUtils.clampLines(result, {
        maxLines,
        lineHeight: fontSize * 1.2
      })
    }

    return result
  }, [title, maxLength, truncateAt, ellipsis, useContainerWidth, containerWidth, maxLines, fontSize, fontFamily])

  // 检测文本是否溢出和容器尺寸变化
  useEffect(() => {
    if (!containerRef.current) return

    const updateLayout = () => {
      const container = containerRef.current
      if (!container) return

      // 获取容器的实际可用宽度
      const containerSize = LayoutUtils.getElementSize(container)
      const availableWidth = containerSize.contentWidth

      setContainerWidth(availableWidth)

      // 计算新的显示文本
      const newDisplayText = calculateTruncatedText()
      setDisplayText(newDisplayText)

      // 检查是否发生了截断
      const isTruncated = newDisplayText !== title || 
        (titleRef.current && titleRef.current.scrollWidth > titleRef.current.clientWidth)
      setIsOverflowing(isTruncated)
    }

    // 立即更新一次
    updateLayout()

    // 监听容器尺寸变化
    const cleanup = LayoutUtils.observeContainerResize(
      containerRef.current,
      () => updateLayout(),
      {
        debounce: true,
        debounceDelay: 100,
        immediate: false
      }
    )

    return cleanup
  }, [title, calculateTruncatedText])

  // 当依赖项变化时重新计算
  useEffect(() => {
    const newDisplayText = calculateTruncatedText()
    setDisplayText(newDisplayText)
    setIsOverflowing(newDisplayText !== title)
  }, [calculateTruncatedText])



  // 处理鼠标悬停
  const handleMouseEnter = () => {
    if (isOverflowing && showTooltip) {
      setShowFullTitle(true)
    }
  }

  const handleMouseLeave = () => {
    setShowFullTitle(false)
  }

  return (
    <div 
      ref={containerRef}
      className="relative w-full"
      style={{ minWidth: 0 }} // 确保容器可以收缩
    >
      <span
        ref={titleRef}
        className={`
          block cursor-default
          ${maxLines === 1 ? 'truncate' : ''}
          ${maxLines > 1 ? 'break-words' : ''}
          ${isOverflowing && showTooltip ? 'cursor-help' : ''}
          ${className}
        `}
        style={{
          fontSize: `${fontSize}px`,
          fontFamily,
          lineHeight: maxLines > 1 ? `${fontSize * 1.2}px` : 'normal',
          display: maxLines > 1 ? '-webkit-box' : 'block',
          WebkitLineClamp: maxLines > 1 ? maxLines : undefined,
          WebkitBoxOrient: maxLines > 1 ? 'vertical' : undefined,
          overflow: maxLines > 1 ? 'hidden' : 'hidden',
          textOverflow: 'ellipsis',
          wordBreak: 'break-word',
          overflowWrap: 'break-word'
        }}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        title={showTooltip && isOverflowing ? title : undefined}
      >
        {displayText}
      </span>
      
      {/* 自定义悬停提示 */}
      {showFullTitle && isOverflowing && (
        <div className="absolute z-50 bottom-full left-0 mb-2 p-3 bg-popover text-popover-foreground text-sm rounded-lg shadow-lg border max-w-sm break-words">
          <div className="whitespace-pre-wrap leading-relaxed">{title}</div>
          {/* 箭头 */}
          <div className="absolute top-full left-4 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-900"></div>
        </div>
      )}
    </div>
  )
})

// 设置显示名称便于调试
TruncatedTitle.displayName = 'TruncatedTitle'

export default TruncatedTitle

// 导出类型定义
export type { TruncatedTitleProps }