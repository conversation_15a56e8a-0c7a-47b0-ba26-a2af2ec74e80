// 选项页面错误边界组件 - 捕获和处理初始化错误

import React, { Component, ErrorInfo, ReactNode } from 'react'
import { AlertTriangle, RefreshCw, Home } from 'lucide-react'

interface Props {
  children: ReactNode
}

interface State {
  hasError: boolean
  error: Error | null
  errorInfo: ErrorInfo | null
  retryCount: number
}

/**
 * 选项页面错误边界组件
 * 捕获JavaScript初始化错误和React渲染错误，提供用户友好的错误界面
 */
class OptionsPageErrorBoundary extends Component<Props, State> {
  private maxRetries = 3

  constructor(props: Props) {
    super(props)
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      retryCount: 0
    }
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    // 更新state以显示错误UI
    return {
      hasError: true,
      error
    }
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    // 检查是否是 Chrome 对象删除错误
    if (error.message.includes("Cannot delete property 'chrome'")) {
      console.warn('选项页面错误边界捕获到 Chrome 对象删除错误，这通常是正常的:', error.message)
      // 尝试恢复，不显示错误界面
      this.setState({
        hasError: false,
        error: null,
        errorInfo: null
      })
      return
    }
    
    console.error('选项页面错误边界捕获到错误:', error, errorInfo)
    
    this.setState({
      error,
      errorInfo
    })

    // 发送错误报告
    this.reportError(error, errorInfo)
  }

  /**
   * 发送错误报告
   */
  private reportError(error: Error, errorInfo: ErrorInfo) {
    try {
      // 构建错误报告
      const errorReport = {
        message: error.message,
        stack: error.stack,
        componentStack: errorInfo.componentStack,
        timestamp: new Date().toISOString(),
        userAgent: navigator.userAgent,
        url: window.location.href,
        type: 'options-page-error'
      }

      // 这里可以发送到错误监控服务
      console.error('错误报告:', errorReport)
      
      // 存储到本地存储以便调试
      try {
        const existingErrors = JSON.parse(localStorage.getItem('universe-bag-errors') || '[]')
        existingErrors.push(errorReport)
        // 只保留最近的10个错误
        if (existingErrors.length > 10) {
          existingErrors.splice(0, existingErrors.length - 10)
        }
        localStorage.setItem('universe-bag-errors', JSON.stringify(existingErrors))
      } catch (storageError) {
        console.error('无法存储错误报告:', storageError)
      }
    } catch (reportError) {
      console.error('发送错误报告失败:', reportError)
    }
  }

  /**
   * 重试加载
   */
  private handleRetry = () => {
    if (this.state.retryCount < this.maxRetries) {
      this.setState(prevState => ({
        hasError: false,
        error: null,
        errorInfo: null,
        retryCount: prevState.retryCount + 1
      }))
    } else {
      // 超过最大重试次数，刷新页面
      window.location.reload()
    }
  }

  /**
   * 返回主页
   */
  private handleGoHome = () => {
    try {
      // 尝试打开弹窗页面
      chrome.action.openPopup()
    } catch (error) {
      // 如果无法打开弹窗，尝试打开新标签页到扩展页面
      window.open(chrome.runtime.getURL('src/popup/index.html'), '_blank')
    }
  }

  /**
   * 清除错误日志
   */
  private handleClearLogs = () => {
    try {
      localStorage.removeItem('universe-bag-errors')
      alert('错误日志已清除')
    } catch (error) {
      console.error('清除错误日志失败:', error)
    }
  }

  /**
   * 获取错误类型描述
   */
  private getErrorTypeDescription(error: Error): string {
    const message = error.message.toLowerCase()
    
    if (message.includes('cannot access') && message.includes('before initialization')) {
      return '模块初始化顺序错误'
    }
    
    if (message.includes('circular dependency')) {
      return '循环依赖错误'
    }
    
    if (message.includes('import') || message.includes('module')) {
      return '模块加载错误'
    }
    
    if (message.includes('react') || message.includes('render')) {
      return 'React渲染错误'
    }
    
    return '未知错误'
  }

  /**
   * 获取用户友好的错误建议
   */
  private getErrorSuggestions(error: Error): string[] {
    const message = error.message.toLowerCase()
    const suggestions: string[] = []
    
    if (message.includes('cannot access') && message.includes('before initialization')) {
      suggestions.push('这通常是由于模块导入顺序问题导致的')
      suggestions.push('请尝试刷新页面或重新加载扩展')
      suggestions.push('如果问题持续存在，请联系开发者')
    } else if (message.includes('network') || message.includes('fetch')) {
      suggestions.push('请检查网络连接')
      suggestions.push('尝试刷新页面')
    } else {
      suggestions.push('请尝试刷新页面')
      suggestions.push('如果问题持续存在，请重新加载扩展')
      suggestions.push('您可以尝试清除浏览器缓存')
    }
    
    return suggestions
  }

  render() {
    if (this.state.hasError) {
      const { error } = this.state
      const errorType = error ? this.getErrorTypeDescription(error) : '未知错误'
      const suggestions = error ? this.getErrorSuggestions(error) : []
      const canRetry = this.state.retryCount < this.maxRetries

      return (
        <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
          <div className="max-w-2xl w-full bg-white rounded-lg shadow-lg border border-gray-200">
            {/* 错误头部 */}
            <div className="flex items-center space-x-4 p-6 border-b border-gray-200 bg-red-50">
              <div className="flex-shrink-0">
                <div className="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center">
                  <AlertTriangle className="w-6 h-6 text-red-600" />
                </div>
              </div>
              <div className="flex-1">
                <h1 className="text-xl font-semibold text-gray-900">页面加载失败</h1>
                <p className="text-sm text-gray-600 mt-1">
                  收藏管理页面遇到了问题，无法正常显示
                </p>
              </div>
            </div>

            {/* 错误详情 */}
            <div className="p-6 space-y-6">
              {/* 错误类型 */}
              <div>
                <h2 className="text-lg font-medium text-gray-900 mb-2">错误类型</h2>
                <div className="bg-gray-50 rounded-lg p-4 border border-gray-200">
                  <p className="text-gray-800 font-medium">{errorType}</p>
                  {error && (
                    <p className="text-sm text-gray-600 mt-1 font-mono">
                      {error.message}
                    </p>
                  )}
                </div>
              </div>

              {/* 解决建议 */}
              {suggestions.length > 0 && (
                <div>
                  <h2 className="text-lg font-medium text-gray-900 mb-2">解决建议</h2>
                  <ul className="space-y-2">
                    {suggestions.map((suggestion, index) => (
                      <li key={index} className="flex items-start space-x-2">
                        <span className="flex-shrink-0 w-1.5 h-1.5 bg-blue-500 rounded-full mt-2"></span>
                        <span className="text-gray-700">{suggestion}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              )}

              {/* 操作按钮 */}
              <div className="flex flex-wrap gap-3 pt-4 border-t border-gray-200">
                {canRetry && (
                  <button
                    onClick={this.handleRetry}
                    className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                  >
                    <RefreshCw className="w-4 h-4" />
                    <span>重试 ({this.maxRetries - this.state.retryCount} 次剩余)</span>
                  </button>
                )}
                
                <button
                  onClick={() => window.location.reload()}
                  className="flex items-center space-x-2 px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
                >
                  <RefreshCw className="w-4 h-4" />
                  <span>刷新页面</span>
                </button>
                
                <button
                  onClick={this.handleGoHome}
                  className="flex items-center space-x-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                >
                  <Home className="w-4 h-4" />
                  <span>返回主页</span>
                </button>
                
                <button
                  onClick={this.handleClearLogs}
                  className="flex items-center space-x-2 px-4 py-2 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700 transition-colors"
                >
                  <span>清除错误日志</span>
                </button>
              </div>

              {/* 调试信息 */}
              {process.env.NODE_ENV === 'development' && error && (
                <details className="mt-6">
                  <summary className="cursor-pointer text-sm font-medium text-gray-700 hover:text-gray-900">
                    显示详细错误信息（开发模式）
                  </summary>
                  <div className="mt-2 p-4 bg-gray-100 rounded-lg border border-gray-200">
                    <pre className="text-xs text-gray-800 whitespace-pre-wrap overflow-auto max-h-40">
                      {error.stack}
                    </pre>
                    {this.state.errorInfo && (
                      <div className="mt-4 pt-4 border-t border-gray-300">
                        <p className="text-xs font-medium text-gray-700 mb-2">组件堆栈:</p>
                        <pre className="text-xs text-gray-600 whitespace-pre-wrap overflow-auto max-h-32">
                          {this.state.errorInfo.componentStack}
                        </pre>
                      </div>
                    )}
                  </div>
                </details>
              )}
            </div>
          </div>
        </div>
      )
    }

    return this.props.children
  }
}

export default OptionsPageErrorBoundary