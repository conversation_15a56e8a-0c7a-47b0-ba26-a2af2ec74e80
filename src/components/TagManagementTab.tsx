// 标签管理主页面组件 - 标签管理的主容器

import React, { useState, useEffect, useCallback } from 'react'
import { Plus, RefreshCw, AlertCircle, Tags } from 'lucide-react'
import TagList from './TagList'
import TagModal from './TagModal'
import ToastContainer from './ToastContainer'
import LoadingIndicator from './LoadingIndicator'
import { tagService } from '../services/tagService'
import { useToast } from '../hooks/useToast'
import { useLoadingState } from '../hooks/useLoadingState'
import { Button } from './ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card'
import type { Tag, TagInput, TagUpdate } from '../types'
import type { TagWithStats, TagSortOption } from './TagList'

// 常量定义
const MODAL_TYPES = {
  CREATE: 'create' as const,
  EDIT: 'edit' as const,
  DELETE: 'delete' as const
}

const DEFAULT_SORT_BY: TagSortOption = 'name-asc'

interface TagManagementTabProps {
  /** 自定义CSS类名 */
  className?: string
}

// 拆分状态接口以提高可维护性
interface TagDataState {
  tags: TagWithStats[]
  loading: boolean
  error: string | null
}

interface ModalState {
  showModal: boolean
  modalType: 'create' | 'edit' | 'delete'
  editingTag: Tag | null
  operationLoading: boolean
}

interface FilterState {
  searchQuery: string
  sortBy: TagSortOption
}

interface SyncState {
  syncing: boolean
}

interface TagManagementState extends TagDataState, ModalState, FilterState, SyncState {}

/**
 * 标签管理主页面组件
 * 作为标签管理的主容器，协调各个子组件的交互
 */
const TagManagementTab: React.FC<TagManagementTabProps> = React.memo(({
  className = ''
}) => {
  const [state, setState] = useState<TagManagementState>({
    tags: [],
    loading: true,
    error: null,
    showModal: false,
    modalType: MODAL_TYPES.CREATE,
    editingTag: null,
    operationLoading: false,
    searchQuery: '',
    sortBy: DEFAULT_SORT_BY,
    syncing: false
  })

  // 使用通知和加载状态管理
  const { toasts, showSuccess, showError, showWarning, closeToast } = useToast()
  const { loadingState, withLoading } = useLoadingState()

  // 统一错误处理函数
  const handleError = useCallback((error: unknown, operation: string) => {
    console.error(`${operation}失败:`, error)
    const errorMessage = error instanceof Error ? error.message : '未知错误'
    showError(`${operation}失败`, errorMessage)
    return errorMessage
  }, [showError])

  // 加载标签数据
  const loadTags = useCallback(async () => {
    try {
      await withLoading(async () => {
        setState(prev => ({ ...prev, loading: true, error: null }))
        
        // 首先同步书签中的标签
        await tagService.syncTagsFromBookmarks()
        
        // 获取所有标签及其统计信息
        const tagsWithStats = await tagService.getAllTagsWithStats()
        
        setState(prev => ({
          ...prev,
          tags: tagsWithStats,
          loading: false
        }))
      }, '正在加载标签数据...')
    } catch (error) {
      const errorMessage = handleError(error, '加载标签数据')
      setState(prev => ({
        ...prev,
        loading: false,
        error: errorMessage
      }))
    }
  }, [withLoading, showError])

  // 组件挂载时加载数据
  useEffect(() => {
    loadTags()
  }, [loadTags])

  // 处理创建标签
  const handleCreateTag = useCallback(() => {
    setState(prev => ({
      ...prev,
      showModal: true,
      modalType: 'create',
      editingTag: null
    }))
  }, [])

  // 处理编辑标签
  const handleEditTag = useCallback((tag: Tag) => {
    setState(prev => ({
      ...prev,
      showModal: true,
      modalType: 'edit',
      editingTag: tag
    }))
  }, [])

  // 处理删除标签
  const handleDeleteTag = useCallback((tag: Tag) => {
    setState(prev => ({
      ...prev,
      showModal: true,
      modalType: 'delete',
      editingTag: tag
    }))
  }, [])

  // 处理模态窗口关闭
  const handleModalClose = useCallback(() => {
    if (state.operationLoading) {
      return // 操作进行中时不允许关闭
    }
    
    setState(prev => ({
      ...prev,
      showModal: false,
      modalType: 'create',
      editingTag: null
    }))
  }, [state.operationLoading])

  // 处理标签保存（创建或编辑）
  const handleTagSave = useCallback(async (data: TagInput | TagUpdate) => {
    try {
      setState(prev => ({ ...prev, operationLoading: true }))

      let result: Tag
      const isCreate = state.modalType === 'create'
      
      if (isCreate) {
        result = await tagService.createTag(data as TagInput)
        showSuccess('创建成功', `标签 "${result.name}" 已创建`)
      } else if (state.modalType === 'edit' && state.editingTag) {
        result = await tagService.updateTag(state.editingTag.id, data as TagUpdate)
        showSuccess('更新成功', `标签 "${result.name}" 已更新`)
      } else {
        throw new Error('无效的操作类型')
      }

      // 重新加载标签数据
      await loadTags()
      
      // 关闭模态窗口
      setState(prev => ({
        ...prev,
        showModal: false,
        modalType: 'create',
        editingTag: null,
        operationLoading: false
      }))
      
    } catch (error) {
      setState(prev => ({ ...prev, operationLoading: false }))
      handleError(error, '保存标签')
    }
  }, [state.modalType, state.editingTag, loadTags, showSuccess, showError])

  // 处理标签删除
  const handleTagDelete = useCallback(async () => {
    if (!state.editingTag) {
      return
    }

    try {
      setState(prev => ({ ...prev, operationLoading: true }))
      const tagName = state.editingTag.name

      await tagService.deleteTag(state.editingTag.id)
      showSuccess('删除成功', `标签 "${tagName}" 已删除`)

      // 重新加载标签数据
      await loadTags()
      
      // 关闭模态窗口
      setState(prev => ({
        ...prev,
        showModal: false,
        modalType: 'create',
        editingTag: null,
        operationLoading: false
      }))
      
    } catch (error) {
      setState(prev => ({ ...prev, operationLoading: false }))
      handleError(error, '删除标签')
    }
  }, [state.editingTag, loadTags, showSuccess, showError])

  // 处理刷新
  const handleRefresh = useCallback(() => {
    loadTags()
  }, [loadTags])

  // 处理手动同步
  const handleManualSync = useCallback(async () => {
    try {
      setState(prev => ({ ...prev, syncing: true }))
      
      // 调用标签服务同步数据
      await tagService.syncTagsFromBookmarks()
      
      // 重新加载标签数据
      await loadTags()
      
      showSuccess('同步成功', '标签数据已从书签中同步')
    } catch (error) {
      handleError(error, '同步标签')
    } finally {
      setState(prev => ({ ...prev, syncing: false }))
    }
  }, [loadTags, showSuccess, handleError])

  // 处理批量删除 - 优化为并发执行
  const handleBatchDelete = useCallback(async (tagIds: string[]) => {
    try {
      await withLoading(async () => {
        // 并发执行删除操作以提高性能
        await Promise.all(tagIds.map(tagId => tagService.deleteTag(tagId)))
      }, `正在删除 ${tagIds.length} 个标签...`)

      showSuccess('批量删除成功', `已删除 ${tagIds.length} 个标签`)
      await loadTags()
    } catch (error) {
      handleError(error, '批量删除')
    }
  }, [withLoading, showSuccess, loadTags, handleError])

  // 处理批量合并
  const handleBatchMerge = useCallback(async (sourceTagIds: string[], targetTagId: string) => {
    try {
      await withLoading(async () => {
        await tagService.batchMergeTags(sourceTagIds, targetTagId)
      }, `正在合并 ${sourceTagIds.length} 个标签...`)

      showSuccess('批量合并成功', `已将 ${sourceTagIds.length} 个标签合并`)
      await loadTags()
    } catch (error) {
      handleError(error, '批量合并')
    }
  }, [withLoading, showSuccess, showError, loadTags])

  // 处理批量设置颜色 - 优化为并发执行
  const handleBatchSetColor = useCallback(async (tagIds: string[], color: string) => {
    try {
      await withLoading(async () => {
        // 并发执行颜色设置操作以提高性能
        await Promise.all(tagIds.map(tagId => tagService.updateTag(tagId, { color })))
      }, `正在为 ${tagIds.length} 个标签设置颜色...`)

      showSuccess('批量设置颜色成功', `已为 ${tagIds.length} 个标签设置颜色`)
      await loadTags()
    } catch (error) {
      handleError(error, '批量设置颜色')
    }
  }, [withLoading, showSuccess, loadTags, handleError])

  // 处理搜索查询变化
  const handleSearchChange = useCallback((query: string) => {
    setState(prev => ({ ...prev, searchQuery: query }))
  }, [])

  // 处理排序选项变化
  const handleSortChange = useCallback((sortBy: TagSortOption) => {
    setState(prev => ({ ...prev, sortBy }))
  }, [])

  // 获取当前编辑标签的书签数量
  const getCurrentTagBookmarkCount = (): number => {
    if (!state.editingTag) return 0
    const tagWithStats = state.tags.find(t => t.id === state.editingTag!.id)
    return tagWithStats?.usageCount || 0
  }

  // 获取现有标签列表（用于重复检查）
  const getExistingTags = (): Array<{ id?: string; name: string }> => {
    return state.tags.map(tag => ({ id: tag.id, name: tag.name }))
  }

  // 渲染错误状态
  const renderErrorState = () => (
    <Card className="text-center py-12">
      <CardContent className="pt-6">
        <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <AlertCircle className="w-8 h-8 text-red-600" />
        </div>
        <CardTitle className="text-lg mb-2">
          加载失败
        </CardTitle>
        <CardDescription className="mb-6">
          {state.error}
        </CardDescription>
        <Button onClick={handleRefresh} variant="default">
          <RefreshCw className="w-4 h-4 mr-2" />
          重试
        </Button>
      </CardContent>
    </Card>
  )

  return (
    <div className={`p-6 ${className}`}>
      {/* 头部区域 */}
      <Card className="mb-6">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center text-2xl">
                <Tags className="w-6 h-6 mr-3 text-primary-600" />
                标签管理
              </CardTitle>
              <CardDescription className="mt-1">
                管理您的书签标签，更好地分类和查找内容
              </CardDescription>
            </div>
            
            <div className="flex items-center space-x-3">
              {/* 手动同步按钮 */}
              <Button
                onClick={handleManualSync}
                variant="outline"
                disabled={state.syncing || state.loading}
                title="从书签中同步标签数据"
              >
                <RefreshCw className={`w-4 h-4 mr-2 ${state.syncing ? 'animate-spin' : ''}`} />
                {state.syncing ? '同步中...' : '手动同步'}
              </Button>
              
              {/* 刷新按钮 */}
              <Button
                onClick={handleRefresh}
                variant="outline"
                disabled={state.loading}
                title="刷新标签列表"
              >
                <RefreshCw className={`w-4 h-4 mr-2 ${state.loading ? 'animate-spin' : ''}`} />
                刷新
              </Button>
              
              {/* 新建标签按钮 */}
              <Button
                onClick={handleCreateTag}
                disabled={state.loading}
              >
                <Plus className="w-4 h-4 mr-2" />
                新建标签
              </Button>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* 主要内容区域 */}
      <div>
        {state.error ? (
          renderErrorState()
        ) : (
          <TagList
            tags={state.tags}
            onTagEdit={handleEditTag}
            onTagDelete={handleDeleteTag}
            onCreateTag={handleCreateTag}
            onBatchDelete={handleBatchDelete}
            onBatchMerge={handleBatchMerge}
            onBatchSetColor={handleBatchSetColor}
            loading={state.loading}
            searchQuery={state.searchQuery}
            onSearchChange={handleSearchChange}
            sortBy={state.sortBy}
            onSortChange={handleSortChange}
          />
        )}
      </div>

      {/* 标签模态窗口 */}
      <TagModal
        isOpen={state.showModal}
        type={state.modalType}
        tag={state.editingTag || undefined}
        bookmarkCount={getCurrentTagBookmarkCount()}
        onSave={state.modalType !== 'delete' ? handleTagSave : undefined}
        onDelete={state.modalType === 'delete' ? handleTagDelete : undefined}
        onClose={handleModalClose}
        loading={state.operationLoading}
        existingTags={getExistingTags()}
      />

      {/* 通知容器 */}
      <ToastContainer
        toasts={toasts}
        onCloseToast={closeToast}
      />

      {/* 全局加载指示器 */}
      <LoadingIndicator
        isVisible={loadingState.isLoading}
        message={loadingState.message}
        progress={loadingState.progress}
        overlay={true}
      />
    </div>
  )
})

// 设置显示名称便于调试
TagManagementTab.displayName = 'TagManagementTab'

export default TagManagementTab

// 导出类型定义
export type { TagManagementTabProps }