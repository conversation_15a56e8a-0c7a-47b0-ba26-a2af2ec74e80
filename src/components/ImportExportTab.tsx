// 导入导出标签页组件

import React, { useState, useCallback } from 'react'
import { 
  Download, 
  Upload, 
  FileText, 
  Database, 
  Globe, 
  CheckCircle, 
  AlertCircle, 
  Loader2,
  Settings,
  Calendar,
  Filter
} from 'lucide-react'
import { Button } from './ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card'
import { Alert, AlertDescription } from './ui/alert'
import { Input } from './ui/input'
import { Label } from './ui/label'
import { Checkbox } from './ui/checkbox'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './ui/select'
import { Progress } from './ui/progress'
import { 
  importExportManagerService,
  type ExportFormat,
  type ImportSource,
  type ExportOptions,
  type ImportOptions,
  type ExportResult,
  type ImportResult
} from '../services/BookmarkImportExportService'
import { 
  ExportAllOptions,
  ExportCategoriesOptions,
  ExportTagsOptions,
  ConflictDetectionResult,
  ConflictResolution
} from '../types'
import ConflictResolutionDialog from './ConflictResolutionDialog'

interface ImportExportTabProps {}

const ImportExportTab: React.FC<ImportExportTabProps> = () => {
  // 导出状态
  const [exportLoading, setExportLoading] = useState(false)
  const [exportProgress, setExportProgress] = useState(0)
  const [exportMessage, setExportMessage] = useState('')
  const [exportResult, setExportResult] = useState<ExportResult | null>(null)
  
  // 导入状态
  const [importLoading, setImportLoading] = useState(false)
  const [importProgress, setImportProgress] = useState(0)
  const [importMessage, setImportMessage] = useState('')
  const [importResult, setImportResult] = useState<ImportResult | null>(null)
  const [importFile, setImportFile] = useState<File | null>(null)
  
  // 冲突处理状态
  const [conflicts, setConflicts] = useState<ConflictDetectionResult | null>(null)
  const [showConflictDialog, setShowConflictDialog] = useState(false)
  const [originalImportData, setOriginalImportData] = useState<any>(null)
  
  // 导出类型和选项
  const [exportType, setExportType] = useState<'all' | 'bookmarks' | 'categories' | 'tags'>('bookmarks')
  const [exportFormat, setExportFormat] = useState<ExportFormat>('json')
  const [includeContent, setIncludeContent] = useState(true)
  const [includeMetadata, setIncludeMetadata] = useState(false)
  const [selectedCategories, setSelectedCategories] = useState<string[]>([])
  const [dateRange, setDateRange] = useState<{start: string, end: string}>({
    start: '',
    end: ''
  })
  
  // 全部数据导出选项
  const [includeBookmarks, setIncludeBookmarks] = useState(true)
  const [includeCategories, setIncludeCategories] = useState(true)
  const [includeTags, setIncludeTags] = useState(true)
  
  // 分类导出选项
  const [includeHierarchy, setIncludeHierarchy] = useState(true)
  const [includeStatistics, setIncludeStatistics] = useState(false)
  
  // 标签导出选项
  const [includeUsageStats, setIncludeUsageStats] = useState(false)
  const [includeRelatedBookmarks, setIncludeRelatedBookmarks] = useState(false)
  
  // 导入选项
  const [importSource, setImportSource] = useState<ImportSource>('json')
  const [skipDuplicates, setSkipDuplicates] = useState(true)
  const [defaultCategory, setDefaultCategory] = useState('导入分类')
  const [validateData, setValidateData] = useState(true)
  
  // 错误状态
  const [error, setError] = useState<string | null>(null)

  // 处理导出
  const handleExport = useCallback(async () => {
    try {
      setExportLoading(true)
      setExportProgress(0)
      setExportMessage('')
      setExportResult(null)
      setError(null)
      
      let result: ExportResult
      
      switch (exportType) {
        case 'all': {
          const options: ExportAllOptions = {
            format: 'json',
            includeBookmarks,
            includeCategories,
            includeTags,
            includeMetadata,
            dateRange: dateRange.start && dateRange.end ? {
              start: new Date(dateRange.start),
              end: new Date(dateRange.end)
            } : undefined
          }
          
          result = await importExportManagerService.exportAllData(
            options,
            (progress, message) => {
              setExportProgress(progress)
              setExportMessage(message)
            }
          )
          break
        }
        
        case 'categories': {
          const options: ExportCategoriesOptions = {
            format: 'json',
            includeHierarchy,
            includeStatistics
          }
          
          result = await importExportManagerService.exportCategories(
            options,
            (progress, message) => {
              setExportProgress(progress)
              setExportMessage(message)
            }
          )
          break
        }
        
        case 'tags': {
          const options: ExportTagsOptions = {
            format: 'json',
            includeUsageStats,
            includeRelatedBookmarks
          }
          
          result = await importExportManagerService.exportTags(
            options,
            (progress, message) => {
              setExportProgress(progress)
              setExportMessage(message)
            }
          )
          break
        }
        
        case 'bookmarks':
        default: {
          const options: ExportOptions = {
            format: exportFormat,
            includeContent,
            includeMetadata,
            categories: selectedCategories.length > 0 ? selectedCategories : undefined,
            dateRange: dateRange.start && dateRange.end ? {
              start: new Date(dateRange.start),
              end: new Date(dateRange.end)
            } : undefined
          }
          
          result = await importExportManagerService.exportBookmarks(
            options,
            (progress, message) => {
              setExportProgress(progress)
              setExportMessage(message)
            }
          )
          break
        }
      }
      
      setExportResult(result)
      
      // 触发下载
      downloadFile(result.data, result.filename, result.format)
      
    } catch (error) {
      console.error('导出失败:', error)
      setError(error instanceof Error ? error.message : '导出失败')
    } finally {
      setExportLoading(false)
    }
  }, [
    exportType, exportFormat, includeContent, includeMetadata, selectedCategories, dateRange,
    includeBookmarks, includeCategories, includeTags, includeHierarchy, includeStatistics,
    includeUsageStats, includeRelatedBookmarks
  ])

  // 处理导入
  const handleImport = useCallback(async () => {
    if (!importFile) {
      setError('请选择要导入的文件')
      return
    }
    
    try {
      setImportLoading(true)
      setImportProgress(0)
      setImportMessage('')
      setImportResult(null)
      setError(null)
      
      const options: ImportOptions = {
        source: importSource,
        skipDuplicates,
        defaultCategory,
        validateData
      }
      
      const result = await importExportManagerService.importData(
        importFile,
        options,
        (progress, message) => {
          setImportProgress(progress)
          setImportMessage(message)
        }
      )
      
      // 检查是否有冲突
      if (result.conflicts && result.conflicts.hasConflicts) {
        setConflicts(result.conflicts)
        setOriginalImportData(importFile) // 保存原始数据用于冲突解决后的导入
        setShowConflictDialog(true)
      } else {
        setImportResult(result)
      }
      
    } catch (error) {
      console.error('导入失败:', error)
      setError(error instanceof Error ? error.message : '导入失败')
    } finally {
      setImportLoading(false)
    }
  }, [importFile, importSource, skipDuplicates, defaultCategory, validateData])

  // 处理冲突解决
  const handleConflictResolution = useCallback(async (resolutions: ConflictResolution[]) => {
    if (!conflicts || !originalImportData) return
    
    try {
      setImportLoading(true)
      setShowConflictDialog(false)
      setImportProgress(0)
      setImportMessage('解决冲突后导入...')
      
      const options: ImportOptions = {
        source: importSource,
        skipDuplicates,
        defaultCategory,
        validateData
      }
      
      // 重新解析原始数据
      const parsedData = await importExportManagerService['parseImportDataNew'](originalImportData, options)
      
      const result = await importExportManagerService.importDataWithResolutions(
        conflicts.conflicts,
        resolutions,
        parsedData,
        options,
        (progress, message) => {
          setImportProgress(progress)
          setImportMessage(message)
        }
      )
      
      setImportResult(result)
      setConflicts(null)
      setOriginalImportData(null)
      
    } catch (error) {
      console.error('冲突解决后导入失败:', error)
      setError(error instanceof Error ? error.message : '冲突解决后导入失败')
    } finally {
      setImportLoading(false)
    }
  }, [conflicts, originalImportData, importSource, skipDuplicates, defaultCategory, validateData])

  // 取消冲突解决
  const handleCancelConflictResolution = useCallback(() => {
    setShowConflictDialog(false)
    setConflicts(null)
    setOriginalImportData(null)
  }, [])

  // 下载文件
  const downloadFile = (data: string | Blob, filename: string, format: ExportFormat) => {
    const blob = data instanceof Blob ? data : new Blob([data], {
      type: format === 'json' ? 'application/json' : 
           format === 'csv' ? 'text/csv' : 
           'text/html'
    })
    
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = filename
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  // 处理文件选择
  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      setImportFile(file)
      setImportResult(null)
      setError(null)
    }
  }

  // 重置状态
  const resetStates = () => {
    setExportResult(null)
    setImportResult(null)
    setError(null)
    setExportProgress(0)
    setImportProgress(0)
    setExportMessage('')
    setImportMessage('')
  }

  return (
    <div className="p-6 space-y-8 bg-background text-foreground">
      {/* 页面标题 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Database className="w-6 h-6 mr-3 text-primary" />
            导入导出
          </CardTitle>
          <CardDescription>
            导出您的收藏数据或从其他来源导入收藏
          </CardDescription>
        </CardHeader>
      </Card>

      {/* 错误提示 */}
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            <div>
              <h3 className="font-medium">操作失败</h3>
              <p className="text-sm mt-1">{error}</p>
              <Button
                variant="link"
                onClick={resetStates}
                className="text-sm underline mt-2 p-0 h-auto"
              >
                重新开始
              </Button>
            </div>
          </AlertDescription>
        </Alert>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* 导出部分 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Download className="w-5 h-5 text-primary-600 mr-2" />
              导出数据
            </CardTitle>
          </CardHeader>
          <CardContent>

            <div className="space-y-4">
              {/* 导出类型选择 */}
              <div>
                <Label className="text-sm font-medium mb-2">
                  导出类型
                </Label>
                <div className="grid grid-cols-2 gap-2">
                  {[
                    { value: 'all', label: '全部数据', icon: Database, desc: '收藏+分类+标签' },
                    { value: 'bookmarks', label: '收藏数据', icon: FileText, desc: '仅收藏内容' },
                    { value: 'categories', label: '分类数据', icon: Filter, desc: '仅分类信息' },
                    { value: 'tags', label: '标签数据', icon: Settings, desc: '仅标签信息' }
                  ].map(({ value, label, icon: Icon, desc }) => (
                    <Button
                      key={value}
                      variant={exportType === value ? "default" : "outline"}
                      onClick={() => setExportType(value as any)}
                      className="p-3 h-auto flex-col"
                    >
                      <Icon className="w-5 h-5 mb-1" />
                      <div className="text-sm font-medium">{label}</div>
                      <div className="text-xs opacity-70">{desc}</div>
                    </Button>
                  ))}
                </div>
              </div>

              {/* 收藏数据导出格式选择 */}
              {exportType === 'bookmarks' && (
                <div>
                  <Label className="text-sm font-medium mb-2">
                    导出格式
                  </Label>
                  <div className="grid grid-cols-3 gap-2">
                    {[
                      { value: 'json', label: 'JSON', icon: FileText, desc: '完整数据' },
                      { value: 'csv', label: 'CSV', icon: Database, desc: '表格格式' },
                      { value: 'html', label: 'HTML', icon: Globe, desc: '网页格式' }
                    ].map(({ value, label, icon: Icon, desc }) => (
                      <Button
                        key={value}
                        variant={exportFormat === value ? "default" : "outline"}
                        onClick={() => setExportFormat(value as ExportFormat)}
                        className="p-3 h-auto flex-col"
                      >
                        <Icon className="w-5 h-5 mb-1" />
                        <div className="text-sm font-medium">{label}</div>
                        <div className="text-xs opacity-70">{desc}</div>
                      </Button>
                    ))}
                  </div>
                </div>
              )}

              {/* 导出选项 */}
              <div className="space-y-3">
                <Label className="text-sm font-medium">
                  导出选项
                </Label>
                
                {/* 全部数据导出选项 */}
                {exportType === 'all' && (
                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="includeBookmarks"
                        checked={includeBookmarks}
                        onCheckedChange={setIncludeBookmarks}
                      />
                      <Label htmlFor="includeBookmarks" className="text-sm">包含收藏数据</Label>
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="includeCategories"
                        checked={includeCategories}
                        onCheckedChange={setIncludeCategories}
                      />
                      <Label htmlFor="includeCategories" className="text-sm">包含分类数据</Label>
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="includeTags"
                        checked={includeTags}
                        onCheckedChange={setIncludeTags}
                      />
                      <Label htmlFor="includeTags" className="text-sm">包含标签数据</Label>
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="includeMetadata"
                        checked={includeMetadata}
                        onCheckedChange={setIncludeMetadata}
                      />
                      <Label htmlFor="includeMetadata" className="text-sm">包含元数据</Label>
                    </div>
                  </div>
                )}
              
                {/* 收藏数据导出选项 */}
                {exportType === 'bookmarks' && (
                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="includeContent"
                        checked={includeContent}
                        onCheckedChange={setIncludeContent}
                      />
                      <Label htmlFor="includeContent" className="text-sm">包含收藏内容</Label>
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="includeMetadata"
                        checked={includeMetadata}
                        onCheckedChange={setIncludeMetadata}
                      />
                      <Label htmlFor="includeMetadata" className="text-sm">包含元数据</Label>
                    </div>
                  </div>
                )}
              
                {/* 分类数据导出选项 */}
                {exportType === 'categories' && (
                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="includeHierarchy"
                        checked={includeHierarchy}
                        onCheckedChange={setIncludeHierarchy}
                      />
                      <Label htmlFor="includeHierarchy" className="text-sm">包含层级关系</Label>
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="includeStatistics"
                        checked={includeStatistics}
                        onCheckedChange={setIncludeStatistics}
                      />
                      <Label htmlFor="includeStatistics" className="text-sm">包含统计信息</Label>
                    </div>
                  </div>
                )}
                
                {/* 标签数据导出选项 */}
                {exportType === 'tags' && (
                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="includeUsageStats"
                        checked={includeUsageStats}
                        onCheckedChange={setIncludeUsageStats}
                      />
                      <Label htmlFor="includeUsageStats" className="text-sm">包含使用统计</Label>
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="includeRelatedBookmarks"
                        checked={includeRelatedBookmarks}
                        onCheckedChange={setIncludeRelatedBookmarks}
                      />
                      <Label htmlFor="includeRelatedBookmarks" className="text-sm">包含相关收藏</Label>
                    </div>
                  </div>
                )}
              </div>

              {/* 日期范围筛选 */}
              <div>
                <Label className="text-sm font-medium mb-2 flex items-center">
                  <Calendar className="w-4 h-4 mr-1" />
                  日期范围筛选（可选）
                </Label>
                <div className="grid grid-cols-2 gap-2">
                  <Input
                    type="date"
                    value={dateRange.start}
                    onChange={(e) => setDateRange(prev => ({ ...prev, start: e.target.value }))}
                    placeholder="开始日期"
                  />
                  <Input
                    type="date"
                    value={dateRange.end}
                    onChange={(e) => setDateRange(prev => ({ ...prev, end: e.target.value }))}
                    placeholder="结束日期"
                  />
                </div>
              </div>

              {/* 导出按钮 */}
              <Button
                onClick={handleExport}
                disabled={exportLoading}
                className="w-full"
              >
                {exportLoading ? (
                  <>
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    导出中...
                  </>
                ) : (
                  <>
                    <Download className="w-4 h-4 mr-2" />
                    开始导出
                  </>
                )}
              </Button>

              {/* 导出进度 */}
              {exportLoading && (
                <div className="space-y-2">
                  <div className="flex justify-between text-sm text-muted-foreground">
                    <span>{exportMessage}</span>
                    <span>{exportProgress}%</span>
                  </div>
                  <Progress value={exportProgress} className="w-full" />
                </div>
              )}

              {/* 导出结果 */}
              {exportResult && (
                <Alert>
                  <CheckCircle className="h-4 w-4" />
                  <AlertDescription>
                    <div>
                      <span className="font-medium">导出成功</span>
                      <p className="text-sm mt-1">
                        已导出 {exportResult.itemCount} 个收藏到 {exportResult.filename}
                      </p>
                    </div>
                  </AlertDescription>
                </Alert>
              )}
            </div>
          </CardContent>
        </Card>

        {/* 导入部分 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Upload className="w-5 h-5 text-primary-600 mr-2" />
              导入收藏
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {/* 导入来源选择 */}
              <div>
                <Label className="text-sm font-medium mb-2">
                  导入来源
                </Label>
                <Select value={importSource} onValueChange={(value) => setImportSource(value as ImportSource)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="json">JSON 文件</SelectItem>
                    <SelectItem value="csv">CSV 文件</SelectItem>
                    <SelectItem value="html">HTML 文件</SelectItem>
                    <SelectItem value="chrome">Chrome 书签</SelectItem>
                    <SelectItem value="firefox">Firefox 书签</SelectItem>
                    <SelectItem value="edge">Edge 书签</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* 文件选择 */}
              <div>
                <Label htmlFor="import-file" className="text-sm font-medium mb-2">
                  选择文件
                </Label>
                <Input
                  id="import-file"
                  type="file"
                  onChange={handleFileSelect}
                  accept={
                    importSource === 'json' ? '.json' :
                    importSource === 'csv' ? '.csv' :
                    '.html,.htm'
                  }
                />
                {importFile && (
                  <p className="text-sm text-muted-foreground mt-1">
                    已选择: {importFile.name} ({(importFile.size / 1024).toFixed(1)} KB)
                  </p>
                )}
              </div>

              {/* 导入选项 */}
              <div className="space-y-3">
                <Label className="text-sm font-medium flex items-center">
                  <Settings className="w-4 h-4 mr-1" />
                  导入选项
                </Label>
                
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="skipDuplicates"
                      checked={skipDuplicates}
                      onCheckedChange={setSkipDuplicates}
                    />
                    <Label htmlFor="skipDuplicates" className="text-sm">跳过重复项</Label>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="validateData"
                      checked={validateData}
                      onCheckedChange={setValidateData}
                    />
                    <Label htmlFor="validateData" className="text-sm">验证数据格式</Label>
                  </div>
                </div>
                
                <div>
                  <Label className="text-sm mb-1">
                    默认分类
                  </Label>
                  <Input
                    type="text"
                    value={defaultCategory}
                    onChange={(e) => setDefaultCategory(e.target.value)}
                    placeholder="导入分类"
                  />
                </div>
              </div>

              {/* 导入按钮 */}
              <Button
                onClick={handleImport}
                disabled={importLoading || !importFile}
                className="w-full"
              >
                {importLoading ? (
                  <>
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    导入中...
                  </>
                ) : (
                  <>
                    <Upload className="w-4 h-4 mr-2" />
                    开始导入
                  </>
                )}
              </Button>

              {/* 导入进度 */}
              {importLoading && (
                <div className="space-y-2">
                  <div className="flex justify-between text-sm text-muted-foreground">
                    <span>{importMessage}</span>
                    <span>{importProgress}%</span>
                  </div>
                  <Progress value={importProgress} className="w-full" />
                </div>
              )}

              {/* 导入结果 */}
              {importResult && (
                <Alert variant={importResult.success ? "default" : "destructive"}>
                  {importResult.success ? (
                    <CheckCircle className="h-4 w-4" />
                  ) : (
                    <AlertCircle className="h-4 w-4" />
                  )}
                  <AlertDescription>
                    <div>
                      <span className="font-medium">
                        {importResult.success ? '导入完成' : '导入失败'}
                      </span>
                      
                      <div className="text-sm mt-2">
                        <p>总计: {importResult.totalItems} 个项目</p>
                        <p>成功: {importResult.importedItems} 个</p>
                        <p>跳过: {importResult.skippedItems} 个</p>
                        <p>错误: {importResult.errorItems} 个</p>
                        
                        {importResult.duplicates.length > 0 && (
                          <p>重复项: {importResult.duplicates.length} 个</p>
                        )}
                        
                        {importResult.errors.length > 0 && (
                          <div className="mt-2">
                            <p className="font-medium">错误详情:</p>
                            <ul className="list-disc list-inside text-xs mt-1 max-h-20 overflow-y-auto">
                              {importResult.errors.slice(0, 5).map((error, index) => (
                                <li key={index}>{error}</li>
                              ))}
                              {importResult.errors.length > 5 && (
                                <li>...还有 {importResult.errors.length - 5} 个错误</li>
                              )}
                            </ul>
                          </div>
                        )}
                      </div>
                    </div>
                  </AlertDescription>
                </Alert>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 使用说明 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Filter className="w-4 h-4 mr-2" />
            使用说明
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-sm space-y-2">
            <p><strong>导出功能：</strong></p>
            <ul className="list-disc list-inside ml-4 space-y-1">
              <li>JSON格式：包含完整的收藏数据，适合备份和迁移</li>
              <li>CSV格式：表格格式，适合在Excel等软件中查看和编辑</li>
              <li>HTML格式：网页格式，适合在浏览器中查看和分享</li>
            </ul>
            
            <p className="mt-3"><strong>导入功能：</strong></p>
            <ul className="list-disc list-inside ml-4 space-y-1">
              <li>支持导入本工具导出的JSON、CSV、HTML文件</li>
              <li>支持导入浏览器书签文件（Chrome、Firefox、Edge）</li>
              <li>建议开启"跳过重复项"避免重复导入</li>
              <li>建议开启"验证数据格式"确保数据完整性</li>
            </ul>
          </div>
        </CardContent>
      </Card>

      {/* 冲突解决对话框 */}
      {conflicts && (
        <ConflictResolutionDialog
          conflicts={conflicts.conflicts}
          onResolve={handleConflictResolution}
          onCancel={handleCancelConflictResolution}
          isOpen={showConflictDialog}
        />
      )}
    </div>
  )
}

export default ImportExportTab