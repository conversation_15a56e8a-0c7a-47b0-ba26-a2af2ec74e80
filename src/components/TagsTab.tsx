// 标签管理标签页组件 - 作为OptionsApp中"标签管理"标签页的内容

import React, { useState, useEffect, useCallback } from 'react'
import { AlertTriangle, RefreshCw } from 'lucide-react'
import TagManagementTab from './TagManagementTab'
import { tagService } from '../services/tagService'
import { Button } from './ui/button'
import { Alert, AlertDescription } from './ui/alert'
import { Card, CardContent, CardTitle, CardDescription } from './ui/card'

interface TagsTabProps {
  /** 自定义CSS类名 */
  className?: string
}

interface TagsTabState {
  /** 初始化状态 */
  initializing: boolean
  /** 初始化错误 */
  initError: string | null
  /** 重试次数 */
  retryCount: number
}

// 常量定义
const MAX_RETRIES = 3
const ERROR_MESSAGES = {
  CHROME_API_UNAVAILABLE: 'Chrome扩展API不可用，请确保在扩展环境中运行',
  INITIALIZATION_FAILED: '初始化失败',
  MAX_RETRIES_REACHED: '已达到最大重试次数，请尝试刷新页面或重新加载扩展'
} as const

// 错误处理工具函数
const formatError = (error: unknown): string => {
  if (error instanceof Error) {
    return error.message
  }
  if (typeof error === 'string') {
    return error
  }
  return ERROR_MESSAGES.INITIALIZATION_FAILED
}

/**
 * 标签管理标签页组件
 * 作为OptionsApp中"标签管理"标签页的内容，集成TagManagementTab组件
 */
const TagsTab: React.FC<TagsTabProps> = React.memo(({
  className = ''
}) => {
  const [state, setState] = useState<TagsTabState>({
    initializing: true,
    initError: null,
    retryCount: 0
  })



  // 初始化标签管理功能
  const initializeTagManagement = useCallback(async () => {
    try {
      setState(prev => ({ 
        ...prev, 
        initializing: true, 
        initError: null 
      }))

      // 检查Chrome扩展API是否可用
      if (!chrome || !chrome.runtime) {
        throw new Error(ERROR_MESSAGES.CHROME_API_UNAVAILABLE)
      }

      // 初始化完成，TagManagementTab会自己处理数据加载和同步
      setState(prev => ({ 
        ...prev, 
        initializing: false 
      }))
    } catch (error) {
      console.error('初始化标签管理失败:', error)
      setState(prev => ({ 
        ...prev, 
        initializing: false,
        initError: formatError(error)
      }))
    }
  }, [])

  // 重试初始化
  const handleRetry = useCallback(async () => {
    if (state.retryCount < MAX_RETRIES) {
      setState(prev => ({ 
        ...prev, 
        retryCount: prev.retryCount + 1 
      }))
      await initializeTagManagement()
    }
  }, [state.retryCount, initializeTagManagement])

  // 组件挂载时初始化
  useEffect(() => {
    initializeTagManagement()
  }, [initializeTagManagement])

  // 渲染初始化加载状态
  if (state.initializing) {
    return (
      <div className={`p-6 ${className}`}>
        <Card>
          <CardContent className="flex items-center justify-center py-12">
            <div className="text-center" role="status" aria-live="polite">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto mb-4"></div>
              <p className="text-muted-foreground mb-2">正在初始化标签管理...</p>
              {state.retryCount > 0 && (
                <p className="text-sm text-gray-500">重试次数: {state.retryCount}/{MAX_RETRIES}</p>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  // 渲染初始化错误状态
  if (state.initError) {
    return (
      <div className={`p-6 ${className}`}>
        <Card className="max-w-md mx-auto">
          <CardContent className="text-center py-12">
            <div className="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <AlertTriangle className="w-6 h-6 text-red-600" />
            </div>
            <CardTitle className="text-lg mb-2">标签管理初始化失败</CardTitle>
            <CardDescription className="mb-4">{state.initError}</CardDescription>
            <div className="flex flex-col sm:flex-row gap-3 justify-center">
              {state.retryCount < MAX_RETRIES && (
                <Button onClick={handleRetry}>
                  <RefreshCw className="w-4 h-4 mr-2" />
                  重试 ({MAX_RETRIES - state.retryCount} 次剩余)
                </Button>
              )}
              <Button 
                variant="secondary"
                onClick={() => window.location.reload()}
              >
                <RefreshCw className="w-4 h-4 mr-2" />
                刷新页面
              </Button>
            </div>
            {state.retryCount >= MAX_RETRIES && (
              <p className="text-sm text-gray-500 mt-4">
                {ERROR_MESSAGES.MAX_RETRIES_REACHED}
              </p>
            )}
          </CardContent>
        </Card>
      </div>
    )
  }

  // 渲染主要内容
  return (
    <div className={`${className}`}>
      {/* 标签管理主组件 */}
      <TagManagementTab className="border-t" />
    </div>
  )
})

TagsTab.displayName = 'TagsTab'

export default TagsTab