// 降级存储服务 - 当IndexedDB不可用时使用Chrome Storage API

import { 
  Bookmark, 
  Category, 
  Tag, 
  BookmarkFilter, 
  SortOptions 
} from '../types'

/**
 * 降级存储服务类
 * 使用Chrome Storage API作为IndexedDB的降级方案
 */
export class FallbackStorageService {
  private readonly STORAGE_KEYS = {
    BOOKMARKS: 'fallback_bookmarks',
    CATEGORIES: 'fallback_categories',
    TAGS: 'fallback_tags',
    SETTINGS: 'fallback_settings'
  } as const

  /**
   * 检查Chrome Storage是否可用
   */
  private isAvailable(): boolean {
    return typeof chrome !== 'undefined' && 
           chrome.storage && 
           chrome.storage.local
  }

  /**
   * 获取存储数据
   */
  private async getStorageData(key: string): Promise<any[]> {
    if (!this.isAvailable()) {
      throw new Error('Chrome Storage不可用')
    }

    try {
      const result = await chrome.storage.local.get([key])
      return result[key] || []
    } catch (error) {
      console.error(`获取存储数据失败 [${key}]:`, error)
      return []
    }
  }

  /**
   * 保存存储数据
   */
  private async setStorageData(key: string, data: any[]): Promise<void> {
    if (!this.isAvailable()) {
      throw new Error('Chrome Storage不可用')
    }

    try {
      await chrome.storage.local.set({ [key]: data })
    } catch (error) {
      console.error(`保存存储数据失败 [${key}]:`, error)
      throw error
    }
  }

  // ==================== 书签操作 ====================

  /**
   * 保存书签
   */
  async saveBookmark(bookmark: Bookmark): Promise<string> {
    const bookmarks = await this.getStorageData(this.STORAGE_KEYS.BOOKMARKS)
    
    // 检查是否已存在
    const existingIndex = bookmarks.findIndex((b: Bookmark) => b.id === bookmark.id)
    
    if (existingIndex >= 0) {
      bookmarks[existingIndex] = bookmark
    } else {
      bookmarks.push(bookmark)
    }
    
    await this.setStorageData(this.STORAGE_KEYS.BOOKMARKS, bookmarks)
    return bookmark.id
  }

  /**
   * 获取书签
   */
  async getBookmark(id: string): Promise<Bookmark | null> {
    const bookmarks = await this.getStorageData(this.STORAGE_KEYS.BOOKMARKS)
    return bookmarks.find((b: Bookmark) => b.id === id) || null
  }

  /**
   * 获取所有书签
   */
  async getBookmarks(filter?: BookmarkFilter, sort?: SortOptions): Promise<Bookmark[]> {
    let bookmarks = await this.getStorageData(this.STORAGE_KEYS.BOOKMARKS)
    
    // 应用筛选
    if (filter) {
      bookmarks = this.applyBookmarkFilter(bookmarks, filter)
    }
    
    // 应用排序
    if (sort) {
      bookmarks = this.sortBookmarks(bookmarks, sort)
    }
    
    // 应用分页
    if (filter?.limit) {
      const offset = filter.offset || 0
      bookmarks = bookmarks.slice(offset, offset + filter.limit)
    }
    
    return bookmarks
  }

  /**
   * 更新书签
   */
  async updateBookmark(id: string, updates: Partial<Bookmark>): Promise<void> {
    const bookmarks = await this.getStorageData(this.STORAGE_KEYS.BOOKMARKS)
    const index = bookmarks.findIndex((b: Bookmark) => b.id === id)
    
    if (index === -1) {
      throw new Error(`书签不存在: ${id}`)
    }
    
    bookmarks[index] = { ...bookmarks[index], ...updates, id }
    await this.setStorageData(this.STORAGE_KEYS.BOOKMARKS, bookmarks)
  }

  /**
   * 删除书签
   */
  async deleteBookmark(id: string): Promise<void> {
    const bookmarks = await this.getStorageData(this.STORAGE_KEYS.BOOKMARKS)
    const filteredBookmarks = bookmarks.filter((b: Bookmark) => b.id !== id)
    await this.setStorageData(this.STORAGE_KEYS.BOOKMARKS, filteredBookmarks)
  }

  /**
   * 批量删除书签
   */
  async deleteBookmarks(ids: string[]): Promise<void> {
    const bookmarks = await this.getStorageData(this.STORAGE_KEYS.BOOKMARKS)
    const filteredBookmarks = bookmarks.filter((b: Bookmark) => !ids.includes(b.id))
    await this.setStorageData(this.STORAGE_KEYS.BOOKMARKS, filteredBookmarks)
  }

  // ==================== 分类操作 ====================

  /**
   * 保存分类
   */
  async saveCategory(category: Category): Promise<string> {
    const categories = await this.getStorageData(this.STORAGE_KEYS.CATEGORIES)
    
    const existingIndex = categories.findIndex((c: Category) => c.id === category.id)
    
    if (existingIndex >= 0) {
      categories[existingIndex] = category
    } else {
      categories.push(category)
    }
    
    await this.setStorageData(this.STORAGE_KEYS.CATEGORIES, categories)
    return category.id
  }

  /**
   * 获取分类
   */
  async getCategory(id: string): Promise<Category | null> {
    const categories = await this.getStorageData(this.STORAGE_KEYS.CATEGORIES)
    return categories.find((c: Category) => c.id === id) || null
  }

  /**
   * 获取所有分类
   */
  async getCategories(): Promise<Category[]> {
    return await this.getStorageData(this.STORAGE_KEYS.CATEGORIES)
  }

  /**
   * 更新分类
   */
  async updateCategory(id: string, updates: Partial<Category>): Promise<void> {
    const categories = await this.getStorageData(this.STORAGE_KEYS.CATEGORIES)
    const index = categories.findIndex((c: Category) => c.id === id)
    
    if (index === -1) {
      throw new Error(`分类不存在: ${id}`)
    }
    
    categories[index] = { ...categories[index], ...updates, id }
    await this.setStorageData(this.STORAGE_KEYS.CATEGORIES, categories)
  }

  /**
   * 删除分类
   */
  async deleteCategory(id: string): Promise<void> {
    const categories = await this.getStorageData(this.STORAGE_KEYS.CATEGORIES)
    const filteredCategories = categories.filter((c: Category) => c.id !== id)
    await this.setStorageData(this.STORAGE_KEYS.CATEGORIES, filteredCategories)
  }

  // ==================== 标签操作 ====================

  /**
   * 保存标签
   */
  async saveTag(tag: Tag): Promise<string> {
    const tags = await this.getStorageData(this.STORAGE_KEYS.TAGS)
    
    const existingIndex = tags.findIndex((t: Tag) => t.id === tag.id)
    
    if (existingIndex >= 0) {
      tags[existingIndex] = tag
    } else {
      tags.push(tag)
    }
    
    await this.setStorageData(this.STORAGE_KEYS.TAGS, tags)
    return tag.id
  }

  /**
   * 获取标签
   */
  async getTag(id: string): Promise<Tag | null> {
    const tags = await this.getStorageData(this.STORAGE_KEYS.TAGS)
    return tags.find((t: Tag) => t.id === id) || null
  }

  /**
   * 根据名称获取标签
   */
  async getTagByName(name: string): Promise<Tag | null> {
    const tags = await this.getStorageData(this.STORAGE_KEYS.TAGS)
    return tags.find((t: Tag) => t.name === name) || null
  }

  /**
   * 获取所有标签
   */
  async getTags(): Promise<Tag[]> {
    return await this.getStorageData(this.STORAGE_KEYS.TAGS)
  }

  /**
   * 更新标签
   */
  async updateTag(id: string, updates: Partial<Tag>): Promise<void> {
    const tags = await this.getStorageData(this.STORAGE_KEYS.TAGS)
    const index = tags.findIndex((t: Tag) => t.id === id)
    
    if (index === -1) {
      throw new Error(`标签不存在: ${id}`)
    }
    
    tags[index] = { ...tags[index], ...updates, id }
    await this.setStorageData(this.STORAGE_KEYS.TAGS, tags)
  }

  /**
   * 删除标签
   */
  async deleteTag(id: string): Promise<void> {
    const tags = await this.getStorageData(this.STORAGE_KEYS.TAGS)
    const filteredTags = tags.filter((t: Tag) => t.id !== id)
    await this.setStorageData(this.STORAGE_KEYS.TAGS, filteredTags)
  }

  // ==================== 设置操作 ====================

  /**
   * 保存设置
   */
  async saveSetting(key: string, value: any): Promise<void> {
    const settings = await this.getStorageData(this.STORAGE_KEYS.SETTINGS)
    const existingIndex = settings.findIndex((s: any) => s.key === key)
    
    const setting = { key, value, updatedAt: new Date() }
    
    if (existingIndex >= 0) {
      settings[existingIndex] = setting
    } else {
      settings.push(setting)
    }
    
    await this.setStorageData(this.STORAGE_KEYS.SETTINGS, settings)
  }

  /**
   * 获取设置
   */
  async getSetting(key: string): Promise<any> {
    const settings = await this.getStorageData(this.STORAGE_KEYS.SETTINGS)
    const setting = settings.find((s: any) => s.key === key)
    return setting ? setting.value : null
  }

  // ==================== 工具方法 ====================

  /**
   * 应用书签筛选
   */
  private applyBookmarkFilter(bookmarks: Bookmark[], filter: BookmarkFilter): Bookmark[] {
    return bookmarks.filter(bookmark => {
      // 文本搜索
      if (filter.query) {
        const query = filter.query.toLowerCase()
        const searchText = `${bookmark.title} ${bookmark.description || ''} ${bookmark.content || ''}`.toLowerCase()
        if (!searchText.includes(query)) {
          return false
        }
      }
      
      // 标签筛选
      if (filter.tags && filter.tags.length > 0) {
        const hasMatchingTag = filter.tags.some(tag => bookmark.tags.includes(tag))
        if (!hasMatchingTag) {
          return false
        }
      }
      
      // 分类筛选
      if (filter.categories && filter.categories.length > 0) {
        if (!filter.categories.includes(bookmark.category)) {
          return false
        }
      }
      
      // 类型筛选
      if (filter.type && bookmark.type !== filter.type) {
        return false
      }
      
      // 日期范围筛选
      if (filter.dateRange) {
        const bookmarkDate = new Date(bookmark.createdAt)
        if (bookmarkDate < filter.dateRange.start || bookmarkDate > filter.dateRange.end) {
          return false
        }
      }
      
      return true
    })
  }

  /**
   * 排序书签
   */
  private sortBookmarks(bookmarks: Bookmark[], sort: SortOptions): Bookmark[] {
    return bookmarks.sort((a, b) => {
      let aValue: any
      let bValue: any
      
      switch (sort.field) {
        case 'createdAt':
          aValue = new Date(a.createdAt).getTime()
          bValue = new Date(b.createdAt).getTime()
          break
        case 'updatedAt':
          aValue = new Date(a.updatedAt).getTime()
          bValue = new Date(b.updatedAt).getTime()
          break
        case 'title':
          aValue = a.title.toLowerCase()
          bValue = b.title.toLowerCase()
          break
        case 'category':
          aValue = a.category.toLowerCase()
          bValue = b.category.toLowerCase()
          break
        default:
          return 0
      }
      
      if (aValue < bValue) {
        return sort.direction === 'asc' ? -1 : 1
      }
      if (aValue > bValue) {
        return sort.direction === 'asc' ? 1 : -1
      }
      return 0
    })
  }

  /**
   * 清空所有数据
   */
  async clearAllData(): Promise<void> {
    const keys = Object.values(this.STORAGE_KEYS)
    
    for (const key of keys) {
      await this.setStorageData(key, [])
    }
  }

  /**
   * 获取数据库统计信息
   */
  async getStats(): Promise<{
    bookmarkCount: number
    categoryCount: number
    tagCount: number
  }> {
    const [bookmarks, categories, tags] = await Promise.all([
      this.getBookmarks(),
      this.getCategories(),
      this.getTags()
    ])
    
    return {
      bookmarkCount: bookmarks.length,
      categoryCount: categories.length,
      tagCount: tags.length
    }
  }
}

// 导出单例实例
export const fallbackStorageService = new FallbackStorageService()