import { 
  Bookmark, 
  BookmarkInput, 
  Category, 
  Tag, 
  BookmarkMetadata 
} from '../types'

/**
 * 序列化工具类
 * 提供数据模型的序列化和反序列化功能
 */
export class SerializationUtils {

  /**
   * 将书签对象序列化为JSON字符串
   * @param bookmark 书签对象
   * @returns JSON字符串
   */
  static serializeBookmark(bookmark: Bookmark): string {
    const serializable = {
      ...bookmark,
      createdAt: bookmark.createdAt.toISOString(),
      updatedAt: bookmark.updatedAt.toISOString(),
      metadata: {
        ...bookmark.metadata,
        publishDate: bookmark.metadata.publishDate?.toISOString()
      }
    }
    return JSON.stringify(serializable)
  }

  /**
   * 从JSON字符串反序列化书签对象
   * @param json JSON字符串
   * @returns 书签对象
   */
  static deserializeBookmark(json: string): Bookmark {
    const data = JSON.parse(json)
    return {
      ...data,
      createdAt: new Date(data.createdAt),
      updatedAt: new Date(data.updatedAt),
      metadata: {
        ...data.metadata,
        publishDate: data.metadata.publishDate ? new Date(data.metadata.publishDate) : undefined
      }
    }
  }

  /**
   * 将书签数组序列化为JSON字符串
   * @param bookmarks 书签数组
   * @returns JSON字符串
   */
  static serializeBookmarks(bookmarks: Bookmark[]): string {
    const serializable = bookmarks.map(bookmark => ({
      ...bookmark,
      createdAt: bookmark.createdAt.toISOString(),
      updatedAt: bookmark.updatedAt.toISOString(),
      metadata: {
        ...bookmark.metadata,
        publishDate: bookmark.metadata.publishDate?.toISOString()
      }
    }))
    return JSON.stringify(serializable)
  }

  /**
   * 从JSON字符串反序列化书签数组
   * @param json JSON字符串
   * @returns 书签数组
   */
  static deserializeBookmarks(json: string): Bookmark[] {
    const data = JSON.parse(json)
    return data.map((item: any) => ({
      ...item,
      createdAt: new Date(item.createdAt),
      updatedAt: new Date(item.updatedAt),
      metadata: {
        ...item.metadata,
        publishDate: item.metadata.publishDate ? new Date(item.metadata.publishDate) : undefined
      }
    }))
  }

  /**
   * 将分类对象序列化为JSON字符串
   * @param category 分类对象
   * @returns JSON字符串
   */
  static serializeCategory(category: Category): string {
    const serializable = {
      ...category,
      createdAt: category.createdAt.toISOString(),
      updatedAt: category.updatedAt.toISOString()
    }
    return JSON.stringify(serializable)
  }

  /**
   * 从JSON字符串反序列化分类对象
   * @param json JSON字符串
   * @returns 分类对象
   */
  static deserializeCategory(json: string): Category {
    const data = JSON.parse(json)
    return {
      ...data,
      createdAt: new Date(data.createdAt),
      updatedAt: new Date(data.updatedAt)
    }
  }

  /**
   * 将分类数组序列化为JSON字符串
   * @param categories 分类数组
   * @returns JSON字符串
   */
  static serializeCategories(categories: Category[]): string {
    const serializable = categories.map(category => ({
      ...category,
      createdAt: category.createdAt.toISOString(),
      updatedAt: category.updatedAt.toISOString()
    }))
    return JSON.stringify(serializable)
  }

  /**
   * 从JSON字符串反序列化分类数组
   * @param json JSON字符串
   * @returns 分类数组
   */
  static deserializeCategories(json: string): Category[] {
    const data = JSON.parse(json)
    return data.map((item: any) => ({
      ...item,
      createdAt: new Date(item.createdAt),
      updatedAt: new Date(item.updatedAt)
    }))
  }

  /**
   * 将标签对象序列化为JSON字符串
   * @param tag 标签对象
   * @returns JSON字符串
   */
  static serializeTag(tag: Tag): string {
    const serializable = {
      ...tag,
      createdAt: tag.createdAt.toISOString(),
      updatedAt: tag.updatedAt.toISOString()
    }
    return JSON.stringify(serializable)
  }

  /**
   * 从JSON字符串反序列化标签对象
   * @param json JSON字符串
   * @returns 标签对象
   */
  static deserializeTag(json: string): Tag {
    const data = JSON.parse(json)
    return {
      ...data,
      createdAt: new Date(data.createdAt),
      updatedAt: new Date(data.updatedAt)
    }
  }

  /**
   * 将标签数组序列化为JSON字符串
   * @param tags 标签数组
   * @returns JSON字符串
   */
  static serializeTags(tags: Tag[]): string {
    const serializable = tags.map(tag => ({
      ...tag,
      createdAt: tag.createdAt.toISOString(),
      updatedAt: tag.updatedAt.toISOString()
    }))
    return JSON.stringify(serializable)
  }

  /**
   * 从JSON字符串反序列化标签数组
   * @param json JSON字符串
   * @returns 标签数组
   */
  static deserializeTags(json: string): Tag[] {
    const data = JSON.parse(json)
    return data.map((item: any) => ({
      ...item,
      createdAt: new Date(item.createdAt),
      updatedAt: new Date(item.updatedAt)
    }))
  }

  /**
   * 将BookmarkInput转换为完整的Bookmark对象
   * @param input 书签输入数据
   * @param id 书签ID（可选，如果不提供则自动生成）
   * @returns 完整的书签对象
   */
  static createBookmarkFromInput(input: BookmarkInput, id?: string): Bookmark {
    const now = new Date()
    const bookmarkId = id || this.generateId()

    return {
      id: bookmarkId,
      type: input.type,
      title: input.title,
      url: input.url,
      content: input.content,
      description: input.description || '',
      tags: input.tags || [],
      category: input.category || '默认分类',
      favicon: input.favicon,
      thumbnail: input.thumbnail,
      createdAt: now,
      updatedAt: now,
      metadata: {
        pageTitle: input.metadata?.pageTitle,
        siteName: input.metadata?.siteName,
        author: input.metadata?.author,
        publishDate: input.metadata?.publishDate,
        wordCount: input.metadata?.wordCount,
        language: input.metadata?.language,
        aiGenerated: input.metadata?.aiGenerated || false
      }
    }
  }

  /**
   * 将CategoryInput转换为完整的Category对象
   * @param input 分类输入数据
   * @param id 分类ID（可选，如果不提供则自动生成）
   * @returns 完整的分类对象
   */
  static createCategoryFromInput(input: CategoryInput, id?: string): Category {
    const now = new Date()
    const categoryId = id || this.generateId()

    return {
      id: categoryId,
      name: input.name,
      description: input.description,
      color: input.color,
      parentId: input.parentId,
      createdAt: now,
      updatedAt: now,
      bookmarkCount: 0
    }
  }

  /**
   * 将TagInput转换为完整的Tag对象
   * @param input 标签输入数据
   * @param id 标签ID（可选，如果不提供则自动生成）
   * @returns 完整的标签对象
   */
  static createTagFromInput(input: TagInput, id?: string): Tag {
    const now = new Date()
    const tagId = id || this.generateId()

    return {
      id: tagId,
      name: input.name,
      color: input.color,
      usageCount: 0,
      createdAt: now,
      updatedAt: now
    }
  }

  /**
   * 生成唯一ID
   * @returns 唯一ID字符串
   */
  static generateId(): string {
    return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * 深拷贝对象
   * @param obj 要拷贝的对象
   * @returns 拷贝后的对象
   */
  static deepClone<T>(obj: T): T {
    if (obj === null || typeof obj !== 'object') {
      return obj
    }

    if (obj instanceof Date) {
      return new Date(obj.getTime()) as unknown as T
    }

    if (Array.isArray(obj)) {
      return obj.map(item => this.deepClone(item)) as unknown as T
    }

    const cloned = {} as T
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        cloned[key] = this.deepClone(obj[key])
      }
    }

    return cloned
  }

  /**
   * 比较两个对象是否相等（深度比较）
   * @param obj1 对象1
   * @param obj2 对象2
   * @returns 是否相等
   */
  static deepEqual(obj1: any, obj2: any): boolean {
    if (obj1 === obj2) {
      return true
    }

    if (obj1 == null || obj2 == null) {
      return obj1 === obj2
    }

    if (typeof obj1 !== typeof obj2) {
      return false
    }

    if (obj1 instanceof Date && obj2 instanceof Date) {
      return obj1.getTime() === obj2.getTime()
    }

    if (Array.isArray(obj1) && Array.isArray(obj2)) {
      if (obj1.length !== obj2.length) {
        return false
      }
      for (let i = 0; i < obj1.length; i++) {
        if (!this.deepEqual(obj1[i], obj2[i])) {
          return false
        }
      }
      return true
    }

    if (typeof obj1 === 'object' && typeof obj2 === 'object') {
      const keys1 = Object.keys(obj1)
      const keys2 = Object.keys(obj2)

      if (keys1.length !== keys2.length) {
        return false
      }

      for (const key of keys1) {
        if (!keys2.includes(key)) {
          return false
        }
        if (!this.deepEqual(obj1[key], obj2[key])) {
          return false
        }
      }
      return true
    }

    return false
  }
}