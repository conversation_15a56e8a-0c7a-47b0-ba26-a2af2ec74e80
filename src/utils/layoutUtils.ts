// 布局工具类 - 提供布局计算、防抖更新、容器尺寸监听等功能

import type { 
  ContainerSize, 
  LayoutCalculation, 
  DebounceConfig, 
  ResizeObserverConfig,
  ResponsiveBreakpoints 
} from '../types/layout'

/**
 * 布局工具类
 * 提供布局计算、响应式处理、容器监听等功能
 */
export class LayoutUtils {
  // 默认响应式断点
  private static readonly DEFAULT_BREAKPOINTS: ResponsiveBreakpoints = {
    xs: 0,
    sm: 576,
    md: 768,
    lg: 992,
    xl: 1200
  }

  // 默认防抖配置
  private static readonly DEFAULT_DEBOUNCE_CONFIG: DebounceConfig = {
    delay: 300,
    immediate: false,
    maxWait: 1000
  }

  // 默认容器观察器配置
  private static readonly DEFAULT_RESIZE_CONFIG: ResizeObserverConfig = {
    debounce: true,
    debounceDelay: 100,
    immediate: false
  }

  // 存储防抖定时器
  private static debounceTimers = new Map<string, number>()
  
  // 存储ResizeObserver实例
  private static resizeObservers = new Map<Element, ResizeObserver>()

  /**
   * 计算最优列数
   * @param containerWidth 容器宽度
   * @param minItemWidth 最小项目宽度
   * @param gap 间距
   * @param maxColumns 最大列数
   * @returns 最优列数
   */
  static calculateOptimalColumns(
    containerWidth: number, 
    minItemWidth: number, 
    gap: number = 16,
    maxColumns: number = 6
  ): number {
    if (containerWidth <= 0 || minItemWidth <= 0) return 1

    // 计算可用宽度（减去间距）
    const availableWidth = containerWidth - gap
    
    // 计算理论上的最大列数
    const theoreticalColumns = Math.floor((availableWidth + gap) / (minItemWidth + gap))
    
    // 限制在合理范围内
    return Math.max(1, Math.min(theoreticalColumns, maxColumns))
  }

  /**
   * 计算布局参数
   * @param containerWidth 容器宽度
   * @param columns 列数
   * @param gap 间距
   * @param aspectRatio 宽高比
   * @returns 布局计算结果
   */
  static calculateLayout(
    containerWidth: number,
    columns: number,
    gap: number = 16,
    aspectRatio: number = 1.2
  ): LayoutCalculation {
    const totalGap = (columns - 1) * gap
    const itemWidth = (containerWidth - totalGap) / columns
    const itemHeight = itemWidth * aspectRatio

    return {
      columns,
      itemWidth: Math.max(0, itemWidth),
      itemHeight: Math.max(0, itemHeight),
      gap,
      totalWidth: containerWidth,
      totalHeight: itemHeight // 单行高度
    }
  }

  /**
   * 防抖布局更新
   * @param key 防抖键值（用于区分不同的防抖任务）
   * @param callback 回调函数
   * @param config 防抖配置
   * @returns 防抖函数
   */
  static debouncedLayoutUpdate(
    key: string,
    callback: () => void, 
    config: Partial<DebounceConfig> = {}
  ): () => void {
    const opts = { ...this.DEFAULT_DEBOUNCE_CONFIG, ...config }

    return () => {
      // 清除之前的定时器
      const existingTimer = this.debounceTimers.get(key)
      if (existingTimer) {
        (globalThis.clearTimeout || clearTimeout)(existingTimer)
      }

      // 如果设置了立即执行
      if (opts.immediate && !existingTimer) {
        callback()
      }

      // 设置新的定时器
      const timer = (globalThis.setTimeout || setTimeout)(() => {
        callback()
        this.debounceTimers.delete(key)
      }, opts.delay)

      this.debounceTimers.set(key, timer)
    }
  }

  /**
   * 检测容器尺寸变化
   * @param element 要监听的元素
   * @param callback 尺寸变化回调
   * @param config 观察器配置
   * @returns 清理函数
   */
  static observeContainerResize(
    element: HTMLElement, 
    callback: (size: ContainerSize) => void,
    config: Partial<ResizeObserverConfig> = {}
  ): () => void {
    const opts = { ...this.DEFAULT_RESIZE_CONFIG, ...config }

    // 如果浏览器不支持ResizeObserver，使用降级方案
    if (!window.ResizeObserver) {
      return this.fallbackResizeObserver(element, callback, opts)
    }

    let debouncedCallback = callback

    // 如果启用防抖
    if (opts.debounce) {
      const debounceKey = `resize-${Date.now()}-${Math.random()}`
      debouncedCallback = this.debouncedLayoutUpdate(
        debounceKey,
        () => {
          const rect = element.getBoundingClientRect()
          callback({
            width: rect.width,
            height: rect.height
          })
        },
        { delay: opts.debounceDelay }
      )
    }

    // 创建ResizeObserver
    const observer = new ResizeObserver((entries) => {
      for (const entry of entries) {
        const { width, height } = entry.contentRect
        if (opts.debounce) {
          (debouncedCallback as () => void)()
        } else {
          callback({ width, height })
        }
      }
    })

    // 开始观察
    observer.observe(element)
    this.resizeObservers.set(element, observer)

    // 如果设置了立即触发
    if (opts.immediate) {
      const rect = element.getBoundingClientRect()
      callback({
        width: rect.width,
        height: rect.height
      })
    }

    // 返回清理函数
    return () => {
      observer.disconnect()
      this.resizeObservers.delete(element)
    }
  }

  /**
   * 获取当前断点
   * @param width 屏幕宽度
   * @param breakpoints 自定义断点
   * @returns 当前断点名称
   */
  static getCurrentBreakpoint(
    width: number, 
    breakpoints: Partial<ResponsiveBreakpoints> = {}
  ): keyof ResponsiveBreakpoints {
    const bp = { ...this.DEFAULT_BREAKPOINTS, ...breakpoints }

    if (width >= bp.xl) return 'xl'
    if (width >= bp.lg) return 'lg'
    if (width >= bp.md) return 'md'
    if (width >= bp.sm) return 'sm'
    return 'xs'
  }

  /**
   * 检查是否为移动设备
   * @param width 屏幕宽度
   * @returns 是否为移动设备
   */
  static isMobile(width: number = window.innerWidth): boolean {
    return width < this.DEFAULT_BREAKPOINTS.md
  }

  /**
   * 检查是否为平板设备
   * @param width 屏幕宽度
   * @returns 是否为平板设备
   */
  static isTablet(width: number = window.innerWidth): boolean {
    return width >= this.DEFAULT_BREAKPOINTS.md && width < this.DEFAULT_BREAKPOINTS.lg
  }

  /**
   * 检查是否为桌面设备
   * @param width 屏幕宽度
   * @returns 是否为桌面设备
   */
  static isDesktop(width: number = window.innerWidth): boolean {
    return width >= this.DEFAULT_BREAKPOINTS.lg
  }

  /**
   * 计算虚拟滚动参数
   * @param containerHeight 容器高度
   * @param itemHeight 项目高度
   * @param scrollTop 滚动位置
   * @param overscan 预渲染项目数
   * @returns 可见范围
   */
  static calculateVirtualScrollRange(
    containerHeight: number,
    itemHeight: number,
    scrollTop: number,
    overscan: number = 5
  ): { start: number; end: number; visibleStart: number; visibleEnd: number } {
    const visibleStart = Math.floor(scrollTop / itemHeight)
    const visibleEnd = Math.min(
      visibleStart + Math.ceil(containerHeight / itemHeight),
      Number.MAX_SAFE_INTEGER
    )

    return {
      start: Math.max(0, visibleStart - overscan),
      end: visibleEnd + overscan,
      visibleStart,
      visibleEnd
    }
  }

  /**
   * 获取元素的精确尺寸（包括边框和内边距）
   * @param element HTML元素
   * @returns 元素尺寸信息
   */
  static getElementSize(element: HTMLElement): ContainerSize & {
    contentWidth: number
    contentHeight: number
    paddingWidth: number
    paddingHeight: number
    borderWidth: number
    borderHeight: number
  } {
    const rect = element.getBoundingClientRect()
    const computedStyle = window.getComputedStyle(element)
    
    const paddingLeft = parseFloat(computedStyle.paddingLeft) || 0
    const paddingRight = parseFloat(computedStyle.paddingRight) || 0
    const paddingTop = parseFloat(computedStyle.paddingTop) || 0
    const paddingBottom = parseFloat(computedStyle.paddingBottom) || 0
    
    const borderLeft = parseFloat(computedStyle.borderLeftWidth) || 0
    const borderRight = parseFloat(computedStyle.borderRightWidth) || 0
    const borderTop = parseFloat(computedStyle.borderTopWidth) || 0
    const borderBottom = parseFloat(computedStyle.borderBottomWidth) || 0

    const paddingWidth = paddingLeft + paddingRight
    const paddingHeight = paddingTop + paddingBottom
    const borderWidth = borderLeft + borderRight
    const borderHeight = borderTop + borderBottom

    return {
      width: rect.width,
      height: rect.height,
      contentWidth: rect.width - paddingWidth - borderWidth,
      contentHeight: rect.height - paddingHeight - borderHeight,
      paddingWidth,
      paddingHeight,
      borderWidth,
      borderHeight
    }
  }

  /**
   * 清理所有防抖定时器
   */
  static clearAllDebounceTimers(): void {
    this.debounceTimers.forEach(timer => (globalThis.clearTimeout || clearTimeout)(timer))
    this.debounceTimers.clear()
  }

  /**
   * 清理所有ResizeObserver
   */
  static clearAllResizeObservers(): void {
    this.resizeObservers.forEach(observer => observer.disconnect())
    this.resizeObservers.clear()
  }

  // 私有方法：ResizeObserver的降级方案
  private static fallbackResizeObserver(
    element: HTMLElement,
    callback: (size: ContainerSize) => void,
    config: ResizeObserverConfig
  ): () => void {
    let lastSize = { width: 0, height: 0 }
    
    const checkResize = () => {
      const rect = element.getBoundingClientRect()
      const currentSize = { width: rect.width, height: rect.height }
      
      if (currentSize.width !== lastSize.width || currentSize.height !== lastSize.height) {
        lastSize = currentSize
        callback(currentSize)
      }
    }

    // 使用定时器定期检查
    const interval = (globalThis.setInterval || setInterval)(checkResize, config.debounceDelay)
    
    // 监听窗口大小变化
    const handleResize = config.debounce 
      ? this.debouncedLayoutUpdate('window-resize', checkResize, { delay: config.debounceDelay })
      : checkResize

    window.addEventListener('resize', handleResize)

    // 立即检查一次
    if (config.immediate) {
      checkResize()
    }

    // 返回清理函数
    return () => {
      (globalThis.clearInterval || clearInterval)(interval)
      window.removeEventListener('resize', handleResize)
    }
  }
}

// 导出默认实例
export default LayoutUtils