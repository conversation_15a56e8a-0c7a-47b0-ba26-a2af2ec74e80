import { indexedDBService } from './indexedDB'
import { SerializationUtils } from './serialization'
import { Bookmark, Category, Tag } from '../types'

/**
 * 数据迁移和备份服务
 * 提供数据版本管理、迁移和备份恢复功能
 */
export class DataMigrationService {
  
  /**
   * 当前数据版本
   */
  private static readonly CURRENT_VERSION = '1.0.0'
  
  /**
   * 版本设置键
   */
  private static readonly VERSION_KEY = 'data_version'

  /**
   * 检查并执行数据迁移
   * @returns Promise<void>
   */
  static async checkAndMigrate(): Promise<void> {
    try {
      await indexedDBService.init()
      
      const currentVersion = await indexedDBService.getSetting(this.VERSION_KEY)
      
      if (!currentVersion) {
        // 首次安装，设置当前版本
        await this.initializeFirstTime()
      } else if (currentVersion !== this.CURRENT_VERSION) {
        // 需要迁移
        await this.migrateData(currentVersion, this.CURRENT_VERSION)
      }
      
      console.log(`数据版本检查完成，当前版本: ${this.CURRENT_VERSION}`)
    } catch (error) {
      console.error('数据迁移检查失败:', error)
      throw error
    }
  }

  /**
   * 首次安装初始化
   * @returns Promise<void>
   */
  private static async initializeFirstTime(): Promise<void> {
    console.log('首次安装，初始化数据...')
    
    // 创建默认分类
    const defaultCategory = {
      id: 'default',
      name: '默认分类',
      description: '系统默认分类',
      color: '#6B7280',
      parentId: undefined,
      createdAt: new Date(),
      updatedAt: new Date(),
      bookmarkCount: 0
    }
    
    await indexedDBService.saveCategory(defaultCategory)
    
    // 设置版本号
    await indexedDBService.saveSetting(this.VERSION_KEY, this.CURRENT_VERSION)
    
    console.log('首次安装初始化完成')
  }

  /**
   * 执行数据迁移
   * @param fromVersion 源版本
   * @param toVersion 目标版本
   * @returns Promise<void>
   */
  private static async migrateData(fromVersion: string, toVersion: string): Promise<void> {
    console.log(`开始数据迁移: ${fromVersion} -> ${toVersion}`)
    
    // 根据版本执行相应的迁移逻辑
    // 这里可以添加具体的迁移步骤
    
    // 更新版本号
    await indexedDBService.saveSetting(this.VERSION_KEY, toVersion)
    
    console.log(`数据迁移完成: ${fromVersion} -> ${toVersion}`)
  }

  /**
   * 导出所有数据
   * @returns Promise<string> JSON格式的数据
   */
  static async exportAllData(): Promise<string> {
    try {
      await indexedDBService.init()
      
      const [bookmarks, categories, tags] = await Promise.all([
        indexedDBService.getBookmarks(),
        indexedDBService.getCategories(),
        indexedDBService.getTags()
      ])
      
      const exportData = {
        version: this.CURRENT_VERSION,
        exportDate: new Date().toISOString(),
        data: {
          bookmarks: bookmarks.map(bookmark => ({
            ...bookmark,
            createdAt: bookmark.createdAt.toISOString(),
            updatedAt: bookmark.updatedAt.toISOString(),
            metadata: {
              ...bookmark.metadata,
              publishDate: bookmark.metadata.publishDate?.toISOString()
            }
          })),
          categories: categories.map(category => ({
            ...category,
            createdAt: category.createdAt.toISOString(),
            updatedAt: category.updatedAt.toISOString()
          })),
          tags: tags.map(tag => ({
            ...tag,
            createdAt: tag.createdAt.toISOString(),
            updatedAt: tag.updatedAt.toISOString()
          }))
        }
      }
      
      return JSON.stringify(exportData, null, 2)
    } catch (error) {
      console.error('导出数据失败:', error)
      throw new Error(`导出数据失败: ${error.message}`)
    }
  }

  /**
   * 导入数据
   * @param jsonData JSON格式的数据
   * @param options 导入选项
   * @returns Promise<void>
   */
  static async importData(
    jsonData: string, 
    options: {
      clearExisting?: boolean
      skipDuplicates?: boolean
    } = {}
  ): Promise<void> {
    try {
      const importData = JSON.parse(jsonData)
      
      // 验证数据格式
      if (!importData.data || !importData.version) {
        throw new Error('无效的数据格式')
      }
      
      await indexedDBService.init()
      
      // 如果需要清空现有数据
      if (options.clearExisting) {
        await indexedDBService.clearAllData()
      }
      
      // 导入分类
      if (importData.data.categories) {
        for (const categoryData of importData.data.categories) {
          const category: Category = {
            ...categoryData,
            createdAt: new Date(categoryData.createdAt),
            updatedAt: new Date(categoryData.updatedAt)
          }
          
          if (options.skipDuplicates) {
            const existing = await indexedDBService.getCategory(category.id)
            if (existing) continue
          }
          
          await indexedDBService.saveCategory(category)
        }
      }
      
      // 导入标签
      if (importData.data.tags) {
        for (const tagData of importData.data.tags) {
          const tag: Tag = {
            ...tagData,
            createdAt: new Date(tagData.createdAt),
            updatedAt: new Date(tagData.updatedAt)
          }
          
          if (options.skipDuplicates) {
            const existing = await indexedDBService.getTag(tag.id)
            if (existing) continue
          }
          
          await indexedDBService.saveTag(tag)
        }
      }
      
      // 导入书签
      if (importData.data.bookmarks) {
        for (const bookmarkData of importData.data.bookmarks) {
          const bookmark: Bookmark = {
            ...bookmarkData,
            createdAt: new Date(bookmarkData.createdAt),
            updatedAt: new Date(bookmarkData.updatedAt),
            metadata: {
              ...bookmarkData.metadata,
              publishDate: bookmarkData.metadata.publishDate 
                ? new Date(bookmarkData.metadata.publishDate) 
                : undefined
            }
          }
          
          if (options.skipDuplicates) {
            const existing = await indexedDBService.getBookmark(bookmark.id)
            if (existing) continue
          }
          
          await indexedDBService.saveBookmark(bookmark)
        }
      }
      
      console.log('数据导入完成')
    } catch (error) {
      console.error('导入数据失败:', error)
      throw new Error(`导入数据失败: ${error.message}`)
    }
  }

  /**
   * 创建数据备份
   * @returns Promise<string> 备份文件名
   */
  static async createBackup(): Promise<string> {
    try {
      const exportData = await this.exportAllData()
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-')
      const filename = `universe-bag-backup-${timestamp}.json`
      
      // 在浏览器环境中，我们可以触发下载
      if (typeof window !== 'undefined') {
        const blob = new Blob([exportData], { type: 'application/json' })
        const url = URL.createObjectURL(blob)
        
        const a = document.createElement('a')
        a.href = url
        a.download = filename
        document.body.appendChild(a)
        a.click()
        document.body.removeChild(a)
        
        URL.revokeObjectURL(url)
      }
      
      return filename
    } catch (error) {
      console.error('创建备份失败:', error)
      throw new Error(`创建备份失败: ${error.message}`)
    }
  }

  /**
   * 从备份文件恢复数据
   * @param file 备份文件
   * @param options 恢复选项
   * @returns Promise<void>
   */
  static async restoreFromBackup(
    file: File, 
    options: {
      clearExisting?: boolean
      skipDuplicates?: boolean
    } = {}
  ): Promise<void> {
    try {
      const fileContent = await this.readFileAsText(file)
      await this.importData(fileContent, options)
      console.log('从备份恢复数据完成')
    } catch (error) {
      console.error('从备份恢复数据失败:', error)
      throw new Error(`从备份恢复数据失败: ${error.message}`)
    }
  }

  /**
   * 读取文件内容为文本
   * @param file 文件对象
   * @returns Promise<string>
   */
  private static readFileAsText(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      
      reader.onload = (event) => {
        resolve(event.target?.result as string)
      }
      
      reader.onerror = () => {
        reject(new Error('读取文件失败'))
      }
      
      reader.readAsText(file)
    })
  }

  /**
   * 验证数据完整性
   * @returns Promise<boolean>
   */
  static async validateDataIntegrity(): Promise<boolean> {
    try {
      await indexedDBService.init()
      
      // 检查基本数据结构
      const stats = await indexedDBService.getStats()
      
      // 检查是否有默认分类
      const defaultCategory = await indexedDBService.getCategory('default')
      if (!defaultCategory) {
        console.warn('缺少默认分类')
        return false
      }
      
      // 检查书签的分类引用是否有效
      const bookmarks = await indexedDBService.getBookmarks()
      const categories = await indexedDBService.getCategories()
      const categoryIds = new Set(categories.map(c => c.id))
      
      for (const bookmark of bookmarks) {
        if (!categoryIds.has(bookmark.category)) {
          console.warn(`书签 ${bookmark.id} 引用了不存在的分类: ${bookmark.category}`)
          return false
        }
      }
      
      console.log('数据完整性验证通过', stats)
      return true
    } catch (error) {
      console.error('数据完整性验证失败:', error)
      return false
    }
  }

  /**
   * 修复数据完整性问题
   * @returns Promise<void>
   */
  static async repairDataIntegrity(): Promise<void> {
    try {
      await indexedDBService.init()
      
      // 确保有默认分类
      let defaultCategory = await indexedDBService.getCategory('default')
      if (!defaultCategory) {
        defaultCategory = {
          id: 'default',
          name: '默认分类',
          description: '系统默认分类',
          color: '#6B7280',
          parentId: undefined,
          createdAt: new Date(),
          updatedAt: new Date(),
          bookmarkCount: 0
        }
        await indexedDBService.saveCategory(defaultCategory)
        console.log('创建了默认分类')
      }
      
      // 修复书签的无效分类引用
      const bookmarks = await indexedDBService.getBookmarks()
      const categories = await indexedDBService.getCategories()
      const categoryIds = new Set(categories.map(c => c.id))
      
      for (const bookmark of bookmarks) {
        if (!categoryIds.has(bookmark.category)) {
          console.log(`修复书签 ${bookmark.id} 的分类引用: ${bookmark.category} -> default`)
          await indexedDBService.updateBookmark(bookmark.id, { category: 'default' })
        }
      }
      
      console.log('数据完整性修复完成')
    } catch (error) {
      console.error('数据完整性修复失败:', error)
      throw error
    }
  }

  /**
   * 获取当前数据版本
   * @returns Promise<string>
   */
  static async getCurrentVersion(): Promise<string> {
    try {
      await indexedDBService.init()
      const version = await indexedDBService.getSetting(this.VERSION_KEY)
      return version || '未知'
    } catch (error) {
      console.error('获取数据版本失败:', error)
      return '错误'
    }
  }
}