// 性能优化工具函数 - 提供组件性能优化相关的工具

import { useCallback, useMemo, useRef, useEffect } from 'react'

/**
 * 防抖Hook
 * @param callback 回调函数
 * @param delay 延迟时间（毫秒）
 * @returns 防抖后的函数
 */
export function useDebounce<T extends (...args: any[]) => any>(
  callback: T,
  delay: number
): T {
  const timeoutRef = useRef<NodeJS.Timeout>()

  return useCallback(
    ((...args: Parameters<T>) => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
      }

      timeoutRef.current = setTimeout(() => {
        callback(...args)
      }, delay)
    }) as T,
    [callback, delay]
  )
}

/**
 * 节流Hook
 * @param callback 回调函数
 * @param delay 延迟时间（毫秒）
 * @returns 节流后的函数
 */
export function useThrottle<T extends (...args: any[]) => any>(
  callback: T,
  delay: number
): T {
  const lastCallRef = useRef<number>(0)
  const timeoutRef = useRef<NodeJS.Timeout>()

  return useCallback(
    ((...args: Parameters<T>) => {
      const now = Date.now()
      const timeSinceLastCall = now - lastCallRef.current

      if (timeSinceLastCall >= delay) {
        lastCallRef.current = now
        callback(...args)
      } else {
        if (timeoutRef.current) {
          clearTimeout(timeoutRef.current)
        }
        
        timeoutRef.current = setTimeout(() => {
          lastCallRef.current = Date.now()
          callback(...args)
        }, delay - timeSinceLastCall)
      }
    }) as T,
    [callback, delay]
  )
}

/**
 * 虚拟滚动Hook
 * @param items 所有项目
 * @param itemHeight 每个项目的高度
 * @param containerHeight 容器高度
 * @param overscan 预渲染项目数量
 * @returns 虚拟滚动相关的状态和方法
 */
export function useVirtualScroll<T>(
  items: T[],
  itemHeight: number,
  containerHeight: number,
  overscan: number = 5
) {
  const scrollTop = useRef(0)
  const containerRef = useRef<HTMLDivElement>(null)

  const visibleRange = useMemo(() => {
    const startIndex = Math.floor(scrollTop.current / itemHeight)
    const endIndex = Math.min(
      startIndex + Math.ceil(containerHeight / itemHeight),
      items.length - 1
    )

    return {
      start: Math.max(0, startIndex - overscan),
      end: Math.min(items.length - 1, endIndex + overscan)
    }
  }, [items.length, itemHeight, containerHeight, overscan])

  const visibleItems = useMemo(() => {
    return items.slice(visibleRange.start, visibleRange.end + 1).map((item, index) => ({
      item,
      index: visibleRange.start + index
    }))
  }, [items, visibleRange])

  const totalHeight = items.length * itemHeight

  const handleScroll = useCallback((e: React.UIEvent<HTMLDivElement>) => {
    scrollTop.current = e.currentTarget.scrollTop
  }, [])

  return {
    containerRef,
    visibleItems,
    totalHeight,
    visibleRange,
    handleScroll,
    offsetY: visibleRange.start * itemHeight
  }
}

/**
 * 图片懒加载Hook
 * @param src 图片源
 * @param placeholder 占位图片
 * @returns 图片加载状态和属性
 */
export function useLazyImage(src: string, placeholder?: string) {
  const imgRef = useRef<HTMLImageElement>(null)
  const [isLoaded, setIsLoaded] = useState(false)
  const [isInView, setIsInView] = useState(false)

  useEffect(() => {
    const img = imgRef.current
    if (!img) return

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsInView(true)
          observer.disconnect()
        }
      },
      { threshold: 0.1 }
    )

    observer.observe(img)

    return () => observer.disconnect()
  }, [])

  useEffect(() => {
    if (!isInView) return

    const img = new Image()
    img.onload = () => setIsLoaded(true)
    img.src = src
  }, [isInView, src])

  return {
    ref: imgRef,
    src: isLoaded ? src : placeholder,
    isLoaded,
    isInView
  }
}

/**
 * 内存化选择器Hook
 * @param selector 选择器函数
 * @param dependencies 依赖项
 * @returns 内存化的结果
 */
export function useMemoizedSelector<T, R>(
  selector: (data: T) => R,
  dependencies: T
): R {
  return useMemo(() => selector(dependencies), [selector, dependencies])
}

/**
 * 批量更新Hook
 * @param initialState 初始状态
 * @returns 状态和批量更新函数
 */
export function useBatchUpdate<T extends Record<string, any>>(initialState: T) {
  const [state, setState] = useState(initialState)
  const pendingUpdatesRef = useRef<Partial<T>>({})
  const timeoutRef = useRef<NodeJS.Timeout>()

  const batchUpdate = useCallback((updates: Partial<T>) => {
    pendingUpdatesRef.current = { ...pendingUpdatesRef.current, ...updates }

    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current)
    }

    timeoutRef.current = setTimeout(() => {
      setState(prev => ({ ...prev, ...pendingUpdatesRef.current }))
      pendingUpdatesRef.current = {}
    }, 0)
  }, [])

  const flushUpdates = useCallback(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current)
    }
    
    if (Object.keys(pendingUpdatesRef.current).length > 0) {
      setState(prev => ({ ...prev, ...pendingUpdatesRef.current }))
      pendingUpdatesRef.current = {}
    }
  }, [])

  return { state, batchUpdate, flushUpdates }
}

/**
 * 组件可见性Hook
 * @param threshold 可见性阈值
 * @returns ref和可见性状态
 */
export function useIntersectionObserver(threshold: number = 0.1) {
  const ref = useRef<HTMLElement>(null)
  const [isVisible, setIsVisible] = useState(false)

  useEffect(() => {
    const element = ref.current
    if (!element) return

    const observer = new IntersectionObserver(
      ([entry]) => {
        setIsVisible(entry.isIntersecting)
      },
      { threshold }
    )

    observer.observe(element)

    return () => observer.disconnect()
  }, [threshold])

  return { ref, isVisible }
}

/**
 * 窗口大小Hook
 * @returns 窗口尺寸信息
 */
export function useWindowSize() {
  const [windowSize, setWindowSize] = useState({
    width: typeof window !== 'undefined' ? window.innerWidth : 0,
    height: typeof window !== 'undefined' ? window.innerHeight : 0
  })

  useEffect(() => {
    const handleResize = () => {
      setWindowSize({
        width: window.innerWidth,
        height: window.innerHeight
      })
    }

    window.addEventListener('resize', handleResize)
    return () => window.removeEventListener('resize', handleResize)
  }, [])

  return windowSize
}

/**
 * 媒体查询Hook
 * @param query 媒体查询字符串
 * @returns 是否匹配
 */
export function useMediaQuery(query: string): boolean {
  const [matches, setMatches] = useState(false)

  useEffect(() => {
    // 检查是否在浏览器环境中
    if (typeof window === 'undefined' || !window.matchMedia) {
      return
    }

    const media = window.matchMedia(query)
    
    if (media.matches !== matches) {
      setMatches(media.matches)
    }

    const listener = () => setMatches(media.matches)
    media.addEventListener('change', listener)
    
    return () => media.removeEventListener('change', listener)
  }, [matches, query])

  return matches
}

/**
 * 性能监控Hook
 * @param name 性能标记名称
 * @returns 性能测量函数
 */
export function usePerformanceMonitor(name: string) {
  const startMark = `${name}-start`
  const endMark = `${name}-end`
  const measureName = `${name}-measure`

  const start = useCallback(() => {
    if (typeof performance !== 'undefined') {
      performance.mark(startMark)
    }
  }, [startMark])

  const end = useCallback(() => {
    if (typeof performance !== 'undefined') {
      performance.mark(endMark)
      performance.measure(measureName, startMark, endMark)
      
      const measure = performance.getEntriesByName(measureName)[0]
      console.log(`${name} took ${measure.duration}ms`)
      
      // 清理标记
      performance.clearMarks(startMark)
      performance.clearMarks(endMark)
      performance.clearMeasures(measureName)
    }
  }, [endMark, measureName, startMark, name])

  return { start, end }
}

/**
 * 缓存Hook
 * @param key 缓存键
 * @param fetcher 数据获取函数
 * @param ttl 缓存时间（毫秒）
 * @returns 缓存的数据和状态
 */
export function useCache<T>(
  key: string,
  fetcher: () => Promise<T>,
  ttl: number = 5 * 60 * 1000 // 5分钟
) {
  const [data, setData] = useState<T | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<Error | null>(null)
  const cacheRef = useRef<Map<string, { data: T; timestamp: number }>>(new Map())

  const fetchData = useCallback(async () => {
    const cached = cacheRef.current.get(key)
    const now = Date.now()

    // 检查缓存是否有效
    if (cached && (now - cached.timestamp) < ttl) {
      setData(cached.data)
      return cached.data
    }

    setLoading(true)
    setError(null)

    try {
      const result = await fetcher()
      
      // 更新缓存
      cacheRef.current.set(key, { data: result, timestamp: now })
      setData(result)
      
      return result
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Unknown error')
      setError(error)
      throw error
    } finally {
      setLoading(false)
    }
  }, [key, fetcher, ttl])

  const invalidate = useCallback(() => {
    cacheRef.current.delete(key)
    setData(null)
  }, [key])

  useEffect(() => {
    fetchData()
  }, [fetchData])

  return { data, loading, error, refetch: fetchData, invalidate }
}

// 导入useState
import { useState } from 'react'