import { 
  Bookmark, 
  BookmarkInput, 
  Category, 
  CategoryInput, 
  Tag, 
  TagInput,
  ValidationResult,
  ValidationError,
  ImportData,
  ExportAllOptions,
  ExportCategoriesOptions,
  ExportTagsOptions,
  ConflictItem,
  ConflictResolution
} from '../types'

/**
 * 验证规则常量
 */
const VALIDATION_RULES = {
  TITLE_MAX_LENGTH: 500,
  CONTENT_MAX_LENGTH: 10000,
  DESCRIPTION_MAX_LENGTH: 1000,
  CATEGORY_NAME_MAX_LENGTH: 100,
  CATEGORY_DESCRIPTION_MAX_LENGTH: 500,
  TAG_NAME_MAX_LENGTH: 50,
  MAX_TAGS_COUNT: 20
} as const

/**
 * 正则表达式常量
 */
const REGEX_PATTERNS = {
  HEX_COLOR: /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/,
  RGB_COLOR: /^rgb\(\s*\d{1,3}\s*,\s*\d{1,3}\s*,\s*\d{1,3}\s*\)$/,
  RGBA_COLOR: /^rgba\(\s*\d{1,3}\s*,\s*\d{1,3}\s*,\s*\d{1,3}\s*,\s*(0|1|0?\.\d+)\s*\)$/
} as const

/**
 * 验证器接口
 */
interface Validator<T> {
  validate(input: T): ValidationResult
}

/**
 * 基础验证器抽象类
 */
abstract class BaseValidator<T> implements Validator<T> {
  protected errors: ValidationError[] = []

  abstract validate(input: T): ValidationResult

  protected addError(field: string, message: string, code: string): void {
    this.errors.push({ field, message, code })
  }

  protected resetErrors(): void {
    this.errors = []
  }

  protected createResult(): ValidationResult {
    return {
      isValid: this.errors.length === 0,
      errors: [...this.errors]
    }
  }

  /**
   * 验证必填字段
   */
  protected validateRequired(value: string | undefined, field: string, fieldName: string): boolean {
    if (!value || value.trim().length === 0) {
      this.addError(field, `${fieldName}不能为空`, `${field.toUpperCase()}_REQUIRED`)
      return false
    }
    return true
  }

  /**
   * 验证字符串长度
   */
  protected validateLength(
    value: string | undefined, 
    maxLength: number, 
    field: string, 
    fieldName: string
  ): boolean {
    if (value && value.length > maxLength) {
      this.addError(field, `${fieldName}长度不能超过${maxLength}个字符`, `${field.toUpperCase()}_TOO_LONG`)
      return false
    }
    return true
  }

  /**
   * 验证URL格式
   */
  protected validateUrl(url: string): boolean {
    try {
      new URL(url)
      return true
    } catch {
      return false
    }
  }

  /**
   * 验证颜色格式
   */
  protected validateColor(color: string): boolean {
    return REGEX_PATTERNS.HEX_COLOR.test(color) ||
           REGEX_PATTERNS.RGB_COLOR.test(color) ||
           REGEX_PATTERNS.RGBA_COLOR.test(color)
  }
}

/**
 * 书签验证器
 */
class BookmarkValidator extends BaseValidator<BookmarkInput> {
  validate(input: BookmarkInput): ValidationResult {
    if (!input) {
      return { isValid: false, errors: [{ field: 'input', message: '输入数据不能为空', code: 'INPUT_NULL' }] }
    }

    this.resetErrors()

    // 验证标题
    this.validateTitle(input.title)
    
    // 验证类型
    this.validateType(input.type)
    
    // 根据类型验证特定字段
    this.validateTypeSpecificFields(input)
    
    // 验证描述
    this.validateDescription(input.description)
    
    // 验证标签
    this.validateTags(input.tags)

    return this.createResult()
  }

  private validateTitle(title: string): void {
    this.validateRequired(title, 'title', '标题')
    this.validateLength(title, VALIDATION_RULES.TITLE_MAX_LENGTH, 'title', '标题')
  }

  private validateType(type: string): void {
    if (!['url', 'text', 'image'].includes(type)) {
      this.addError('type', '无效的书签类型', 'INVALID_TYPE')
    }
  }

  private validateTypeSpecificFields(input: BookmarkInput): void {
    switch (input.type) {
      case 'url':
        this.validateUrlField(input.url)
        break
      case 'text':
        this.validateContentField(input.content)
        break
      case 'image':
        // 图片类型的特殊验证逻辑
        break
    }
  }

  private validateUrlField(url: string | undefined): void {
    if (!this.validateRequired(url, 'url', 'URL')) return
    
    if (url && !this.validateUrl(url)) {
      this.addError('url', '无效的URL格式', 'INVALID_URL')
    }
  }

  private validateContentField(content: string | undefined): void {
    if (!this.validateRequired(content, 'content', '内容')) return
    
    this.validateLength(content, VALIDATION_RULES.CONTENT_MAX_LENGTH, 'content', '内容')
  }

  private validateDescription(description: string | undefined): void {
    this.validateLength(description, VALIDATION_RULES.DESCRIPTION_MAX_LENGTH, 'description', '描述')
  }

  private validateTags(tags: string[] | undefined): void {
    if (!tags) return

    if (tags.length > VALIDATION_RULES.MAX_TAGS_COUNT) {
      this.addError('tags', `标签数量不能超过${VALIDATION_RULES.MAX_TAGS_COUNT}个`, 'TOO_MANY_TAGS')
      return
    }

    for (const tag of tags) {
      if (tag.length > VALIDATION_RULES.TAG_NAME_MAX_LENGTH) {
        this.addError('tags', `单个标签长度不能超过${VALIDATION_RULES.TAG_NAME_MAX_LENGTH}个字符`, 'TAG_TOO_LONG')
        break
      }
    }
  }
}

/**
 * 分类验证器
 */
class CategoryValidator extends BaseValidator<CategoryInput> {
  validate(input: CategoryInput): ValidationResult {
    if (!input) {
      return { isValid: false, errors: [{ field: 'input', message: '输入数据不能为空', code: 'INPUT_NULL' }] }
    }

    this.resetErrors()

    // 验证名称
    this.validateRequired(input.name, 'name', '分类名称')
    this.validateLength(input.name, VALIDATION_RULES.CATEGORY_NAME_MAX_LENGTH, 'name', '分类名称')

    // 验证描述
    this.validateLength(input.description, VALIDATION_RULES.CATEGORY_DESCRIPTION_MAX_LENGTH, 'description', '描述')

    // 验证颜色
    if (input.color && !this.validateColor(input.color)) {
      this.addError('color', '无效的颜色格式', 'INVALID_COLOR')
    }

    return this.createResult()
  }
}

/**
 * 标签验证器
 */
class TagValidator extends BaseValidator<TagInput> {
  validate(input: TagInput): ValidationResult {
    if (!input) {
      return { isValid: false, errors: [{ field: 'input', message: '输入数据不能为空', code: 'INPUT_NULL' }] }
    }

    this.resetErrors()

    // 验证名称
    this.validateRequired(input.name, 'name', '标签名称')
    this.validateLength(input.name, VALIDATION_RULES.TAG_NAME_MAX_LENGTH, 'name', '标签名称')

    // 验证颜色
    if (input.color && !this.validateColor(input.color)) {
      this.addError('color', '无效的颜色格式', 'INVALID_COLOR')
    }

    return this.createResult()
  }
}

/**
 * 数据清理器
 */
class DataSanitizer {
  /**
   * 清理字符串字段
   */
  private static sanitizeString(value: string | undefined): string | undefined {
    return value?.trim() || undefined
  }

  /**
   * 清理字符串数组
   */
  private static sanitizeStringArray(values: string[] | undefined): string[] {
    return values?.map(v => v.trim()).filter(v => v.length > 0) || []
  }

  /**
   * 清理书签数据
   */
  static sanitizeBookmarkInput(input: BookmarkInput): BookmarkInput {
    return {
      ...input,
      title: this.sanitizeString(input.title) || '',
      url: this.sanitizeString(input.url),
      content: this.sanitizeString(input.content),
      description: this.sanitizeString(input.description),
      tags: this.sanitizeStringArray(input.tags),
      category: this.sanitizeString(input.category) || 'default'
    }
  }

  /**
   * 清理分类数据
   */
  static sanitizeCategoryInput(input: CategoryInput): CategoryInput {
    return {
      ...input,
      name: this.sanitizeString(input.name) || '',
      description: this.sanitizeString(input.description),
      color: this.sanitizeString(input.color),
      parentId: this.sanitizeString(input.parentId)
    }
  }

  /**
   * 清理标签数据
   */
  static sanitizeTagInput(input: TagInput): TagInput {
    return {
      ...input,
      name: this.sanitizeString(input.name) || '',
      color: this.sanitizeString(input.color)
    }
  }
}

/**
 * 验证工具类
 * 提供数据模型的验证功能
 */
export class ValidationUtils {
  private static bookmarkValidator = new BookmarkValidator()
  private static categoryValidator = new CategoryValidator()
  private static tagValidator = new TagValidator()

  /**
   * 验证书签输入数据
   * @param input 书签输入数据
   * @returns 验证结果
   */
  static validateBookmarkInput(input: BookmarkInput): ValidationResult {
    return this.bookmarkValidator.validate(input)
  }

  /**
   * 验证分类输入数据
   * @param input 分类输入数据
   * @returns 验证结果
   */
  static validateCategoryInput(input: CategoryInput): ValidationResult {
    return this.categoryValidator.validate(input)
  }

  /**
   * 验证标签输入数据
   * @param input 标签输入数据
   * @returns 验证结果
   */
  static validateTagInput(input: TagInput): ValidationResult {
    return this.tagValidator.validate(input)
  }

  /**
   * 批量验证书签数据
   * @param inputs 书签输入数据数组
   * @returns 批量验证结果
   */
  static validateBookmarkInputBatch(inputs: BookmarkInput[]): Array<{index: number, result: ValidationResult}> {
    return inputs.map((input, index) => ({
      index,
      result: this.validateBookmarkInput(input)
    }))
  }

  /**
   * 验证导入数据结构
   * @param data 导入数据
   * @returns 验证结果
   */
  static validateImportData(data: ImportData): ValidationResult {
    const errors: ValidationError[] = []

    // 验证版本信息
    if (!data.version) {
      errors.push({
        field: 'version',
        message: '缺少版本信息',
        code: 'VERSION_REQUIRED'
      })
    }

    // 验证导出日期
    if (!data.exportDate) {
      errors.push({
        field: 'exportDate',
        message: '缺少导出日期',
        code: 'EXPORT_DATE_REQUIRED'
      })
    }

    // 验证元数据
    if (!data.metadata) {
      errors.push({
        field: 'metadata',
        message: '缺少元数据信息',
        code: 'METADATA_REQUIRED'
      })
    }

    // 验证数据内容
    const hasData = data.bookmarks?.length || data.categories?.length || data.tags?.length
    if (!hasData) {
      errors.push({
        field: 'data',
        message: '导入数据不能为空',
        code: 'NO_DATA'
      })
    }

    return {
      isValid: errors.length === 0,
      errors
    }
  }

  /**
   * 清理和标准化书签数据
   * @param input 原始输入数据
   * @returns 清理后的数据
   */
  static sanitizeBookmarkInput(input: BookmarkInput): BookmarkInput {
    return DataSanitizer.sanitizeBookmarkInput(input)
  }

  /**
   * 清理和标准化分类数据
   * @param input 原始输入数据
   * @returns 清理后的数据
   */
  static sanitizeCategoryInput(input: CategoryInput): CategoryInput {
    return DataSanitizer.sanitizeCategoryInput(input)
  }

  /**
   * 清理和标准化标签数据
   * @param input 原始输入数据
   * @returns 清理后的数据
   */
  static sanitizeTagInput(input: TagInput): TagInput {
    return DataSanitizer.sanitizeTagInput(input)
  }

  /**
   * 获取验证规则配置
   * @returns 验证规则常量
   */
  static getValidationRules() {
    return VALIDATION_RULES
  }
}