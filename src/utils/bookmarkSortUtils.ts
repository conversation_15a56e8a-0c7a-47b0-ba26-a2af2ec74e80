// 收藏排序工具函数

import type { BookmarkSortOption } from '../components/BookmarkSortSelector'

/**
 * 收藏排序工具类
 * 提供各种排序方式的实现
 */
export class BookmarkSortUtils {
  
  /**
   * 对收藏数组进行排序
   * @param bookmarks 收藏数组
   * @param sortOption 排序选项
   * @returns 排序后的收藏数组
   */
  static sortBookmarks<T extends any>(bookmarks: T[], sortOption: BookmarkSortOption): T[] {
    // 创建副本避免修改原数组
    const sortedBookmarks = [...bookmarks]
    
    switch (sortOption) {
      case 'created-desc':
        return sortedBookmarks.sort((a, b) => {
          const dateA = new Date(a.createdAt).getTime()
          const dateB = new Date(b.createdAt).getTime()
          return dateB - dateA // 降序：最新在前
        })
        
      case 'created-asc':
        return sortedBookmarks.sort((a, b) => {
          const dateA = new Date(a.createdAt).getTime()
          const dateB = new Date(b.createdAt).getTime()
          return dateA - dateB // 升序：最旧在前
        })
        
      case 'updated-desc':
        return sortedBookmarks.sort((a, b) => {
          const dateA = new Date(a.updatedAt || a.createdAt).getTime()
          const dateB = new Date(b.updatedAt || b.createdAt).getTime()
          return dateB - dateA // 降序：最近更新在前
        })
        
      case 'updated-asc':
        return sortedBookmarks.sort((a, b) => {
          const dateA = new Date(a.updatedAt || a.createdAt).getTime()
          const dateB = new Date(b.updatedAt || b.createdAt).getTime()
          return dateA - dateB // 升序：最久未更新在前
        })
        
      case 'title-asc':
        return sortedBookmarks.sort((a, b) => {
          const titleA = (a.title || '').toLowerCase()
          const titleB = (b.title || '').toLowerCase()
          return titleA.localeCompare(titleB, 'zh-CN', { numeric: true }) // 升序：A-Z
        })
        
      case 'title-desc':
        return sortedBookmarks.sort((a, b) => {
          const titleA = (a.title || '').toLowerCase()
          const titleB = (b.title || '').toLowerCase()
          return titleB.localeCompare(titleA, 'zh-CN', { numeric: true }) // 降序：Z-A
        })
        
      case 'category-asc':
        return sortedBookmarks.sort((a, b) => {
          const categoryA = (a.category || '未分类').toLowerCase()
          const categoryB = (b.category || '未分类').toLowerCase()
          // 先按分类排序，分类相同时按创建时间降序
          const categoryCompare = categoryA.localeCompare(categoryB, 'zh-CN', { numeric: true })
          if (categoryCompare !== 0) {
            return categoryCompare
          }
          // 分类相同时按创建时间降序
          const dateA = new Date(a.createdAt).getTime()
          const dateB = new Date(b.createdAt).getTime()
          return dateB - dateA
        })
        
      case 'category-desc':
        return sortedBookmarks.sort((a, b) => {
          const categoryA = (a.category || '未分类').toLowerCase()
          const categoryB = (b.category || '未分类').toLowerCase()
          // 先按分类排序，分类相同时按创建时间降序
          const categoryCompare = categoryB.localeCompare(categoryA, 'zh-CN', { numeric: true })
          if (categoryCompare !== 0) {
            return categoryCompare
          }
          // 分类相同时按创建时间降序
          const dateA = new Date(a.createdAt).getTime()
          const dateB = new Date(b.createdAt).getTime()
          return dateB - dateA
        })
        
      default:
        // 默认按创建时间降序
        return sortedBookmarks.sort((a, b) => {
          const dateA = new Date(a.createdAt).getTime()
          const dateB = new Date(b.createdAt).getTime()
          return dateB - dateA
        })
    }
  }
  
  /**
   * 获取排序选项的显示名称
   * @param sortOption 排序选项
   * @returns 显示名称
   */
  static getSortOptionLabel(sortOption: BookmarkSortOption): string {
    const labels: Record<BookmarkSortOption, string> = {
      'created-desc': '最新添加',
      'created-asc': '最早添加',
      'updated-desc': '最近更新',
      'updated-asc': '最久未更新',
      'title-asc': '标题 A-Z',
      'title-desc': '标题 Z-A',
      'category-asc': '分类 A-Z',
      'category-desc': '分类 Z-A'
    }
    
    return labels[sortOption] || '最新添加'
  }
  
  /**
   * 获取排序选项的描述
   * @param sortOption 排序选项
   * @returns 描述文本
   */
  static getSortOptionDescription(sortOption: BookmarkSortOption): string {
    const descriptions: Record<BookmarkSortOption, string> = {
      'created-desc': '按创建时间降序，最新添加的收藏显示在前面',
      'created-asc': '按创建时间升序，最早添加的收藏显示在前面',
      'updated-desc': '按更新时间降序，最近更新的收藏显示在前面',
      'updated-asc': '按更新时间升序，最久未更新的收藏显示在前面',
      'title-asc': '按标题字母顺序升序排列',
      'title-desc': '按标题字母顺序降序排列',
      'category-asc': '按分类字母顺序升序排列，同分类内按创建时间降序',
      'category-desc': '按分类字母顺序降序排列，同分类内按创建时间降序'
    }
    
    return descriptions[sortOption] || '按创建时间降序排列'
  }
  
  /**
   * 验证排序选项是否有效
   * @param sortOption 排序选项
   * @returns 是否有效
   */
  static isValidSortOption(sortOption: string): sortOption is BookmarkSortOption {
    const validOptions: BookmarkSortOption[] = [
      'created-desc', 'created-asc',
      'updated-desc', 'updated-asc',
      'title-asc', 'title-desc',
      'category-asc', 'category-desc'
    ]
    
    return validOptions.includes(sortOption as BookmarkSortOption)
  }
  
  /**
   * 获取默认排序选项
   * @returns 默认排序选项
   */
  static getDefaultSortOption(): BookmarkSortOption {
    return 'created-desc'
  }
}

export default BookmarkSortUtils