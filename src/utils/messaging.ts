// 消息通信工具函数

import {
  ExtensionMessage,
  ExtensionMessageResponse,
  MessageResponse
} from '../types/messages'

/**
 * 消息发送器类
 * 提供统一的消息发送接口
 */
export class MessageSender {
  private static requestIdCounter = 0

  /**
   * 生成请求ID
   */
  private static generateRequestId(): string {
    return `req_${Date.now()}_${++this.requestIdCounter}`
  }

  /**
   * 发送消息到background script
   */
  static async sendToBackground<T extends ExtensionMessage, R extends MessageResponse>(
    message: Omit<T, 'requestId' | 'timestamp'>
  ): Promise<R> {
    const fullMessage: T = {
      ...message,
      requestId: this.generateRequestId(),
      timestamp: Date.now()
    } as T

    try {
      console.log('发送消息到background:', fullMessage.type, fullMessage.data)
      
      const response = await chrome.runtime.sendMessage(fullMessage)
      
      if (!response) {
        throw new Error('未收到响应')
      }

      if (!response.success) {
        throw new Error(response.error || '操作失败')
      }

      console.log('收到background响应:', response)
      return response as R
    } catch (error) {
      console.error('发送消息失败:', error)
      throw new Error(`消息发送失败: ${error}`)
    }
  }

  /**
   * 发送消息到content script
   */
  static async sendToContent<T extends ExtensionMessage, R extends MessageResponse>(
    tabId: number,
    message: Omit<T, 'requestId' | 'timestamp'>
  ): Promise<R> {
    const fullMessage: T = {
      ...message,
      requestId: this.generateRequestId(),
      timestamp: Date.now()
    } as T

    try {
      console.log('发送消息到content script:', fullMessage.type, fullMessage.data)
      
      const response = await chrome.tabs.sendMessage(tabId, fullMessage)
      
      if (!response) {
        throw new Error('未收到响应')
      }

      if (!response.success) {
        throw new Error(response.error || '操作失败')
      }

      console.log('收到content script响应:', response)
      return response as R
    } catch (error) {
      console.error('发送消息到content script失败:', error)
      throw new Error(`消息发送失败: ${error}`)
    }
  }

  /**
   * 发送消息到popup
   */
  static async sendToPopup<T extends ExtensionMessage, R extends MessageResponse>(
    message: Omit<T, 'requestId' | 'timestamp'>
  ): Promise<R> {
    const fullMessage: T = {
      ...message,
      requestId: this.generateRequestId(),
      timestamp: Date.now()
    } as T

    try {
      console.log('发送消息到popup:', fullMessage.type, fullMessage.data)
      
      // 获取popup窗口
      const views = chrome.extension.getViews({ type: 'popup' })
      if (views.length === 0) {
        throw new Error('Popup未打开')
      }

      // 这里需要实现popup的消息接收机制
      // 暂时抛出错误，提醒需要实现
      throw new Error('Popup消息发送功能待实现')
    } catch (error) {
      console.error('发送消息到popup失败:', error)
      throw new Error(`消息发送失败: ${error}`)
    }
  }

  /**
   * 广播消息到所有content scripts
   */
  static async broadcastToAllTabs<T extends ExtensionMessage>(
    message: Omit<T, 'requestId' | 'timestamp'>
  ): Promise<void> {
    const fullMessage: T = {
      ...message,
      requestId: this.generateRequestId(),
      timestamp: Date.now()
    } as T

    try {
      console.log('广播消息到所有标签页:', fullMessage.type, fullMessage.data)
      
      const tabs = await chrome.tabs.query({})
      const promises = tabs.map(tab => {
        if (tab.id) {
          return chrome.tabs.sendMessage(tab.id, fullMessage).catch(error => {
            // 忽略无法发送的标签页（如chrome://页面）
            console.warn(`无法向标签页 ${tab.id} 发送消息:`, error)
          })
        }
      })

      await Promise.allSettled(promises)
      console.log('广播消息完成')
    } catch (error) {
      console.error('广播消息失败:', error)
      throw new Error(`广播消息失败: ${error}`)
    }
  }
}

/**
 * 消息接收器类
 * 提供统一的消息接收处理
 */
export class MessageReceiver {
  private handlers: Map<string, Function> = new Map()

  /**
   * 注册消息处理器
   */
  registerHandler<T extends ExtensionMessage, R extends MessageResponse>(
    messageType: T['type'],
    handler: (message: T, sender: chrome.runtime.MessageSender) => Promise<R>
  ): void {
    this.handlers.set(messageType, handler)
    console.log(`注册消息处理器: ${messageType}`)
  }

  /**
   * 移除消息处理器
   */
  unregisterHandler(messageType: string): void {
    this.handlers.delete(messageType)
    console.log(`移除消息处理器: ${messageType}`)
  }

  /**
   * 处理接收到的消息
   */
  async handleMessage(
    message: ExtensionMessage,
    sender: chrome.runtime.MessageSender
  ): Promise<ExtensionMessageResponse> {
    try {
      const handler = this.handlers.get(message.type)
      
      if (!handler) {
        return {
          success: false,
          error: `未找到消息处理器: ${message.type}`,
          requestId: message.requestId
        }
      }

      console.log(`处理消息: ${message.type}`, message.data)
      const response = await handler(message, sender)
      
      // 确保响应包含请求ID
      if (message.requestId) {
        response.requestId = message.requestId
      }

      return response
    } catch (error) {
      console.error(`处理消息失败 [${message.type}]:`, error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '未知错误',
        requestId: message.requestId
      }
    }
  }

  /**
   * 启动消息监听
   */
  startListening(): void {
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
      // 异步处理消息
      this.handleMessage(message, sender)
        .then(response => sendResponse(response))
        .catch(error => {
          console.error('消息处理异常:', error)
          sendResponse({
            success: false,
            error: error.message || '消息处理异常',
            requestId: message.requestId
          })
        })
      
      // 返回true表示异步响应
      return true
    })
    
    console.log('消息监听器已启动')
  }

  /**
   * 停止消息监听
   */
  stopListening(): void {
    // Chrome扩展API不提供直接移除监听器的方法
    // 这里只是清空处理器映射
    this.handlers.clear()
    console.log('消息监听器已停止')
  }
}

/**
 * 消息验证工具
 */
export class MessageValidator {
  /**
   * 验证消息格式
   */
  static validateMessage(message: any): { isValid: boolean; errors: string[] } {
    const errors: string[] = []

    if (!message || typeof message !== 'object') {
      errors.push('消息必须是对象')
      return { isValid: false, errors }
    }

    if (!message.type || typeof message.type !== 'string') {
      errors.push('消息类型必须是字符串')
    }

    if (message.requestId && typeof message.requestId !== 'string') {
      errors.push('请求ID必须是字符串')
    }

    if (message.timestamp && typeof message.timestamp !== 'number') {
      errors.push('时间戳必须是数字')
    }

    return {
      isValid: errors.length === 0,
      errors
    }
  }

  /**
   * 验证响应格式
   */
  static validateResponse(response: any): { isValid: boolean; errors: string[] } {
    const errors: string[] = []

    if (!response || typeof response !== 'object') {
      errors.push('响应必须是对象')
      return { isValid: false, errors }
    }

    if (typeof response.success !== 'boolean') {
      errors.push('success字段必须是布尔值')
    }

    if (response.error && typeof response.error !== 'string') {
      errors.push('error字段必须是字符串')
    }

    if (response.requestId && typeof response.requestId !== 'string') {
      errors.push('requestId字段必须是字符串')
    }

    return {
      isValid: errors.length === 0,
      errors
    }
  }
}

/**
 * 便捷的消息发送函数
 */

// 发送Ping消息
export async function sendPing(): Promise<MessageResponse> {
  return MessageSender.sendToBackground({
    type: 'PING'
  })
}

// 获取设置
export async function getSettings(): Promise<MessageResponse<Record<string, any>>> {
  return MessageSender.sendToBackground({
    type: 'GET_SETTINGS'
  })
}

// 更新设置
export async function updateSettings(settings: Record<string, any>): Promise<MessageResponse> {
  return MessageSender.sendToBackground({
    type: 'UPDATE_SETTINGS',
    data: settings
  })
}

// 检查收藏状态
export async function checkBookmarkStatus(url: string): Promise<MessageResponse<{ isBookmarked: boolean; bookmarkId?: string }>> {
  return MessageSender.sendToBackground({
    type: 'CHECK_BOOKMARK_STATUS',
    data: { url }
  })
}

// 快速收藏
export async function quickBookmark(data: {
  title: string
  url: string
  favIconUrl?: string
  selectedText?: string
}): Promise<MessageResponse<{ bookmarkId: string }>> {
  return MessageSender.sendToBackground({
    type: 'QUICK_BOOKMARK',
    data
  })
}

// AI生成建议
export async function generateAISuggestions(data: {
  content: string
  url?: string
  title?: string
}): Promise<MessageResponse> {
  return MessageSender.sendToBackground({
    type: 'AI_GENERATE_SUGGESTIONS',
    data
  })
}