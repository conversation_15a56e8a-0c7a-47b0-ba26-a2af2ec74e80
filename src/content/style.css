/* Universe Bag Content Script 样式 */

/* 浮窗相关样式（后续任务会用到） */
.universe-bag-floating-widget {
  position: fixed;
  z-index: 2147483647; /* 最高层级 */
  pointer-events: auto;
}

/* 确保不影响页面原有样式 */
.universe-bag-floating-widget * {
  box-sizing: border-box;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* 微型图标样式 */
.universe-bag-mini-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: #3b82f6;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  transition: all 0.2s ease;
  opacity: 0.8;
}

.universe-bag-mini-icon:hover {
  opacity: 1;
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

/* 信息浮窗样式 */
.universe-bag-info-popup {
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  padding: 12px;
  max-width: 300px;
  border: 1px solid #e5e7eb;
}

.universe-bag-info-popup h3 {
  margin: 0 0 8px 0;
  font-size: 14px;
  font-weight: 600;
  color: #1f2937;
}

.universe-bag-info-popup p {
  margin: 0 0 8px 0;
  font-size: 12px;
  color: #6b7280;
  line-height: 1.4;
}

.universe-bag-info-popup .tags {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  margin-top: 8px;
}

.universe-bag-info-popup .tag {
  background: #eff6ff;
  color: #2563eb;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 11px;
  font-weight: 500;
}

/* 防止与页面样式冲突 */
.universe-bag-floating-widget,
.universe-bag-floating-widget * {
  all: initial;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
}

/* 重新应用必要的样式 */
.universe-bag-floating-widget {
  position: fixed !important;
  z-index: 2147483647 !important;
  pointer-events: auto !important;
}