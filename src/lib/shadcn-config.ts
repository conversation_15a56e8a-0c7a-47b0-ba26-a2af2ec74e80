// shadcn/ui 组件配置和工具函数

import { type ClassValue } from "clsx"
import { cn } from "./utils"

// shadcn 组件默认配置
export const shadcnConfig = {
  // 默认主题配置
  theme: {
    radius: "0.5rem",
    colors: {
      primary: "hsl(var(--primary))",
      secondary: "hsl(var(--secondary))",
      destructive: "hsl(var(--destructive))",
      muted: "hsl(var(--muted))",
      accent: "hsl(var(--accent))",
      background: "hsl(var(--background))",
      foreground: "hsl(var(--foreground))",
      card: "hsl(var(--card))",
      border: "hsl(var(--border))",
      input: "hsl(var(--input))",
      ring: "hsl(var(--ring))"
    }
  },
  
  // 组件默认变体配置
  variants: {
    button: {
      default: "default",
      sizes: ["sm", "default", "lg", "icon"] as const,
      variants: ["default", "destructive", "outline", "secondary", "ghost", "link"] as const
    },
    badge: {
      default: "default",
      variants: ["default", "secondary", "destructive", "outline"] as const
    },
    card: {
      default: "default"
    },
    input: {
      default: "default"
    },
    select: {
      default: "default"
    }
  },
  
  // 动画配置
  animations: {
    duration: {
      fast: "150ms",
      normal: "200ms",
      slow: "300ms"
    },
    easing: {
      default: "cubic-bezier(0.4, 0, 0.2, 1)",
      in: "cubic-bezier(0.4, 0, 1, 1)",
      out: "cubic-bezier(0, 0, 0.2, 1)",
      inOut: "cubic-bezier(0.4, 0, 0.2, 1)"
    }
  },
  
  // 间距配置
  spacing: {
    xs: "0.25rem",
    sm: "0.5rem",
    md: "1rem",
    lg: "1.5rem",
    xl: "2rem",
    "2xl": "3rem"
  },
  
  // 断点配置
  breakpoints: {
    sm: "640px",
    md: "768px",
    lg: "1024px",
    xl: "1280px",
    "2xl": "1536px"
  }
} as const

// 组件样式生成器
export const createComponentStyles = {
  // 按钮样式生成器
  button: (variant?: string, size?: string, className?: string) => {
    const baseClasses = "inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50"
    return cn(baseClasses, className)
  },
  
  // 输入框样式生成器
  input: (className?: string) => {
    const baseClasses = "flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
    return cn(baseClasses, className)
  },
  
  // 卡片样式生成器
  card: (className?: string) => {
    const baseClasses = "rounded-lg border bg-card text-card-foreground shadow-sm"
    return cn(baseClasses, className)
  },
  
  // 标签样式生成器
  badge: (variant?: string, className?: string) => {
    const baseClasses = "inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
    return cn(baseClasses, className)
  }
}

// 主题工具函数
export const themeUtils = {
  // 获取CSS变量值
  getCSSVariable: (variable: string): string => {
    if (typeof window !== 'undefined') {
      return getComputedStyle(document.documentElement).getPropertyValue(variable).trim()
    }
    return ''
  },
  
  // 设置CSS变量值
  setCSSVariable: (variable: string, value: string): void => {
    if (typeof window !== 'undefined') {
      document.documentElement.style.setProperty(variable, value)
    }
  },
  
  // 切换主题
  toggleTheme: (): void => {
    if (typeof window !== 'undefined') {
      const root = document.documentElement
      const isDark = root.classList.contains('dark')
      root.classList.toggle('dark', !isDark)
      localStorage.setItem('theme', isDark ? 'light' : 'dark')
    }
  },
  
  // 获取当前主题
  getCurrentTheme: (): 'light' | 'dark' | 'system' => {
    if (typeof window !== 'undefined') {
      const stored = localStorage.getItem('theme')
      if (stored === 'light' || stored === 'dark') {
        return stored
      }
    }
    return 'system'
  },
  
  // 应用主题
  applyTheme: (theme: 'light' | 'dark' | 'system'): void => {
    if (typeof window !== 'undefined') {
      const root = document.documentElement
      
      if (theme === 'system') {
        const systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light'
        root.classList.toggle('dark', systemTheme === 'dark')
      } else {
        root.classList.toggle('dark', theme === 'dark')
      }
      
      localStorage.setItem('theme', theme)
    }
  }
}

// 响应式工具函数
export const responsiveUtils = {
  // 获取当前断点
  getCurrentBreakpoint: (): string => {
    if (typeof window !== 'undefined') {
      const width = window.innerWidth
      if (width >= 1536) return '2xl'
      if (width >= 1280) return 'xl'
      if (width >= 1024) return 'lg'
      if (width >= 768) return 'md'
      if (width >= 640) return 'sm'
    }
    return 'xs'
  },
  
  // 检查是否为移动设备
  isMobile: (): boolean => {
    if (typeof window !== 'undefined') {
      return window.innerWidth < 768
    }
    return false
  },
  
  // 检查是否为平板设备
  isTablet: (): boolean => {
    if (typeof window !== 'undefined') {
      const width = window.innerWidth
      return width >= 768 && width < 1024
    }
    return false
  },
  
  // 检查是否为桌面设备
  isDesktop: (): boolean => {
    if (typeof window !== 'undefined') {
      return window.innerWidth >= 1024
    }
    return false
  }
}

// 动画工具函数
export const animationUtils = {
  // 创建过渡类名
  createTransition: (property: string = 'all', duration: string = '200ms', easing: string = 'cubic-bezier(0.4, 0, 0.2, 1)'): string => {
    return `transition-${property} duration-${duration} ease-${easing}`
  },
  
  // 创建动画延迟
  createDelay: (delay: number): string => {
    return `delay-${delay}`
  },
  
  // 创建动画持续时间
  createDuration: (duration: number): string => {
    return `duration-${duration}`
  }
}

// 可访问性工具函数
export const a11yUtils = {
  // 生成唯一ID
  generateId: (prefix: string = 'shadcn'): string => {
    return `${prefix}-${Math.random().toString(36).substr(2, 9)}`
  },
  
  // 创建ARIA标签
  createAriaLabel: (label: string, describedBy?: string): Record<string, string> => {
    const attrs: Record<string, string> = { 'aria-label': label }
    if (describedBy) {
      attrs['aria-describedby'] = describedBy
    }
    return attrs
  },
  
  // 创建ARIA状态
  createAriaState: (expanded?: boolean, selected?: boolean, disabled?: boolean): Record<string, string> => {
    const attrs: Record<string, string> = {}
    if (typeof expanded === 'boolean') attrs['aria-expanded'] = expanded.toString()
    if (typeof selected === 'boolean') attrs['aria-selected'] = selected.toString()
    if (typeof disabled === 'boolean') attrs['aria-disabled'] = disabled.toString()
    return attrs
  }
}

// 表单工具函数
export const formUtils = {
  // 创建表单字段ID
  createFieldId: (name: string): string => {
    return `field-${name}-${Math.random().toString(36).substr(2, 6)}`
  },
  
  // 创建错误消息ID
  createErrorId: (fieldName: string): string => {
    return `error-${fieldName}-${Math.random().toString(36).substr(2, 6)}`
  },
  
  // 创建描述ID
  createDescriptionId: (fieldName: string): string => {
    return `description-${fieldName}-${Math.random().toString(36).substr(2, 6)}`
  },
  
  // 验证必填字段
  validateRequired: (value: any): boolean => {
    if (typeof value === 'string') return value.trim().length > 0
    if (Array.isArray(value)) return value.length > 0
    return value != null
  },
  
  // 验证邮箱格式
  validateEmail: (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(email)
  },
  
  // 验证URL格式
  validateUrl: (url: string): boolean => {
    try {
      new URL(url)
      return true
    } catch {
      return false
    }
  }
}

// 导出所有配置和工具
export default {
  config: shadcnConfig,
  styles: createComponentStyles,
  theme: themeUtils,
  responsive: responsiveUtils,
  animation: animationUtils,
  a11y: a11yUtils,
  form: formUtils
}