// Worker管理器 - 管理Web Workers的创建、通信和生命周期

import { WorkerMessage, WorkerMessageType, ParseTask, ValidationTask, TransformTask } from '../workers/DataProcessingWorker'

/**
 * Worker任务状态
 */
export enum TaskStatus {
  PENDING = 'PENDING',
  RUNNING = 'RUNNING',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED',
  CANCELLED = 'CANCELLED'
}

/**
 * Worker任务接口
 */
export interface WorkerTask {
  id: string
  type: WorkerMessageType
  status: TaskStatus
  progress: number
  result?: any
  error?: string
  startTime: Date
  endTime?: Date
  onProgress?: (progress: number, message: string) => void
  onComplete?: (result: any) => void
  onError?: (error: string) => void
}

/**
 * Worker池配置
 */
export interface WorkerPoolConfig {
  maxWorkers: number
  workerScript: string
  taskTimeout: number // 任务超时时间（毫秒）
  retryAttempts: number
}

/**
 * Worker管理器类
 * 提供Worker池管理、任务调度、负载均衡等功能
 */
export class WorkerManager {
  private workers: Worker[] = []
  private tasks: Map<string, WorkerTask> = new Map()
  private taskQueue: WorkerTask[] = []
  private workerBusy: boolean[] = []
  private config: WorkerPoolConfig

  private readonly defaultConfig: WorkerPoolConfig = {
    maxWorkers: Math.max(1, Math.floor(navigator.hardwareConcurrency / 2) || 2),
    workerScript: '/src/workers/DataProcessingWorker.js',
    taskTimeout: 300000, // 5分钟
    retryAttempts: 2
  }

  constructor(config: Partial<WorkerPoolConfig> = {}) {
    this.config = { ...this.defaultConfig, ...config }
    this.initializeWorkerPool()
  }

  /**
   * 解析数据任务
   * @param content 文件内容
   * @param format 文件格式
   * @param options 解析选项
   * @param onProgress 进度回调
   * @returns Promise<any>
   */
  async parseData(
    content: string,
    format: 'json' | 'csv' | 'html',
    options?: any,
    onProgress?: (progress: number, message: string) => void
  ): Promise<any> {
    const task: ParseTask = { content, format, options }
    
    return this.executeTask(
      WorkerMessageType.PARSE_JSON,
      task,
      onProgress
    )
  }

  /**
   * 验证数据任务
   * @param data 导入数据
   * @param strict 严格模式
   * @param onProgress 进度回调
   * @returns Promise<any>
   */
  async validateData(
    data: any,
    strict: boolean = false,
    onProgress?: (progress: number, message: string) => void
  ): Promise<any> {
    const task: ValidationTask = { data, strict }
    
    return this.executeTask(
      WorkerMessageType.VALIDATE_DATA,
      task,
      onProgress
    )
  }

  /**
   * 转换数据任务
   * @param data 原始数据
   * @param targetFormat 目标格式
   * @param options 转换选项
   * @param onProgress 进度回调
   * @returns Promise<any>
   */
  async transformData(
    data: any,
    targetFormat: string,
    options?: any,
    onProgress?: (progress: number, message: string) => void
  ): Promise<any> {
    const task: TransformTask = { data, targetFormat, options }
    
    return this.executeTask(
      WorkerMessageType.TRANSFORM_DATA,
      task,
      onProgress
    )
  }

  /**
   * 检测冲突任务
   * @param data 检测数据
   * @param onProgress 进度回调
   * @returns Promise<any>
   */
  async detectConflicts(
    data: any,
    onProgress?: (progress: number, message: string) => void
  ): Promise<any> {
    return this.executeTask(
      WorkerMessageType.DETECT_CONFLICTS,
      data,
      onProgress
    )
  }

  /**
   * 取消任务
   * @param taskId 任务ID
   * @returns boolean
   */
  cancelTask(taskId: string): boolean {
    const task = this.tasks.get(taskId)
    if (!task || task.status === TaskStatus.COMPLETED || task.status === TaskStatus.FAILED) {
      return false
    }

    task.status = TaskStatus.CANCELLED
    task.endTime = new Date()
    
    // 从队列中移除
    const queueIndex = this.taskQueue.findIndex(t => t.id === taskId)
    if (queueIndex >= 0) {
      this.taskQueue.splice(queueIndex, 1)
    }

    return true
  }

  /**
   * 获取任务状态
   * @param taskId 任务ID
   * @returns WorkerTask | undefined
   */
  getTaskStatus(taskId: string): WorkerTask | undefined {
    return this.tasks.get(taskId)
  }

  /**
   * 获取所有任务状态
   * @returns WorkerTask[]
   */
  getAllTasks(): WorkerTask[] {
    return Array.from(this.tasks.values())
  }

  /**
   * 获取Worker池状态
   * @returns Worker池状态信息
   */
  getWorkerPoolStatus(): {
    totalWorkers: number
    busyWorkers: number
    availableWorkers: number
    queuedTasks: number
    activeTasks: number
  } {
    const busyCount = this.workerBusy.filter(busy => busy).length
    const activeTasks = Array.from(this.tasks.values())
      .filter(task => task.status === TaskStatus.RUNNING).length

    return {
      totalWorkers: this.workers.length,
      busyWorkers: busyCount,
      availableWorkers: this.workers.length - busyCount,
      queuedTasks: this.taskQueue.length,
      activeTasks
    }
  }

  /**
   * 清理已完成的任务
   * @param olderThan 清理指定时间之前的任务
   */
  cleanupTasks(olderThan?: Date): void {
    const cutoffTime = olderThan || new Date(Date.now() - 24 * 60 * 60 * 1000) // 24小时前
    
    for (const [taskId, task] of this.tasks.entries()) {
      if (task.endTime && task.endTime < cutoffTime && 
          (task.status === TaskStatus.COMPLETED || task.status === TaskStatus.FAILED)) {
        this.tasks.delete(taskId)
      }
    }
  }

  /**
   * 销毁Worker池
   */
  destroy(): void {
    // 取消所有待处理的任务
    for (const task of this.tasks.values()) {
      if (task.status === TaskStatus.PENDING || task.status === TaskStatus.RUNNING) {
        this.cancelTask(task.id)
      }
    }

    // 终止所有Worker
    this.workers.forEach(worker => {
      worker.terminate()
    })

    this.workers = []
    this.workerBusy = []
    this.tasks.clear()
    this.taskQueue = []
  }

  // ==================== 私有方法 ====================

  /**
   * 初始化Worker池
   */
  private initializeWorkerPool(): void {
    for (let i = 0; i < this.config.maxWorkers; i++) {
      try {
        const worker = new Worker(this.config.workerScript, { type: 'module' })
        worker.addEventListener('message', (event) => this.handleWorkerMessage(i, event))
        worker.addEventListener('error', (error) => this.handleWorkerError(i, error))
        
        this.workers.push(worker)
        this.workerBusy.push(false)
      } catch (error) {
        console.error(`创建Worker ${i} 失败:`, error)
      }
    }

    if (this.workers.length === 0) {
      console.warn('无法创建任何Worker，将在主线程中执行任务')
    }
  }

  /**
   * 执行任务
   * @param type 任务类型
   * @param data 任务数据
   * @param onProgress 进度回调
   * @returns Promise<any>
   */
  private async executeTask(
    type: WorkerMessageType,
    data: any,
    onProgress?: (progress: number, message: string) => void
  ): Promise<any> {
    const taskId = this.generateTaskId()
    
    const task: WorkerTask = {
      id: taskId,
      type,
      status: TaskStatus.PENDING,
      progress: 0,
      startTime: new Date(),
      onProgress,
      onComplete: undefined,
      onError: undefined
    }

    this.tasks.set(taskId, task)

    return new Promise((resolve, reject) => {
      task.onComplete = (result) => {
        task.result = result
        task.status = TaskStatus.COMPLETED
        task.endTime = new Date()
        resolve(result)
      }

      task.onError = (error) => {
        task.error = error
        task.status = TaskStatus.FAILED
        task.endTime = new Date()
        reject(new Error(error))
      }

      // 设置超时
      setTimeout(() => {
        if (task.status === TaskStatus.RUNNING || task.status === TaskStatus.PENDING) {
          this.cancelTask(taskId)
          reject(new Error('任务执行超时'))
        }
      }, this.config.taskTimeout)

      // 尝试立即执行或加入队列
      this.scheduleTask(task, data)
    })
  }

  /**
   * 调度任务
   * @param task 任务对象
   * @param data 任务数据
   */
  private scheduleTask(task: WorkerTask, data: any): void {
    const availableWorkerIndex = this.findAvailableWorker()
    
    if (availableWorkerIndex >= 0) {
      this.executeTaskOnWorker(availableWorkerIndex, task, data)
    } else {
      // 没有可用Worker，加入队列
      this.taskQueue.push(task)
    }
  }

  /**
   * 在指定Worker上执行任务
   * @param workerIndex Worker索引
   * @param task 任务对象
   * @param data 任务数据
   */
  private executeTaskOnWorker(workerIndex: number, task: WorkerTask, data: any): void {
    if (workerIndex >= this.workers.length) {
      task.onError?.('Worker不可用')
      return
    }

    this.workerBusy[workerIndex] = true
    task.status = TaskStatus.RUNNING

    const message: WorkerMessage = {
      type: task.type,
      id: task.id,
      data
    }

    this.workers[workerIndex].postMessage(message)
  }

  /**
   * 处理Worker消息
   * @param workerIndex Worker索引
   * @param event 消息事件
   */
  private handleWorkerMessage(workerIndex: number, event: MessageEvent<WorkerMessage>): void {
    const { type, id, data, progress, error } = event.data
    const task = this.tasks.get(id)

    if (!task) {
      console.warn(`收到未知任务的消息: ${id}`)
      return
    }

    switch (type) {
      case WorkerMessageType.PROGRESS_UPDATE:
        task.progress = progress || 0
        task.onProgress?.(task.progress, data as string)
        break

      case WorkerMessageType.COMPLETE:
        this.workerBusy[workerIndex] = false
        task.onComplete?.(data)
        this.processNextTask()
        break

      case WorkerMessageType.ERROR:
        this.workerBusy[workerIndex] = false
        task.onError?.(error || '未知错误')
        this.processNextTask()
        break
    }
  }

  /**
   * 处理Worker错误
   * @param workerIndex Worker索引
   * @param error 错误事件
   */
  private handleWorkerError(workerIndex: number, error: ErrorEvent): void {
    console.error(`Worker ${workerIndex} 错误:`, error)
    this.workerBusy[workerIndex] = false
    
    // 重新创建Worker
    try {
      this.workers[workerIndex].terminate()
      const newWorker = new Worker(this.config.workerScript, { type: 'module' })
      newWorker.addEventListener('message', (event) => this.handleWorkerMessage(workerIndex, event))
      newWorker.addEventListener('error', (error) => this.handleWorkerError(workerIndex, error))
      this.workers[workerIndex] = newWorker
    } catch (recreateError) {
      console.error(`重新创建Worker ${workerIndex} 失败:`, recreateError)
    }

    this.processNextTask()
  }

  /**
   * 处理队列中的下一个任务
   */
  private processNextTask(): void {
    if (this.taskQueue.length === 0) {
      return
    }

    const availableWorkerIndex = this.findAvailableWorker()
    if (availableWorkerIndex >= 0) {
      const nextTask = this.taskQueue.shift()
      if (nextTask && nextTask.status !== TaskStatus.CANCELLED) {
        // 需要重新获取任务数据，这里简化处理
        // 实际实现中应该保存任务数据
        console.warn('队列任务执行需要重新设计数据传递机制')
      }
    }
  }

  /**
   * 查找可用的Worker
   * @returns Worker索引，-1表示没有可用Worker
   */
  private findAvailableWorker(): number {
    return this.workerBusy.findIndex(busy => !busy)
  }

  /**
   * 生成任务ID
   * @returns 唯一任务ID
   */
  private generateTaskId(): string {
    return `task_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }
}

// 导出单例实例
export const workerManager = new WorkerManager()