import type { ViewMode } from '../components/ViewModeSelector'

// 布局配置接口
export interface LayoutConfig {
  // 卡片视图配置
  card: {
    columns: number
    showThumbnails: boolean
    showDescriptions: boolean
    cardHeight: 'auto' | 'fixed'
    minCardWidth: number
    gap: number
  }
  
  // 行视图配置
  row: {
    showFavicons: boolean
    showCategories: boolean
    showTags: boolean
    density: 'comfortable' | 'compact'
    height: number
  }
  
  // 紧凑视图配置
  compact: {
    itemsPerRow: number
    showMetadata: boolean
    spacing: 'tight' | 'normal'
    minItemWidth: number
  }
}

// 默认布局配置
const DEFAULT_LAYOUT_CONFIG: LayoutConfig = {
  card: {
    columns: 3,
    showThumbnails: true,
    showDescriptions: true,
    cardHeight: 'auto',
    minCardWidth: 280,
    gap: 16
  },
  row: {
    showFavicons: true,
    showCategories: true,
    showTags: false,
    density: 'comfortable',
    height: 48
  },
  compact: {
    itemsPerRow: 4,
    showMetadata: true,
    spacing: 'normal',
    minItemWidth: 200
  }
}

// 存储键名
const STORAGE_KEYS = {
  VIEW_MODE: 'bookmark-view-mode',
  LAYOUT_CONFIG: 'bookmark-layout-config'
}

/**
 * 视图偏好服务
 * 管理用户的视图模式和布局配置偏好
 */
export class ViewPreferenceService {
  /**
   * 保存视图模式偏好
   */
  static async saveViewMode(mode: ViewMode): Promise<void> {
    try {
      localStorage.setItem(STORAGE_KEYS.VIEW_MODE, mode)
    } catch (error) {
      console.error('保存视图模式失败:', error)
      throw new Error('无法保存视图模式偏好')
    }
  }

  /**
   * 获取视图模式偏好
   */
  static async getViewMode(): Promise<ViewMode> {
    try {
      const savedMode = localStorage.getItem(STORAGE_KEYS.VIEW_MODE) as ViewMode
      if (savedMode && ['card', 'row', 'compact'].includes(savedMode)) {
        return savedMode
      }
      return 'card' // 默认模式
    } catch (error) {
      console.error('获取视图模式失败:', error)
      return 'card'
    }
  }

  /**
   * 保存布局配置
   */
  static async saveLayoutConfig(config: Partial<LayoutConfig>): Promise<void> {
    try {
      const currentConfig = await this.getLayoutConfig()
      const mergedConfig = this.mergeLayoutConfig(currentConfig, config)
      localStorage.setItem(STORAGE_KEYS.LAYOUT_CONFIG, JSON.stringify(mergedConfig))
    } catch (error) {
      console.error('保存布局配置失败:', error)
      throw new Error('无法保存布局配置')
    }
  }

  /**
   * 获取布局配置
   */
  static async getLayoutConfig(): Promise<LayoutConfig> {
    try {
      const savedConfig = localStorage.getItem(STORAGE_KEYS.LAYOUT_CONFIG)
      if (savedConfig) {
        const parsedConfig = JSON.parse(savedConfig)
        return this.mergeLayoutConfig(DEFAULT_LAYOUT_CONFIG, parsedConfig)
      }
      return DEFAULT_LAYOUT_CONFIG
    } catch (error) {
      console.error('获取布局配置失败:', error)
      return DEFAULT_LAYOUT_CONFIG
    }
  }

  /**
   * 重置为默认配置
   */
  static async resetToDefaults(): Promise<void> {
    try {
      localStorage.removeItem(STORAGE_KEYS.VIEW_MODE)
      localStorage.removeItem(STORAGE_KEYS.LAYOUT_CONFIG)
    } catch (error) {
      console.error('重置配置失败:', error)
      throw new Error('无法重置配置')
    }
  }

  /**
   * 获取特定视图模式的配置
   */
  static async getViewModeConfig(mode: ViewMode): Promise<LayoutConfig[ViewMode]> {
    const config = await this.getLayoutConfig()
    return config[mode]
  }

  /**
   * 更新特定视图模式的配置
   */
  static async updateViewModeConfig(
    mode: ViewMode, 
    config: Partial<LayoutConfig[ViewMode]>
  ): Promise<void> {
    const currentConfig = await this.getLayoutConfig()
    const updatedConfig = {
      ...currentConfig,
      [mode]: { ...currentConfig[mode], ...config }
    }
    await this.saveLayoutConfig(updatedConfig)
  }

  /**
   * 合并布局配置
   */
  private static mergeLayoutConfig(
    base: LayoutConfig, 
    override: Partial<LayoutConfig>
  ): LayoutConfig {
    return {
      card: { ...base.card, ...override.card },
      row: { ...base.row, ...override.row },
      compact: { ...base.compact, ...override.compact }
    }
  }

  /**
   * 验证布局配置
   */
  static validateLayoutConfig(config: Partial<LayoutConfig>): boolean {
    try {
      // 验证卡片配置
      if (config.card) {
        const { columns, minCardWidth, gap } = config.card
        if (columns !== undefined && (columns < 1 || columns > 6)) return false
        if (minCardWidth !== undefined && (minCardWidth < 200 || minCardWidth > 500)) return false
        if (gap !== undefined && (gap < 0 || gap > 32)) return false
      }

      // 验证行配置
      if (config.row) {
        const { height, density } = config.row
        if (height !== undefined && (height < 32 || height > 80)) return false
        if (density !== undefined && !['comfortable', 'compact'].includes(density)) return false
      }

      // 验证紧凑配置
      if (config.compact) {
        const { itemsPerRow, minItemWidth } = config.compact
        if (itemsPerRow !== undefined && (itemsPerRow < 2 || itemsPerRow > 8)) return false
        if (minItemWidth !== undefined && (minItemWidth < 150 || minItemWidth > 300)) return false
      }

      return true
    } catch (error) {
      console.error('配置验证失败:', error)
      return false
    }
  }

  /**
   * 导出配置
   */
  static async exportConfig(): Promise<{
    viewMode: ViewMode
    layoutConfig: LayoutConfig
  }> {
    const [viewMode, layoutConfig] = await Promise.all([
      this.getViewMode(),
      this.getLayoutConfig()
    ])

    return { viewMode, layoutConfig }
  }

  /**
   * 导入配置
   */
  static async importConfig(config: {
    viewMode?: ViewMode
    layoutConfig?: Partial<LayoutConfig>
  }): Promise<void> {
    if (config.viewMode) {
      await this.saveViewMode(config.viewMode)
    }

    if (config.layoutConfig && this.validateLayoutConfig(config.layoutConfig)) {
      await this.saveLayoutConfig(config.layoutConfig)
    }
  }
}