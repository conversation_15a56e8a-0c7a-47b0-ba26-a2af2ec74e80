// 标签管理服务 - 提供标签管理的核心业务逻辑

import { Tag, TagInput, TagUpdate, Bookmark } from '../types'
import { indexedDBService } from '../utils/indexedDB'
import { ModelFactory } from '../utils/modelFactory'
import { ValidationUtils } from '../utils/validation'

/**
 * 标签统计信息接口
 */
export interface TagWithStats extends Tag {
  usageCount: number
}

/**
 * 标签排序选项
 */
export type TagSortOption = 
  | 'name-asc' | 'name-desc'
  | 'usage-asc' | 'usage-desc'
  | 'created-asc' | 'created-desc'
  | 'updated-asc' | 'updated-desc'

/**
 * 标签服务类
 * 提供标签管理的核心业务逻辑，包括CRUD操作、统计信息计算、标签与书签的关联管理
 */
export class TagService {

  /**
   * 获取所有标签及其统计信息
   * @returns Promise<TagWithStats[]> 包含统计信息的标签列表
   */
  async getAllTagsWithStats(): Promise<TagWithStats[]> {
    try {
      const [tags, bookmarks] = await Promise.all([
        indexedDBService.getTags(),
        indexedDBService.getBookmarks()
      ])

      // 计算每个标签的使用次数
      const tagUsageMap = new Map<string, number>()
      
      // 统计书签中标签的使用情况
      bookmarks.forEach(bookmark => {
        bookmark.tags.forEach(tagName => {
          tagUsageMap.set(tagName, (tagUsageMap.get(tagName) || 0) + 1)
        })
      })

      // 为每个标签添加统计信息
      const tagsWithStats: TagWithStats[] = tags.map(tag => ({
        ...tag,
        usageCount: tagUsageMap.get(tag.name) || 0
      }))

      return tagsWithStats
    } catch (error) {
      console.error('获取标签统计信息失败:', error)
      throw new Error('获取标签统计信息失败')
    }
  }

  /**
   * 从现有书签中同步标签
   * @returns Promise<Tag[]> 同步后的标签列表
   */
  async syncTagsFromBookmarks(): Promise<Tag[]> {
    try {
      const [bookmarks, existingTags] = await Promise.all([
        indexedDBService.getBookmarks(),
        indexedDBService.getTags()
      ])

      // 获取现有标签名称集合
      const existingTagNames = new Set(existingTags.map(tag => tag.name))
      
      // 从书签中提取所有标签名称
      const bookmarkTags = new Set<string>()
      bookmarks.forEach(bookmark => {
        bookmark.tags.forEach(tagName => {
          if (tagName && tagName.trim()) {
            bookmarkTags.add(tagName.trim())
          }
        })
      })

      // 创建新发现的标签
      const newTags: Tag[] = []
      const now = new Date()

      for (const tagName of bookmarkTags) {
        if (!existingTagNames.has(tagName)) {
          const newTag: Tag = {
            id: this.generateTagId(),
            name: tagName,
            color: this.generateTagColor(tagName),
            usageCount: 0, // 将在后续计算中更新
            createdAt: now,
            updatedAt: now
          }
          
          await indexedDBService.saveTag(newTag)
          newTags.push(newTag)
        }
      }

      // 更新所有标签的使用次数
      await this.updateAllTagUsageCounts()

      // 返回所有标签（包括新创建的）
      const allTags = await indexedDBService.getTags()
      console.log(`同步完成，新增 ${newTags.length} 个标签`)
      
      return allTags
    } catch (error) {
      console.error('同步标签失败:', error)
      throw new Error('同步标签失败')
    }
  }

  /**
   * 创建新标签
   * @param tagData 标签数据
   * @returns Promise<Tag> 创建的标签
   */
  async createTag(tagData: TagInput): Promise<Tag> {
    try {
      // 验证标签名称唯一性
      const isUnique = await this.validateTagName(tagData.name)
      if (!isUnique) {
        throw new Error(`标签名称 "${tagData.name}" 已存在`)
      }

      const now = new Date()
      const newTag: Tag = {
        id: this.generateTagId(),
        name: tagData.name.trim(),
        color: tagData.color || this.generateTagColor(tagData.name),
        usageCount: 0,
        createdAt: now,
        updatedAt: now
      }

      await indexedDBService.saveTag(newTag)
      console.log(`创建标签成功: ${newTag.name}`)
      
      return newTag
    } catch (error) {
      console.error('创建标签失败:', error)
      throw error
    }
  }

  /**
   * 更新标签
   * @param tagId 标签ID
   * @param updates 更新数据
   * @returns Promise<Tag> 更新后的标签
   */
  async updateTag(tagId: string, updates: TagUpdate): Promise<Tag> {
    try {
      const existingTag = await indexedDBService.getTag(tagId)
      if (!existingTag) {
        throw new Error(`标签不存在: ${tagId}`)
      }

      // 如果更新名称，验证唯一性
      if (updates.name && updates.name !== existingTag.name) {
        const isUnique = await this.validateTagName(updates.name, tagId)
        if (!isUnique) {
          throw new Error(`标签名称 "${updates.name}" 已存在`)
        }
      }

      const updatedTag: Tag = {
        ...existingTag,
        ...updates,
        id: tagId, // 确保ID不被修改
        updatedAt: new Date()
      }

      // 如果名称发生变化，需要更新相关书签的标签
      if (updates.name && updates.name !== existingTag.name) {
        await this.updateBookmarkTags(existingTag.name, updates.name)
      }

      await indexedDBService.saveTag(updatedTag)
      console.log(`更新标签成功: ${updatedTag.name}`)
      
      return updatedTag
    } catch (error) {
      console.error('更新标签失败:', error)
      throw error
    }
  }

  /**
   * 删除标签
   * @param tagId 标签ID
   * @returns Promise<void>
   */
  async deleteTag(tagId: string): Promise<void> {
    try {
      const tag = await indexedDBService.getTag(tagId)
      if (!tag) {
        throw new Error(`标签不存在: ${tagId}`)
      }

      // 获取使用该标签的书签数量
      const usageCount = await this.getTagUsageCount(tagId)
      
      if (usageCount > 0) {
        // 从相关书签中移除该标签
        await this.removeTagFromBookmarks(tag.name)
        console.log(`已从 ${usageCount} 个书签中移除标签`)
      }

      // 删除标签
      await indexedDBService.deleteTag(tagId)
      console.log(`删除标签成功: ${tag.name}`)
    } catch (error) {
      console.error('删除标签失败:', error)
      throw error
    }
  }

  /**
   * 验证标签名称唯一性
   * @param name 标签名称
   * @param excludeId 排除的标签ID（用于更新时的验证）
   * @returns Promise<boolean> 是否唯一
   */
  async validateTagName(name: string, excludeId?: string): Promise<boolean> {
    try {
      const tags = await indexedDBService.getTags()
      const trimmedName = name.trim().toLowerCase()
      
      return !tags.some(tag => 
        tag.name.toLowerCase() === trimmedName && 
        tag.id !== excludeId
      )
    } catch (error) {
      console.error('验证标签名称失败:', error)
      return false
    }
  }

  /**
   * 根据名称获取标签
   * @param name 标签名称
   * @returns Promise<Tag | null> 标签对象或null
   */
  async getTagByName(name: string): Promise<Tag | null> {
    try {
      const tags = await indexedDBService.getTags()
      return tags.find(tag => 
        tag.name.toLowerCase() === name.toLowerCase()
      ) || null
    } catch (error) {
      console.error('根据名称获取标签失败:', error)
      return null
    }
  }

  /**
   * 获取所有标签
   * @returns Promise<Tag[]> 标签列表
   */
  async getTags(): Promise<Tag[]> {
    try {
      return await indexedDBService.getTags()
    } catch (error) {
      console.error('获取标签列表失败:', error)
      return []
    }
  }

  /**
   * 获取标签的使用次数
   * @param tagId 标签ID
   * @returns Promise<number> 使用次数
   */
  async getTagUsageCount(tagId: string): Promise<number> {
    try {
      const tag = await indexedDBService.getTag(tagId)
      if (!tag) {
        return 0
      }

      const bookmarks = await indexedDBService.getBookmarks()
      return bookmarks.filter(bookmark => bookmark.tags.includes(tag.name)).length
    } catch (error) {
      console.error('获取标签使用次数失败:', error)
      return 0
    }
  }

  /**
   * 根据标签名称获取使用次数
   * @param tagName 标签名称
   * @returns Promise<number> 使用次数
   */
  async getTagUsageCountByName(tagName: string): Promise<number> {
    try {
      const bookmarks = await indexedDBService.getBookmarks()
      return bookmarks.filter(bookmark => bookmark.tags.includes(tagName)).length
    } catch (error) {
      console.error('获取标签使用次数失败:', error)
      return 0
    }
  }

  /**
   * 获取标签相关的书签
   * @param tagId 标签ID
   * @returns Promise<Bookmark[]> 相关书签列表
   */
  async getBookmarksByTag(tagId: string): Promise<Bookmark[]> {
    try {
      const tag = await indexedDBService.getTag(tagId)
      if (!tag) {
        return []
      }

      const bookmarks = await indexedDBService.getBookmarks()
      return bookmarks.filter(bookmark => bookmark.tags.includes(tag.name))
    } catch (error) {
      console.error('获取标签相关书签失败:', error)
      return []
    }
  }

  /**
   * 根据标签名称获取相关书签
   * @param tagName 标签名称
   * @returns Promise<Bookmark[]> 相关书签列表
   */
  async getBookmarksByTagName(tagName: string): Promise<Bookmark[]> {
    try {
      const bookmarks = await indexedDBService.getBookmarks()
      return bookmarks.filter(bookmark => bookmark.tags.includes(tagName))
    } catch (error) {
      console.error('获取标签相关书签失败:', error)
      return []
    }
  }

  /**
   * 批量更新书签的标签
   * @param bookmarkId 书签ID
   * @param tags 新的标签列表
   * @returns Promise<void>
   */
  async updateBookmarkTagsList(bookmarkId: string, tags: string[]): Promise<void> {
    try {
      const bookmark = await indexedDBService.getBookmark(bookmarkId)
      if (!bookmark) {
        throw new Error(`书签不存在: ${bookmarkId}`)
      }

      // 更新书签的标签
      await indexedDBService.updateBookmark(bookmarkId, { tags })

      // 更新标签使用计数
      await this.updateAllTagUsageCounts()

      console.log(`更新书签标签成功: ${bookmarkId}`)
    } catch (error) {
      console.error('更新书签标签失败:', error)
      throw error
    }
  }

  /**
   * 合并标签
   * @param sourceTagId 源标签ID
   * @param targetTagId 目标标签ID
   * @returns Promise<void>
   */
  async mergeTags(sourceTagId: string, targetTagId: string): Promise<void> {
    try {
      const [sourceTag, targetTag] = await Promise.all([
        indexedDBService.getTag(sourceTagId),
        indexedDBService.getTag(targetTagId)
      ])

      if (!sourceTag) {
        throw new Error(`源标签不存在: ${sourceTagId}`)
      }
      if (!targetTag) {
        throw new Error(`目标标签不存在: ${targetTagId}`)
      }

      // 获取使用源标签的所有书签
      const bookmarks = await this.getBookmarksByTag(sourceTagId)

      // 将源标签替换为目标标签
      for (const bookmark of bookmarks) {
        const updatedTags = bookmark.tags
          .filter(tag => tag !== sourceTag.name) // 移除源标签
          .concat(targetTag.name) // 添加目标标签
        
        // 去重
        const uniqueTags = Array.from(new Set(updatedTags))
        
        await indexedDBService.updateBookmark(bookmark.id, { tags: uniqueTags })
      }

      // 删除源标签
      await indexedDBService.deleteTag(sourceTagId)

      // 更新标签使用计数
      await this.updateAllTagUsageCounts()

      console.log(`合并标签成功: ${sourceTag.name} -> ${targetTag.name}`)
    } catch (error) {
      console.error('合并标签失败:', error)
      throw error
    }
  }

  /**
   * 批量合并标签
   * @param sourceTagIds 源标签ID列表
   * @param targetTagId 目标标签ID
   * @returns Promise<void>
   */
  async batchMergeTags(sourceTagIds: string[], targetTagId: string): Promise<void> {
    try {
      // 获取目标标签
      const targetTag = await indexedDBService.getTag(targetTagId)
      if (!targetTag) {
        throw new Error(`目标标签不存在: ${targetTagId}`)
      }

      // 获取所有源标签
      const sourceTags = await Promise.all(
        sourceTagIds.map(id => indexedDBService.getTag(id))
      )

      // 检查所有源标签是否存在
      const missingTags = sourceTags
        .map((tag, index) => ({ tag, id: sourceTagIds[index] }))
        .filter(({ tag }) => !tag)
        .map(({ id }) => id)

      if (missingTags.length > 0) {
        throw new Error(`源标签不存在: ${missingTags.join(', ')}`)
      }

      const validSourceTags = sourceTags.filter(Boolean) as Tag[]
      const sourceTagNames = validSourceTags.map(tag => tag.name)

      // 获取所有书签
      const bookmarks = await indexedDBService.getBookmarks()

      // 更新使用任何源标签的书签
      for (const bookmark of bookmarks) {
        const hasSourceTag = bookmark.tags.some(tag => sourceTagNames.includes(tag))
        
        if (hasSourceTag) {
          // 移除所有源标签，添加目标标签
          const updatedTags = bookmark.tags
            .filter(tag => !sourceTagNames.includes(tag)) // 移除所有源标签
            .concat(targetTag.name) // 添加目标标签
          
          // 去重
          const uniqueTags = Array.from(new Set(updatedTags))
          
          await indexedDBService.updateBookmark(bookmark.id, { tags: uniqueTags })
        }
      }

      // 删除所有源标签
      for (const sourceTagId of sourceTagIds) {
        await indexedDBService.deleteTag(sourceTagId)
      }

      // 更新标签使用计数
      await this.updateAllTagUsageCounts()

      console.log(`批量合并标签成功: [${sourceTagNames.join(', ')}] -> ${targetTag.name}`)
    } catch (error) {
      console.error('批量合并标签失败:', error)
      throw error
    }
  }

  /**
   * 搜索标签
   * @param query 搜索关键词
   * @returns Promise<Tag[]> 搜索结果
   */
  async searchTags(query: string): Promise<Tag[]> {
    try {
      const tags = await indexedDBService.getTags()
      const searchQuery = query.toLowerCase().trim()
      
      if (!searchQuery) {
        return tags
      }

      return tags.filter(tag => 
        tag.name.toLowerCase().includes(searchQuery)
      )
    } catch (error) {
      console.error('搜索标签失败:', error)
      return []
    }
  }

  /**
   * 获取热门标签
   * @param limit 返回数量限制（默认10）
   * @returns Promise<TagWithStats[]> 热门标签列表
   */
  async getPopularTags(limit: number = 10): Promise<TagWithStats[]> {
    try {
      const tagsWithStats = await this.getAllTagsWithStats()
      
      return tagsWithStats
        .sort((a, b) => b.usageCount - a.usageCount)
        .slice(0, limit)
    } catch (error) {
      console.error('获取热门标签失败:', error)
      return []
    }
  }

  /**
   * 排序标签
   * @param tags 标签列表
   * @param sortBy 排序选项
   * @returns 排序后的标签列表
   */
  sortTags(tags: TagWithStats[], sortBy: TagSortOption): TagWithStats[] {
    return [...tags].sort((a, b) => {
      switch (sortBy) {
        case 'name-asc':
          return a.name.localeCompare(b.name)
        case 'name-desc':
          return b.name.localeCompare(a.name)
        case 'usage-asc':
          return a.usageCount - b.usageCount
        case 'usage-desc':
          return b.usageCount - a.usageCount
        case 'created-asc':
          return a.createdAt.getTime() - b.createdAt.getTime()
        case 'created-desc':
          return b.createdAt.getTime() - a.createdAt.getTime()
        case 'updated-asc':
          return a.updatedAt.getTime() - b.updatedAt.getTime()
        case 'updated-desc':
          return b.updatedAt.getTime() - a.updatedAt.getTime()
        default:
          return 0
      }
    })
  }

  /**
   * 筛选标签
   * @param tags 标签列表
   * @param query 搜索关键词
   * @returns 筛选后的标签列表
   */
  filterTags(tags: TagWithStats[], query: string): TagWithStats[] {
    if (!query.trim()) {
      return tags
    }

    const searchQuery = query.toLowerCase().trim()
    return tags.filter(tag => 
      tag.name.toLowerCase().includes(searchQuery)
    )
  }

  // ==================== 私有辅助方法 ====================

  /**
   * 生成标签ID
   * @returns string 唯一的标签ID
   */
  private generateTagId(): string {
    return `tag_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * 生成标签颜色
   * @param name 标签名称
   * @returns string 颜色值
   */
  private generateTagColor(name: string): string {
    // 基于标签名称生成一致的颜色
    const colors = [
      '#3B82F6', // 蓝色
      '#10B981', // 绿色
      '#F59E0B', // 黄色
      '#EF4444', // 红色
      '#8B5CF6', // 紫色
      '#06B6D4', // 青色
      '#F97316', // 橙色
      '#84CC16', // 青绿色
      '#EC4899', // 粉色
      '#6B7280'  // 灰色
    ]
    
    // 使用名称的哈希值选择颜色
    let hash = 0
    for (let i = 0; i < name.length; i++) {
      hash = ((hash << 5) - hash + name.charCodeAt(i)) & 0xffffffff
    }
    
    return colors[Math.abs(hash) % colors.length]
  }

  /**
   * 更新书签中的标签名称（当标签名称发生变化时）
   * @param oldTagName 旧标签名称
   * @param newTagName 新标签名称
   * @returns Promise<void>
   */
  private async updateBookmarkTags(oldTagName: string, newTagName: string): Promise<void> {
    try {
      const bookmarks = await indexedDBService.getBookmarks()
      const bookmarksToUpdate = bookmarks.filter(bookmark => 
        bookmark.tags.includes(oldTagName)
      )
      
      for (const bookmark of bookmarksToUpdate) {
        const updatedTags = bookmark.tags.map(tag => 
          tag === oldTagName ? newTagName : tag
        )
        
        await indexedDBService.updateBookmark(bookmark.id, { 
          tags: updatedTags 
        })
      }
      
      console.log(`已更新 ${bookmarksToUpdate.length} 个书签的标签名称`)
    } catch (error) {
      console.error('更新书签标签名称失败:', error)
      throw error
    }
  }

  /**
   * 从书签中移除指定标签
   * @param tagName 要移除的标签名称
   * @returns Promise<void>
   */
  private async removeTagFromBookmarks(tagName: string): Promise<void> {
    try {
      const bookmarks = await indexedDBService.getBookmarks()
      const bookmarksToUpdate = bookmarks.filter(bookmark => 
        bookmark.tags.includes(tagName)
      )
      
      for (const bookmark of bookmarksToUpdate) {
        const updatedTags = bookmark.tags.filter(tag => tag !== tagName)
        
        await indexedDBService.updateBookmark(bookmark.id, { 
          tags: updatedTags 
        })
      }
      
      console.log(`已从 ${bookmarksToUpdate.length} 个书签中移除标签`)
    } catch (error) {
      console.error('从书签中移除标签失败:', error)
      throw error
    }
  }

  /**
   * 更新所有标签的使用次数
   * @returns Promise<void>
   */
  private async updateAllTagUsageCounts(): Promise<void> {
    try {
      const [tags, bookmarks] = await Promise.all([
        indexedDBService.getTags(),
        indexedDBService.getBookmarks()
      ])

      // 计算每个标签的使用次数
      const tagUsageMap = new Map<string, number>()
      
      bookmarks.forEach(bookmark => {
        bookmark.tags.forEach(tagName => {
          tagUsageMap.set(tagName, (tagUsageMap.get(tagName) || 0) + 1)
        })
      })

      // 更新每个标签的使用次数
      for (const tag of tags) {
        const usageCount = tagUsageMap.get(tag.name) || 0
        if (tag.usageCount !== usageCount) {
          await indexedDBService.updateTag(tag.id, { usageCount })
        }
      }

      console.log('标签使用次数更新完成')
    } catch (error) {
      console.error('更新标签使用次数失败:', error)
      // 不抛出错误，避免影响主要功能
    }
  }
}

// 导出单例实例
export const tagService = new TagService()