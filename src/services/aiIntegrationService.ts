// AI集成服务类 - 统一管理AI集成相关的业务逻辑

import {
  AIProviderConfig,
  AIProviderInfo,
  AIConnectionResult,
  AIModel,
  AIProvider
} from '../types/ai'
import { aiProviderService } from './aiProviderService'
// import { aiModelService } from './aiModelService'
// import { aiConfigService } from './aiConfigService'
import { ChromeStorageService } from '../utils/chromeStorage'

/**
 * AI集成服务类
 * 提供统一的AI集成管理接口
 */
export class AIIntegrationService {
  
  /**
   * 支持的AI提供商列表
   */
  private static readonly SUPPORTED_PROVIDERS: AIProviderInfo[] = [
    {
      id: 'ollama',
      name: 'Olla<PERSON>',
      description: '本地部署的开源AI模型服务',
      icon: '🦙',
      type: 'ollama',
      defaultBaseUrl: 'http://localhost:11434',
      requiresApiKey: false,
      supportedFeatures: ['chat', 'completion', 'embedding'],
      documentationUrl: 'https://ollama.ai/docs'
    },
    {
      id: 'lm-studio',
      name: 'LM Studio',
      description: '本地AI模型运行环境',
      icon: '🎬',
      type: 'lm-studio',
      defaultBaseUrl: 'http://localhost:1234/v1',
      requiresApiKey: false,
      supportedFeatures: ['chat', 'completion'],
      documentationUrl: 'https://lmstudio.ai/docs'
    },
    {
      id: 'openrouter',
      name: 'OpenRouter',
      description: '多模型聚合API服务',
      icon: '🌐',
      type: 'openrouter',
      defaultBaseUrl: 'https://openrouter.ai/api/v1',
      requiresApiKey: true,
      supportedFeatures: ['chat', 'completion'],
      documentationUrl: 'https://openrouter.ai/docs'
    },
    {
      id: 'openai',
      name: 'OpenAI',
      description: 'OpenAI GPT系列模型',
      icon: '🤖',
      type: 'openai',
      defaultBaseUrl: 'https://api.openai.com/v1',
      requiresApiKey: true,
      supportedFeatures: ['chat', 'completion', 'embedding'],
      documentationUrl: 'https://platform.openai.com/docs'
    },
    {
      id: 'azure-openai',
      name: 'Azure OpenAI',
      description: 'Microsoft Azure部署的OpenAI模型',
      icon: '☁️',
      type: 'azure-openai',
      defaultBaseUrl: 'https://your-resource.openai.azure.com',
      requiresApiKey: true,
      supportedFeatures: ['chat', 'completion', 'embedding', 'image-generation'],
      documentationUrl: 'https://docs.microsoft.com/azure/cognitive-services/openai/'
    },
    {
      id: 'claude',
      name: 'Anthropic Claude',
      description: 'Anthropic的Claude系列模型',
      icon: '🧠',
      type: 'claude',
      defaultBaseUrl: 'https://api.anthropic.com/v1',
      requiresApiKey: true,
      supportedFeatures: ['chat'],
      documentationUrl: 'https://docs.anthropic.com'
    },
    {
      id: 'gemini',
      name: 'Google Gemini',
      description: 'Google的Gemini系列模型',
      icon: '💎',
      type: 'gemini',
      defaultBaseUrl: 'https://generativelanguage.googleapis.com/v1',
      requiresApiKey: true,
      supportedFeatures: ['chat', 'completion'],
      documentationUrl: 'https://ai.google.dev/docs'
    },
    {
      id: 'deepseek',
      name: 'DeepSeek',
      description: 'DeepSeek AI模型服务',
      icon: '🔍',
      type: 'deepseek',
      defaultBaseUrl: 'https://api.deepseek.com',
      requiresApiKey: true,
      supportedFeatures: ['chat', 'completion'],
      documentationUrl: 'https://platform.deepseek.com/docs'
    },
    {
      id: 'zhipu',
      name: '智谱AI',
      description: '智谱AI GLM系列模型',
      icon: '🧮',
      type: 'zhipu',
      defaultBaseUrl: 'https://open.bigmodel.cn/api/paas/v4',
      requiresApiKey: true,
      supportedFeatures: ['chat', 'completion'],
      documentationUrl: 'https://open.bigmodel.cn/dev/api'
    },
    {
      id: 'qwen',
      name: '通义千问',
      description: '阿里云通义千问模型',
      icon: '☁️',
      type: 'qwen',
      defaultBaseUrl: 'https://dashscope.aliyuncs.com/api/v1',
      requiresApiKey: true,
      supportedFeatures: ['chat', 'completion'],
      documentationUrl: 'https://help.aliyun.com/zh/dashscope'
    },
    {
      id: 'together',
      name: 'Together AI',
      description: 'Together AI模型聚合服务',
      icon: '🤝',
      type: 'together',
      defaultBaseUrl: 'https://api.together.xyz/v1',
      requiresApiKey: true,
      supportedFeatures: ['chat', 'completion'],
      documentationUrl: 'https://docs.together.ai'
    },
    {
      id: 'grok',
      name: 'xAI Grok',
      description: 'xAI的Grok模型',
      icon: '🚀',
      type: 'grok',
      defaultBaseUrl: 'https://api.x.ai/v1',
      requiresApiKey: true,
      supportedFeatures: ['chat'],
      documentationUrl: 'https://docs.x.ai'
    },
    {
      id: 'xinference',
      name: 'Xinference',
      description: 'Xinference分布式推理引擎',
      icon: '🔗',
      type: 'xinference',
      defaultBaseUrl: 'http://localhost:9997',
      requiresApiKey: false,
      supportedFeatures: ['chat', 'completion', 'embedding'],
      documentationUrl: 'https://inference.readthedocs.io'
    },
    {
      id: 'custom',
      name: '自定义API',
      description: '自定义AI服务API',
      icon: '⚙️',
      type: 'custom',
      defaultBaseUrl: '',
      requiresApiKey: false,
      supportedFeatures: ['chat', 'completion'],
      documentationUrl: ''
    }
  ]

  /**
   * 存储键名
   */
  private static readonly STORAGE_KEY = 'ai_providers'
  private static readonly SELECTED_MODELS_KEY = 'ai_selected_models'

  /**
   * 获取支持的提供商列表
   * @returns AIProviderInfo[]
   */
  getSupportedProviders(): AIProviderInfo[] {
    return [...AIIntegrationService.SUPPORTED_PROVIDERS]
  }

  /**
   * 获取提供商信息
   * @param providerId 提供商ID
   * @returns AIProviderInfo | null
   */
  getProviderInfo(providerId: string): AIProviderInfo | null {
    return AIIntegrationService.SUPPORTED_PROVIDERS.find(p => p.id === providerId) || null
  }

  /**
   * 获取已配置的提供商列表
   * @returns Promise<AIProviderConfig[]>
   */
  async getConfiguredProviders(): Promise<AIProviderConfig[]> {
    try {
      const providers = await ChromeStorageService.getSyncSetting(AIIntegrationService.STORAGE_KEY, [])
      return providers.map((provider: any) => ({
        ...provider,
        createdAt: new Date(provider.createdAt),
        updatedAt: new Date(provider.updatedAt)
      }))
    } catch (error) {
      console.error('获取已配置提供商失败:', error)
      return []
    }
  }

  /**
   * 配置AI提供商
   * @param config 提供商配置
   * @returns Promise<void>
   */
  async configureProvider(config: Omit<AIProviderConfig, 'id' | 'createdAt' | 'updatedAt'>): Promise<void> {
    try {
      // 验证提供商信息
      const providerInfo = this.getProviderInfo(config.type)
      if (!providerInfo) {
        throw new Error(`不支持的提供商类型: ${config.type}`)
      }

      // 验证必需字段
      if (!config.name.trim()) {
        throw new Error('提供商名称不能为空')
      }

      if (!config.baseUrl.trim()) {
        throw new Error('API基础URL不能为空')
      }

      if (providerInfo.requiresApiKey && !config.apiKey?.trim()) {
        throw new Error('该提供商需要API密钥')
      }

      // 验证URL格式
      try {
        new URL(config.baseUrl)
      } catch {
        throw new Error('API基础URL格式无效')
      }

      const providers = await this.getConfiguredProviders()
      
      // 检查是否已存在同名提供商
      const existingProvider = providers.find(p => p.name === config.name)
      if (existingProvider) {
        throw new Error('已存在同名的提供商配置')
      }

      const newProvider: AIProviderConfig = {
        ...config,
        id: `${config.type}_${Date.now()}`,
        createdAt: new Date(),
        updatedAt: new Date()
      }

      const updatedProviders = [...providers, newProvider]
      await ChromeStorageService.saveSyncSetting(AIIntegrationService.STORAGE_KEY, updatedProviders)

      console.log('AI提供商配置成功:', newProvider.name)
    } catch (error) {
      console.error('配置AI提供商失败:', error)
      throw error
    }
  }

  /**
   * 更新提供商配置
   * @param providerId 提供商ID
   * @param updates 更新内容
   * @returns Promise<void>
   */
  async updateProvider(providerId: string, updates: Partial<Omit<AIProviderConfig, 'id' | 'createdAt'>>): Promise<void> {
    try {
      const providers = await this.getConfiguredProviders()
      const providerIndex = providers.findIndex(p => p.id === providerId)
      
      if (providerIndex === -1) {
        throw new Error('未找到指定的提供商配置')
      }

      const updatedProvider = {
        ...providers[providerIndex],
        ...updates,
        updatedAt: new Date()
      }

      // 验证更新后的配置
      if (updates.name && !updates.name.trim()) {
        throw new Error('提供商名称不能为空')
      }

      if (updates.baseUrl) {
        try {
          new URL(updates.baseUrl)
        } catch {
          throw new Error('API基础URL格式无效')
        }
      }

      providers[providerIndex] = updatedProvider
      await ChromeStorageService.saveSyncSetting(AIIntegrationService.STORAGE_KEY, providers)

      console.log('AI提供商配置更新成功:', updatedProvider.name)
    } catch (error) {
      console.error('更新AI提供商配置失败:', error)
      throw error
    }
  }

  /**
   * 删除提供商配置
   * @param providerId 提供商ID
   * @returns Promise<void>
   */
  async removeProvider(providerId: string): Promise<void> {
    try {
      const providers = await this.getConfiguredProviders()
      const filteredProviders = providers.filter(p => p.id !== providerId)
      
      if (filteredProviders.length === providers.length) {
        throw new Error('未找到指定的提供商配置')
      }

      await ChromeStorageService.saveSyncSetting(AIIntegrationService.STORAGE_KEY, filteredProviders)
      
      // 清理相关的模型缓存 (暂时跳过)
      console.log(`清理模型缓存: ${providerId}`)

      console.log('AI提供商配置删除成功')
    } catch (error) {
      console.error('删除AI提供商配置失败:', error)
      throw error
    }
  }

  /**
   * 测试提供商连接
   * @param providerId 提供商ID
   * @returns Promise<AIConnectionResult>
   */
  async testConnection(providerId: string): Promise<AIConnectionResult> {
    try {
      const providers = await this.getConfiguredProviders()
      const provider = providers.find(p => p.id === providerId)
      
      if (!provider) {
        throw new Error('未找到指定的提供商配置')
      }

      console.log(`开始测试AI提供商连接: ${provider.name}`)
      
      const startTime = Date.now()
      
      // 使用真实的aiProviderService进行连接测试
      const testResult = await aiProviderService.testConnection(provider)
      
      const responseTime = Date.now() - startTime
      
      const result: AIConnectionResult = {
        providerId: provider.id,
        success: testResult.success,
        responseTime,
        modelCount: testResult.modelCount,
        testedAt: new Date(),
        error: testResult.error
      }
      
      console.log(`AI提供商连接测试完成: ${provider.name} - ${result.success ? '成功' : '失败'}`)
      
      return result
    } catch (error) {
      console.error('测试AI提供商连接失败:', error)
      return {
        providerId,
        success: false,
        error: (error as Error).message,
        testedAt: new Date()
      }
    }
  }

  /**
   * 测试所有提供商连接
   * @returns Promise<AIConnectionResult[]>
   */
  async testAllConnections(): Promise<AIConnectionResult[]> {
    try {
      const providers = await this.getConfiguredProviders()
      const results: AIConnectionResult[] = []

      console.log(`开始测试所有AI提供商连接，共 ${providers.length} 个`)

      for (const provider of providers) {
        if (provider.enabled) {
          try {
            const result = await this.testConnection(provider.id)
            results.push(result)
          } catch (error) {
            results.push({
              providerId: provider.id,
              success: false,
              error: (error as Error).message,
              testedAt: new Date()
            })
          }
        }
      }

      const successCount = results.filter(r => r.success).length
      console.log(`所有AI提供商连接测试完成: ${successCount}/${results.length} 成功`)

      return results
    } catch (error) {
      console.error('测试所有AI提供商连接失败:', error)
      return []
    }
  }

  /**
   * 获取可用模型列表
   * @param providerId 提供商ID
   * @param forceRefresh 是否强制刷新
   * @returns Promise<AIModel[]>
   */
  async getAvailableModels(providerId: string, forceRefresh: boolean = false): Promise<AIModel[]> {
    try {
      const providers = await this.getConfiguredProviders()
      const provider = providers.find(p => p.id === providerId)
      
      if (!provider) {
        throw new Error('未找到指定的提供商配置')
      }

      console.log(`获取AI模型列表: ${provider.name}`)
      
      // 使用真实的aiProviderService获取模型列表
      const models = await aiProviderService.getModels(provider)
      
      console.log(`获取AI模型列表成功: ${provider.name} - ${models.length} 个模型`)
      
      return models
    } catch (error) {
      console.error('获取AI模型列表失败:', (error as Error).message)
      // 如果获取失败，返回模拟数据作为后备
      const providers = await this.getConfiguredProviders()
      const provider = providers.find(p => p.id === providerId)
      if (provider) {
        console.log(`使用模拟数据作为后备: ${provider.name}`)
        return this.generateMockModels(provider.type)
      }
      return []
    }
  }

  /**
   * 搜索模型
   * @param providerId 提供商ID
   * @param query 搜索关键词
   * @returns Promise<AIModel[]>
   */
  async searchModels(providerId: string, query: string): Promise<AIModel[]> {
    try {
      const models = await this.getAvailableModels(providerId)
      // 简单的模型搜索实现
      return models.filter(model => 
        model.name.toLowerCase().includes(query.toLowerCase()) ||
        model.displayName.toLowerCase().includes(query.toLowerCase()) ||
        model.description?.toLowerCase().includes(query.toLowerCase())
      )
    } catch (error) {
      console.error('搜索AI模型失败:', (error as Error).message)
      return []
    }
  }

  /**
   * 保存配置
   * @returns Promise<void>
   */
  async saveConfiguration(): Promise<void> {
    try {
      // 配置已经在各个操作中自动保存
      console.log('AI集成配置保存成功')
    } catch (error) {
      console.error('保存AI集成配置失败:', (error as Error).message)
      throw error
    }
  }

  /**
   * 加载配置
   * @returns Promise<void>
   */
  async loadConfiguration(): Promise<void> {
    try {
      // 配置会在需要时自动加载
      console.log('AI集成配置加载成功')
    } catch (error) {
      console.error('加载AI集成配置失败:', (error as Error).message)
      throw error
    }
  }

  /**
   * 导出配置
   * @returns Promise<string>
   */
  async exportConfiguration(): Promise<string> {
    try {
      const providers = await this.getConfiguredProviders()
      
      // 移除敏感信息
      const exportData = providers.map(provider => ({
        ...provider,
        apiKey: provider.apiKey ? '***已隐藏***' : undefined
      }))

      const exportConfig = {
        version: '1.0',
        exportedAt: new Date().toISOString(),
        providers: exportData
      }

      return JSON.stringify(exportConfig, null, 2)
    } catch (error) {
      console.error('导出AI集成配置失败:', (error as Error).message)
      throw error
    }
  }

  /**
   * 导入配置
   * @param configJson 配置JSON字符串
   * @returns Promise<void>
   */
  async importConfiguration(configJson: string): Promise<void> {
    try {
      const importData = JSON.parse(configJson)
      
      if (!importData.providers || !Array.isArray(importData.providers)) {
        throw new Error('无效的配置格式')
      }

      const currentProviders = await this.getConfiguredProviders()
      
      for (const providerData of importData.providers) {
        // 跳过已隐藏API密钥的配置
        if (providerData.apiKey === '***已隐藏***') {
          continue
        }

        // 检查是否已存在同名配置
        const existingProvider = currentProviders.find(p => p.name === providerData.name)
        if (existingProvider) {
          console.warn(`跳过已存在的提供商配置: ${providerData.name}`)
          continue
        }

        // 导入新配置
        await this.configureProvider({
          name: providerData.name,
          type: providerData.type,
          baseUrl: providerData.baseUrl,
          apiKey: providerData.apiKey,
          headers: providerData.headers,
          timeout: providerData.timeout,
          enabled: providerData.enabled ?? true
        })
      }

      console.log('AI集成配置导入成功')
    } catch (error) {
      console.error('导入AI集成配置失败:', (error as Error).message)
      throw error
    }
  }

  /**
   * 保存选中的模型
   * @param providerId 提供商ID
   * @param modelId 模型ID
   * @returns Promise<void>
   */
  async saveSelectedModel(providerId: string, modelId: string): Promise<void> {
    try {
      const selectedModels = await this.getSelectedModels()
      selectedModels[providerId] = {
        modelId,
        selectedAt: new Date().toISOString()
      }
      
      await ChromeStorageService.saveSyncSetting(AIIntegrationService.SELECTED_MODELS_KEY, selectedModels)
      console.log(`模型选择已保存: ${providerId} -> ${modelId}`)
    } catch (error) {
      console.error('保存模型选择失败:', (error as Error).message)
      throw error
    }
  }

  /**
   * 获取选中的模型
   * @returns Promise<Record<string, {modelId: string, selectedAt: string}>>
   */
  async getSelectedModels(): Promise<Record<string, {modelId: string, selectedAt: string}>> {
    try {
      return await ChromeStorageService.getSyncSetting(AIIntegrationService.SELECTED_MODELS_KEY, {})
    } catch (error) {
      console.error('获取选中模型失败:', (error as Error).message)
      return {}
    }
  }

  /**
   * 移除选中的模型
   * @param providerId 提供商ID
   * @returns Promise<void>
   */
  async removeSelectedModel(providerId: string): Promise<void> {
    try {
      const selectedModels = await this.getSelectedModels()
      delete selectedModels[providerId]
      
      await ChromeStorageService.saveSyncSetting(AIIntegrationService.SELECTED_MODELS_KEY, selectedModels)
      console.log(`模型选择已移除: ${providerId}`)
    } catch (error) {
      console.error('移除模型选择失败:', (error as Error).message)
      throw error
    }
  }

  /**
   * 获取提供商统计信息
   * @returns Promise<{total: number, enabled: number, connected: number}>
   */
  async getProviderStats(): Promise<{total: number, enabled: number, connected: number}> {
    try {
      const providers = await this.getConfiguredProviders()
      const total = providers.length
      const enabled = providers.filter(p => p.enabled).length
      
      // 测试连接状态
      const connectionResults = await this.testAllConnections()
      const connected = connectionResults.filter(r => r.success).length

      return { total, enabled, connected }
    } catch (error) {
      console.error('获取提供商统计信息失败:', (error as Error).message)
      return { total: 0, enabled: 0, connected: 0 }
    }
  }

  /**
   * 生成模拟模型列表
   * @param providerType 提供商类型
   * @returns AIModel[]
   */
  private generateMockModels(providerType: AIProvider): AIModel[] {
    const models: AIModel[] = []
    
    switch (providerType) {
      case 'openai':
        models.push(
          {
            id: 'gpt-4',
            name: 'gpt-4',
            displayName: 'GPT-4',
            description: 'OpenAI最先进的大型语言模型',
            capabilities: ['chat', 'completion', 'reasoning'],
            tags: ['OpenAI', 'GPT-4', '推理', '对话'],
            providerId: 'openai',
            isRecommended: true,
            isPopular: true
          },
          {
            id: 'gpt-3.5-turbo',
            name: 'gpt-3.5-turbo',
            displayName: 'GPT-3.5 Turbo',
            description: 'OpenAI高效的对话模型',
            capabilities: ['chat', 'completion'],
            tags: ['OpenAI', 'GPT-3.5', '对话', '高效'],
            providerId: 'openai',
            isRecommended: true,
            isPopular: true
          }
        )
        break
        
      case 'claude':
        models.push(
          {
            id: 'claude-3-opus',
            name: 'claude-3-opus',
            displayName: 'Claude 3 Opus',
            description: 'Anthropic最强大的AI模型',
            capabilities: ['chat', 'reasoning', 'analysis'],
            tags: ['Anthropic', 'Claude', '推理', '分析'],
            providerId: 'claude',
            isRecommended: true,
            isPopular: true
          },
          {
            id: 'claude-3-sonnet',
            name: 'claude-3-sonnet',
            displayName: 'Claude 3 Sonnet',
            description: 'Anthropic平衡性能的AI模型',
            capabilities: ['chat', 'reasoning'],
            tags: ['Anthropic', 'Claude', '平衡'],
            providerId: 'claude',
            isRecommended: true
          }
        )
        break
        
      case 'gemini':
        models.push(
          {
            id: 'gemini-pro',
            name: 'gemini-pro',
            displayName: 'Gemini Pro',
            description: 'Google先进的多模态AI模型',
            capabilities: ['chat', 'completion', 'multimodal'],
            tags: ['Google', 'Gemini', '多模态'],
            providerId: 'gemini',
            isRecommended: true,
            isPopular: true
          }
        )
        break
        
      case 'deepseek':
        models.push(
          {
            id: 'deepseek-chat',
            name: 'deepseek-chat',
            displayName: 'DeepSeek Chat',
            description: 'DeepSeek对话模型',
            capabilities: ['chat', 'completion'],
            tags: ['DeepSeek', '对话', '中文'],
            providerId: 'deepseek',
            isRecommended: true
          }
        )
        break
        
      case 'zhipu':
        models.push(
          {
            id: 'glm-4',
            name: 'glm-4',
            displayName: 'GLM-4',
            description: '智谱AI最新一代对话模型',
            capabilities: ['chat', 'completion'],
            tags: ['智谱AI', 'GLM', '中文', '对话'],
            providerId: 'zhipu',
            isRecommended: true
          }
        )
        break
        
      case 'qwen':
        models.push(
          {
            id: 'qwen-turbo',
            name: 'qwen-turbo',
            displayName: '通义千问 Turbo',
            description: '阿里云通义千问高效模型',
            capabilities: ['chat', 'completion'],
            tags: ['阿里云', '通义千问', '中文'],
            providerId: 'qwen',
            isRecommended: true
          }
        )
        break
        
      default:
        models.push(
          {
            id: 'default-model',
            name: 'default-model',
            displayName: '默认模型',
            description: '通用AI模型',
            capabilities: ['chat', 'completion'],
            tags: ['通用'],
            providerId: providerType,
            isRecommended: false
          }
        )
    }
    
    return models
  }
}

// 导出单例实例
export const aiIntegrationService = new AIIntegrationService()