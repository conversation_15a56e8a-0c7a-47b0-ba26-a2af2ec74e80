// 安全验证服务 - 提供文件安全检查和数据脱敏功能

import { ValidationResult, ValidationError, ImportData } from '../types'

/**
 * 安全风险级别
 */
export enum SecurityRiskLevel {
  SAFE = 'SAFE',
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  CRITICAL = 'CRITICAL'
}

/**
 * 安全检查结果
 */
export interface SecurityCheckResult {
  safe: boolean
  riskLevel: SecurityRiskLevel
  issues: SecurityIssue[]
  recommendations: string[]
}

/**
 * 安全问题
 */
export interface SecurityIssue {
  type: string
  severity: SecurityRiskLevel
  description: string
  location?: string
  suggestion: string
}

/**
 * 文件安全配置
 */
export interface FileSecurityConfig {
  maxFileSize: number // 最大文件大小（字节）
  allowedMimeTypes: string[] // 允许的MIME类型
  allowedExtensions: string[] // 允许的文件扩展名
  scanContent: boolean // 是否扫描文件内容
  blockExecutable: boolean // 是否阻止可执行文件
}

/**
 * 内容安全配置
 */
export interface ContentSecurityConfig {
  maxStringLength: number // 最大字符串长度
  allowedProtocols: string[] // 允许的URL协议
  blockScripts: boolean // 是否阻止脚本内容
  blockExternalLinks: boolean // 是否阻止外部链接
  sanitizeHtml: boolean // 是否清理HTML
}

/**
 * 安全验证服务类
 * 提供文件类型验证、内容安全检查、数据脱敏等功能
 */
export class SecurityValidator {
  private readonly defaultFileConfig: FileSecurityConfig = {
    maxFileSize: 50 * 1024 * 1024, // 50MB
    allowedMimeTypes: [
      'application/json',
      'text/plain',
      'text/csv',
      'text/html',
      'application/xml',
      'text/xml'
    ],
    allowedExtensions: ['.json', '.txt', '.csv', '.html', '.htm', '.xml'],
    scanContent: true,
    blockExecutable: true
  }

  private readonly defaultContentConfig: ContentSecurityConfig = {
    maxStringLength: 100000, // 100KB
    allowedProtocols: ['http:', 'https:', 'ftp:', 'mailto:'],
    blockScripts: true,
    blockExternalLinks: false,
    sanitizeHtml: true
  }

  /**
   * 验证文件安全性
   * @param file 文件对象
   * @param config 安全配置
   * @returns Promise<SecurityCheckResult>
   */
  async validateFile(
    file: File, 
    config: Partial<FileSecurityConfig> = {}
  ): Promise<SecurityCheckResult> {
    const finalConfig = { ...this.defaultFileConfig, ...config }
    const issues: SecurityIssue[] = []
    let riskLevel = SecurityRiskLevel.SAFE

    // 检查文件大小
    if (file.size > finalConfig.maxFileSize) {
      issues.push({
        type: 'FILE_SIZE',
        severity: SecurityRiskLevel.MEDIUM,
        description: `文件大小 ${this.formatFileSize(file.size)} 超过限制 ${this.formatFileSize(finalConfig.maxFileSize)}`,
        suggestion: '请选择更小的文件或分批导入'
      })
      riskLevel = this.updateRiskLevel(riskLevel, SecurityRiskLevel.MEDIUM)
    }

    // 检查文件类型
    const mimeTypeCheck = this.validateMimeType(file, finalConfig)
    if (mimeTypeCheck.issues.length > 0) {
      issues.push(...mimeTypeCheck.issues)
      riskLevel = this.updateRiskLevel(riskLevel, mimeTypeCheck.riskLevel)
    }

    // 检查文件扩展名
    const extensionCheck = this.validateFileExtension(file, finalConfig)
    if (extensionCheck.issues.length > 0) {
      issues.push(...extensionCheck.issues)
      riskLevel = this.updateRiskLevel(riskLevel, extensionCheck.riskLevel)
    }

    // 检查可执行文件
    if (finalConfig.blockExecutable) {
      const executableCheck = this.checkExecutableFile(file)
      if (executableCheck.issues.length > 0) {
        issues.push(...executableCheck.issues)
        riskLevel = this.updateRiskLevel(riskLevel, executableCheck.riskLevel)
      }
    }

    // 扫描文件内容
    if (finalConfig.scanContent && file.size < 10 * 1024 * 1024) { // 只扫描小于10MB的文件
      try {
        const content = await this.readFileContent(file)
        const contentCheck = await this.validateFileContent(content)
        if (contentCheck.issues.length > 0) {
          issues.push(...contentCheck.issues)
          riskLevel = this.updateRiskLevel(riskLevel, contentCheck.riskLevel)
        }
      } catch (error) {
        issues.push({
          type: 'CONTENT_SCAN',
          severity: SecurityRiskLevel.LOW,
          description: '无法扫描文件内容',
          suggestion: '手动检查文件内容的安全性'
        })
      }
    }

    return {
      safe: riskLevel === SecurityRiskLevel.SAFE || riskLevel === SecurityRiskLevel.LOW,
      riskLevel,
      issues,
      recommendations: this.generateRecommendations(issues)
    }
  }

  /**
   * 验证导入数据的安全性
   * @param data 导入数据
   * @param config 内容安全配置
   * @returns SecurityCheckResult
   */
  validateImportDataSecurity(
    data: ImportData, 
    config: Partial<ContentSecurityConfig> = {}
  ): SecurityCheckResult {
    const finalConfig = { ...this.defaultContentConfig, ...config }
    const issues: SecurityIssue[] = []
    let riskLevel = SecurityRiskLevel.SAFE

    // 检查收藏夹数据
    if (data.bookmarks) {
      data.bookmarks.forEach((bookmark, index) => {
        const bookmarkCheck = this.validateBookmarkSecurity(bookmark, finalConfig, `bookmarks[${index}]`)
        if (bookmarkCheck.issues.length > 0) {
          issues.push(...bookmarkCheck.issues)
          riskLevel = this.updateRiskLevel(riskLevel, bookmarkCheck.riskLevel)
        }
      })
    }

    // 检查分类数据
    if (data.categories) {
      data.categories.forEach((category, index) => {
        const categoryCheck = this.validateCategorySecurity(category, finalConfig, `categories[${index}]`)
        if (categoryCheck.issues.length > 0) {
          issues.push(...categoryCheck.issues)
          riskLevel = this.updateRiskLevel(riskLevel, categoryCheck.riskLevel)
        }
      })
    }

    // 检查标签数据
    if (data.tags) {
      data.tags.forEach((tag, index) => {
        const tagCheck = this.validateTagSecurity(tag, finalConfig, `tags[${index}]`)
        if (tagCheck.issues.length > 0) {
          issues.push(...tagCheck.issues)
          riskLevel = this.updateRiskLevel(riskLevel, tagCheck.riskLevel)
        }
      })
    }

    return {
      safe: riskLevel === SecurityRiskLevel.SAFE || riskLevel === SecurityRiskLevel.LOW,
      riskLevel,
      issues,
      recommendations: this.generateRecommendations(issues)
    }
  }

  /**
   * 数据脱敏处理
   * @param data 原始数据
   * @returns 脱敏后的数据
   */
  sanitizeImportData(data: ImportData): ImportData {
    const sanitizedData = JSON.parse(JSON.stringify(data)) // 深拷贝

    // 脱敏收藏夹数据
    if (sanitizedData.bookmarks) {
      sanitizedData.bookmarks = sanitizedData.bookmarks.map(bookmark => ({
        ...bookmark,
        title: this.sanitizeText(bookmark.title),
        description: bookmark.description ? this.sanitizeText(bookmark.description) : undefined,
        content: bookmark.content ? this.sanitizeText(bookmark.content) : undefined,
        url: bookmark.url ? this.sanitizeUrl(bookmark.url) : undefined
      }))
    }

    // 脱敏分类数据
    if (sanitizedData.categories) {
      sanitizedData.categories = sanitizedData.categories.map(category => ({
        ...category,
        name: this.sanitizeText(category.name),
        description: category.description ? this.sanitizeText(category.description) : undefined
      }))
    }

    // 脱敏标签数据
    if (sanitizedData.tags) {
      sanitizedData.tags = sanitizedData.tags.map(tag => ({
        ...tag,
        name: this.sanitizeText(tag.name)
      }))
    }

    return sanitizedData
  }

  /**
   * 检查URL安全性
   * @param url URL字符串
   * @returns SecurityCheckResult
   */
  validateUrlSecurity(url: string): SecurityCheckResult {
    const issues: SecurityIssue[] = []
    let riskLevel = SecurityRiskLevel.SAFE

    try {
      const urlObj = new URL(url)
      
      // 检查协议
      if (!this.defaultContentConfig.allowedProtocols.includes(urlObj.protocol)) {
        issues.push({
          type: 'UNSAFE_PROTOCOL',
          severity: SecurityRiskLevel.HIGH,
          description: `不安全的URL协议: ${urlObj.protocol}`,
          suggestion: '只使用HTTP、HTTPS等安全协议'
        })
        riskLevel = SecurityRiskLevel.HIGH
      }

      // 检查可疑域名
      const suspiciousDomains = [
        'localhost',
        '127.0.0.1',
        '0.0.0.0',
        'file://'
      ]
      
      if (suspiciousDomains.some(domain => url.includes(domain))) {
        issues.push({
          type: 'SUSPICIOUS_DOMAIN',
          severity: SecurityRiskLevel.MEDIUM,
          description: '检测到本地或可疑域名',
          suggestion: '确认URL的安全性'
        })
        riskLevel = this.updateRiskLevel(riskLevel, SecurityRiskLevel.MEDIUM)
      }

      // 检查URL长度
      if (url.length > 2048) {
        issues.push({
          type: 'URL_TOO_LONG',
          severity: SecurityRiskLevel.LOW,
          description: 'URL长度过长',
          suggestion: '使用短链接或检查URL有效性'
        })
        riskLevel = this.updateRiskLevel(riskLevel, SecurityRiskLevel.LOW)
      }

    } catch (error) {
      issues.push({
        type: 'INVALID_URL',
        severity: SecurityRiskLevel.MEDIUM,
        description: '无效的URL格式',
        suggestion: '检查URL格式是否正确'
      })
      riskLevel = SecurityRiskLevel.MEDIUM
    }

    return {
      safe: riskLevel === SecurityRiskLevel.SAFE || riskLevel === SecurityRiskLevel.LOW,
      riskLevel,
      issues,
      recommendations: this.generateRecommendations(issues)
    }
  }

  // ==================== 私有方法 ====================

  /**
   * 验证MIME类型
   */
  private validateMimeType(file: File, config: FileSecurityConfig): SecurityCheckResult {
    const issues: SecurityIssue[] = []
    let riskLevel = SecurityRiskLevel.SAFE

    if (!config.allowedMimeTypes.includes(file.type)) {
      const severity = this.isExecutableMimeType(file.type) ? 
        SecurityRiskLevel.CRITICAL : SecurityRiskLevel.MEDIUM
      
      issues.push({
        type: 'INVALID_MIME_TYPE',
        severity,
        description: `不允许的文件类型: ${file.type}`,
        suggestion: '请选择支持的文件格式'
      })
      riskLevel = severity
    }

    return { safe: riskLevel === SecurityRiskLevel.SAFE, riskLevel, issues, recommendations: [] }
  }

  /**
   * 验证文件扩展名
   */
  private validateFileExtension(file: File, config: FileSecurityConfig): SecurityCheckResult {
    const issues: SecurityIssue[] = []
    let riskLevel = SecurityRiskLevel.SAFE

    const extension = this.getFileExtension(file.name)
    if (!config.allowedExtensions.includes(extension)) {
      const severity = this.isExecutableExtension(extension) ? 
        SecurityRiskLevel.CRITICAL : SecurityRiskLevel.MEDIUM
      
      issues.push({
        type: 'INVALID_EXTENSION',
        severity,
        description: `不允许的文件扩展名: ${extension}`,
        suggestion: '请选择支持的文件格式'
      })
      riskLevel = severity
    }

    return { safe: riskLevel === SecurityRiskLevel.SAFE, riskLevel, issues, recommendations: [] }
  }

  /**
   * 检查可执行文件
   */
  private checkExecutableFile(file: File): SecurityCheckResult {
    const issues: SecurityIssue[] = []
    let riskLevel = SecurityRiskLevel.SAFE

    const executableExtensions = [
      '.exe', '.bat', '.cmd', '.com', '.scr', '.pif',
      '.js', '.vbs', '.ps1', '.sh', '.app', '.dmg'
    ]

    const extension = this.getFileExtension(file.name)
    if (executableExtensions.includes(extension)) {
      issues.push({
        type: 'EXECUTABLE_FILE',
        severity: SecurityRiskLevel.CRITICAL,
        description: '检测到可执行文件',
        suggestion: '不要上传可执行文件'
      })
      riskLevel = SecurityRiskLevel.CRITICAL
    }

    return { safe: riskLevel === SecurityRiskLevel.SAFE, riskLevel, issues, recommendations: [] }
  }

  /**
   * 验证文件内容
   */
  private async validateFileContent(content: string): Promise<SecurityCheckResult> {
    const issues: SecurityIssue[] = []
    let riskLevel = SecurityRiskLevel.SAFE

    // 检查脚本内容
    const scriptPatterns = [
      /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
      /javascript:/gi,
      /vbscript:/gi,
      /on\w+\s*=/gi
    ]

    for (const pattern of scriptPatterns) {
      if (pattern.test(content)) {
        issues.push({
          type: 'SCRIPT_CONTENT',
          severity: SecurityRiskLevel.HIGH,
          description: '检测到脚本内容',
          suggestion: '移除脚本代码或使用纯数据格式'
        })
        riskLevel = this.updateRiskLevel(riskLevel, SecurityRiskLevel.HIGH)
        break
      }
    }

    // 检查可疑URL
    const urlPattern = /https?:\/\/[^\s<>"']+/gi
    const urls = content.match(urlPattern) || []
    
    for (const url of urls) {
      const urlCheck = this.validateUrlSecurity(url)
      if (!urlCheck.safe) {
        issues.push({
          type: 'SUSPICIOUS_URL',
          severity: SecurityRiskLevel.MEDIUM,
          description: `检测到可疑URL: ${url.substring(0, 50)}...`,
          suggestion: '检查URL的安全性'
        })
        riskLevel = this.updateRiskLevel(riskLevel, SecurityRiskLevel.MEDIUM)
      }
    }

    return { safe: riskLevel === SecurityRiskLevel.SAFE, riskLevel, issues, recommendations: [] }
  }

  /**
   * 验证收藏夹安全性
   */
  private validateBookmarkSecurity(
    bookmark: any, 
    config: ContentSecurityConfig, 
    location: string
  ): SecurityCheckResult {
    const issues: SecurityIssue[] = []
    let riskLevel = SecurityRiskLevel.SAFE

    // 检查字符串长度
    const fields = ['title', 'description', 'content']
    for (const field of fields) {
      if (bookmark[field] && bookmark[field].length > config.maxStringLength) {
        issues.push({
          type: 'STRING_TOO_LONG',
          severity: SecurityRiskLevel.LOW,
          description: `${field}字段过长`,
          location: `${location}.${field}`,
          suggestion: '截断过长的文本'
        })
        riskLevel = this.updateRiskLevel(riskLevel, SecurityRiskLevel.LOW)
      }
    }

    // 检查URL安全性
    if (bookmark.url) {
      const urlCheck = this.validateUrlSecurity(bookmark.url)
      if (!urlCheck.safe) {
        issues.push(...urlCheck.issues.map(issue => ({
          ...issue,
          location: `${location}.url`
        })))
        riskLevel = this.updateRiskLevel(riskLevel, urlCheck.riskLevel)
      }
    }

    return { safe: riskLevel === SecurityRiskLevel.SAFE, riskLevel, issues, recommendations: [] }
  }

  /**
   * 验证分类安全性
   */
  private validateCategorySecurity(
    category: any, 
    config: ContentSecurityConfig, 
    location: string
  ): SecurityCheckResult {
    const issues: SecurityIssue[] = []
    let riskLevel = SecurityRiskLevel.SAFE

    // 检查名称长度
    if (category.name && category.name.length > 100) {
      issues.push({
        type: 'NAME_TOO_LONG',
        severity: SecurityRiskLevel.LOW,
        description: '分类名称过长',
        location: `${location}.name`,
        suggestion: '使用更短的分类名称'
      })
      riskLevel = SecurityRiskLevel.LOW
    }

    return { safe: riskLevel === SecurityRiskLevel.SAFE, riskLevel, issues, recommendations: [] }
  }

  /**
   * 验证标签安全性
   */
  private validateTagSecurity(
    tag: any, 
    config: ContentSecurityConfig, 
    location: string
  ): SecurityCheckResult {
    const issues: SecurityIssue[] = []
    let riskLevel = SecurityRiskLevel.SAFE

    // 检查名称长度
    if (tag.name && tag.name.length > 50) {
      issues.push({
        type: 'NAME_TOO_LONG',
        severity: SecurityRiskLevel.LOW,
        description: '标签名称过长',
        location: `${location}.name`,
        suggestion: '使用更短的标签名称'
      })
      riskLevel = SecurityRiskLevel.LOW
    }

    return { safe: riskLevel === SecurityRiskLevel.SAFE, riskLevel, issues, recommendations: [] }
  }

  /**
   * 清理文本内容
   */
  private sanitizeText(text: string): string {
    if (!text) return text

    // 移除HTML标签
    let sanitized = text.replace(/<[^>]*>/g, '')
    
    // 移除脚本内容
    sanitized = sanitized.replace(/javascript:/gi, '')
    sanitized = sanitized.replace(/vbscript:/gi, '')
    
    // 移除控制字符
    sanitized = sanitized.replace(/[\x00-\x1F\x7F]/g, '')
    
    // 限制长度
    if (sanitized.length > this.defaultContentConfig.maxStringLength) {
      sanitized = sanitized.substring(0, this.defaultContentConfig.maxStringLength) + '...'
    }
    
    return sanitized.trim()
  }

  /**
   * 清理URL
   */
  private sanitizeUrl(url: string): string {
    try {
      const urlObj = new URL(url)
      
      // 只允许安全协议
      if (!this.defaultContentConfig.allowedProtocols.includes(urlObj.protocol)) {
        return ''
      }
      
      return urlObj.toString()
    } catch {
      return ''
    }
  }

  /**
   * 更新风险级别
   */
  private updateRiskLevel(current: SecurityRiskLevel, newLevel: SecurityRiskLevel): SecurityRiskLevel {
    const levels = [
      SecurityRiskLevel.SAFE,
      SecurityRiskLevel.LOW,
      SecurityRiskLevel.MEDIUM,
      SecurityRiskLevel.HIGH,
      SecurityRiskLevel.CRITICAL
    ]
    
    const currentIndex = levels.indexOf(current)
    const newIndex = levels.indexOf(newLevel)
    
    return newIndex > currentIndex ? newLevel : current
  }

  /**
   * 生成安全建议
   */
  private generateRecommendations(issues: SecurityIssue[]): string[] {
    const recommendations = new Set<string>()
    
    issues.forEach(issue => {
      recommendations.add(issue.suggestion)
    })
    
    return Array.from(recommendations)
  }

  /**
   * 读取文件内容
   */
  private readFileContent(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.onload = () => resolve(reader.result as string)
      reader.onerror = () => reject(new Error('读取文件失败'))
      reader.readAsText(file, 'utf-8')
    })
  }

  /**
   * 获取文件扩展名
   */
  private getFileExtension(filename: string): string {
    const lastDot = filename.lastIndexOf('.')
    return lastDot >= 0 ? filename.substring(lastDot).toLowerCase() : ''
  }

  /**
   * 检查是否为可执行MIME类型
   */
  private isExecutableMimeType(mimeType: string): boolean {
    const executableTypes = [
      'application/x-executable',
      'application/x-msdownload',
      'application/x-msdos-program',
      'application/javascript',
      'text/javascript'
    ]
    
    return executableTypes.includes(mimeType)
  }

  /**
   * 检查是否为可执行扩展名
   */
  private isExecutableExtension(extension: string): boolean {
    const executableExtensions = [
      '.exe', '.bat', '.cmd', '.com', '.scr', '.pif',
      '.js', '.vbs', '.ps1', '.sh', '.app', '.dmg'
    ]
    
    return executableExtensions.includes(extension)
  }

  /**
   * 格式化文件大小
   */
  private formatFileSize(bytes: number): string {
    const units = ['B', 'KB', 'MB', 'GB']
    let size = bytes
    let unitIndex = 0
    
    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024
      unitIndex++
    }
    
    return `${size.toFixed(1)} ${units[unitIndex]}`
  }
}

// 导出单例实例
export const securityValidator = new SecurityValidator()