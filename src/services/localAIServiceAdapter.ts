// 通用本地AI服务适配器

import { AIModel, AIProviderConfig, AIConnectionResult } from '../types/ai'

/**
 * 本地服务配置接口
 */
export interface LocalServiceConfig {
  name: string
  baseUrl: string
  port?: number
  protocol?: 'http' | 'https'
  apiPath?: string
  healthCheckPath?: string
  modelsPath?: string
  timeout?: number
  headers?: Record<string, string>
}

/**
 * 本地服务发现结果接口
 */
export interface LocalServiceDiscoveryResult {
  services: LocalServiceConfig[]
  errors: string[]
}

/**
 * 本地服务适配器接口
 */
export interface LocalServiceAdapter {
  name: string
  detectService(config: LocalServiceConfig): Promise<boolean>
  getModels(config: LocalServiceConfig): Promise<AIModel[]>
  testConnection(config: LocalServiceConfig): Promise<AIConnectionResult>
}

/**
 * 通用本地AI服务适配器类
 * 提供统一的本地AI服务接口和自动发现功能
 */
export class LocalAIServiceAdapter {
  
  /**
   * 默认的本地服务配置
   */
  private static readonly DEFAULT_SERVICES: LocalServiceConfig[] = [
    {
      name: 'Ollama',
      baseUrl: 'http://localhost:11434',
      port: 11434,
      protocol: 'http',
      apiPath: '/api',
      healthCheckPath: '/api/version',
      modelsPath: '/api/tags',
      timeout: 10000
    },
    {
      name: 'LM Studio',
      baseUrl: 'http://localhost:1234',
      port: 1234,
      protocol: 'http',
      apiPath: '/v1',
      healthCheckPath: '/v1/models',
      modelsPath: '/v1/models',
      timeout: 10000
    },
    {
      name: 'Xinference',
      baseUrl: 'http://localhost:9997',
      port: 9997,
      protocol: 'http',
      apiPath: '/v1',
      healthCheckPath: '/v1/cluster/status',
      modelsPath: '/v1/models',
      timeout: 10000
    },
    {
      name: 'Text Generation WebUI',
      baseUrl: 'http://localhost:5000',
      port: 5000,
      protocol: 'http',
      apiPath: '/v1',
      healthCheckPath: '/v1/models',
      modelsPath: '/v1/models',
      timeout: 10000
    },
    {
      name: 'LocalAI',
      baseUrl: 'http://localhost:8080',
      port: 8080,
      protocol: 'http',
      apiPath: '/v1',
      healthCheckPath: '/v1/models',
      modelsPath: '/v1/models',
      timeout: 10000
    }
  ]

  /**
   * 常见的本地AI服务端口范围
   */
  private static readonly COMMON_PORTS = [
    1234,  // LM Studio
    5000,  // Text Generation WebUI
    7860,  // Gradio默认端口
    8000,  // FastAPI默认端口
    8080,  // LocalAI
    9997,  // Xinference
    11434, // Ollama
    8888,  // Jupyter
    3000,  // 通用开发端口
    4000,  // 通用开发端口
  ]

  /**
   * 自动发现本地AI服务
   * @param customPorts 自定义端口列表
   * @returns Promise<LocalServiceDiscoveryResult>
   */
  async discoverLocalServices(customPorts: number[] = []): Promise<LocalServiceDiscoveryResult> {
    const services: LocalServiceConfig[] = []
    const errors: string[] = []
    
    // 合并默认端口和自定义端口
    const portsToScan = [...new Set([...LocalAIServiceAdapter.COMMON_PORTS, ...customPorts])]
    
    console.log(`开始扫描本地AI服务，共 ${portsToScan.length} 个端口`)

    // 并发扫描所有端口
    const scanPromises = portsToScan.map(async (port) => {
      try {
        const service = await this.scanPort(port)
        if (service) {
          services.push(service)
        }
      } catch (error) {
        errors.push(`端口 ${port}: ${error.message}`)
      }
    })

    await Promise.allSettled(scanPromises)

    console.log(`发现 ${services.length} 个本地AI服务`)
    
    return { services, errors }
  }

  /**
   * 扫描指定端口的服务
   * @param port 端口号
   * @returns Promise<LocalServiceConfig | null>
   */
  private async scanPort(port: number): Promise<LocalServiceConfig | null> {
    const baseUrl = `http://localhost:${port}`
    
    // 尝试不同的API路径
    const apiPaths = ['/v1', '/api', '']
    
    for (const apiPath of apiPaths) {
      try {
        const service = await this.detectServiceType(baseUrl, apiPath, port)
        if (service) {
          return service
        }
      } catch (error) {
        // 继续尝试下一个路径
        continue
      }
    }
    
    // 如果所有路径都失败，抛出错误以便在上层记录
    throw new Error(`无法连接到端口 ${port}`)
  }

  /**
   * 检测服务类型
   * @param baseUrl 基础URL
   * @param apiPath API路径
   * @param port 端口号
   * @returns Promise<LocalServiceConfig | null>
   */
  private async detectServiceType(baseUrl: string, apiPath: string, port: number): Promise<LocalServiceConfig | null> {
    const fullBaseUrl = `${baseUrl}${apiPath}`
    
    // 尝试不同的健康检查端点
    const healthCheckPaths = [
      '/version',
      '/models',
      '/cluster/status',
      '/health',
      '/status',
      ''
    ]

    for (const healthPath of healthCheckPaths) {
      try {
        const response = await fetch(`${fullBaseUrl}${healthPath}`, {
          method: 'GET',
          signal: AbortSignal.timeout(3000)
        })

        if (response.ok) {
          const serviceType = await this.identifyServiceType(response, fullBaseUrl)
          
          return {
            name: serviceType || `Unknown Service (Port ${port})`,
            baseUrl: fullBaseUrl,
            port,
            protocol: 'http',
            apiPath,
            healthCheckPath: healthPath,
            modelsPath: '/models',
            timeout: 10000
          }
        }
      } catch (error) {
        // 继续尝试下一个路径
        continue
      }
    }

    return null
  }

  /**
   * 识别服务类型
   * @param response HTTP响应
   * @param baseUrl 基础URL
   * @returns Promise<string | null>
   */
  private async identifyServiceType(response: Response, baseUrl: string): Promise<string | null> {
    try {
      const data = await response.json()
      
      // 根据响应内容识别服务类型
      if (data.version && response.url.includes('/api/version')) {
        return 'Ollama'
      }
      
      if (data.version && response.url.includes('/cluster/status')) {
        return 'Xinference'
      }
      
      if (data.data && Array.isArray(data.data) && response.url.includes('/models')) {
        // 进一步检查是否是LM Studio
        if (baseUrl.includes(':1234')) {
          return 'LM Studio'
        }
        return 'OpenAI Compatible API'
      }
      
      if (data.object === 'list' && data.data) {
        return 'OpenAI Compatible API'
      }
      
      // 检查响应头
      const serverHeader = response.headers.get('server')
      if (serverHeader) {
        if (serverHeader.includes('ollama')) return 'Ollama'
        if (serverHeader.includes('xinference')) return 'Xinference'
        if (serverHeader.includes('fastapi')) return 'FastAPI Service'
        if (serverHeader.includes('uvicorn')) return 'Uvicorn Service'
      }
      
      return null
    } catch (error) {
      // 如果无法解析JSON，尝试从URL推断
      if (response.url.includes(':11434')) return 'Ollama'
      if (response.url.includes(':1234')) return 'LM Studio'
      if (response.url.includes(':9997')) return 'Xinference'
      if (response.url.includes(':5000')) return 'Text Generation WebUI'
      if (response.url.includes(':8080')) return 'LocalAI'
      
      return null
    }
  }

  /**
   * 测试本地服务连接
   * @param config 服务配置
   * @returns Promise<AIConnectionResult>
   */
  async testLocalServiceConnection(config: LocalServiceConfig): Promise<AIConnectionResult> {
    const startTime = Date.now()
    
    try {
      const healthCheckUrl = `${config.baseUrl}${config.healthCheckPath || '/models'}`
      
      const response = await fetch(healthCheckUrl, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          ...config.headers
        },
        signal: AbortSignal.timeout(config.timeout || 10000)
      })

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      // 尝试获取模型数量
      let modelCount = 0
      try {
        const modelsResponse = await fetch(`${config.baseUrl}${config.modelsPath || '/models'}`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            ...config.headers
          },
          signal: AbortSignal.timeout(config.timeout || 10000)
        })

        if (modelsResponse.ok) {
          const modelsData = await modelsResponse.json()
          if (modelsData.models) {
            modelCount = modelsData.models.length
          } else if (modelsData.data) {
            modelCount = modelsData.data.length
          }
        }
      } catch (error) {
        // 忽略模型获取错误
      }

      const responseTime = Date.now() - startTime

      return {
        providerId: config.name.toLowerCase().replace(/\s+/g, '-'),
        success: true,
        responseTime,
        modelCount,
        testedAt: new Date()
      }
    } catch (error) {
      const responseTime = Date.now() - startTime
      
      let errorMessage = error.message
      if (error.name === 'AbortError') {
        errorMessage = `连接超时，请检查${config.name}服务是否运行`
      } else if (error.message.includes('ECONNREFUSED')) {
        errorMessage = `${config.name}服务未运行或端口不正确`
      }

      return {
        providerId: config.name.toLowerCase().replace(/\s+/g, '-'),
        success: false,
        responseTime,
        error: errorMessage,
        testedAt: new Date()
      }
    }
  }

  /**
   * 获取本地服务模型列表
   * @param config 服务配置
   * @returns Promise<AIModel[]>
   */
  async getLocalServiceModels(config: LocalServiceConfig): Promise<AIModel[]> {
    try {
      const modelsUrl = `${config.baseUrl}${config.modelsPath || '/models'}`
      
      const response = await fetch(modelsUrl, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          ...config.headers
        },
        signal: AbortSignal.timeout(config.timeout || 15000)
      })

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const data = await response.json()
      
      // 处理不同的响应格式
      let models: any[] = []
      if (data.models) {
        models = data.models // Ollama格式
      } else if (data.data) {
        models = data.data // OpenAI兼容格式
      } else if (Array.isArray(data)) {
        models = data
      }

      return models.map((model, index) => {
        const modelId = model.model_uid || model.id || model.name || `model-${index}`
        const modelName = model.model_name || model.id || model.name || modelId
        
        return {
          id: modelId,
          name: modelName,
          displayName: this.formatModelDisplayName(modelName, config.name),
          description: model.model_description || model.description || `${config.name}本地模型`,
          size: model.model_size_in_billions ? `${model.model_size_in_billions}B` : 
                model.size ? this.formatBytes(model.size) : undefined,
          parameters: model.model_size_in_billions ? `${model.model_size_in_billions}B` : 
                     model.details?.parameter_size || undefined,
          capabilities: this.determineModelCapabilities(model, config.name),
          tags: this.extractModelTags(model, config.name),
          providerId: config.name.toLowerCase().replace(/\s+/g, '-'),
          isRecommended: this.isModelRecommended(modelName),
          isPopular: this.isModelPopular(modelName)
        }
      })
    } catch (error) {
      console.error(`获取${config.name}模型列表失败:`, error)
      return []
    }
  }

  /**
   * 格式化模型显示名称
   * @param modelName 模型名称
   * @param serviceName 服务名称
   * @returns string
   */
  private formatModelDisplayName(modelName: string, serviceName: string): string {
    // 移除常见的文件扩展名
    const cleanName = modelName.replace(/\.(gguf|bin|safetensors)$/i, '')
    
    // 如果名称太长，尝试提取关键信息
    if (cleanName.length > 50) {
      const parts = cleanName.split(/[/\\]/)
      return parts[parts.length - 1] || cleanName
    }
    
    return cleanName
  }

  /**
   * 确定模型能力
   * @param model 模型信息
   * @param serviceName 服务名称
   * @returns string[]
   */
  private determineModelCapabilities(model: any, serviceName: string): string[] {
    const capabilities = ['chat', 'completion']
    const modelName = (model.model_name || model.id || model.name || '').toLowerCase()
    
    // 根据模型名称添加特定能力
    if (modelName.includes('code') || modelName.includes('coder')) {
      capabilities.push('coding')
    }
    
    if (modelName.includes('instruct') || modelName.includes('chat')) {
      capabilities.push('instruction-following')
    }
    
    if (modelName.includes('embed')) {
      capabilities.push('embedding')
    }
    
    // 根据服务类型添加能力
    if (serviceName === 'Xinference' && model.model_type === 'embedding') {
      capabilities.push('embedding')
    }
    
    return [...new Set(capabilities)]
  }

  /**
   * 提取模型标签
   * @param model 模型信息
   * @param serviceName 服务名称
   * @returns string[]
   */
  private extractModelTags(model: any, serviceName: string): string[] {
    const tags = [serviceName]
    const modelName = (model.model_name || model.id || model.name || '').toLowerCase()
    
    // 添加模型类型标签
    if (model.model_type) {
      tags.push(model.model_type.toUpperCase())
    }
    
    // 添加量化信息
    if (model.quantization) {
      tags.push(model.quantization)
    }
    
    // 添加参数大小
    if (model.model_size_in_billions) {
      tags.push(`${model.model_size_in_billions}B参数`)
    }
    
    // 添加特殊标签
    if (modelName.includes('code')) {
      tags.push('代码生成')
    }
    
    if (modelName.includes('chat') || modelName.includes('instruct')) {
      tags.push('对话')
    }
    
    return tags
  }

  /**
   * 判断是否为推荐模型
   * @param modelName 模型名称
   * @returns boolean
   */
  private isModelRecommended(modelName: string): boolean {
    const recommendedKeywords = [
      'llama', 'qwen', 'chatglm', 'mistral', 'codellama', 'deepseek'
    ]
    const name = modelName.toLowerCase()
    return recommendedKeywords.some(keyword => name.includes(keyword))
  }

  /**
   * 判断是否为热门模型
   * @param modelName 模型名称
   * @returns boolean
   */
  private isModelPopular(modelName: string): boolean {
    const popularKeywords = [
      'llama', 'qwen', 'chatglm', 'chat', 'instruct', 'mistral'
    ]
    const name = modelName.toLowerCase()
    return popularKeywords.some(keyword => name.includes(keyword))
  }

  /**
   * 格式化字节大小
   * @param bytes 字节数
   * @returns string
   */
  private formatBytes(bytes: number): string {
    if (bytes === 0) return '0 Bytes'
    
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  /**
   * 创建自定义本地服务配置
   * @param name 服务名称
   * @param baseUrl 基础URL
   * @param options 可选配置
   * @returns LocalServiceConfig
   */
  createCustomServiceConfig(
    name: string, 
    baseUrl: string, 
    options: Partial<LocalServiceConfig> = {}
  ): LocalServiceConfig {
    const url = new URL(baseUrl)
    
    return {
      name,
      baseUrl,
      port: parseInt(url.port) || (url.protocol === 'https:' ? 443 : 80),
      protocol: url.protocol.replace(':', '') as 'http' | 'https',
      apiPath: options.apiPath || '/v1',
      healthCheckPath: options.healthCheckPath || '/models',
      modelsPath: options.modelsPath || '/models',
      timeout: options.timeout || 10000,
      headers: options.headers || {}
    }
  }

  /**
   * 获取默认服务配置列表
   * @returns LocalServiceConfig[]
   */
  getDefaultServices(): LocalServiceConfig[] {
    return [...LocalAIServiceAdapter.DEFAULT_SERVICES]
  }
}

// 导出单例实例
export const localAIServiceAdapter = new LocalAIServiceAdapter()