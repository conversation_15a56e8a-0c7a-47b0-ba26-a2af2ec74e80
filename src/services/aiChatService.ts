// AI聊天服务 - 处理AI文本生成和对话请求

import { aiIntegrationService } from './aiIntegrationService'
import { aiProviderService } from './aiProviderService'
import { ChromeStorageService } from '../utils/chromeStorage'
import { DefaultAIModelAPI } from './defaultAIModelAPI'

interface AIGenerateRequest {
  prompt: string
  generationType: 'description' | 'summary' | 'tags' | 'title' | 'notes'
  context?: Record<string, any>
  maxLength?: number
}

interface AIGenerateResponse {
  content: string
  suggestions?: string[]
  metadata?: {
    model?: string
    provider?: string
    timestamp?: string
  }
}

interface AIChatMessage {
  role: 'user' | 'assistant' | 'system'
  content: string
  timestamp?: string
}

/**
 * AI聊天服务类
 * 提供AI文本生成和对话功能
 */
export class AIChatService {
  private static readonly DEFAULT_MODEL_KEY = 'ai_default_model'
  private static readonly CHAT_HISTORY_KEY = 'ai_chat_history'

  /**
   * 获取默认AI模型配置
   */
  private async getDefaultModel(): Promise<{providerId: string, modelId: string} | null> {
    try {
      // 首先尝试使用新的默认AI模型服务
      const defaultChatModel = await DefaultAIModelAPI.getDefaultChatModel()
      
      if (defaultChatModel) {
        // 解析模型ID，格式为 providerId_modelId
        const [providerId, modelId] = defaultChatModel.id.split('_', 2)
        
        if (providerId && modelId) {
          // 验证提供商是否存在
          const providers = await aiIntegrationService.getConfiguredProviders()
          const provider = providers.find(p => p.id === providerId)
          
          if (provider) {
            return {
              providerId,
              modelId
            }
          } else {
            console.warn(`默认模型的提供商不存在: ${providerId}，尝试查找替代方案`)
          }
        }
      }

      // 如果新服务没有配置，尝试使用旧的存储方式（向后兼容）
      const legacyDefaultModel = await ChromeStorageService.getSyncSetting(
        AIChatService.DEFAULT_MODEL_KEY, 
        null
      )
      
      if (legacyDefaultModel) {
        // 验证旧配置的提供商是否存在
        const providers = await aiIntegrationService.getConfiguredProviders()
        const provider = providers.find(p => p.id === legacyDefaultModel.providerId)
        
        if (provider) {
          return legacyDefaultModel
        } else {
          console.warn(`旧配置的提供商不存在: ${legacyDefaultModel.providerId}，清理旧配置`)
          await ChromeStorageService.removeSyncSetting(AIChatService.DEFAULT_MODEL_KEY)
        }
      }

      // 如果都没有，尝试获取第一个可用的模型
      const providers = await aiIntegrationService.getConfiguredProviders()
      const enabledProvider = providers.find(p => p.enabled)
      
      if (enabledProvider) {
        const models = await aiIntegrationService.getAvailableModels(enabledProvider.id)
        const recommendedModel = models.find(m => m.isRecommended) || models[0]
        
        if (recommendedModel) {
          const defaultConfig = {
            providerId: enabledProvider.id,
            modelId: recommendedModel.id
          }
          
          // 保存为默认模型（使用旧格式保持兼容性）
          await this.setDefaultModel(defaultConfig.providerId, defaultConfig.modelId)
          return defaultConfig
        }
      }

      return null
    } catch (error) {
      console.error('获取默认AI模型失败:', error)
      return null
    }
  }

  /**
   * 设置默认AI模型
   */
  async setDefaultModel(providerId: string, modelId: string): Promise<void> {
    try {
      await ChromeStorageService.saveSyncSetting(AIChatService.DEFAULT_MODEL_KEY, {
        providerId,
        modelId
      })
      console.log(`默认AI模型已设置: ${providerId}/${modelId}`)
    } catch (error) {
      console.error('设置默认AI模型失败:', error)
      throw error
    }
  }

  /**
   * 生成AI文本内容
   */
  async generateText(request: AIGenerateRequest): Promise<AIGenerateResponse> {
    try {
      console.log('开始生成AI文本:', request.generationType)
      
      const defaultModel = await this.getDefaultModel()
      if (!defaultModel) {
        console.error('未找到默认AI模型配置')
        throw new Error('未找到可用的AI模型，请先在"默认AI模型"页面配置模型')
      }

      console.log('使用默认模型:', defaultModel)

      // 获取提供商配置
      const providers = await aiIntegrationService.getConfiguredProviders()
      console.log('可用的提供商列表:', providers.map(p => ({ id: p.id, name: p.name, type: p.type })))
      console.log('查找提供商ID:', defaultModel.providerId)
      
      const provider = providers.find(p => p.id === defaultModel.providerId)
      
      if (!provider) {
        console.error('AI提供商配置不存在:', defaultModel.providerId)
        console.error('可用的提供商ID:', providers.map(p => p.id))
        
        // 尝试按类型查找提供商（兼容性处理）
        const providerByType = providers.find(p => p.type === defaultModel.providerId)
        if (providerByType) {
          console.log('找到按类型匹配的提供商:', providerByType.id)
          // 更新默认模型配置为正确的提供商ID
          await this.setDefaultModel(providerByType.id, defaultModel.modelId)
          return await this.generateText(request) // 重新调用
        }
        
        throw new Error(`AI提供商配置不存在: ${defaultModel.providerId}`)
      }

      console.log('使用提供商:', provider.name)

      // 构建聊天消息
      const messages: AIChatMessage[] = [
        {
          role: 'system',
          content: this.buildSystemPrompt(request.generationType)
        },
        {
          role: 'user',
          content: request.prompt
        }
      ]

      // 调用AI提供商API
      const response = await this.callAIProvider(provider, defaultModel.modelId, messages, request.maxLength)
      
      // 解析响应
      const result = this.parseAIResponse(response, request.generationType)
      
      console.log('AI文本生成成功')
      
      return {
        content: result.content,
        suggestions: result.suggestions,
        metadata: {
          model: defaultModel.modelId,
          provider: provider.name,
          timestamp: new Date().toISOString()
        }
      }
    } catch (error) {
      console.error('AI文本生成失败:', error)
      throw error
    }
  }

  /**
   * 构建系统提示词
   */
  private buildSystemPrompt(generationType: string): string {
    const prompts = {
      description: `你是一个专业的内容描述生成助手。请根据用户提供的信息，生成简洁明了的描述。要求：
1. 描述应该准确反映内容的核心价值
2. 使用简洁的中文表达
3. 长度控制在50-200字之间
4. 避免重复用户已提供的信息`,

      summary: `你是一个专业的内容摘要生成助手。请根据用户提供的信息，生成简洁的摘要。要求：
1. 提取关键信息和要点
2. 使用简洁的中文表达
3. 长度控制在50-100字之间
4. 突出重点内容`,

      tags: `你是一个专业的标签推荐助手。请根据用户提供的信息，推荐相关标签。要求：
1. 推荐5-8个相关标签
2. 标签应该准确反映内容特征
3. 使用中文，每个标签不超过4个字
4. 返回格式：标签1,标签2,标签3`,

      title: `你是一个专业的标题生成助手。请根据用户提供的信息，生成合适的标题。要求：
1. 标题应该简洁明了
2. 准确反映内容主题
3. 使用中文表达
4. 长度控制在10-50字之间`,

      notes: `你是一个专业的笔记生成助手。请根据用户提供的信息，生成有用的笔记或想法。要求：
1. 提供有价值的见解或补充信息
2. 使用中文表达
3. 长度控制在100-300字之间
4. 可以包含使用建议或相关思考`
    }

    return prompts[generationType] || prompts.description
  }

  /**
   * 调用AI提供商API
   */
  private async callAIProvider(
    provider: any, 
    modelId: string, 
    messages: AIChatMessage[], 
    maxLength?: number
  ): Promise<string> {
    try {
      console.log('调用AI提供商API:', provider.name, modelId)
      
      // 调用真实的AI提供商服务
      const response = await aiProviderService.generateText({
        providerId: provider.id,
        modelId: modelId,
        messages: messages.map(msg => ({
          role: msg.role,
          content: msg.content
        })),
        maxTokens: maxLength ? Math.ceil(maxLength / 2) : 500, // 估算token数量
        temperature: 0.7
      })

      if (response.success && response.content) {
        console.log('AI API调用成功')
        return response.content
      } else {
        throw new Error(response.error || 'AI API返回空内容')
      }
    } catch (error) {
      console.error('调用AI提供商API失败:', error)
      
      // 抛出错误，让上层服务处理降级策略
      throw new Error(`AI API调用失败: ${error.message}`)
    }
  }



  /**
   * 解析AI响应
   */
  private parseAIResponse(response: string, generationType: string): {
    content: string
    suggestions?: string[]
  } {
    const content = response.trim()
    
    // 如果是标签类型，解析为建议列表
    if (generationType === 'tags') {
      const tags = content.split(/[,，]/).map(tag => tag.trim()).filter(Boolean)
      return {
        content: tags.join(', '),
        suggestions: tags
      }
    }

    // 其他类型直接返回内容
    return {
      content,
      suggestions: this.generateSuggestions(content, generationType)
    }
  }

  /**
   * 生成相关建议
   */
  private generateSuggestions(content: string, generationType: string): string[] {
    // 根据内容和类型生成一些建议
    const suggestions: string[] = []
    
    if (generationType === 'description') {
      suggestions.push(
        content.substring(0, Math.min(50, content.length)) + '...',
        '简化版：' + content.substring(0, 30) + '...',
        '详细版：' + content + '，值得深入了解。'
      )
    } else if (generationType === 'title') {
      suggestions.push(
        content + ' - 推荐',
        '【精选】' + content,
        content + ' | 实用工具'
      )
    }

    return suggestions.slice(0, 3) // 最多返回3个建议
  }

  /**
   * 保存聊天历史
   */
  async saveChatHistory(messages: AIChatMessage[]): Promise<void> {
    try {
      const history = await ChromeStorageService.getLocalSetting(
        AIChatService.CHAT_HISTORY_KEY, 
        []
      )
      
      const updatedHistory = [...history, ...messages].slice(-100) // 保留最近100条消息
      
      await ChromeStorageService.saveLocalSetting(
        AIChatService.CHAT_HISTORY_KEY, 
        updatedHistory
      )
    } catch (error) {
      console.error('保存聊天历史失败:', error)
    }
  }

  /**
   * 获取聊天历史
   */
  async getChatHistory(): Promise<AIChatMessage[]> {
    try {
      return await ChromeStorageService.getLocalSetting(
        AIChatService.CHAT_HISTORY_KEY, 
        []
      )
    } catch (error) {
      console.error('获取聊天历史失败:', error)
      return []
    }
  }

  /**
   * 清除聊天历史
   */
  async clearChatHistory(): Promise<void> {
    try {
      await ChromeStorageService.saveLocalSetting(
        AIChatService.CHAT_HISTORY_KEY, 
        []
      )
    } catch (error) {
      console.error('清除聊天历史失败:', error)
    }
  }
}

// 导出单例实例
export const aiChatService = new AIChatService()