// 默认AI模型API服务 - 为其他页面提供调用接口

import { defaultAIModelService, AvailableAIModel } from './defaultAIModelService'
import { aiIntegrationService } from './aiIntegrationService'

/**
 * 默认AI模型API服务类
 * 提供给其他组件和页面调用的统一接口
 */
export class DefaultAIModelAPI {
  
  /**
   * 获取指定使用场景的默认模型
   * @param usageId 使用场景ID
   * @returns Promise<AvailableAIModel | null>
   */
  static async getDefaultModel(usageId: string): Promise<AvailableAIModel | null> {
    try {
      const model = await defaultAIModelService.getRecommendedModel(usageId)
      
      // 如果主要模型不可用，尝试使用备用模型
      if (!model) {
        return await defaultAIModelService.getFallbackModel(usageId)
      }
      
      return model
    } catch (error) {
      console.error(`获取默认模型失败 (${usageId}):`, error)
      return null
    }
  }

  /**
   * 获取默认聊天模型
   * @returns Promise<AvailableAIModel | null>
   */
  static async getDefaultChatModel(): Promise<AvailableAIModel | null> {
    return this.getDefaultModel('default-chat')
  }

  /**
   * 获取翻译模型
   * @returns Promise<AvailableAIModel | null>
   */
  static async getTranslationModel(): Promise<AvailableAIModel | null> {
    return this.getDefaultModel('translation')
  }

  /**
   * 获取标签命名模型
   * @returns Promise<AvailableAIModel | null>
   */
  static async getTagNamingModel(): Promise<AvailableAIModel | null> {
    return this.getDefaultModel('tag-naming')
  }

  /**
   * 获取文件夹命名模型
   * @returns Promise<AvailableAIModel | null>
   */
  static async getFolderNamingModel(): Promise<AvailableAIModel | null> {
    return this.getDefaultModel('folder-naming')
  }

  /**
   * 获取内容分析模型
   * @returns Promise<AvailableAIModel | null>
   */
  static async getContentAnalysisModel(): Promise<AvailableAIModel | null> {
    return this.getDefaultModel('content-analysis')
  }

  /**
   * 获取摘要生成模型
   * @returns Promise<AvailableAIModel | null>
   */
  static async getSummaryGenerationModel(): Promise<AvailableAIModel | null> {
    return this.getDefaultModel('summary-generation')
  }

  /**
   * 获取模型的提供商配置
   * @param modelId 模型ID (格式: providerId_modelId)
   * @returns Promise<{provider: any, model: any} | null>
   */
  static async getModelProviderConfig(modelId: string): Promise<{provider: any, model: any} | null> {
    try {
      // 解析模型ID，格式为 providerId_modelId
      const underscoreIndex = modelId.indexOf('_')
      
      if (underscoreIndex === -1) {
        console.error('无效的模型ID格式，缺少下划线分隔符:', modelId)
        return null
      }
      
      const providerId = modelId.substring(0, underscoreIndex)
      const actualModelId = modelId.substring(underscoreIndex + 1)
      
      if (!providerId || !actualModelId) {
        console.error('无效的模型ID格式:', modelId)
        return null
      }

      console.log(`解析模型ID: ${modelId} -> 提供商: ${providerId}, 模型: ${actualModelId}`)

      // 获取提供商配置
      const providers = await aiIntegrationService.getConfiguredProviders()
      const provider = providers.find(p => p.id === providerId)
      
      if (!provider) {
        console.error('未找到提供商配置:', providerId)
        console.log('可用的提供商:', providers.map(p => p.id))
        return null
      }

      // 获取模型信息
      const models = await aiIntegrationService.getAvailableModels(providerId)
      const model = models.find(m => m.id === actualModelId)
      
      if (!model) {
        console.error('未找到模型信息:', actualModelId)
        console.log('可用的模型:', models.map(m => m.id))
        return null
      }

      return { provider, model }
    } catch (error) {
      console.error('获取模型提供商配置失败:', error)
      return null
    }
  }

  /**
   * 调用指定使用场景的AI模型
   * @param usageId 使用场景ID
   * @param prompt 提示词
   * @param options 调用选项
   * @returns Promise<string | null>
   */
  static async callModel(
    usageId: string, 
    prompt: string, 
    options: {
      temperature?: number
      maxTokens?: number
      stream?: boolean
    } = {}
  ): Promise<string | null> {
    try {
      const model = await this.getDefaultModel(usageId)
      
      if (!model) {
        console.error(`没有为使用场景 ${usageId} 配置默认模型`)
        return null
      }

      const config = await this.getModelProviderConfig(model.id)
      
      if (!config) {
        console.error(`无法获取模型 ${model.id} 的配置`)
        return null
      }

      // 这里应该调用实际的AI服务
      // 暂时返回模拟响应
      console.log(`调用AI模型: ${model.displayName} (${model.provider})`)
      console.log(`提示词: ${prompt}`)
      console.log(`选项:`, options)
      
      // TODO: 实现实际的AI模型调用
      return `这是来自 ${model.displayName} 的模拟响应: ${prompt}`
    } catch (error) {
      console.error('调用AI模型失败:', error)
      return null
    }
  }

  /**
   * 检查指定使用场景是否已配置模型
   * @param usageId 使用场景ID
   * @returns Promise<boolean>
   */
  static async isModelConfigured(usageId: string): Promise<boolean> {
    try {
      const model = await this.getDefaultModel(usageId)
      return model !== null
    } catch (error) {
      console.error('检查模型配置失败:', error)
      return false
    }
  }

  /**
   * 获取所有已配置的使用场景
   * @returns Promise<string[]>
   */
  static async getConfiguredUsages(): Promise<string[]> {
    try {
      const usages = await defaultAIModelService.getDefaultModelUsages()
      return usages
        .filter(usage => usage.selectedModelId && usage.enabled)
        .map(usage => usage.id)
    } catch (error) {
      console.error('获取已配置使用场景失败:', error)
      return []
    }
  }

  /**
   * 获取使用场景的统计信息
   * @returns Promise<{total: number, configured: number, available: number}>
   */
  static async getUsageStatistics(): Promise<{total: number, configured: number, available: number}> {
    try {
      const [usageStats, availableModels] = await Promise.all([
        defaultAIModelService.getUsageStats(),
        defaultAIModelService.getAvailableModels()
      ])

      return {
        total: usageStats.total,
        configured: usageStats.configured,
        available: availableModels.length
      }
    } catch (error) {
      console.error('获取使用场景统计信息失败:', error)
      return { total: 0, configured: 0, available: 0 }
    }
  }

  /**
   * 验证模型是否可用
   * @param modelId 模型ID
   * @returns Promise<boolean>
   */
  static async validateModel(modelId: string): Promise<boolean> {
    try {
      const config = await this.getModelProviderConfig(modelId)
      
      if (!config) {
        return false
      }

      // 测试提供商连接
      const connectionResult = await aiIntegrationService.testConnection(config.provider.id)
      return connectionResult.success
    } catch (error) {
      console.error('验证模型失败:', error)
      return false
    }
  }

  /**
   * 获取推荐的模型配置
   * @returns Promise<{usageId: string, modelId: string}[]>
   */
  static async getRecommendedConfiguration(): Promise<{usageId: string, modelId: string}[]> {
    try {
      const availableModels = await defaultAIModelService.getAvailableModels()
      
      if (availableModels.length === 0) {
        return []
      }

      // 按推荐程度排序
      const sortedModels = availableModels.sort((a, b) => {
        if (a.isRecommended && !b.isRecommended) return -1
        if (!a.isRecommended && b.isRecommended) return 1
        if (a.isPopular && !b.isPopular) return -1
        if (!a.isPopular && b.isPopular) return 1
        return 0
      })

      const primaryModel = sortedModels[0]
      const usages = await defaultAIModelService.getDefaultModelUsages()

      return usages.map(usage => ({
        usageId: usage.id,
        modelId: primaryModel.id
      }))
    } catch (error) {
      console.error('获取推荐配置失败:', error)
      return []
    }
  }

  /**
   * 批量设置模型配置
   * @param configurations 配置列表
   * @returns Promise<void>
   */
  static async batchSetConfiguration(
    configurations: {usageId: string, modelId: string, fallbackModelId?: string}[]
  ): Promise<void> {
    try {
      for (const config of configurations) {
        await defaultAIModelService.updateUsageModel(
          config.usageId, 
          config.modelId, 
          config.fallbackModelId || null
        )
      }
      
      console.log('批量设置模型配置成功')
    } catch (error) {
      console.error('批量设置模型配置失败:', error)
      throw error
    }
  }
}

// 导出便捷的调用函数
export const getDefaultChatModel = DefaultAIModelAPI.getDefaultChatModel
export const getTranslationModel = DefaultAIModelAPI.getTranslationModel
export const getTagNamingModel = DefaultAIModelAPI.getTagNamingModel
export const getFolderNamingModel = DefaultAIModelAPI.getFolderNamingModel
export const getContentAnalysisModel = DefaultAIModelAPI.getContentAnalysisModel
export const getSummaryGenerationModel = DefaultAIModelAPI.getSummaryGenerationModel
export const callDefaultModel = DefaultAIModelAPI.callModel
export const isModelConfigured = DefaultAIModelAPI.isModelConfigured