// 错误恢复服务 - 提供自动重试和错误恢复机制

import { ValidationError } from '../types'

/**
 * 错误类型枚举
 */
export enum ErrorType {
  NETWORK_ERROR = 'NETWORK_ERROR',
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  STORAGE_ERROR = 'STORAGE_ERROR',
  PARSING_ERROR = 'PARSING_ERROR',
  CONFLICT_ERROR = 'CONFLICT_ERROR',
  PERMISSION_ERROR = 'PERMISSION_ERROR',
  TIMEOUT_ERROR = 'TIMEOUT_ERROR',
  UNKNOWN_ERROR = 'UNKNOWN_ERROR'
}

/**
 * 错误严重级别
 */
export enum ErrorSeverity {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  CRITICAL = 'CRITICAL'
}

/**
 * 错误信息接口
 */
export interface ErrorInfo {
  type: ErrorType
  severity: ErrorSeverity
  message: string
  details?: any
  timestamp: Date
  recoverable: boolean
  suggestions: string[]
  retryCount?: number
}

/**
 * 重试配置接口
 */
export interface RetryConfig {
  maxRetries: number
  baseDelay: number
  maxDelay: number
  backoffMultiplier: number
  retryableErrors: ErrorType[]
}

/**
 * 恢复结果接口
 */
export interface RecoveryResult<T> {
  success: boolean
  data?: T
  error?: ErrorInfo
  recoveryActions: string[]
}

/**
 * 部分失败结果接口
 */
export interface PartialFailureResult<T> {
  successfulItems: T[]
  failedItems: Array<{
    item: T
    error: ErrorInfo
  }>
  totalItems: number
  successCount: number
  failureCount: number
}

/**
 * 错误恢复服务类
 * 提供自动重试、错误分类、恢复建议等功能
 */
export class ErrorRecoveryService {
  private errorLog: ErrorInfo[] = []
  private readonly maxLogSize = 1000

  private readonly defaultRetryConfig: RetryConfig = {
    maxRetries: 3,
    baseDelay: 1000,
    maxDelay: 10000,
    backoffMultiplier: 2,
    retryableErrors: [
      ErrorType.NETWORK_ERROR,
      ErrorType.STORAGE_ERROR,
      ErrorType.TIMEOUT_ERROR
    ]
  }

  /**
   * 带重试的操作执行
   * @param operation 要执行的操作
   * @param config 重试配置
   * @returns Promise<T>
   */
  async retryWithBackoff<T>(
    operation: () => Promise<T>,
    config: Partial<RetryConfig> = {}
  ): Promise<T> {
    const finalConfig = { ...this.defaultRetryConfig, ...config }
    let lastError: Error

    for (let attempt = 1; attempt <= finalConfig.maxRetries + 1; attempt++) {
      try {
        const result = await operation()
        
        // 如果之前有失败，记录恢复成功
        if (attempt > 1) {
          this.logError({
            type: ErrorType.UNKNOWN_ERROR,
            severity: ErrorSeverity.LOW,
            message: `操作在第${attempt}次尝试后成功恢复`,
            timestamp: new Date(),
            recoverable: true,
            suggestions: [],
            retryCount: attempt - 1
          })
        }
        
        return result
      } catch (error) {
        lastError = error as Error
        const errorInfo = this.classifyError(error as Error)
        
        // 如果错误不可重试或已达到最大重试次数
        if (!finalConfig.retryableErrors.includes(errorInfo.type) || 
            attempt > finalConfig.maxRetries) {
          
          this.logError({
            ...errorInfo,
            retryCount: attempt - 1
          })
          
          throw error
        }
        
        // 计算延迟时间
        const delay = Math.min(
          finalConfig.baseDelay * Math.pow(finalConfig.backoffMultiplier, attempt - 1),
          finalConfig.maxDelay
        )
        
        console.warn(`操作失败，${delay}ms后进行第${attempt}次重试:`, error.message)
        await this.delay(delay)
      }
    }
    
    throw lastError!
  }

  /**
   * 处理部分失败的批量操作
   * @param items 要处理的项目列表
   * @param processor 处理函数
   * @param continueOnError 是否在错误时继续
   * @returns Promise<PartialFailureResult<T>>
   */
  async handlePartialFailure<T, R>(
    items: T[],
    processor: (item: T, index: number) => Promise<R>,
    continueOnError: boolean = true
  ): Promise<PartialFailureResult<R>> {
    const successfulItems: R[] = []
    const failedItems: Array<{ item: T, error: ErrorInfo }> = []

    for (let i = 0; i < items.length; i++) {
      try {
        const result = await processor(items[i], i)
        successfulItems.push(result)
      } catch (error) {
        const errorInfo = this.classifyError(error as Error)
        failedItems.push({
          item: items[i],
          error: errorInfo
        })
        
        this.logError(errorInfo)
        
        if (!continueOnError) {
          break
        }
      }
    }

    return {
      successfulItems,
      failedItems,
      totalItems: items.length,
      successCount: successfulItems.length,
      failureCount: failedItems.length
    }
  }

  /**
   * 尝试恢复操作
   * @param operation 原始操作
   * @param error 发生的错误
   * @returns Promise<RecoveryResult<T>>
   */
  async attemptRecovery<T>(
    operation: () => Promise<T>,
    error: Error
  ): Promise<RecoveryResult<T>> {
    const errorInfo = this.classifyError(error)
    const recoveryActions: string[] = []

    try {
      // 根据错误类型尝试不同的恢复策略
      switch (errorInfo.type) {
        case ErrorType.STORAGE_ERROR:
          recoveryActions.push('清理存储空间')
          await this.clearTemporaryData()
          break
          
        case ErrorType.NETWORK_ERROR:
          recoveryActions.push('检查网络连接')
          await this.delay(2000) // 等待网络恢复
          break
          
        case ErrorType.VALIDATION_ERROR:
          recoveryActions.push('数据清理和标准化')
          // 这里可以尝试数据清理
          break
          
        case ErrorType.PERMISSION_ERROR:
          recoveryActions.push('请求必要权限')
          // 这里可以尝试重新请求权限
          break
          
        default:
          recoveryActions.push('通用错误恢复')
          await this.delay(1000)
      }

      // 重新尝试操作
      const result = await operation()
      
      return {
        success: true,
        data: result,
        recoveryActions
      }
    } catch (recoveryError) {
      return {
        success: false,
        error: this.classifyError(recoveryError as Error),
        recoveryActions
      }
    }
  }

  /**
   * 分类错误类型
   * @param error 错误对象
   * @returns ErrorInfo
   */
  classifyError(error: Error): ErrorInfo {
    const message = error.message.toLowerCase()
    let type = ErrorType.UNKNOWN_ERROR
    let severity = ErrorSeverity.MEDIUM
    const suggestions: string[] = []

    // 网络错误
    if (message.includes('network') || message.includes('fetch') || 
        message.includes('connection') || message.includes('timeout')) {
      type = ErrorType.NETWORK_ERROR
      severity = ErrorSeverity.MEDIUM
      suggestions.push('检查网络连接')
      suggestions.push('稍后重试')
    }
    // 存储错误
    else if (message.includes('storage') || message.includes('quota') || 
             message.includes('disk') || message.includes('space')) {
      type = ErrorType.STORAGE_ERROR
      severity = ErrorSeverity.HIGH
      suggestions.push('清理存储空间')
      suggestions.push('检查磁盘空间')
    }
    // 解析错误
    else if (message.includes('parse') || message.includes('json') || 
             message.includes('syntax') || message.includes('format')) {
      type = ErrorType.PARSING_ERROR
      severity = ErrorSeverity.HIGH
      suggestions.push('检查文件格式')
      suggestions.push('验证数据结构')
    }
    // 验证错误
    else if (message.includes('validation') || message.includes('invalid') || 
             message.includes('required') || message.includes('format')) {
      type = ErrorType.VALIDATION_ERROR
      severity = ErrorSeverity.MEDIUM
      suggestions.push('检查数据格式')
      suggestions.push('修正无效数据')
    }
    // 权限错误
    else if (message.includes('permission') || message.includes('access') || 
             message.includes('denied') || message.includes('unauthorized')) {
      type = ErrorType.PERMISSION_ERROR
      severity = ErrorSeverity.HIGH
      suggestions.push('检查访问权限')
      suggestions.push('重新授权')
    }
    // 冲突错误
    else if (message.includes('conflict') || message.includes('duplicate') || 
             message.includes('exists')) {
      type = ErrorType.CONFLICT_ERROR
      severity = ErrorSeverity.LOW
      suggestions.push('解决数据冲突')
      suggestions.push('选择合并策略')
    }
    // 超时错误
    else if (message.includes('timeout') || message.includes('time out')) {
      type = ErrorType.TIMEOUT_ERROR
      severity = ErrorSeverity.MEDIUM
      suggestions.push('增加超时时间')
      suggestions.push('分批处理数据')
    }

    return {
      type,
      severity,
      message: error.message,
      details: error.stack,
      timestamp: new Date(),
      recoverable: this.isRecoverable(type),
      suggestions
    }
  }

  /**
   * 记录错误
   * @param error 错误信息
   */
  logError(error: ErrorInfo): void {
    this.errorLog.push(error)
    
    // 限制日志大小
    if (this.errorLog.length > this.maxLogSize) {
      this.errorLog = this.errorLog.slice(-this.maxLogSize)
    }
    
    // 根据严重级别输出日志
    switch (error.severity) {
      case ErrorSeverity.CRITICAL:
        console.error('CRITICAL ERROR:', error)
        break
      case ErrorSeverity.HIGH:
        console.error('HIGH ERROR:', error)
        break
      case ErrorSeverity.MEDIUM:
        console.warn('MEDIUM ERROR:', error)
        break
      case ErrorSeverity.LOW:
        console.info('LOW ERROR:', error)
        break
    }
  }

  /**
   * 获取错误日志
   * @param filterType 过滤错误类型
   * @param limit 限制数量
   * @returns ErrorInfo[]
   */
  getErrorLog(filterType?: ErrorType, limit?: number): ErrorInfo[] {
    let filteredLog = this.errorLog
    
    if (filterType) {
      filteredLog = filteredLog.filter(error => error.type === filterType)
    }
    
    if (limit) {
      filteredLog = filteredLog.slice(-limit)
    }
    
    return filteredLog.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
  }

  /**
   * 清理错误日志
   */
  clearErrorLog(): void {
    this.errorLog = []
  }

  /**
   * 获取错误统计
   * @returns 错误统计信息
   */
  getErrorStatistics(): Record<string, any> {
    const stats = {
      total: this.errorLog.length,
      byType: {} as Record<ErrorType, number>,
      bySeverity: {} as Record<ErrorSeverity, number>,
      recent: this.errorLog.filter(e => 
        Date.now() - e.timestamp.getTime() < 24 * 60 * 60 * 1000
      ).length
    }

    this.errorLog.forEach(error => {
      stats.byType[error.type] = (stats.byType[error.type] || 0) + 1
      stats.bySeverity[error.severity] = (stats.bySeverity[error.severity] || 0) + 1
    })

    return stats
  }

  /**
   * 生成错误报告
   * @returns 错误报告字符串
   */
  generateErrorReport(): string {
    const stats = this.getErrorStatistics()
    const recentErrors = this.getErrorLog(undefined, 10)
    
    let report = `错误报告 - ${new Date().toLocaleString()}\n`
    report += `==========================================\n\n`
    
    report += `总体统计:\n`
    report += `- 总错误数: ${stats.total}\n`
    report += `- 最近24小时: ${stats.recent}\n\n`
    
    report += `按类型分布:\n`
    Object.entries(stats.byType).forEach(([type, count]) => {
      report += `- ${type}: ${count}\n`
    })
    
    report += `\n按严重级别分布:\n`
    Object.entries(stats.bySeverity).forEach(([severity, count]) => {
      report += `- ${severity}: ${count}\n`
    })
    
    if (recentErrors.length > 0) {
      report += `\n最近错误:\n`
      recentErrors.forEach((error, index) => {
        report += `${index + 1}. [${error.severity}] ${error.type}: ${error.message}\n`
        report += `   时间: ${error.timestamp.toLocaleString()}\n`
        if (error.suggestions.length > 0) {
          report += `   建议: ${error.suggestions.join(', ')}\n`
        }
        report += `\n`
      })
    }
    
    return report
  }

  // ==================== 私有方法 ====================

  /**
   * 判断错误是否可恢复
   * @param errorType 错误类型
   * @returns 是否可恢复
   */
  private isRecoverable(errorType: ErrorType): boolean {
    const recoverableTypes = [
      ErrorType.NETWORK_ERROR,
      ErrorType.STORAGE_ERROR,
      ErrorType.TIMEOUT_ERROR,
      ErrorType.CONFLICT_ERROR
    ]
    
    return recoverableTypes.includes(errorType)
  }

  /**
   * 延迟执行
   * @param ms 延迟毫秒数
   * @returns Promise<void>
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  /**
   * 清理临时数据
   * @returns Promise<void>
   */
  private async clearTemporaryData(): Promise<void> {
    try {
      // 这里可以实现清理临时文件、缓存等逻辑
      console.log('清理临时数据...')
      
      // 清理内存中的临时数据
      if (typeof window !== 'undefined' && window.gc) {
        window.gc()
      }
    } catch (error) {
      console.warn('清理临时数据失败:', error)
    }
  }
}

// 导出单例实例
export const errorRecoveryService = new ErrorRecoveryService()