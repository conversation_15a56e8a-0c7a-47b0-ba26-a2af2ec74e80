// AI模型服务类 - 处理模型搜索、筛选和缓存功能

import {
  AIModel,
  AIProviderConfig,
  ModelFilter
} from '../types/ai'
import { aiProviderService } from './aiProviderService'
import { ChromeStorageService } from '../utils/chromeStorage'

/**
 * 模型缓存项接口
 */
interface ModelCacheItem {
  providerId: string
  models: AIModel[]
  cachedAt: Date
  expiresAt: Date
}

/**
 * AI模型服务类
 * 处理模型相关的操作，包括搜索、筛选、缓存等
 */
export class AIModelService {

  /**
   * 缓存存储键名
   */
  private static readonly CACHE_STORAGE_KEY = 'ai_models_cache'

  /**
   * 默认缓存过期时间（24小时）
   */
  private static readonly DEFAULT_CACHE_DURATION = 24 * 60 * 60 * 1000

  /**
   * 推荐模型列表
   */
  private static readonly RECOMMENDED_MODELS = [
    'gpt-4',
    'gpt-3.5-turbo',
    'claude-3-opus',
    'claude-3-sonnet',
    'llama2',
    'qwen',
    'deepseek-chat'
  ]

  /**
   * 热门模型列表
   */
  private static readonly POPULAR_MODELS = [
    'gpt-3.5-turbo',
    'gpt-4',
    'claude-3-haiku',
    'llama2-chat',
    'mistral',
    'qwen-chat'
  ]

  /**
   * 获取模型列表
   * @param config 提供商配置
   * @param forceRefresh 是否强制刷新
   * @returns Promise<AIModel[]>
   */
  async getModels(config: AIProviderConfig, forceRefresh: boolean = false): Promise<AIModel[]> {
    try {
      // 检查缓存
      if (!forceRefresh) {
        const cachedModels = await this.getCachedModels(config.id)
        if (cachedModels) {
          console.log(`使用缓存的模型列表: ${config.name}`)
          return cachedModels
        }
      }

      console.log(`从API获取模型列表: ${config.name}`)
      
      // 从API获取模型列表
      const models = await aiProviderService.getModels(config)
      
      // 缓存模型列表
      if (models.length > 0) {
        await this.cacheModels(config.id, models)
      }

      return models
    } catch (error) {
      console.error('获取模型列表失败:', error)
      
      // 尝试返回缓存的模型列表
      const cachedModels = await this.getCachedModels(config.id)
      return cachedModels || []
    }
  }

  /**
   * 搜索模型（增强版，支持聚合服务的丰富信息）
   * @param models 模型列表
   * @param query 搜索关键词
   * @returns AIModel[]
   */
  searchModels(models: AIModel[], query: string): AIModel[] {
    if (!query.trim()) {
      return models
    }

    const searchTerm = query.toLowerCase().trim()
    
    return models.filter(model => {
      // 搜索模型名称
      if (model.name.toLowerCase().includes(searchTerm)) {
        return true
      }
      
      // 搜索显示名称
      if (model.displayName.toLowerCase().includes(searchTerm)) {
        return true
      }
      
      // 搜索描述
      if (model.description?.toLowerCase().includes(searchTerm)) {
        return true
      }
      
      // 搜索标签
      if (model.tags?.some(tag => tag.toLowerCase().includes(searchTerm))) {
        return true
      }
      
      // 搜索能力
      if (model.capabilities?.some(cap => cap.toLowerCase().includes(searchTerm))) {
        return true
      }

      // 增强搜索：搜索参数大小
      if (model.parameters?.toLowerCase().includes(searchTerm)) {
        return true
      }

      // 增强搜索：搜索模型大小
      if (model.size?.toLowerCase().includes(searchTerm)) {
        return true
      }

      // 增强搜索：搜索提供商ID
      if (model.providerId.toLowerCase().includes(searchTerm)) {
        return true
      }

      // 聚合服务特殊搜索：搜索定价信息
      if (this.isAggregateProvider(model.providerId) && model.description) {
        if (model.description.toLowerCase().includes('定价') && 
            (searchTerm.includes('price') || searchTerm.includes('cost') || 
             searchTerm.includes('定价') || searchTerm.includes('价格'))) {
          return true
        }
      }

      // 模糊搜索：支持部分匹配
      if (this.fuzzyMatch(model.name, searchTerm) || 
          this.fuzzyMatch(model.displayName, searchTerm)) {
        return true
      }
      
      return false
    }).sort((a, b) => {
      // 按相关性排序
      const aScore = this.calculateRelevanceScore(a, searchTerm)
      const bScore = this.calculateRelevanceScore(b, searchTerm)
      return bScore - aScore
    })
  }

  /**
   * 高级模型搜索（支持多条件组合搜索）
   * @param models 模型列表
   * @param searchOptions 搜索选项
   * @returns AIModel[]
   */
  advancedSearchModels(models: AIModel[], searchOptions: {
    query?: string
    provider?: string[]
    capabilities?: string[]
    tags?: string[]
    minContextLength?: number
    maxContextLength?: number
    includeRecommended?: boolean
    includePopular?: boolean
    sortBy?: 'relevance' | 'name' | 'contextLength' | 'provider'
    sortOrder?: 'asc' | 'desc'
  }): AIModel[] {
    let filteredModels = [...models]

    // 基础关键词搜索
    if (searchOptions.query) {
      filteredModels = this.searchModels(filteredModels, searchOptions.query)
    }

    // 按提供商筛选
    if (searchOptions.provider && searchOptions.provider.length > 0) {
      filteredModels = filteredModels.filter(model => 
        searchOptions.provider!.includes(model.providerId)
      )
    }

    // 按能力筛选
    if (searchOptions.capabilities && searchOptions.capabilities.length > 0) {
      filteredModels = filteredModels.filter(model => 
        model.capabilities && searchOptions.capabilities!.every(cap => 
          model.capabilities!.includes(cap)
        )
      )
    }

    // 按标签筛选
    if (searchOptions.tags && searchOptions.tags.length > 0) {
      filteredModels = filteredModels.filter(model => 
        model.tags && searchOptions.tags!.some(tag => 
          model.tags!.includes(tag)
        )
      )
    }

    // 按上下文长度筛选
    if (searchOptions.minContextLength || searchOptions.maxContextLength) {
      filteredModels = filteredModels.filter(model => {
        const contextLength = model.contextLength || model.maxTokens
        if (!contextLength) return false

        if (searchOptions.minContextLength && contextLength < searchOptions.minContextLength) {
          return false
        }

        if (searchOptions.maxContextLength && contextLength > searchOptions.maxContextLength) {
          return false
        }

        return true
      })
    }

    // 推荐模型筛选
    if (searchOptions.includeRecommended !== undefined) {
      if (searchOptions.includeRecommended) {
        filteredModels = filteredModels.filter(model => model.isRecommended)
      } else {
        filteredModels = filteredModels.filter(model => !model.isRecommended)
      }
    }

    // 热门模型筛选
    if (searchOptions.includePopular !== undefined) {
      if (searchOptions.includePopular) {
        filteredModels = filteredModels.filter(model => model.isPopular)
      } else {
        filteredModels = filteredModels.filter(model => !model.isPopular)
      }
    }

    // 排序
    const sortBy = searchOptions.sortBy || 'relevance'
    const sortOrder = searchOptions.sortOrder || 'desc'

    filteredModels.sort((a, b) => {
      let comparison = 0

      switch (sortBy) {
        case 'name':
          comparison = a.displayName.localeCompare(b.displayName)
          break
        case 'contextLength':
          const aContext = a.contextLength || a.maxTokens || 0
          const bContext = b.contextLength || b.maxTokens || 0
          comparison = aContext - bContext
          break
        case 'provider':
          comparison = a.providerId.localeCompare(b.providerId)
          break
        case 'relevance':
        default:
          // 相关性排序（推荐 > 热门 > 名称）
          if (a.isRecommended && !b.isRecommended) comparison = -1
          else if (!a.isRecommended && b.isRecommended) comparison = 1
          else if (a.isPopular && !b.isPopular) comparison = -1
          else if (!a.isPopular && b.isPopular) comparison = 1
          else comparison = a.displayName.localeCompare(b.displayName)
          break
      }

      return sortOrder === 'asc' ? comparison : -comparison
    })

    return filteredModels
  }

  /**
   * 按分类搜索模型
   * @param models 模型列表
   * @param category 分类
   * @returns AIModel[]
   */
  searchModelsByCategory(models: AIModel[], category: string): AIModel[] {
    const categoryMap: Record<string, string[]> = {
      'chat': ['chat', 'conversation', 'dialogue'],
      'coding': ['code', 'programming', 'development', 'coder'],
      'writing': ['writing', 'content', 'creative'],
      'analysis': ['analysis', 'reasoning', 'logic'],
      'multimodal': ['vision', 'image', 'multimodal'],
      'long-context': ['long-context', 'long', 'extended'],
      'fast': ['fast', 'quick', 'turbo', 'rapid'],
      'large': ['large', 'big', 'huge'],
      'small': ['small', 'mini', 'compact'],
      'open-source': ['open', 'opensource', 'free'],
      'commercial': ['commercial', 'paid', 'premium']
    }

    const searchTerms = categoryMap[category.toLowerCase()] || [category]

    return models.filter(model => {
      return searchTerms.some(term => {
        return (
          model.name.toLowerCase().includes(term) ||
          model.displayName.toLowerCase().includes(term) ||
          model.description?.toLowerCase().includes(term) ||
          model.tags?.some(tag => tag.toLowerCase().includes(term)) ||
          model.capabilities?.some(cap => cap.toLowerCase().includes(term))
        )
      })
    })
  }

  /**
   * 获取搜索建议
   * @param models 模型列表
   * @param query 搜索关键词
   * @returns string[]
   */
  getSearchSuggestions(models: AIModel[], query: string): string[] {
    if (!query.trim()) return []

    const suggestions = new Set<string>()
    const searchTerm = query.toLowerCase()

    models.forEach(model => {
      // 模型名称建议
      if (model.name.toLowerCase().includes(searchTerm)) {
        suggestions.add(model.name)
      }

      // 显示名称建议
      if (model.displayName.toLowerCase().includes(searchTerm)) {
        suggestions.add(model.displayName)
      }

      // 标签建议
      model.tags?.forEach(tag => {
        if (tag.toLowerCase().includes(searchTerm)) {
          suggestions.add(tag)
        }
      })

      // 能力建议
      model.capabilities?.forEach(cap => {
        if (cap.toLowerCase().includes(searchTerm)) {
          suggestions.add(cap)
        }
      })
    })

    return Array.from(suggestions).slice(0, 10) // 限制建议数量
  }

  /**
   * 筛选模型
   * @param models 模型列表
   * @param filters 筛选条件
   * @returns AIModel[]
   */
  filterModels(models: AIModel[], filters: ModelFilter): AIModel[] {
    let filteredModels = [...models]

    // 按大小筛选
    if (filters.size && filters.size.length > 0) {
      filteredModels = filteredModels.filter(model => 
        model.size && filters.size!.some(size => 
          model.size!.toLowerCase().includes(size.toLowerCase())
        )
      )
    }

    // 按类型筛选
    if (filters.type && filters.type.length > 0) {
      filteredModels = filteredModels.filter(model => 
        model.capabilities && filters.type!.some(type => 
          model.capabilities!.includes(type)
        )
      )
    }

    // 按能力筛选
    if (filters.capabilities && filters.capabilities.length > 0) {
      filteredModels = filteredModels.filter(model => 
        model.capabilities && filters.capabilities!.every(cap => 
          model.capabilities!.includes(cap)
        )
      )
    }

    // 按标签筛选
    if (filters.tags && filters.tags.length > 0) {
      filteredModels = filteredModels.filter(model => 
        model.tags && filters.tags!.some(tag => 
          model.tags!.includes(tag)
        )
      )
    }

    // 只显示推荐模型
    if (filters.isRecommended) {
      filteredModels = filteredModels.filter(model => model.isRecommended)
    }

    // 只显示热门模型
    if (filters.isPopular) {
      filteredModels = filteredModels.filter(model => model.isPopular)
    }

    return filteredModels
  }

  /**
   * 缓存模型列表
   * @param providerId 提供商ID
   * @param models 模型列表
   * @returns Promise<void>
   */
  async cacheModels(providerId: string, models: AIModel[]): Promise<void> {
    try {
      const cache = await this.getModelCache()
      
      const cacheItem: ModelCacheItem = {
        providerId,
        models,
        cachedAt: new Date(),
        expiresAt: new Date(Date.now() + AIModelService.DEFAULT_CACHE_DURATION)
      }

      // 更新或添加缓存项
      const existingIndex = cache.findIndex(item => item.providerId === providerId)
      if (existingIndex >= 0) {
        cache[existingIndex] = cacheItem
      } else {
        cache.push(cacheItem)
      }

      await ChromeStorageService.saveLocalSetting(AIModelService.CACHE_STORAGE_KEY, cache)
      console.log(`模型列表已缓存: ${providerId} - ${models.length} 个模型`)
    } catch (error) {
      console.error('缓存模型列表失败:', error)
    }
  }

  /**
   * 获取缓存的模型列表
   * @param providerId 提供商ID
   * @returns Promise<AIModel[] | null>
   */
  async getCachedModels(providerId: string): Promise<AIModel[] | null> {
    try {
      const cache = await this.getModelCache()
      const cacheItem = cache.find(item => item.providerId === providerId)
      
      if (!cacheItem) {
        return null
      }

      // 检查是否过期
      if (cacheItem.expiresAt < new Date()) {
        console.log(`模型缓存已过期: ${providerId}`)
        await this.clearModelCache(providerId)
        return null
      }

      return cacheItem.models
    } catch (error) {
      console.error('获取缓存模型列表失败:', error)
      return null
    }
  }

  /**
   * 清理模型缓存
   * @param providerId 提供商ID（可选，不传则清理所有）
   * @returns Promise<void>
   */
  async clearModelCache(providerId?: string): Promise<void> {
    try {
      if (providerId) {
        // 清理指定提供商的缓存
        const cache = await this.getModelCache()
        const filteredCache = cache.filter(item => item.providerId !== providerId)
        await ChromeStorageService.saveLocalSetting(AIModelService.CACHE_STORAGE_KEY, filteredCache)
        console.log(`已清理模型缓存: ${providerId}`)
      } else {
        // 清理所有缓存
        await ChromeStorageService.saveLocalSetting(AIModelService.CACHE_STORAGE_KEY, [])
        console.log('已清理所有模型缓存')
      }
    } catch (error) {
      console.error('清理模型缓存失败:', error)
    }
  }

  /**
   * 获取推荐模型
   * @param useCase 使用场景
   * @returns AIModel[]
   */
  getRecommendedModels(useCase: string): AIModel[] {
    // 根据使用场景返回推荐模型
    // 这里可以根据实际需求扩展更复杂的推荐逻辑
    const recommendations: Record<string, string[]> = {
      'chat': ['gpt-4', 'claude-3-opus', 'claude-3-sonnet'],
      'coding': ['gpt-4', 'deepseek-coder', 'claude-3-opus'],
      'writing': ['claude-3-opus', 'gpt-4', 'claude-3-sonnet'],
      'analysis': ['gpt-4', 'claude-3-opus', 'gemini-pro'],
      'translation': ['gpt-3.5-turbo', 'claude-3-haiku', 'qwen'],
      'general': ['gpt-3.5-turbo', 'claude-3-haiku', 'llama2']
    }

    const recommendedIds = recommendations[useCase] || recommendations['general']
    
    // 这里返回模拟数据，实际应该从已加载的模型中筛选
    return recommendedIds.map(id => ({
      id,
      name: id,
      displayName: id,
      description: `推荐用于${useCase}的模型`,
      capabilities: ['chat'],
      providerId: 'recommended',
      isRecommended: true,
      isPopular: AIModelService.POPULAR_MODELS.includes(id)
    }))
  }

  /**
   * 获取热门模型
   * @returns AIModel[]
   */
  getPopularModels(): AIModel[] {
    return AIModelService.POPULAR_MODELS.map(id => ({
      id,
      name: id,
      displayName: id,
      description: '热门AI模型',
      capabilities: ['chat'],
      providerId: 'popular',
      isRecommended: AIModelService.RECOMMENDED_MODELS.includes(id),
      isPopular: true
    }))
  }

  /**
   * 获取模型统计信息
   * @returns Promise<{totalModels: number, cachedProviders: number, cacheSize: number}>
   */
  async getModelStats(): Promise<{totalModels: number, cachedProviders: number, cacheSize: number}> {
    try {
      const cache = await this.getModelCache()
      
      const totalModels = cache.reduce((sum, item) => sum + item.models.length, 0)
      const cachedProviders = cache.length
      const cacheSize = JSON.stringify(cache).length

      return {
        totalModels,
        cachedProviders,
        cacheSize
      }
    } catch (error) {
      console.error('获取模型统计信息失败:', error)
      return {
        totalModels: 0,
        cachedProviders: 0,
        cacheSize: 0
      }
    }
  }

  /**
   * 清理过期缓存
   * @returns Promise<number> 清理的缓存项数量
   */
  async cleanExpiredCache(): Promise<number> {
    try {
      const cache = await this.getModelCache()
      const now = new Date()
      
      const validCache = cache.filter(item => item.expiresAt > now)
      const expiredCount = cache.length - validCache.length
      
      if (expiredCount > 0) {
        await ChromeStorageService.saveLocalSetting(AIModelService.CACHE_STORAGE_KEY, validCache)
        console.log(`清理了 ${expiredCount} 个过期的模型缓存项`)
      }
      
      return expiredCount
    } catch (error) {
      console.error('清理过期模型缓存失败:', error)
      return 0
    }
  }

  // ==================== 私有方法 ====================

  /**
   * 获取模型缓存
   * @returns Promise<ModelCacheItem[]>
   */
  private async getModelCache(): Promise<ModelCacheItem[]> {
    try {
      const cache = await ChromeStorageService.getLocalSetting(AIModelService.CACHE_STORAGE_KEY, [])
      
      // 转换日期字符串为Date对象
      return cache.map((item: any) => ({
        ...item,
        cachedAt: new Date(item.cachedAt),
        expiresAt: new Date(item.expiresAt)
      }))
    } catch (error) {
      console.error('获取模型缓存失败:', error)
      return []
    }
  }

  /**
   * 计算搜索相关性得分（增强版）
   * @param model 模型
   * @param searchTerm 搜索词
   * @returns number
   */
  private calculateRelevanceScore(model: AIModel, searchTerm: string): number {
    let score = 0

    // 名称完全匹配得分最高
    if (model.name.toLowerCase() === searchTerm) {
      score += 100
    } else if (model.name.toLowerCase().startsWith(searchTerm)) {
      score += 80
    } else if (model.name.toLowerCase().includes(searchTerm)) {
      score += 60
    }

    // 显示名称匹配
    if (model.displayName.toLowerCase() === searchTerm) {
      score += 90
    } else if (model.displayName.toLowerCase().startsWith(searchTerm)) {
      score += 70
    } else if (model.displayName.toLowerCase().includes(searchTerm)) {
      score += 40
    }

    // 描述匹配
    if (model.description?.toLowerCase().includes(searchTerm)) {
      score += 20
    }

    // 标签匹配
    if (model.tags?.some(tag => tag.toLowerCase() === searchTerm)) {
      score += 50
    } else if (model.tags?.some(tag => tag.toLowerCase().includes(searchTerm))) {
      score += 30
    }

    // 能力匹配
    if (model.capabilities?.some(cap => cap.toLowerCase() === searchTerm)) {
      score += 45
    } else if (model.capabilities?.some(cap => cap.toLowerCase().includes(searchTerm))) {
      score += 25
    }

    // 参数大小匹配
    if (model.parameters?.toLowerCase().includes(searchTerm)) {
      score += 15
    }

    // 模型大小匹配
    if (model.size?.toLowerCase().includes(searchTerm)) {
      score += 15
    }

    // 提供商匹配
    if (model.providerId.toLowerCase().includes(searchTerm)) {
      score += 10
    }

    // 聚合服务额外加分（因为信息更丰富）
    if (this.isAggregateProvider(model.providerId)) {
      score += 5
    }

    // 推荐模型加分
    if (model.isRecommended) {
      score += 10
    }

    // 热门模型加分
    if (model.isPopular) {
      score += 5
    }

    // 模糊匹配加分
    if (this.fuzzyMatch(model.name, searchTerm) || this.fuzzyMatch(model.displayName, searchTerm)) {
      score += 8
    }

    return score
  }

  /**
   * 判断是否为聚合服务提供商
   * @param providerId 提供商ID
   * @returns boolean
   */
  private isAggregateProvider(providerId: string): boolean {
    const aggregateProviders = ['openrouter', 'together']
    return aggregateProviders.includes(providerId)
  }

  /**
   * 模糊匹配算法
   * @param text 文本
   * @param pattern 模式
   * @returns boolean
   */
  private fuzzyMatch(text: string, pattern: string): boolean {
    const textLower = text.toLowerCase()
    const patternLower = pattern.toLowerCase()
    
    // 简单的模糊匹配：检查模式中的字符是否按顺序出现在文本中
    let patternIndex = 0
    
    for (let i = 0; i < textLower.length && patternIndex < patternLower.length; i++) {
      if (textLower[i] === patternLower[patternIndex]) {
        patternIndex++
      }
    }
    
    return patternIndex === patternLower.length
  }

  /**
   * 提取模型关键词
   * @param model 模型
   * @returns string[]
   */
  private extractModelKeywords(model: AIModel): string[] {
    const keywords = new Set<string>()
    
    // 从名称提取关键词
    keywords.add(model.name.toLowerCase())
    keywords.add(model.displayName.toLowerCase())
    
    // 从描述提取关键词
    if (model.description) {
      const words = model.description.toLowerCase().match(/\b\w+\b/g) || []
      words.forEach(word => {
        if (word.length > 2) { // 忽略太短的词
          keywords.add(word)
        }
      })
    }
    
    // 添加标签
    model.tags?.forEach(tag => keywords.add(tag.toLowerCase()))
    
    // 添加能力
    model.capabilities?.forEach(cap => keywords.add(cap.toLowerCase()))
    
    // 添加提供商
    keywords.add(model.providerId.toLowerCase())
    
    return Array.from(keywords)
  }

  /**
   * 获取模型分类统计
   * @param models 模型列表
   * @returns Record<string, number>
   */
  getModelCategoryStats(models: AIModel[]): Record<string, number> {
    const stats: Record<string, number> = {}
    
    models.forEach(model => {
      // 按提供商统计
      stats[`provider_${model.providerId}`] = (stats[`provider_${model.providerId}`] || 0) + 1
      
      // 按能力统计
      model.capabilities?.forEach(cap => {
        stats[`capability_${cap}`] = (stats[`capability_${cap}`] || 0) + 1
      })
      
      // 按标签统计
      model.tags?.forEach(tag => {
        stats[`tag_${tag}`] = (stats[`tag_${tag}`] || 0) + 1
      })
      
      // 按推荐状态统计
      if (model.isRecommended) {
        stats['recommended'] = (stats['recommended'] || 0) + 1
      }
      
      // 按热门状态统计
      if (model.isPopular) {
        stats['popular'] = (stats['popular'] || 0) + 1
      }
    })
    
    return stats
  }

  /**
   * 获取搜索性能指标
   * @param models 模型列表
   * @param query 搜索查询
   * @returns {searchTime: number, resultCount: number, totalModels: number}
   */
  getSearchPerformanceMetrics(models: AIModel[], query: string): {
    searchTime: number
    resultCount: number
    totalModels: number
  } {
    const startTime = performance.now()
    const results = this.searchModels(models, query)
    const endTime = performance.now()
    
    return {
      searchTime: endTime - startTime,
      resultCount: results.length,
      totalModels: models.length
    }
  }
}

// 导出单例实例
export const aiModelService = new AIModelService()