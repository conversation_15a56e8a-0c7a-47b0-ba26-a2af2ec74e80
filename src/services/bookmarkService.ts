// 收藏服务类 - 负责收藏功能的核心业务逻辑

import { 
  Bookmark, 
  BookmarkInput, 
  BookmarkUpdate,
  PageInfo,
  BookmarkFilter,
  SortOptions,
  DuplicateGroup
} from '../types'
import { indexedDBService } from '../utils/indexedDB'
import { ModelFactory } from '../utils/modelFactory'
import { ValidationUtils } from '../utils/validation'

/**
 * 收藏服务类
 * 提供收藏数据的保存、管理和查询功能
 */
export class BookmarkService {
  
  /**
   * 保存收藏
   * @param input 收藏输入数据
   * @returns Promise<string> 收藏ID
   */
  async saveBookmark(input: BookmarkInput): Promise<string> {
    try {
      // 确保数据库已初始化
      await indexedDBService.init()
      
      // 验证输入数据
      const validationResult = ValidationUtils.validateBookmarkInput(input)
      if (!validationResult.isValid) {
        throw new Error(`收藏数据验证失败: ${validationResult.errors.map(e => e.message).join(', ')}`)
      }

      // 创建收藏对象
      const bookmark = ModelFactory.createBookmark(input)

      // 检查重复
      if (input.url) {
        const existingBookmark = await this.findBookmarkByUrl(input.url)
        if (existingBookmark) {
          console.warn(`URL已存在收藏: ${input.url}`)
          // 可以选择更新现有收藏或抛出错误
          // 这里选择更新现有收藏
          return await this.updateExistingBookmark(existingBookmark, input)
        }
      }

      // 保存到数据库
      const bookmarkId = await indexedDBService.saveBookmark(bookmark)
      
      // 更新标签使用计数
      await this.updateTagUsageCount(bookmark.tags, 1)
      
      // 更新分类书签计数
      await this.updateCategoryBookmarkCount(bookmark.category, 1)

      console.log(`收藏保存成功: ${bookmarkId}`)
      return bookmarkId
    } catch (error) {
      console.error('保存收藏失败:', error)
      throw error
    }
  }

  /**
   * 从页面信息创建收藏
   * @param pageInfo 页面信息
   * @param additionalData 额外数据
   * @returns Promise<string> 收藏ID
   */
  async saveBookmarkFromPageInfo(
    pageInfo: PageInfo, 
    additionalData?: Partial<BookmarkInput>
  ): Promise<string> {
    try {
      // 确保数据库已初始化
      await indexedDBService.init()
      
      // 使用ModelFactory从页面信息创建收藏
      const bookmark = ModelFactory.createBookmarkFromPageInfo(pageInfo, additionalData)
      
      // 保存收藏
      return await indexedDBService.saveBookmark(bookmark)
    } catch (error) {
      console.error('从页面信息保存收藏失败:', error)
      throw error
    }
  }

  /**
   * 快速收藏当前页面
   * @param title 页面标题
   * @param url 页面URL
   * @param favicon 网站图标
   * @param selectedText 选中的文字
   * @returns Promise<string> 收藏ID
   */
  async quickBookmark(
    title: string, 
    url: string, 
    favicon?: string, 
    selectedText?: string
  ): Promise<string> {
    try {
      // 确保数据库已初始化
      await indexedDBService.init()
      
      const input: BookmarkInput = {
        type: selectedText ? 'text' : 'url',
        title,
        url,
        content: selectedText,
        favicon,
        category: '默认分类',
        tags: [],
        metadata: {
          pageTitle: title,
          siteName: this.extractSiteName(url),
          publishDate: new Date(),
          wordCount: selectedText ? selectedText.length : undefined,
          aiGenerated: false
        }
      }

      return await this.saveBookmark(input)
    } catch (error) {
      console.error('快速收藏失败:', error)
      throw error
    }
  }

  /**
   * 收藏选中文字
   * @param selectedText 选中的文字
   * @param url 页面URL
   * @param title 页面标题
   * @param context 上下文信息
   * @returns Promise<string> 收藏ID
   */
  async bookmarkSelectedText(
    selectedText: string,
    url: string,
    title: string,
    context?: string
  ): Promise<string> {
    try {
      // 确保数据库已初始化
      await indexedDBService.init()
      
      const input: BookmarkInput = {
        type: 'text',
        title: `摘录: ${selectedText.substring(0, 50)}${selectedText.length > 50 ? '...' : ''}`,
        url,
        content: selectedText,
        description: context,
        category: '文字摘录',
        tags: [],
        metadata: {
          pageTitle: title,
          siteName: this.extractSiteName(url),
          publishDate: new Date(),
          wordCount: selectedText.length,
          aiGenerated: false
        }
      }

      return await this.saveBookmark(input)
    } catch (error) {
      console.error('收藏选中文字失败:', error)
      throw error
    }
  }

  /**
   * 获取收藏
   * @param id 收藏ID
   * @returns Promise<Bookmark | null>
   */
  async getBookmark(id: string): Promise<Bookmark | null> {
    try {
      // 确保数据库已初始化
      await indexedDBService.init()
      
      return await indexedDBService.getBookmark(id)
    } catch (error) {
      console.error('获取收藏失败:', error)
      throw error
    }
  }

  /**
   * 获取收藏列表
   * @param filter 筛选条件
   * @param sort 排序选项
   * @returns Promise<Bookmark[]>
   */
  async getBookmarks(filter?: BookmarkFilter, sort?: SortOptions): Promise<Bookmark[]> {
    try {
      // 确保数据库已初始化
      await indexedDBService.init()
      
      return await indexedDBService.getBookmarks(filter, sort)
    } catch (error) {
      console.error('获取收藏列表失败:', error)
      throw error
    }
  }

  /**
   * 更新收藏
   * @param id 收藏ID
   * @param updates 更新数据
   * @returns Promise<void>
   */
  async updateBookmark(id: string, updates: BookmarkUpdate): Promise<void> {
    try {
      const existingBookmark = await this.getBookmark(id)
      if (!existingBookmark) {
        throw new Error(`收藏不存在: ${id}`)
      }

      // 处理标签变更
      const oldTags = existingBookmark.tags
      const newTags = updates.tags || oldTags

      // 更新标签使用计数
      const removedTags = oldTags.filter(tag => !newTags.includes(tag))
      const addedTags = newTags.filter(tag => !oldTags.includes(tag))
      
      await this.updateTagUsageCount(removedTags, -1)
      await this.updateTagUsageCount(addedTags, 1)

      // 处理分类变更
      if (updates.category && updates.category !== existingBookmark.category) {
        await this.updateCategoryBookmarkCount(existingBookmark.category, -1)
        await this.updateCategoryBookmarkCount(updates.category, 1)
      }

      // 更新收藏
      const updateData: Partial<Bookmark> = {
        ...updates,
        updatedAt: new Date()
      }
      
      // 确保metadata字段的完整性
      if (updates.metadata) {
        updateData.metadata = {
          ...existingBookmark.metadata,
          ...updates.metadata,
          // 确保必需的字段存在
          aiGenerated: updates.metadata.aiGenerated ?? existingBookmark.metadata.aiGenerated
        }
      }
      
      await indexedDBService.updateBookmark(id, updateData)

      console.log(`收藏更新成功: ${id}`)
    } catch (error) {
      console.error('更新收藏失败:', error)
      throw error
    }
  }

  /**
   * 删除收藏
   * @param id 收藏ID
   * @returns Promise<void>
   */
  async deleteBookmark(id: string): Promise<void> {
    try {
      const bookmark = await this.getBookmark(id)
      if (!bookmark) {
        throw new Error(`收藏不存在: ${id}`)
      }

      // 更新标签使用计数
      await this.updateTagUsageCount(bookmark.tags, -1)
      
      // 更新分类书签计数
      await this.updateCategoryBookmarkCount(bookmark.category, -1)

      // 删除收藏
      await indexedDBService.deleteBookmark(id)

      console.log(`收藏删除成功: ${id}`)
    } catch (error) {
      console.error('删除收藏失败:', error)
      throw error
    }
  }

  /**
   * 批量删除收藏
   * @param ids 收藏ID数组
   * @returns Promise<void>
   */
  async deleteBookmarks(ids: string[]): Promise<void> {
    try {
      // 获取所有要删除的收藏
      const bookmarks = await Promise.all(
        ids.map(id => this.getBookmark(id))
      )

      // 统计标签和分类使用情况
      const tagCounts: Record<string, number> = {}
      const categoryCounts: Record<string, number> = {}

      bookmarks.forEach(bookmark => {
        if (bookmark) {
          bookmark.tags.forEach(tag => {
            tagCounts[tag] = (tagCounts[tag] || 0) + 1
          })
          categoryCounts[bookmark.category] = (categoryCounts[bookmark.category] || 0) + 1
        }
      })

      // 更新标签使用计数
      for (const [tag, count] of Object.entries(tagCounts)) {
        await this.updateTagUsageCount([tag], -count)
      }

      // 更新分类书签计数
      for (const [category, count] of Object.entries(categoryCounts)) {
        await this.updateCategoryBookmarkCount(category, -count)
      }

      // 批量删除收藏
      await indexedDBService.deleteBookmarks(ids)

      console.log(`批量删除收藏成功: ${ids.length}个`)
    } catch (error) {
      console.error('批量删除收藏失败:', error)
      throw error
    }
  }

  /**
   * 检查URL是否已收藏
   * @param url URL地址
   * @returns Promise<{isBookmarked: boolean, bookmarkId?: string}>
   */
  async checkBookmarkStatus(url: string): Promise<{isBookmarked: boolean, bookmarkId?: string}> {
    try {
      const bookmark = await this.findBookmarkByUrl(url)
      return {
        isBookmarked: !!bookmark,
        bookmarkId: bookmark?.id
      }
    } catch (error) {
      console.error('检查收藏状态失败:', error)
      return { isBookmarked: false }
    }
  }

  /**
   * 根据URL查找收藏
   * @param url URL地址
   * @returns Promise<Bookmark | null>
   */
  async findBookmarkByUrl(url: string): Promise<Bookmark | null> {
    try {
      const bookmarks = await indexedDBService.getBookmarks()
      
      // 标准化URL进行比较
      const normalizedUrl = this.normalizeUrl(url)
      
      return bookmarks.find(bookmark => 
        bookmark.url && this.normalizeUrl(bookmark.url) === normalizedUrl
      ) || null
    } catch (error) {
      console.error('根据URL查找收藏失败:', error)
      return null
    }
  }

  /**
   * 搜索收藏
   * @param query 搜索关键词
   * @param filter 额外筛选条件
   * @returns Promise<Bookmark[]>
   */
  async searchBookmarks(query: string, filter?: Omit<BookmarkFilter, 'query'>): Promise<Bookmark[]> {
    try {
      const searchFilter: BookmarkFilter = {
        ...filter,
        query: query.trim()
      }
      
      return await this.getBookmarks(searchFilter)
    } catch (error) {
      console.error('搜索收藏失败:', error)
      throw error
    }
  }

  /**
   * 检测重复收藏
   * @param threshold 相似度阈值（默认0.8）
   * @returns Promise<DuplicateGroup[]>
   */
  async detectDuplicates(threshold: number = 0.8): Promise<DuplicateGroup[]> {
    try {
      const bookmarks = await this.getBookmarks()
      const duplicateGroups: DuplicateGroup[] = []
      const processed = new Set<string>()

      for (let i = 0; i < bookmarks.length; i++) {
        if (processed.has(bookmarks[i].id)) continue

        const currentBookmark = bookmarks[i]
        const similarBookmarks = [currentBookmark]

        for (let j = i + 1; j < bookmarks.length; j++) {
          if (processed.has(bookmarks[j].id)) continue

          const similarity = ModelFactory.calculateBookmarkSimilarity(
            currentBookmark, 
            bookmarks[j]
          )

          if (similarity >= threshold) {
            similarBookmarks.push(bookmarks[j])
            processed.add(bookmarks[j].id)
          }
        }

        if (similarBookmarks.length > 1) {
          duplicateGroups.push({
            id: `dup_${Date.now()}_${i}`,
            bookmarks: similarBookmarks,
            similarity: threshold,
            type: this.determineDuplicateType(similarBookmarks)
          })
        }

        processed.add(currentBookmark.id)
      }

      return duplicateGroups
    } catch (error) {
      console.error('检测重复收藏失败:', error)
      throw error
    }
  }

  /**
   * 获取收藏统计信息
   * @returns Promise<object> 统计信息
   */
  async getBookmarkStats(): Promise<{
    totalCount: number
    typeStats: Record<string, number>
    categoryStats: Record<string, number>
    tagStats: Record<string, number>
    recentCount: number
  }> {
    try {
      const bookmarks = await this.getBookmarks()
      const now = new Date()
      const oneWeekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)

      const stats = {
        totalCount: bookmarks.length,
        typeStats: {} as Record<string, number>,
        categoryStats: {} as Record<string, number>,
        tagStats: {} as Record<string, number>,
        recentCount: 0
      }

      bookmarks.forEach(bookmark => {
        // 类型统计
        stats.typeStats[bookmark.type] = (stats.typeStats[bookmark.type] || 0) + 1
        
        // 分类统计
        stats.categoryStats[bookmark.category] = (stats.categoryStats[bookmark.category] || 0) + 1
        
        // 标签统计
        bookmark.tags.forEach(tag => {
          stats.tagStats[tag] = (stats.tagStats[tag] || 0) + 1
        })
        
        // 最近收藏统计
        if (bookmark.createdAt >= oneWeekAgo) {
          stats.recentCount++
        }
      })

      return stats
    } catch (error) {
      console.error('获取收藏统计失败:', error)
      throw error
    }
  }

  // ==================== 私有方法 ====================

  /**
   * 更新现有收藏
   * @param existingBookmark 现有收藏
   * @param newInput 新输入数据
   * @returns Promise<string> 收藏ID
   */
  private async updateExistingBookmark(
    existingBookmark: Bookmark, 
    newInput: BookmarkInput
  ): Promise<string> {
    // 合并标签
    const mergedTags = Array.from(new Set([
      ...existingBookmark.tags,
      ...(newInput.tags || [])
    ]))

    // 更新描述（如果新输入有描述）
    const updatedDescription = newInput.description || existingBookmark.description

    // 更新内容（如果新输入有内容）
    const updatedContent = newInput.content || existingBookmark.content

    await this.updateBookmark(existingBookmark.id, {
      tags: mergedTags,
      description: updatedDescription,
      ...(updatedContent && { content: updatedContent })
    })

    return existingBookmark.id
  }

  /**
   * 更新标签使用计数
   * @param tags 标签数组
   * @param delta 变化量
   */
  private async updateTagUsageCount(tags: string[], delta: number): Promise<void> {
    try {
      // 确保数据库已初始化
      await indexedDBService.init()
      
      for (const tagName of tags) {
        let tag = await indexedDBService.getTagByName(tagName)
        
        if (!tag) {
          // 创建新标签
          tag = ModelFactory.createTag({ name: tagName }, false)
          tag.usageCount = Math.max(0, delta)
          await indexedDBService.saveTag(tag)
        } else {
          // 更新现有标签
          const newUsageCount = Math.max(0, (tag.usageCount || 0) + delta)
          await indexedDBService.updateTag(tag.id, { usageCount: newUsageCount })
        }
      }
    } catch (error) {
      console.error('更新标签使用计数失败:', error)
      // 不抛出错误，避免影响主要功能
    }
  }

  /**
   * 更新分类书签计数
   * @param categoryName 分类名称
   * @param delta 变化量
   */
  private async updateCategoryBookmarkCount(categoryName: string, delta: number): Promise<void> {
    try {
      // 确保数据库已初始化
      await indexedDBService.init()
      
      const categories = await indexedDBService.getCategories()
      let category = categories?.find(cat => cat.name === categoryName)
      
      if (!category) {
        // 创建新分类
        category = ModelFactory.createCategory({ name: categoryName }, false)
        category.bookmarkCount = Math.max(0, delta)
        await indexedDBService.saveCategory(category)
      } else {
        // 更新现有分类
        const newBookmarkCount = Math.max(0, (category.bookmarkCount || 0) + delta)
        await indexedDBService.updateCategory(category.id, { bookmarkCount: newBookmarkCount })
      }
    } catch (error) {
      console.error('更新分类书签计数失败:', error)
      // 不抛出错误，避免影响主要功能
    }
  }

  /**
   * 从URL提取网站名称
   * @param url URL字符串
   * @returns 网站名称
   */
  private extractSiteName(url: string): string {
    try {
      const urlObj = new URL(url)
      return urlObj.hostname.replace('www.', '')
    } catch {
      return '未知网站'
    }
  }

  /**
   * 标准化URL
   * @param url 原始URL
   * @returns 标准化后的URL
   */
  private normalizeUrl(url: string): string {
    try {
      const urlObj = new URL(url)
      // 移除查询参数和片段，统一协议
      return `${urlObj.protocol}//${urlObj.host}${urlObj.pathname}`.toLowerCase()
    } catch {
      return url.toLowerCase()
    }
  }

  /**
   * 确定重复类型
   * @param bookmarks 相似收藏数组
   * @returns 重复类型
   */
  private determineDuplicateType(bookmarks: Bookmark[]): 'url' | 'content' | 'title' {
    // 检查URL重复
    const urls = bookmarks.map(b => b.url).filter(Boolean)
    if (urls.length > 1 && new Set(urls).size === 1) {
      return 'url'
    }

    // 检查内容重复
    const contents = bookmarks.map(b => b.content).filter(Boolean)
    if (contents.length > 1) {
      return 'content'
    }

    // 默认为标题重复
    return 'title'
  }
}

// 导出单例实例
export const bookmarkService = new BookmarkService()