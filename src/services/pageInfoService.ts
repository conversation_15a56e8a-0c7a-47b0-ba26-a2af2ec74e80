// 页面信息提取服务 - 负责从网页中提取元数据和内容信息

import { PageInfo } from '../types'

/**
 * 页面信息提取服务类
 * 提供页面信息提取和元数据收集功能
 */
export class PageInfoService {

  /**
   * 提取当前页面信息
   * @param options 提取选项
   * @returns Promise<PageInfo> 页面信息
   */
  async extractPageInfo(options?: {
    selectedText?: string
    includeMetadata?: boolean
  }): Promise<PageInfo> {
    try {
      const pageInfo: PageInfo = {
        title: this.extractTitle(),
        url: window.location.href,
        favicon: this.extractFavicon(),
        selectedText: options?.selectedText || this.getSelectedText(),
        timestamp: new Date()
      }

      // 如果需要包含元数据
      if (options?.includeMetadata) {
        const metadata = this.extractMetadata()
        Object.assign(pageInfo, metadata)
      }

      return pageInfo
    } catch (error) {
      console.error('提取页面信息失败:', error)
      throw new Error(`页面信息提取失败: ${error}`)
    }
  }

  /**
   * 提取页面标题
   * @returns 页面标题
   */
  private extractTitle(): string {
    // 优先级：页面标题 > meta标题 > h1标题 > 默认标题
    const title = document.title ||
                  this.getMetaContent('title') ||
                  this.getMetaContent('og:title') ||
                  this.getMetaContent('twitter:title') ||
                  document.querySelector('h1')?.textContent ||
                  '无标题页面'

    return title.trim()
  }

  /**
   * 提取网站图标
   * @returns 图标URL
   */
  private extractFavicon(): string | undefined {
    // 查找各种类型的图标
    const iconSelectors = [
      'link[rel="icon"]',
      'link[rel="shortcut icon"]',
      'link[rel="apple-touch-icon"]',
      'link[rel="apple-touch-icon-precomposed"]'
    ]

    for (const selector of iconSelectors) {
      const iconElement = document.querySelector(selector) as HTMLLinkElement
      if (iconElement?.href) {
        return this.resolveUrl(iconElement.href)
      }
    }

    // 尝试默认favicon路径
    const defaultFavicon = `${window.location.protocol}//${window.location.host}/favicon.ico`
    return defaultFavicon
  }

  /**
   * 获取选中的文字
   * @returns 选中的文字内容
   */
  private getSelectedText(): string | undefined {
    const selection = window.getSelection()
    if (selection && selection.toString().trim()) {
      return selection.toString().trim()
    }
    return undefined
  }

  /**
   * 提取页面元数据
   * @returns 元数据对象
   */
  private extractMetadata(): Record<string, any> {
    const metadata: Record<string, any> = {}

    // 基本元数据
    metadata.description = this.getMetaContent('description') ||
                          this.getMetaContent('og:description') ||
                          this.getMetaContent('twitter:description')

    metadata.keywords = this.getMetaContent('keywords')
    metadata.author = this.getMetaContent('author') ||
                     this.getMetaContent('article:author')

    // Open Graph 元数据
    metadata.ogTitle = this.getMetaContent('og:title')
    metadata.ogDescription = this.getMetaContent('og:description')
    metadata.ogImage = this.getMetaContent('og:image')
    metadata.ogUrl = this.getMetaContent('og:url')
    metadata.ogType = this.getMetaContent('og:type')
    metadata.ogSiteName = this.getMetaContent('og:site_name')

    // Twitter Card 元数据
    metadata.twitterCard = this.getMetaContent('twitter:card')
    metadata.twitterTitle = this.getMetaContent('twitter:title')
    metadata.twitterDescription = this.getMetaContent('twitter:description')
    metadata.twitterImage = this.getMetaContent('twitter:image')

    // 文章相关元数据
    metadata.publishedTime = this.getMetaContent('article:published_time')
    metadata.modifiedTime = this.getMetaContent('article:modified_time')
    metadata.section = this.getMetaContent('article:section')
    metadata.tags = this.getMetaContent('article:tag')

    // 语言信息
    metadata.language = document.documentElement.lang ||
                       this.getMetaContent('language') ||
                       'zh-CN'

    // 页面类型检测
    metadata.pageType = this.detectPageType()

    // 内容统计
    metadata.wordCount = this.calculateWordCount()
    metadata.imageCount = document.querySelectorAll('img').length
    metadata.linkCount = document.querySelectorAll('a').length

    // 清理空值
    Object.keys(metadata).forEach(key => {
      if (!metadata[key]) {
        delete metadata[key]
      }
    })

    return metadata
  }

  /**
   * 获取meta标签内容
   * @param name meta标签名称或property
   * @returns meta内容
   */
  private getMetaContent(name: string): string | undefined {
    // 尝试name属性
    let meta = document.querySelector(`meta[name="${name}"]`) as HTMLMetaElement
    if (meta?.content) {
      return meta.content.trim()
    }

    // 尝试property属性
    meta = document.querySelector(`meta[property="${name}"]`) as HTMLMetaElement
    if (meta?.content) {
      return meta.content.trim()
    }

    return undefined
  }

  /**
   * 检测页面类型
   * @returns 页面类型
   */
  private detectPageType(): string {
    // 检查Open Graph类型
    const ogType = this.getMetaContent('og:type')
    if (ogType) {
      return ogType
    }

    // 根据URL和内容特征检测
    const url = window.location.href.toLowerCase()
    const title = document.title.toLowerCase()

    if (url.includes('/article/') || url.includes('/post/') || 
        document.querySelector('article') ||
        document.querySelector('.article') ||
        document.querySelector('.post')) {
      return 'article'
    }

    if (url.includes('/video/') || 
        document.querySelector('video') ||
        title.includes('视频')) {
      return 'video'
    }

    if (url.includes('/product/') || 
        url.includes('/item/') ||
        document.querySelector('.product') ||
        title.includes('商品')) {
      return 'product'
    }

    if (url.includes('/news/') || 
        document.querySelector('.news') ||
        title.includes('新闻')) {
      return 'news'
    }

    return 'website'
  }

  /**
   * 计算页面字数
   * @returns 字数统计
   */
  private calculateWordCount(): number {
    // 获取主要内容区域
    const contentSelectors = [
      'article',
      '.content',
      '.article-content',
      '.post-content',
      'main',
      '#content'
    ]

    let contentElement: Element | null = null
    for (const selector of contentSelectors) {
      contentElement = document.querySelector(selector)
      if (contentElement) break
    }

    // 如果没找到特定内容区域，使用body
    if (!contentElement) {
      contentElement = document.body
    }

    const text = contentElement.textContent || ''
    
    // 中文字符统计
    const chineseChars = text.match(/[\u4e00-\u9fa5]/g) || []
    
    // 英文单词统计
    const englishWords = text.match(/[a-zA-Z]+/g) || []
    
    return chineseChars.length + englishWords.length
  }

  /**
   * 解析相对URL为绝对URL
   * @param url 可能是相对的URL
   * @returns 绝对URL
   */
  private resolveUrl(url: string): string {
    try {
      return new URL(url, window.location.href).href
    } catch {
      return url
    }
  }

  /**
   * 提取链接信息
   * @param url 链接URL
   * @returns Promise<object> 链接信息
   */
  async extractLinkInfo(url: string): Promise<{
    title?: string
    description?: string
    favicon?: string
  }> {
    try {
      // 在当前页面中查找该链接
      const linkElement = document.querySelector(`a[href="${url}"]`) as HTMLAnchorElement
      
      const linkInfo: any = {}

      if (linkElement) {
        // 从链接元素获取信息
        linkInfo.title = linkElement.title || 
                        linkElement.textContent?.trim() ||
                        linkElement.getAttribute('aria-label')

        // 尝试从链接的data属性获取更多信息
        linkInfo.description = linkElement.getAttribute('data-description')
      }

      // 尝试从URL推断信息
      if (!linkInfo.title) {
        const urlObj = new URL(url)
        linkInfo.title = urlObj.pathname.split('/').pop() || urlObj.hostname
      }

      // 尝试获取网站图标
      if (!linkInfo.favicon) {
        const urlObj = new URL(url)
        linkInfo.favicon = `${urlObj.protocol}//${urlObj.host}/favicon.ico`
      }

      return linkInfo
    } catch (error) {
      console.error('提取链接信息失败:', error)
      return {}
    }
  }

  /**
   * 获取页面截图（如果支持）
   * @returns Promise<string | undefined> Base64编码的截图
   */
  async captureScreenshot(): Promise<string | undefined> {
    try {
      // 这个功能需要在background script中实现
      // 因为content script没有截图权限
      return undefined
    } catch (error) {
      console.error('获取页面截图失败:', error)
      return undefined
    }
  }

  /**
   * 提取页面中的图片信息
   * @returns 图片信息数组
   */
  extractImages(): Array<{
    src: string
    alt?: string
    title?: string
    width?: number
    height?: number
  }> {
    const images = Array.from(document.querySelectorAll('img'))
    
    return images
      .filter(img => img.src && !img.src.startsWith('data:')) // 过滤掉base64图片
      .map(img => ({
        src: this.resolveUrl(img.src),
        alt: img.alt,
        title: img.title,
        width: img.naturalWidth || img.width,
        height: img.naturalHeight || img.height
      }))
      .filter(img => img.width && img.height && img.width > 50 && img.height > 50) // 过滤小图片
  }

  /**
   * 检测页面是否为单页应用
   * @returns 是否为SPA
   */
  isSinglePageApp(): boolean {
    // 检测常见的SPA框架特征
    const indicators = [
      () => !!(window as any).React,
      () => !!(window as any).Vue,
      () => !!(window as any).angular,
      () => document.querySelector('[data-reactroot]'),
      () => document.querySelector('#app'),
      () => document.querySelector('.vue-app'),
      () => document.querySelector('[ng-app]'),
      () => document.body.innerHTML.includes('router-outlet')
    ]

    return indicators.some(check => {
      try {
        return check()
      } catch {
        return false
      }
    })
  }

  /**
   * 监听页面变化（用于SPA）
   * @param callback 变化回调函数
   * @returns 清理函数
   */
  observePageChanges(callback: () => void): () => void {
    let lastUrl = window.location.href
    let lastTitle = document.title

    // 监听URL变化
    const checkChanges = () => {
      if (window.location.href !== lastUrl || document.title !== lastTitle) {
        lastUrl = window.location.href
        lastTitle = document.title
        callback()
      }
    }

    // 监听popstate事件（浏览器前进后退）
    window.addEventListener('popstate', checkChanges)

    // 监听pushState和replaceState（SPA路由变化）
    const originalPushState = history.pushState
    const originalReplaceState = history.replaceState

    history.pushState = function(...args) {
      originalPushState.apply(history, args)
      setTimeout(checkChanges, 0)
    }

    history.replaceState = function(...args) {
      originalReplaceState.apply(history, args)
      setTimeout(checkChanges, 0)
    }

    // 监听DOM变化
    const observer = new MutationObserver(() => {
      if (document.title !== lastTitle) {
        lastTitle = document.title
        callback()
      }
    })

    observer.observe(document.head, {
      childList: true,
      subtree: true
    })

    // 返回清理函数
    return () => {
      window.removeEventListener('popstate', checkChanges)
      history.pushState = originalPushState
      history.replaceState = originalReplaceState
      observer.disconnect()
    }
  }
}

// 导出单例实例
export const pageInfoService = new PageInfoService()