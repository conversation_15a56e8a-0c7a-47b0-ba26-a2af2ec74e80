// 内存优化处理器 - 提供分批处理和内存管理功能

/**
 * 处理配置接口
 */
export interface ProcessingConfig {
  batchSize: number // 批次大小
  maxConcurrency: number // 最大并发数
  memoryThreshold: number // 内存阈值（MB）
  gcInterval: number // 垃圾回收间隔（毫秒）
  progressCallback?: (progress: number, message: string) => void
}

/**
 * 内存管理策略接口
 */
export interface MemoryStrategy {
  shouldTriggerGC(memoryInfo: MemoryInfo, threshold: number): boolean
  getOptimalBatchSize(itemCount: number, availableMemory: number): number
  getOptimalConcurrency(memoryUsage: number): number
}

/**
 * 进度报告器接口
 */
export interface ProgressReporter {
  reportProgress(progress: number, message: string): void
  reportBatchComplete(batchIndex: number, totalBatches: number, processedItems: number): void
  reportError(batchIndex: number, error: Error): void
}

/**
 * 批处理结果接口
 */
export interface BatchProcessingResult<T> {
  results: T[]
  totalProcessed: number
  totalBatches: number
  processingTime: number
  memoryUsage: {
    initial: number
    peak: number
    final: number
  }
  errors: Array<{
    batchIndex: number
    error: Error
    items: any[]
  }>
}

/**
 * 内存使用信息
 */
export interface MemoryInfo {
  used: number // 已使用内存（MB）
  total: number // 总内存（MB）
  percentage: number // 使用百分比
  available: number // 可用内存（MB）
}

/**
 * 默认内存管理策略
 */
class DefaultMemoryStrategy implements MemoryStrategy {
  shouldTriggerGC(memoryInfo: MemoryInfo, threshold: number): boolean {
    return memoryInfo.used > threshold || memoryInfo.percentage > 80
  }

  getOptimalBatchSize(itemCount: number, availableMemory: number): number {
    const baseBatchSize = 100
    if (availableMemory < 50) return Math.max(10, Math.floor(baseBatchSize * 0.3))
    if (itemCount > 10000) return Math.max(50, Math.floor(baseBatchSize * 0.7))
    return baseBatchSize
  }

  getOptimalConcurrency(memoryUsage: number): number {
    if (memoryUsage > 80) return 1
    if (memoryUsage > 60) return 2
    return 3
  }
}

/**
 * 默认进度报告器
 */
class DefaultProgressReporter implements ProgressReporter {
  constructor(private callback?: (progress: number, message: string) => void) {}

  reportProgress(progress: number, message: string): void {
    this.callback?.(progress, message)
  }

  reportBatchComplete(batchIndex: number, totalBatches: number, processedItems: number): void {
    const progress = Math.floor(((batchIndex + 1) / totalBatches) * 100)
    this.reportProgress(progress, `处理批次 ${batchIndex + 1}/${totalBatches}，已处理 ${processedItems} 项`)
  }

  reportError(batchIndex: number, error: Error): void {
    console.error(`批次 ${batchIndex} 处理失败:`, error)
  }
}

/**
 * 内存监控器类
 */
class MemoryMonitor {
  private monitorInterval?: NodeJS.Timeout
  private gcInterval?: NodeJS.Timeout
  private readonly observers: Array<(memoryInfo: MemoryInfo) => void> = []

  constructor(
    private strategy: MemoryStrategy,
    private processor: MemoryOptimizedProcessor
  ) {}

  start(config: ProcessingConfig): void {
    this.stop() // 确保之前的监控已停止

    // 内存监控
    this.monitorInterval = setInterval(() => {
      const memoryInfo = this.processor.getMemoryUsage()
      this.notifyObservers(memoryInfo)

      if (memoryInfo.used > config.memoryThreshold) {
        console.warn(`内存使用过高: ${memoryInfo.used}MB (${memoryInfo.percentage}%)`)
        
        if (memoryInfo.percentage > 90) {
          console.error('内存使用率超过90%，可能影响性能')
        }
      }
    }, 2000)

    // 定期垃圾回收
    this.gcInterval = setInterval(() => {
      const memoryInfo = this.processor.getMemoryUsage()
      if (this.strategy.shouldTriggerGC(memoryInfo, config.memoryThreshold)) {
        this.processor.performGarbageCollection()
      }
    }, config.gcInterval)
  }

  stop(): void {
    if (this.monitorInterval) {
      clearInterval(this.monitorInterval)
      this.monitorInterval = undefined
    }
    
    if (this.gcInterval) {
      clearInterval(this.gcInterval)
      this.gcInterval = undefined
    }
  }

  addObserver(observer: (memoryInfo: MemoryInfo) => void): void {
    this.observers.push(observer)
  }

  removeObserver(observer: (memoryInfo: MemoryInfo) => void): void {
    const index = this.observers.indexOf(observer)
    if (index > -1) {
      this.observers.splice(index, 1)
    }
  }

  private notifyObservers(memoryInfo: MemoryInfo): void {
    this.observers.forEach(observer => observer(memoryInfo))
  }
}

/**
 * 批处理执行器类
 */
class BatchExecutor {
  constructor(
    private semaphore: Semaphore,
    private progressReporter: ProgressReporter
  ) {}

  async executeBatch<TInput, TOutput>(
    batch: TInput[],
    batchIndex: number,
    totalBatches: number,
    processor: (batch: TInput[], batchIndex: number) => Promise<TOutput[]>,
    onMemoryCheck: () => Promise<void>
  ): Promise<{ results: TOutput[], error?: Error }> {
    const release = await this.semaphore.acquire()
    
    try {
      const results = await processor(batch, batchIndex)
      
      this.progressReporter.reportBatchComplete(batchIndex, totalBatches, results.length)
      
      // 内存检查
      await onMemoryCheck()
      
      return { results }
    } catch (error) {
      const err = error as Error
      this.progressReporter.reportError(batchIndex, err)
      return { results: [], error: err }
    } finally {
      release()
    }
  }
}

/**
 * 内存优化处理器类
 * 提供分批处理、内存监控、垃圾回收等功能
 */
export class MemoryOptimizedProcessor {
  private readonly defaultConfig: ProcessingConfig = {
    batchSize: 100,
    maxConcurrency: 3,
    memoryThreshold: 100, // 100MB
    gcInterval: 5000 // 5秒
  }

  private isProcessing = false
  private memoryStrategy: MemoryStrategy
  private memoryMonitor: MemoryMonitor

  constructor(strategy?: MemoryStrategy) {
    this.memoryStrategy = strategy || new DefaultMemoryStrategy()
    this.memoryMonitor = new MemoryMonitor(this.memoryStrategy, this)
  }

  /**
   * 分批处理数据
   * @param items 要处理的数据项
   * @param processor 处理函数
   * @param config 处理配置
   * @returns Promise<BatchProcessingResult<T>>
   */
  async processBatches<TInput, TOutput>(
    items: TInput[],
    processor: (batch: TInput[], batchIndex: number) => Promise<TOutput[]>,
    config: Partial<ProcessingConfig> = {}
  ): Promise<BatchProcessingResult<TOutput>> {
    const finalConfig = this.mergeConfig(config)
    const processingContext = this.initializeProcessing(finalConfig)
    
    try {
      const batches = this.createBatches(items, finalConfig.batchSize)
      const executor = new BatchExecutor(
        new Semaphore(finalConfig.maxConcurrency),
        new DefaultProgressReporter(finalConfig.progressCallback)
      )
      
      const results = await this.executeBatches(
        batches,
        processor,
        executor,
        processingContext
      )
      
      return this.createProcessingResult(results, processingContext, batches.length)
    } finally {
      this.finalizeProcessing()
    }
  }

  /**
   * 初始化处理上下文
   */
  private initializeProcessing(config: ProcessingConfig) {
    const startTime = Date.now()
    const initialMemory = this.getMemoryUsage()
    
    this.isProcessing = true
    this.memoryMonitor.start(config)
    
    return {
      startTime,
      initialMemory,
      peakMemory: initialMemory.used,
      results: [] as any[],
      errors: [] as Array<{ batchIndex: number, error: Error, items: any[] }>
    }
  }

  /**
   * 执行所有批次
   */
  private async executeBatches<TInput, TOutput>(
    batches: TInput[][],
    processor: (batch: TInput[], batchIndex: number) => Promise<TOutput[]>,
    executor: BatchExecutor,
    context: any
  ): Promise<TOutput[]> {
    const batchPromises = batches.map(async (batch, index) => {
      const result = await executor.executeBatch(
        batch,
        index,
        batches.length,
        processor,
        () => this.checkAndOptimizeMemory(context)
      )
      
      if (result.error) {
        context.errors.push({
          batchIndex: index,
          error: result.error,
          items: batch
        })
      }
      
      return result.results
    })
    
    const batchResults = await Promise.all(batchPromises)
    return batchResults.flat()
  }

  /**
   * 检查并优化内存
   */
  private async checkAndOptimizeMemory(context: any): Promise<void> {
    const currentMemory = this.getMemoryUsage()
    context.peakMemory = Math.max(context.peakMemory, currentMemory.used)
    
    if (this.memoryStrategy.shouldTriggerGC(currentMemory, this.defaultConfig.memoryThreshold)) {
      await this.performGarbageCollection()
    }
  }

  /**
   * 创建处理结果
   */
  private createProcessingResult<TOutput>(
    results: TOutput[],
    context: any,
    totalBatches: number
  ): BatchProcessingResult<TOutput> {
    const endTime = Date.now()
    const finalMemory = this.getMemoryUsage()
    
    return {
      results,
      totalProcessed: results.length,
      totalBatches,
      processingTime: endTime - context.startTime,
      memoryUsage: {
        initial: context.initialMemory.used,
        peak: context.peakMemory,
        final: finalMemory.used
      },
      errors: context.errors
    }
  }

  /**
   * 合并配置
   */
  private mergeConfig(config: Partial<ProcessingConfig>): ProcessingConfig {
    return { ...this.defaultConfig, ...config }
  }

  /**
   * 完成处理
   */
  private finalizeProcessing(): void {
    this.isProcessing = false
    this.memoryMonitor.stop()
  }

  /**
   * 流式处理大量数据
   * @param items 数据项
   * @param processor 处理函数
   * @param config 配置
   * @returns AsyncGenerator<TOutput>
   */
  async* processStream<TInput, TOutput>(
    items: TInput[],
    processor: (item: TInput, index: number) => Promise<TOutput>,
    config: Partial<ProcessingConfig> = {}
  ): AsyncGenerator<TOutput, void, unknown> {
    const finalConfig = this.mergeConfig(config)
    const batches = this.createBatches(items, finalConfig.batchSize)
    
    this.isProcessing = true
    this.memoryMonitor.start(finalConfig)
    
    try {
      for (let batchIndex = 0; batchIndex < batches.length; batchIndex++) {
        const batch = batches[batchIndex]
        
        // 并发处理批次内的项目
        const batchPromises = batch.map((item, itemIndex) => 
          processor(item, batchIndex * finalConfig.batchSize + itemIndex)
        )
        
        const batchResults = await Promise.allSettled(batchPromises)
        
        for (const result of batchResults) {
          if (result.status === 'fulfilled') {
            yield result.value
          }
        }
        
        // 更新进度
        const progress = Math.floor(((batchIndex + 1) / batches.length) * 100)
        finalConfig.progressCallback?.(
          progress, 
          `流式处理批次 ${batchIndex + 1}/${batches.length}`
        )
        
        // 内存检查和清理
        if (this.getMemoryUsage().used > finalConfig.memoryThreshold) {
          await this.performGarbageCollection()
        }
      }
    } finally {
      this.isProcessing = false
      this.memoryMonitor.stop()
    }
  }

  /**
   * 内存安全的数据转换
   * @param data 原始数据
   * @param transformer 转换函数
   * @param config 配置
   * @returns Promise<T>
   */
  async safeTransform<TInput, TOutput>(
    data: TInput,
    transformer: (data: TInput) => Promise<TOutput>,
    config: Partial<ProcessingConfig> = {}
  ): Promise<TOutput> {
    const finalConfig = { ...this.defaultConfig, ...config }
    const initialMemory = this.getMemoryUsage()
    
    // 检查初始内存状态
    if (initialMemory.used > finalConfig.memoryThreshold) {
      await this.performGarbageCollection()
    }
    
    try {
      const result = await transformer(data)
      
      // 检查转换后的内存使用
      const finalMemory = this.getMemoryUsage()
      if (finalMemory.used > finalConfig.memoryThreshold) {
        await this.performGarbageCollection()
      }
      
      return result
    } catch (error) {
      // 错误时也进行内存清理
      await this.performGarbageCollection()
      throw error
    }
  }

  /**
   * 获取内存使用信息
   * @returns MemoryInfo
   */
  getMemoryUsage(): MemoryInfo {
    if (typeof window !== 'undefined' && 'performance' in window && 'memory' in performance) {
      const memory = (performance as any).memory
      const used = Math.round(memory.usedJSHeapSize / 1024 / 1024)
      const total = Math.round(memory.totalJSHeapSize / 1024 / 1024)
      const limit = Math.round(memory.jsHeapSizeLimit / 1024 / 1024)
      
      return {
        used,
        total,
        percentage: Math.round((used / limit) * 100),
        available: limit - used
      }
    }
    
    // 浏览器不支持内存API时的估算
    return {
      used: 0,
      total: 0,
      percentage: 0,
      available: 1000 // 假设1GB可用
    }
  }

  /**
   * 执行垃圾回收
   * @returns Promise<void>
   */
  async performGarbageCollection(): Promise<void> {
    try {
      // 尝试手动触发垃圾回收（仅在开发环境或支持的浏览器中）
      if (typeof window !== 'undefined' && 'gc' in window) {
        (window as any).gc()
      }
      
      // 清理可能的内存泄漏
      await this.clearTemporaryReferences()
      
      // 给垃圾回收器一些时间
      await this.delay(100)
      
    } catch (error) {
      console.warn('垃圾回收失败:', error)
    }
  }

  /**
   * 检查是否需要内存优化
   * @param threshold 内存阈值
   * @returns boolean
   */
  needsMemoryOptimization(threshold?: number): boolean {
    const memoryInfo = this.getMemoryUsage()
    const actualThreshold = threshold || this.defaultConfig.memoryThreshold
    
    return this.memoryStrategy.shouldTriggerGC(memoryInfo, actualThreshold)
  }

  /**
   * 获取处理建议
   * @param itemCount 项目数量
   * @param itemSize 单项大小估算（KB）
   * @returns 处理建议
   */
  getProcessingRecommendations(itemCount: number, itemSize: number = 1): {
    batchSize: number
    maxConcurrency: number
    estimatedMemory: number
    recommendations: string[]
  } {
    const memoryInfo = this.getMemoryUsage()
    const estimatedTotalSize = (itemCount * itemSize) / 1024 // MB
    
    const batchSize = this.memoryStrategy.getOptimalBatchSize(itemCount, memoryInfo.available)
    const maxConcurrency = this.memoryStrategy.getOptimalConcurrency(memoryInfo.percentage)
    const recommendations = this.generateRecommendations(
      memoryInfo,
      estimatedTotalSize,
      itemCount
    )
    
    return {
      batchSize,
      maxConcurrency,
      estimatedMemory: estimatedTotalSize,
      recommendations
    }
  }

  /**
   * 生成处理建议
   */
  private generateRecommendations(
    memoryInfo: MemoryInfo,
    estimatedTotalSize: number,
    itemCount: number
  ): string[] {
    const recommendations: string[] = []
    
    if (estimatedTotalSize > memoryInfo.available * 0.5) {
      recommendations.push('数据量较大，建议使用较小的批次大小')
    }
    
    if (itemCount > 10000) {
      recommendations.push('项目数量很多，建议降低并发数')
    }
    
    if (memoryInfo.percentage > 70) {
      recommendations.push('当前内存使用率较高，建议使用保守的处理参数')
    }
    
    if (estimatedTotalSize > memoryInfo.available) {
      recommendations.push('数据量超过可用内存，建议分批导入或清理内存')
    }
    
    return recommendations
  }

  // ==================== 私有方法 ====================

  /**
   * 创建批次
   */
  private createBatches<T>(items: T[], batchSize: number): T[][] {
    const batches: T[][] = []
    
    for (let i = 0; i < items.length; i += batchSize) {
      batches.push(items.slice(i, i + batchSize))
    }
    
    return batches
  }

  /**
   * 清理临时引用
   */
  private async clearTemporaryReferences(): Promise<void> {
    // 这里可以清理一些临时的全局引用
    // 例如清理缓存、临时DOM元素等
    
    // 清理可能的事件监听器
    if (typeof window !== 'undefined') {
      // 移除可能的临时事件监听器
    }
    
    // 清理可能的定时器
    // 这里可以添加具体的清理逻辑
  }

  /**
   * 延迟执行
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }
}

/**
 * 信号量类 - 用于控制并发数
 */
class Semaphore {
  private permits: number
  private waitQueue: Array<() => void> = []

  constructor(permits: number) {
    this.permits = permits
  }

  async acquire(): Promise<() => void> {
    return new Promise((resolve) => {
      if (this.permits > 0) {
        this.permits--
        resolve(() => this.release())
      } else {
        this.waitQueue.push(() => {
          this.permits--
          resolve(() => this.release())
        })
      }
    })
  }

  private release(): void {
    this.permits++
    
    if (this.waitQueue.length > 0) {
      const next = this.waitQueue.shift()
      if (next) {
        next()
      }
    }
  }
}

// 导出单例实例
export const memoryOptimizedProcessor = new MemoryOptimizedProcessor()