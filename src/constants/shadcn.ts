// shadcn/ui 组件相关常量定义

// 组件变体常量
export const BUTTON_VARIANTS = {
  DEFAULT: 'default',
  DESTRUCTIVE: 'destructive',
  OUTLINE: 'outline',
  SECONDARY: 'secondary',
  GHOST: 'ghost',
  LINK: 'link'
} as const

export const BUTTON_SIZES = {
  DEFAULT: 'default',
  SM: 'sm',
  LG: 'lg',
  ICON: 'icon'
} as const

export const BADGE_VARIANTS = {
  DEFAULT: 'default',
  SECONDARY: 'secondary',
  DESTRUCTIVE: 'destructive',
  OUTLINE: 'outline'
} as const

// 主题相关常量
export const THEMES = {
  LIGHT: 'light',
  DARK: 'dark',
  SYSTEM: 'system'
} as const

export const CSS_VARIABLES = {
  BACKGROUND: '--background',
  FOREGROUND: '--foreground',
  CARD: '--card',
  CARD_FOREGROUND: '--card-foreground',
  POPOVER: '--popover',
  POPOVER_FOREGROUND: '--popover-foreground',
  PRIMARY: '--primary',
  PRIMARY_FOREGROUND: '--primary-foreground',
  SECONDARY: '--secondary',
  SECONDARY_FOREGROUND: '--secondary-foreground',
  MUTED: '--muted',
  MUTED_FOREGROUND: '--muted-foreground',
  ACCENT: '--accent',
  ACCENT_FOREGROUND: '--accent-foreground',
  DESTRUCTIVE: '--destructive',
  DESTRUCTIVE_FOREGROUND: '--destructive-foreground',
  BORDER: '--border',
  INPUT: '--input',
  RING: '--ring',
  RADIUS: '--radius'
} as const

// 动画相关常量
export const ANIMATION_DURATIONS = {
  FAST: 150,
  NORMAL: 200,
  SLOW: 300,
  SLOWER: 500
} as const

export const ANIMATION_EASINGS = {
  DEFAULT: 'cubic-bezier(0.4, 0, 0.2, 1)',
  IN: 'cubic-bezier(0.4, 0, 1, 1)',
  OUT: 'cubic-bezier(0, 0, 0.2, 1)',
  IN_OUT: 'cubic-bezier(0.4, 0, 0.2, 1)'
} as const

// 间距相关常量
export const SPACING = {
  XS: '0.25rem',
  SM: '0.5rem',
  MD: '1rem',
  LG: '1.5rem',
  XL: '2rem',
  '2XL': '3rem',
  '3XL': '4rem',
  '4XL': '5rem'
} as const

// 断点相关常量
export const BREAKPOINTS = {
  SM: 640,
  MD: 768,
  LG: 1024,
  XL: 1280,
  '2XL': 1536
} as const

// Z-index 层级常量
export const Z_INDEX = {
  DROPDOWN: 1000,
  STICKY: 1020,
  FIXED: 1030,
  MODAL_BACKDROP: 1040,
  MODAL: 1050,
  POPOVER: 1060,
  TOOLTIP: 1070,
  TOAST: 1080
} as const

// 组件尺寸常量
export const COMPONENT_SIZES = {
  XS: 'xs',
  SM: 'sm',
  MD: 'md',
  LG: 'lg',
  XL: 'xl'
} as const

// 颜色变体常量
export const COLOR_VARIANTS = {
  DEFAULT: 'default',
  PRIMARY: 'primary',
  SECONDARY: 'secondary',
  SUCCESS: 'success',
  WARNING: 'warning',
  DANGER: 'danger',
  INFO: 'info'
} as const

// 位置相关常量
export const POSITIONS = {
  TOP: 'top',
  RIGHT: 'right',
  BOTTOM: 'bottom',
  LEFT: 'left'
} as const

export const ALIGNMENTS = {
  START: 'start',
  CENTER: 'center',
  END: 'end'
} as const

// 表单相关常量
export const FORM_FIELD_TYPES = {
  TEXT: 'text',
  EMAIL: 'email',
  PASSWORD: 'password',
  NUMBER: 'number',
  TEL: 'tel',
  URL: 'url',
  SEARCH: 'search',
  TEXTAREA: 'textarea',
  SELECT: 'select',
  CHECKBOX: 'checkbox',
  RADIO: 'radio',
  FILE: 'file',
  DATE: 'date',
  TIME: 'time',
  DATETIME_LOCAL: 'datetime-local'
} as const

export const FORM_VALIDATION_RULES = {
  REQUIRED: 'required',
  MIN_LENGTH: 'minLength',
  MAX_LENGTH: 'maxLength',
  PATTERN: 'pattern',
  EMAIL: 'email',
  URL: 'url',
  MIN: 'min',
  MAX: 'max'
} as const

// 状态相关常量
export const COMPONENT_STATES = {
  IDLE: 'idle',
  LOADING: 'loading',
  SUCCESS: 'success',
  ERROR: 'error',
  DISABLED: 'disabled'
} as const

// 键盘事件常量
export const KEYBOARD_KEYS = {
  ENTER: 'Enter',
  ESCAPE: 'Escape',
  SPACE: ' ',
  TAB: 'Tab',
  ARROW_UP: 'ArrowUp',
  ARROW_DOWN: 'ArrowDown',
  ARROW_LEFT: 'ArrowLeft',
  ARROW_RIGHT: 'ArrowRight',
  HOME: 'Home',
  END: 'End',
  PAGE_UP: 'PageUp',
  PAGE_DOWN: 'PageDown'
} as const

// ARIA 相关常量
export const ARIA_ATTRIBUTES = {
  LABEL: 'aria-label',
  LABELLEDBY: 'aria-labelledby',
  DESCRIBEDBY: 'aria-describedby',
  EXPANDED: 'aria-expanded',
  SELECTED: 'aria-selected',
  CHECKED: 'aria-checked',
  DISABLED: 'aria-disabled',
  HIDDEN: 'aria-hidden',
  LIVE: 'aria-live',
  ATOMIC: 'aria-atomic',
  RELEVANT: 'aria-relevant',
  BUSY: 'aria-busy',
  INVALID: 'aria-invalid',
  REQUIRED: 'aria-required',
  READONLY: 'aria-readonly',
  MULTILINE: 'aria-multiline',
  AUTOCOMPLETE: 'aria-autocomplete',
  HASPOPUP: 'aria-haspopup',
  CONTROLS: 'aria-controls',
  OWNS: 'aria-owns',
  ACTIVEDESCENDANT: 'aria-activedescendant'
} as const

export const ARIA_LIVE_REGIONS = {
  OFF: 'off',
  POLITE: 'polite',
  ASSERTIVE: 'assertive'
} as const

// 数据属性常量
export const DATA_ATTRIBUTES = {
  STATE: 'data-state',
  ORIENTATION: 'data-orientation',
  SIDE: 'data-side',
  ALIGN: 'data-align',
  DISABLED: 'data-disabled',
  HIGHLIGHTED: 'data-highlighted',
  SELECTED: 'data-selected',
  PLACEHOLDER: 'data-placeholder'
} as const

// 组件默认配置
export const DEFAULT_CONFIGS = {
  BUTTON: {
    variant: BUTTON_VARIANTS.DEFAULT,
    size: BUTTON_SIZES.DEFAULT
  },
  BADGE: {
    variant: BADGE_VARIANTS.DEFAULT
  },
  DIALOG: {
    closeOnEscape: true,
    closeOnOutsideClick: true
  },
  TOOLTIP: {
    delayDuration: 700,
    skipDelayDuration: 300
  },
  TOAST: {
    duration: 5000,
    position: 'bottom-right'
  }
} as const

// 正则表达式常量
export const REGEX_PATTERNS = {
  EMAIL: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  URL: /^https?:\/\/.+/,
  PHONE: /^\+?[\d\s\-\(\)]+$/,
  ALPHANUMERIC: /^[a-zA-Z0-9]+$/,
  ALPHA: /^[a-zA-Z]+$/,
  NUMERIC: /^\d+$/,
  HEX_COLOR: /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/,
  RGB_COLOR: /^rgb\(\s*\d+\s*,\s*\d+\s*,\s*\d+\s*\)$/,
  RGBA_COLOR: /^rgba\(\s*\d+\s*,\s*\d+\s*,\s*\d+\s*,\s*[\d.]+\s*\)$/
} as const

// 错误消息常量
export const ERROR_MESSAGES = {
  REQUIRED: '此字段为必填项',
  INVALID_EMAIL: '请输入有效的邮箱地址',
  INVALID_URL: '请输入有效的URL地址',
  MIN_LENGTH: '输入内容过短',
  MAX_LENGTH: '输入内容过长',
  INVALID_FORMAT: '格式不正确',
  NETWORK_ERROR: '网络连接错误',
  SERVER_ERROR: '服务器错误',
  UNKNOWN_ERROR: '未知错误'
} as const

// 成功消息常量
export const SUCCESS_MESSAGES = {
  SAVED: '保存成功',
  UPDATED: '更新成功',
  DELETED: '删除成功',
  CREATED: '创建成功',
  UPLOADED: '上传成功',
  SENT: '发送成功'
} as const