// 通知管理Hook - 统一管理通知状态

import { useState, useCallback } from 'react'
import type { ToastType } from '../components/Toast'

export interface ToastMessage {
  id: string
  type: ToastType
  title: string
  message?: string
  autoClose?: number
}

export interface UseToastReturn {
  /** 当前通知列表 */
  toasts: ToastMessage[]
  /** 显示成功通知 */
  showSuccess: (title: string, message?: string, autoClose?: number) => void
  /** 显示错误通知 */
  showError: (title: string, message?: string, autoClose?: number) => void
  /** 显示警告通知 */
  showWarning: (title: string, message?: string, autoClose?: number) => void
  /** 显示信息通知 */
  showInfo: (title: string, message?: string, autoClose?: number) => void
  /** 关闭指定通知 */
  closeToast: (id: string) => void
  /** 关闭所有通知 */
  clearToasts: () => void
}

/**
 * 通知管理Hook
 * 提供统一的通知管理功能
 */
export const useToast = (): UseToastReturn => {
  const [toasts, setToasts] = useState<ToastMessage[]>([])

  // 生成唯一ID
  const generateId = useCallback(() => {
    return `toast-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
  }, [])

  // 添加通知
  const addToast = useCallback((
    type: ToastType,
    title: string,
    message?: string,
    autoClose: number = 5000
  ) => {
    const id = generateId()
    const newToast: ToastMessage = {
      id,
      type,
      title,
      message,
      autoClose
    }

    setToasts(prev => [...prev, newToast])

    // 如果设置了自动关闭，则在指定时间后自动移除
    if (autoClose > 0) {
      setTimeout(() => {
        setToasts(prev => prev.filter(toast => toast.id !== id))
      }, autoClose + 300) // 额外300ms等待动画完成
    }
  }, [generateId])

  // 显示成功通知
  const showSuccess = useCallback((title: string, message?: string, autoClose?: number) => {
    addToast('success', title, message, autoClose)
  }, [addToast])

  // 显示错误通知
  const showError = useCallback((title: string, message?: string, autoClose?: number) => {
    addToast('error', title, message, autoClose || 8000) // 错误通知默认显示更长时间
  }, [addToast])

  // 显示警告通知
  const showWarning = useCallback((title: string, message?: string, autoClose?: number) => {
    addToast('warning', title, message, autoClose || 6000)
  }, [addToast])

  // 显示信息通知
  const showInfo = useCallback((title: string, message?: string, autoClose?: number) => {
    addToast('info', title, message, autoClose)
  }, [addToast])

  // 关闭指定通知
  const closeToast = useCallback((id: string) => {
    setToasts(prev => prev.filter(toast => toast.id !== id))
  }, [])

  // 关闭所有通知
  const clearToasts = useCallback(() => {
    setToasts([])
  }, [])

  return {
    toasts,
    showSuccess,
    showError,
    showWarning,
    showInfo,
    closeToast,
    clearToasts
  }
}