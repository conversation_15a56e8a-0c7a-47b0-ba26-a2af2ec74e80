// 加载状态管理Hook - 统一管理加载状态

import { useState, useCallback, useRef } from 'react'

export interface LoadingState {
  /** 是否正在加载 */
  isLoading: boolean
  /** 加载消息 */
  message?: string
  /** 进度百分比 (0-100) */
  progress?: number
}

export interface UseLoadingStateReturn {
  /** 当前加载状态 */
  loadingState: LoadingState
  /** 开始加载 */
  startLoading: (message?: string) => void
  /** 更新加载进度 */
  updateProgress: (progress: number, message?: string) => void
  /** 结束加载 */
  stopLoading: () => void
  /** 执行异步操作并管理加载状态 */
  withLoading: <T>(
    operation: () => Promise<T>,
    message?: string
  ) => Promise<T>
}

/**
 * 加载状态管理Hook
 * 提供统一的加载状态管理功能
 */
export const useLoadingState = (): UseLoadingStateReturn => {
  const [loadingState, setLoadingState] = useState<LoadingState>({
    isLoading: false
  })
  
  const loadingRef = useRef<boolean>(false)

  // 开始加载
  const startLoading = useCallback((message?: string) => {
    loadingRef.current = true
    setLoadingState({
      isLoading: true,
      message,
      progress: undefined
    })
  }, [])

  // 更新加载进度
  const updateProgress = useCallback((progress: number, message?: string) => {
    if (!loadingRef.current) return
    
    setLoadingState(prev => ({
      ...prev,
      progress: Math.max(0, Math.min(100, progress)),
      message: message || prev.message
    }))
  }, [])

  // 结束加载
  const stopLoading = useCallback(() => {
    loadingRef.current = false
    setLoadingState({
      isLoading: false
    })
  }, [])

  // 执行异步操作并管理加载状态
  const withLoading = useCallback(async <T>(
    operation: () => Promise<T>,
    message?: string
  ): Promise<T> => {
    try {
      startLoading(message)
      const result = await operation()
      return result
    } finally {
      stopLoading()
    }
  }, [startLoading, stopLoading])

  return {
    loadingState,
    startLoading,
    updateProgress,
    stopLoading,
    withLoading
  }
}