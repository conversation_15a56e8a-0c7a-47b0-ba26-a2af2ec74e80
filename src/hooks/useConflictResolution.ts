// 冲突解决逻辑的自定义 Hook

import { useState, useCallback, useMemo } from 'react'
import { ConflictItem, ConflictResolution } from '../types'

export interface UseConflictResolutionReturn {
  // 状态
  resolutions: ConflictResolution[]
  currentConflictIndex: number
  editingConflictId: string | null
  editedData: Record<string, any>
  showBatchActions: boolean
  
  // 计算属性
  currentConflict: ConflictItem | undefined
  currentResolution: ConflictResolution | undefined
  stats: {
    resolved: number
    total: number
    byType: Record<string, number>
  }
  
  // 操作方法
  handleSingleResolution: (conflictId: string, action: ConflictResolution['action'], data?: any) => void
  handleBatchResolution: (action: 'keep_existing' | 'use_imported') => void
  handleManualEdit: (conflictId: string) => void
  handleSaveManualEdit: () => void
  handleCancelManualEdit: () => void
  navigateToConflict: (index: number) => void
  handleComplete: () => boolean
  handleResetAll: () => void
  setShowBatchActions: (show: boolean) => void
  setEditedData: (data: Record<string, any> | ((prev: Record<string, any>) => Record<string, any>)) => void
}

export const useConflictResolution = (
  conflicts: ConflictItem[],
  onResolve: (resolutions: ConflictResolution[]) => void
): UseConflictResolutionReturn => {
  // 状态管理
  const [resolutions, setResolutions] = useState<ConflictResolution[]>([])
  const [currentConflictIndex, setCurrentConflictIndex] = useState(0)
  const [editingConflictId, setEditingConflictId] = useState<string | null>(null)
  const [editedData, setEditedData] = useState<Record<string, any>>({})
  const [showBatchActions, setShowBatchActions] = useState(false)
  
  // 计算属性
  const currentConflict = conflicts[currentConflictIndex]
  const currentResolution = resolutions.find(r => r.conflictId === currentConflict?.id)
  
  const stats = useMemo(() => {
    const resolved = resolutions.length
    const total = conflicts.length
    const byType = conflicts.reduce((acc, conflict) => {
      acc[conflict.type] = (acc[conflict.type] || 0) + 1
      return acc
    }, {} as Record<string, number>)
    
    return { resolved, total, byType }
  }, [resolutions.length, conflicts.length])

  // 处理单个冲突解决
  const handleSingleResolution = useCallback((
    conflictId: string, 
    action: ConflictResolution['action'],
    data?: any
  ) => {
    setResolutions(prev => {
      const existing = prev.find(r => r.conflictId === conflictId)
      const newResolution: ConflictResolution = {
        conflictId,
        action,
        ...(action === 'manual_edit' && { manualData: data })
      }
      
      if (existing) {
        return prev.map(r => r.conflictId === conflictId ? newResolution : r)
      } else {
        return [...prev, newResolution]
      }
    })
    
    // 自动跳转到下一个未解决的冲突
    if (currentConflictIndex < conflicts.length - 1) {
      const nextUnresolvedIndex = conflicts.findIndex((conflict, index) => 
        index > currentConflictIndex && 
        !resolutions.some(r => r.conflictId === conflict.id)
      )
      
      if (nextUnresolvedIndex !== -1) {
        setCurrentConflictIndex(nextUnresolvedIndex)
      } else {
        setCurrentConflictIndex(currentConflictIndex + 1)
      }
    }
  }, [currentConflictIndex, conflicts, resolutions])

  // 处理批量操作
  const handleBatchResolution = useCallback((action: 'keep_existing' | 'use_imported') => {
    const batchResolutions = conflicts
      .filter(conflict => !resolutions.some(r => r.conflictId === conflict.id))
      .map(conflict => ({
        conflictId: conflict.id,
        action
      }))
    
    setResolutions(prev => [...prev, ...batchResolutions])
    setShowBatchActions(false)
  }, [conflicts, resolutions])

  // 处理手动编辑
  const handleManualEdit = useCallback((conflictId: string) => {
    const conflict = conflicts.find(c => c.id === conflictId)
    if (conflict) {
      setEditingConflictId(conflictId)
      setEditedData({ ...conflict.importData })
    }
  }, [conflicts])

  // 保存手动编辑
  const handleSaveManualEdit = useCallback(() => {
    if (editingConflictId) {
      handleSingleResolution(editingConflictId, 'manual_edit', editedData)
      setEditingConflictId(null)
      setEditedData({})
    }
  }, [editingConflictId, editedData, handleSingleResolution])

  // 取消手动编辑
  const handleCancelManualEdit = useCallback(() => {
    setEditingConflictId(null)
    setEditedData({})
  }, [])

  // 导航到指定冲突
  const navigateToConflict = useCallback((index: number) => {
    if (index >= 0 && index < conflicts.length) {
      setCurrentConflictIndex(index)
    }
  }, [conflicts.length])

  // 完成解决
  const handleComplete = useCallback(() => {
    if (resolutions.length === conflicts.length) {
      onResolve(resolutions)
      return true
    }
    return false
  }, [resolutions, conflicts.length, onResolve])

  // 重置所有解决方案
  const handleResetAll = useCallback(() => {
    setResolutions([])
    setCurrentConflictIndex(0)
    setEditingConflictId(null)
    setEditedData({})
  }, [])

  return {
    // 状态
    resolutions,
    currentConflictIndex,
    editingConflictId,
    editedData,
    showBatchActions,
    
    // 计算属性
    currentConflict,
    currentResolution,
    stats,
    
    // 操作方法
    handleSingleResolution,
    handleBatchResolution,
    handleManualEdit,
    handleSaveManualEdit,
    handleCancelManualEdit,
    navigateToConflict,
    handleComplete,
    handleResetAll,
    setShowBatchActions,
    setEditedData
  }
}