import { useState, useEffect } from 'react'
import type { ViewMode } from '../components/ViewModeSelector'

// 默认视图模式
const DEFAULT_VIEW_MODE: ViewMode = 'card'

// 本地存储键名
const STORAGE_KEY = 'bookmark-view-mode'

/**
 * 视图模式管理Hook
 * 提供视图模式状态管理和本地存储功能
 */
export const useViewMode = () => {
  const [viewMode, setViewMode] = useState<ViewMode>(DEFAULT_VIEW_MODE)
  const [isLoading, setIsLoading] = useState(true)

  // 从本地存储加载视图模式
  useEffect(() => {
    const loadViewMode = async () => {
      try {
        // 添加适当的延迟以确保加载状态可见，避免闪烁
        await new Promise(resolve => setTimeout(resolve, 100))
        
        const savedMode = localStorage.getItem(STORAGE_KEY) as ViewMode
        if (savedMode && ['card', 'row', 'compact'].includes(savedMode)) {
          setViewMode(savedMode)
        }
      } catch (error) {
        console.warn('加载视图模式失败:', error)
        // 使用默认模式
        setViewMode(DEFAULT_VIEW_MODE)
      } finally {
        // 确保加载状态至少持续200ms，避免快速闪烁
        setTimeout(() => {
          setIsLoading(false)
        }, 100)
      }
    }

    loadViewMode()
  }, [])

  // 更新视图模式
  const updateViewMode = (newMode: ViewMode) => {
    try {
      setViewMode(newMode)
      localStorage.setItem(STORAGE_KEY, newMode)
    } catch (error) {
      console.error('保存视图模式失败:', error)
    }
  }

  // 重置为默认模式
  const resetViewMode = () => {
    updateViewMode(DEFAULT_VIEW_MODE)
  }

  return {
    viewMode,
    setViewMode: updateViewMode,
    resetViewMode,
    isLoading
  }
}