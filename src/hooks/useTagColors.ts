// 标签颜色获取Hook

import { useState, useEffect, useCallback } from 'react'
import { tagService } from '../services/tagService'
import type { Tag } from '../types'

/**
 * 标签颜色映射接口
 */
export interface TagColorMap {
  [tagName: string]: string
}

/**
 * 标签颜色Hook返回值
 */
export interface UseTagColorsReturn {
  /** 标签颜色映射 */
  tagColors: TagColorMap
  /** 是否正在加载 */
  loading: boolean
  /** 获取指定标签的颜色 */
  getTagColor: (tagName: string) => string
  /** 刷新标签颜色数据 */
  refreshTagColors: () => Promise<void>
}

/**
 * 标签颜色获取Hook
 * 提供标签名称到颜色的映射，用于在收藏管理中显示带颜色的标签
 */
export const useTagColors = (): UseTagColorsReturn => {
  const [tagColors, setTagColors] = useState<TagColorMap>({})
  const [loading, setLoading] = useState(true)

  /**
   * 加载标签颜色数据
   */
  const loadTagColors = useCallback(async () => {
    try {
      setLoading(true)
      const tags = await tagService.getTags()
      
      // 构建标签名称到颜色的映射
      const colorMap: TagColorMap = {}
      tags.forEach((tag: Tag) => {
        colorMap[tag.name] = tag.color || '#6B7280' // 默认灰色
      })
      
      setTagColors(colorMap)
    } catch (error) {
      console.error('加载标签颜色失败:', error)
      setTagColors({})
    } finally {
      setLoading(false)
    }
  }, [])

  /**
   * 获取指定标签的颜色
   */
  const getTagColor = useCallback((tagName: string): string => {
    return tagColors[tagName] || '#6B7280' // 默认灰色
  }, [tagColors])

  /**
   * 刷新标签颜色数据
   */
  const refreshTagColors = useCallback(async () => {
    await loadTagColors()
  }, [loadTagColors])

  // 初始化加载
  useEffect(() => {
    loadTagColors()
  }, [loadTagColors])

  return {
    tagColors,
    loading,
    getTagColor,
    refreshTagColors
  }
}

export default useTagColors