// 数据处理Worker - 在后台线程中处理数据转换和验证

import { ImportData, BookmarkInput, CategoryInput, TagInput, ValidationResult } from '../types'

/**
 * Worker消息类型
 */
export enum WorkerMessageType {
  PARSE_JSON = 'PARSE_JSON',
  VALIDATE_DATA = 'VALIDATE_DATA',
  TRANSFORM_DATA = 'TRANSFORM_DATA',
  DETECT_CONFLICTS = 'DETECT_CONFLICTS',
  PROGRESS_UPDATE = 'PROGRESS_UPDATE',
  ERROR = 'ERROR',
  COMPLETE = 'COMPLETE'
}

/**
 * Worker消息接口
 */
export interface WorkerMessage {
  type: WorkerMessageType
  id: string
  data?: any
  progress?: number
  error?: string
}

/**
 * 解析任务配置
 */
export interface ParseTask {
  content: string
  format: 'json' | 'csv' | 'html'
  options?: any
}

/**
 * 验证任务配置
 */
export interface ValidationTask {
  data: ImportData
  strict?: boolean
}

/**
 * 转换任务配置
 */
export interface TransformTask {
  data: any
  targetFormat: string
  options?: any
}

/**
 * 数据处理Worker类
 * 在Web Worker中运行，处理CPU密集型任务
 */
class DataProcessingWorker {
  private taskQueue: Map<string, any> = new Map()

  constructor() {
    // 监听主线程消息
    self.addEventListener('message', this.handleMessage.bind(this))
  }

  /**
   * 处理主线程消息
   * @param event 消息事件
   */
  private async handleMessage(event: MessageEvent<WorkerMessage>): Promise<void> {
    const { type, id, data } = event.data

    try {
      switch (type) {
        case WorkerMessageType.PARSE_JSON:
          await this.handleParseJson(id, data as ParseTask)
          break
          
        case WorkerMessageType.VALIDATE_DATA:
          await this.handleValidateData(id, data as ValidationTask)
          break
          
        case WorkerMessageType.TRANSFORM_DATA:
          await this.handleTransformData(id, data as TransformTask)
          break
          
        case WorkerMessageType.DETECT_CONFLICTS:
          await this.handleDetectConflicts(id, data)
          break
          
        default:
          this.sendError(id, `未知的任务类型: ${type}`)
      }
    } catch (error) {
      this.sendError(id, error instanceof Error ? error.message : '未知错误')
    }
  }

  /**
   * 处理JSON解析任务
   * @param taskId 任务ID
   * @param task 解析任务
   */
  private async handleParseJson(taskId: string, task: ParseTask): Promise<void> {
    this.sendProgress(taskId, 0, '开始解析JSON数据...')
    
    try {
      let parsedData: any
      
      switch (task.format) {
        case 'json':
          parsedData = await this.parseJsonData(task.content, taskId)
          break
          
        case 'csv':
          parsedData = await this.parseCsvData(task.content, taskId)
          break
          
        case 'html':
          parsedData = await this.parseHtmlData(task.content, taskId)
          break
          
        default:
          throw new Error(`不支持的格式: ${task.format}`)
      }
      
      this.sendProgress(taskId, 100, '解析完成')
      this.sendComplete(taskId, parsedData)
      
    } catch (error) {
      this.sendError(taskId, `解析失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  /**
   * 处理数据验证任务
   * @param taskId 任务ID
   * @param task 验证任务
   */
  private async handleValidateData(taskId: string, task: ValidationTask): Promise<void> {
    this.sendProgress(taskId, 0, '开始验证数据...')
    
    try {
      const validationResult = await this.validateImportData(task.data, task.strict || false, taskId)
      
      this.sendProgress(taskId, 100, '验证完成')
      this.sendComplete(taskId, validationResult)
      
    } catch (error) {
      this.sendError(taskId, `验证失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  /**
   * 处理数据转换任务
   * @param taskId 任务ID
   * @param task 转换任务
   */
  private async handleTransformData(taskId: string, task: TransformTask): Promise<void> {
    this.sendProgress(taskId, 0, '开始转换数据...')
    
    try {
      const transformedData = await this.transformData(task.data, task.targetFormat, taskId)
      
      this.sendProgress(taskId, 100, '转换完成')
      this.sendComplete(taskId, transformedData)
      
    } catch (error) {
      this.sendError(taskId, `转换失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  /**
   * 处理冲突检测任务
   * @param taskId 任务ID
   * @param data 检测数据
   */
  private async handleDetectConflicts(taskId: string, data: any): Promise<void> {
    this.sendProgress(taskId, 0, '开始检测冲突...')
    
    try {
      // 这里实现冲突检测逻辑
      const conflicts = await this.detectDataConflicts(data, taskId)
      
      this.sendProgress(taskId, 100, '冲突检测完成')
      this.sendComplete(taskId, conflicts)
      
    } catch (error) {
      this.sendError(taskId, `冲突检测失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  // ==================== 数据处理方法 ====================

  /**
   * 解析JSON数据
   * @param content JSON内容
   * @param taskId 任务ID
   * @returns Promise<ImportData>
   */
  private async parseJsonData(content: string, taskId: string): Promise<ImportData> {
    this.sendProgress(taskId, 20, '解析JSON格式...')
    
    try {
      const data = JSON.parse(content)
      
      this.sendProgress(taskId, 50, '验证数据结构...')
      
      // 检查是否是有效的导入数据格式
      if (!this.isValidImportData(data)) {
        throw new Error('无效的导入数据格式')
      }
      
      this.sendProgress(taskId, 80, '处理数据字段...')
      
      // 标准化数据格式
      const normalizedData = this.normalizeImportData(data)
      
      return normalizedData
      
    } catch (error) {
      if (error instanceof SyntaxError) {
        throw new Error('JSON格式错误，请检查文件内容')
      }
      throw error
    }
  }

  /**
   * 解析CSV数据
   * @param content CSV内容
   * @param taskId 任务ID
   * @returns Promise<ImportData>
   */
  private async parseCsvData(content: string, taskId: string): Promise<ImportData> {
    this.sendProgress(taskId, 20, '解析CSV格式...')
    
    const lines = content.split('\n').filter(line => line.trim())
    if (lines.length < 2) {
      throw new Error('CSV文件格式无效')
    }
    
    const headers = this.parseCsvLine(lines[0])
    const bookmarks: BookmarkInput[] = []
    
    this.sendProgress(taskId, 40, '处理CSV行数据...')
    
    for (let i = 1; i < lines.length; i++) {
      const values = this.parseCsvLine(lines[i])
      if (values.length >= headers.length) {
        const bookmark = this.convertCsvRowToBookmark(headers, values)
        if (bookmark) {
          bookmarks.push(bookmark)
        }
      }
      
      // 更新进度
      if (i % 100 === 0) {
        const progress = 40 + Math.floor((i / lines.length) * 40)
        this.sendProgress(taskId, progress, `处理第 ${i} 行...`)
      }
    }
    
    return {
      version: '1.0',
      exportDate: new Date().toISOString(),
      exportType: 'bookmarks',
      metadata: {
        source: 'CSV Import',
        totalBookmarks: bookmarks.length,
        totalCategories: 0,
        totalTags: 0,
        exportOptions: {}
      },
      bookmarks
    }
  }

  /**
   * 解析HTML数据
   * @param content HTML内容
   * @param taskId 任务ID
   * @returns Promise<ImportData>
   */
  private async parseHtmlData(content: string, taskId: string): Promise<ImportData> {
    this.sendProgress(taskId, 20, '解析HTML格式...')
    
    // 创建临时DOM解析器
    const parser = new DOMParser()
    const doc = parser.parseFromString(content, 'text/html')
    
    this.sendProgress(taskId, 40, '提取链接数据...')
    
    const links = doc.querySelectorAll('a[href]')
    const bookmarks: BookmarkInput[] = []
    
    links.forEach((link, index) => {
      const href = link.getAttribute('href')
      const title = link.textContent?.trim()
      
      if (href && title && href.startsWith('http')) {
        bookmarks.push({
          type: 'url',
          title,
          url: href,
          category: 'HTML导入',
          tags: []
        })
      }
      
      // 更新进度
      if (index % 50 === 0) {
        const progress = 40 + Math.floor((index / links.length) * 40)
        this.sendProgress(taskId, progress, `处理第 ${index} 个链接...`)
      }
    })
    
    return {
      version: '1.0',
      exportDate: new Date().toISOString(),
      exportType: 'bookmarks',
      metadata: {
        source: 'HTML Import',
        totalBookmarks: bookmarks.length,
        totalCategories: 0,
        totalTags: 0,
        exportOptions: {}
      },
      bookmarks
    }
  }

  /**
   * 验证导入数据
   * @param data 导入数据
   * @param strict 严格模式
   * @param taskId 任务ID
   * @returns Promise<ValidationResult>
   */
  private async validateImportData(
    data: ImportData, 
    strict: boolean, 
    taskId: string
  ): Promise<ValidationResult> {
    const errors: any[] = []
    let progress = 0
    
    // 验证基本结构
    this.sendProgress(taskId, 10, '验证基本结构...')
    
    if (!data.version) {
      errors.push({ field: 'version', message: '缺少版本号' })
    }
    
    if (!data.exportDate) {
      errors.push({ field: 'exportDate', message: '缺少导出日期' })
    }
    
    // 验证收藏夹数据
    if (data.bookmarks) {
      this.sendProgress(taskId, 30, '验证收藏夹数据...')
      
      for (let i = 0; i < data.bookmarks.length; i++) {
        const bookmark = data.bookmarks[i]
        
        if (!bookmark.title) {
          errors.push({ field: `bookmarks[${i}].title`, message: '标题不能为空' })
        }
        
        if (bookmark.type === 'url' && !bookmark.url) {
          errors.push({ field: `bookmarks[${i}].url`, message: 'URL类型必须提供URL' })
        }
        
        // 更新进度
        if (i % 100 === 0) {
          progress = 30 + Math.floor((i / data.bookmarks.length) * 30)
          this.sendProgress(taskId, progress, `验证收藏 ${i + 1}/${data.bookmarks.length}`)
        }
      }
    }
    
    // 验证分类数据
    if (data.categories) {
      this.sendProgress(taskId, 70, '验证分类数据...')
      
      data.categories.forEach((category, index) => {
        if (!category.name) {
          errors.push({ field: `categories[${index}].name`, message: '分类名称不能为空' })
        }
      })
    }
    
    // 验证标签数据
    if (data.tags) {
      this.sendProgress(taskId, 90, '验证标签数据...')
      
      data.tags.forEach((tag, index) => {
        if (!tag.name) {
          errors.push({ field: `tags[${index}].name`, message: '标签名称不能为空' })
        }
      })
    }
    
    return {
      isValid: errors.length === 0,
      errors
    }
  }

  /**
   * 转换数据格式
   * @param data 原始数据
   * @param targetFormat 目标格式
   * @param taskId 任务ID
   * @returns Promise<any>
   */
  private async transformData(data: any, targetFormat: string, taskId: string): Promise<any> {
    switch (targetFormat) {
      case 'csv':
        return this.transformToCSV(data, taskId)
      case 'html':
        return this.transformToHTML(data, taskId)
      case 'json':
        return this.transformToJSON(data, taskId)
      default:
        throw new Error(`不支持的目标格式: ${targetFormat}`)
    }
  }

  /**
   * 检测数据冲突
   * @param data 检测数据
   * @param taskId 任务ID
   * @returns Promise<any>
   */
  private async detectDataConflicts(data: any, taskId: string): Promise<any> {
    // 这里实现简化的冲突检测逻辑
    this.sendProgress(taskId, 50, '分析数据冲突...')
    
    // 模拟冲突检测过程
    await this.delay(1000)
    
    return {
      hasConflicts: false,
      conflicts: [],
      summary: {
        bookmarkConflicts: 0,
        categoryConflicts: 0,
        tagConflicts: 0
      }
    }
  }

  // ==================== 工具方法 ====================

  /**
   * 检查是否为有效的导入数据
   * @param data 数据对象
   * @returns boolean
   */
  private isValidImportData(data: any): boolean {
    return data && 
           (data.version || data.bookmarks || data.categories || data.tags) &&
           typeof data === 'object'
  }

  /**
   * 标准化导入数据
   * @param data 原始数据
   * @returns ImportData
   */
  private normalizeImportData(data: any): ImportData {
    return {
      version: data.version || '1.0',
      exportDate: data.exportDate || new Date().toISOString(),
      exportType: data.exportType || 'all',
      metadata: data.metadata || {
        source: 'Unknown',
        totalBookmarks: data.bookmarks?.length || 0,
        totalCategories: data.categories?.length || 0,
        totalTags: data.tags?.length || 0,
        exportOptions: {}
      },
      bookmarks: data.bookmarks,
      categories: data.categories,
      tags: data.tags
    }
  }

  /**
   * 解析CSV行
   * @param line CSV行
   * @returns string[]
   */
  private parseCsvLine(line: string): string[] {
    const result: string[] = []
    let current = ''
    let inQuotes = false
    
    for (let i = 0; i < line.length; i++) {
      const char = line[i]
      
      if (char === '"') {
        inQuotes = !inQuotes
      } else if (char === ',' && !inQuotes) {
        result.push(current.trim())
        current = ''
      } else {
        current += char
      }
    }
    
    result.push(current.trim())
    return result
  }

  /**
   * 转换CSV行为收藏夹
   * @param headers 表头
   * @param values 值
   * @returns BookmarkInput | null
   */
  private convertCsvRowToBookmark(headers: string[], values: string[]): BookmarkInput | null {
    const getValueByHeader = (header: string) => {
      const index = headers.findIndex(h => h.toLowerCase().includes(header.toLowerCase()))
      return index >= 0 ? values[index]?.trim() : ''
    }
    
    const title = getValueByHeader('title') || getValueByHeader('标题')
    const url = getValueByHeader('url') || getValueByHeader('链接')
    
    if (!title && !url) {
      return null
    }
    
    return {
      type: 'url',
      title: title || '无标题',
      url: url,
      description: getValueByHeader('description') || getValueByHeader('描述'),
      category: getValueByHeader('category') || getValueByHeader('分类') || '导入分类',
      tags: (getValueByHeader('tags') || getValueByHeader('标签')).split(',').map(t => t.trim()).filter(Boolean)
    }
  }

  /**
   * 转换为CSV格式
   */
  private async transformToCSV(data: ImportData, taskId: string): Promise<string> {
    this.sendProgress(taskId, 30, '生成CSV格式...')
    
    if (!data.bookmarks) {
      throw new Error('没有收藏夹数据可转换')
    }
    
    const headers = ['标题', 'URL', '描述', '分类', '标签', '创建时间']
    const rows = data.bookmarks.map(bookmark => [
      bookmark.title,
      bookmark.url || '',
      bookmark.description || '',
      bookmark.category || '',
      (bookmark.tags || []).join(','),
      new Date().toISOString()
    ])
    
    const csvContent = [headers, ...rows]
      .map(row => row.map(cell => `"${cell}"`).join(','))
      .join('\n')
    
    return csvContent
  }

  /**
   * 转换为HTML格式
   */
  private async transformToHTML(data: ImportData, taskId: string): Promise<string> {
    this.sendProgress(taskId, 30, '生成HTML格式...')
    
    // 简化的HTML生成
    let html = `<!DOCTYPE html>
<html>
<head>
    <title>导出数据</title>
    <meta charset="UTF-8">
</head>
<body>
    <h1>导出数据</h1>
`
    
    if (data.bookmarks) {
      html += `    <h2>收藏夹 (${data.bookmarks.length})</h2>\n    <ul>\n`
      data.bookmarks.forEach(bookmark => {
        html += `        <li><a href="${bookmark.url || '#'}">${bookmark.title}</a></li>\n`
      })
      html += `    </ul>\n`
    }
    
    html += `</body>\n</html>`
    
    return html
  }

  /**
   * 转换为JSON格式
   */
  private async transformToJSON(data: any, taskId: string): Promise<string> {
    this.sendProgress(taskId, 30, '生成JSON格式...')
    return JSON.stringify(data, null, 2)
  }

  // ==================== 消息发送方法 ====================

  /**
   * 发送进度更新
   * @param taskId 任务ID
   * @param progress 进度百分比
   * @param message 进度消息
   */
  private sendProgress(taskId: string, progress: number, message: string): void {
    self.postMessage({
      type: WorkerMessageType.PROGRESS_UPDATE,
      id: taskId,
      progress,
      data: message
    })
  }

  /**
   * 发送完成消息
   * @param taskId 任务ID
   * @param result 结果数据
   */
  private sendComplete(taskId: string, result: any): void {
    self.postMessage({
      type: WorkerMessageType.COMPLETE,
      id: taskId,
      data: result
    })
  }

  /**
   * 发送错误消息
   * @param taskId 任务ID
   * @param error 错误信息
   */
  private sendError(taskId: string, error: string): void {
    self.postMessage({
      type: WorkerMessageType.ERROR,
      id: taskId,
      error
    })
  }

  /**
   * 延迟执行
   * @param ms 毫秒数
   * @returns Promise<void>
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }
}

// 创建Worker实例
new DataProcessingWorker()