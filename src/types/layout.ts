// 布局和文本处理相关的类型定义

// 文本截断选项
export interface TruncateOptions {
  /** 截断位置 */
  position: 'start' | 'middle' | 'end'
  /** 省略号文本 */
  ellipsis: string
  /** 是否按单词边界截断 */
  wordBoundary: boolean
  /** 是否保留HTML标签 */
  preserveHtml?: boolean
}

// 文本截断结果
export interface TruncateResult {
  /** 截断后的文本 */
  text: string
  /** 是否发生了截断 */
  isTruncated: boolean
  /** 原始文本长度 */
  originalLength: number
  /** 截断后文本长度 */
  truncatedLength: number
}

// 容器尺寸信息
export interface ContainerSize {
  width: number
  height: number
}

// 布局配置
export interface LayoutConfig {
  /** 卡片视图配置 */
  card: {
    columns: number
    showThumbnails: boolean
    showDescriptions: boolean
    cardHeight: 'auto' | 'fixed'
    minCardWidth: number
    gap: number
  }
  
  /** 行视图配置 */
  row: {
    showFavicons: boolean
    showCategories: boolean
    showTags: boolean
    density: 'comfortable' | 'compact'
    height: number
  }
  
  /** 紧凑视图配置 */
  compact: {
    itemsPerRow: number
    showMetadata: boolean
    spacing: 'tight' | 'normal'
    minItemWidth: number
  }
}

// 视图模式类型（扩展现有的ViewMode）
export type ExtendedViewMode = 'card' | 'row' | 'compact'

// 视图模式配置
export interface ViewModeConfig {
  id: ExtendedViewMode
  name: string
  icon: string
  description: string
  defaultConfig: Partial<LayoutConfig>
}

// 响应式断点
export interface ResponsiveBreakpoints {
  xs: number  // 0-575px
  sm: number  // 576-767px
  md: number  // 768-991px
  lg: number  // 992-1199px
  xl: number  // 1200px+
}

// 文本测量结果
export interface TextMeasurement {
  width: number
  height: number
  lines: number
}

// 布局计算结果
export interface LayoutCalculation {
  columns: number
  itemWidth: number
  itemHeight: number
  gap: number
  totalWidth: number
  totalHeight: number
}

// 防抖配置
export interface DebounceConfig {
  delay: number
  immediate?: boolean
  maxWait?: number
}

// 容器观察器配置
export interface ResizeObserverConfig {
  /** 是否启用防抖 */
  debounce: boolean
  /** 防抖延迟时间 */
  debounceDelay: number
  /** 是否立即触发 */
  immediate: boolean
}

// 文本行限制配置
export interface LineClampConfig {
  /** 最大行数 */
  maxLines: number
  /** 行高 */
  lineHeight: number
  /** 是否显示展开按钮 */
  showExpand?: boolean
  /** 展开按钮文本 */
  expandText?: string
  /** 收起按钮文本 */
  collapseText?: string
}

// URL美化选项
export interface UrlBeautifyOptions {
  /** 是否移除协议 */
  removeProtocol: boolean
  /** 是否移除www */
  removeWww: boolean
  /** 是否移除尾部斜杠 */
  removeTrailingSlash: boolean
  /** 最大长度 */
  maxLength?: number
  /** 是否显示路径 */
  showPath: boolean
}