// 数据验证相关类型定义

// 验证错误级别
export type ValidationErrorLevel = 'error' | 'warning' | 'info'

// 数据验证错误
export interface ValidationError {
  field: string
  message: string
  code: string
  level?: ValidationErrorLevel
  value?: any
}

// 数据验证结果
export interface ValidationResult {
  isValid: boolean
  errors: ValidationError[]
  warnings?: ValidationError[]
}

// 批量验证结果
export interface BatchValidationResult {
  totalItems: number
  validItems: number
  invalidItems: number
  results: Array<{
    index: number
    item: any
    validation: ValidationResult
  }>
}

// 验证规则类型
export type ValidationRule = 'required' | 'url' | 'email' | 'length' | 'pattern' | 'custom'

// 验证规则配置
export interface ValidationRuleConfig {
  type: ValidationRule
  message?: string
  params?: Record<string, any>
  validator?: (value: any) => boolean | string
}

// 字段验证配置
export interface FieldValidationConfig {
  field: string
  rules: ValidationRuleConfig[]
  optional?: boolean
}

// 验证模式配置
export interface ValidationSchema {
  name: string
  fields: FieldValidationConfig[]
  customValidators?: Array<(data: any) => ValidationError[]>
}