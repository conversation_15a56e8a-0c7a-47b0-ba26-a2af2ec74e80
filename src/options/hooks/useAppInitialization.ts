// 应用初始化逻辑的自定义Hook
import { useState, useEffect } from 'react'
import { setupGlobalErrorHandler } from '../../utils/errorHandler'

export const useAppInitialization = () => {
  const [loading, setLoading] = useState(true)
  const [initError, setInitError] = useState<string | null>(null)
  const [retryCount, setRetryCount] = useState(0)
  const maxRetries = 3

  // 初始化页面
  const initializePage = async () => {
    try {
      setLoading(true)
      setInitError(null)
      
      // 检查Chrome扩展API是否可用
      if (!chrome || !chrome.runtime) {
        throw new Error('Chrome扩展API不可用，请确保在扩展环境中运行')
      }
      
      // 模拟一些初始化检查
      await new Promise(resolve => setTimeout(resolve, 100))
      
      setLoading(false)
    } catch (error) {
      console.error('初始化页面失败:', error)
      setInitError(error instanceof Error ? error.message : '初始化失败')
      setLoading(false)
    }
  }

  // 重试初始化
  const handleRetry = async () => {
    if (retryCount < maxRetries) {
      setRetryCount(prev => prev + 1)
      await initializePage()
    }
  }

  // 组件挂载时初始化
  useEffect(() => {
    // 设置全局错误处理器
    setupGlobalErrorHandler()
    initializePage()
  }, [])

  return {
    loading,
    initError,
    retryCount,
    maxRetries,
    handleRetry
  }
}