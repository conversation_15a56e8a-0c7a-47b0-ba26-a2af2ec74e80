/**
 * 懒加载 Hook
 * 用于延迟加载组件和内容，提升性能
 */

import { useState, useEffect, useRef, useCallback } from 'react'

interface UseLazyLoadOptions {
  threshold?: number
  rootMargin?: string
  triggerOnce?: boolean
  delay?: number
}

/**
 * 基于 Intersection Observer 的懒加载 Hook
 */
export const useLazyLoad = (options: UseLazyLoadOptions = {}) => {
  const {
    threshold = 0.1,
    rootMargin = '50px',
    triggerOnce = true,
    delay = 0
  } = options

  const [isVisible, setIsVisible] = useState(false)
  const [isLoaded, setIsLoaded] = useState(false)
  const elementRef = useRef<HTMLElement>(null)
  const observerRef = useRef<IntersectionObserver | null>(null)

  const observe = useCallback(() => {
    if (!elementRef.current || !('IntersectionObserver' in window)) {
      // 如果不支持 IntersectionObserver，直接设置为可见
      setIsVisible(true)
      return
    }

    observerRef.current = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            if (delay > 0) {
              setTimeout(() => {
                setIsVisible(true)
              }, delay)
            } else {
              setIsVisible(true)
            }

            if (triggerOnce && observerRef.current) {
              observerRef.current.unobserve(entry.target)
            }
          } else if (!triggerOnce) {
            setIsVisible(false)
          }
        })
      },
      {
        threshold,
        rootMargin
      }
    )

    observerRef.current.observe(elementRef.current)
  }, [threshold, rootMargin, triggerOnce, delay])

  useEffect(() => {
    observe()

    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect()
      }
    }
  }, [observe])

  // 标记为已加载
  const markAsLoaded = useCallback(() => {
    setIsLoaded(true)
  }, [])

  return {
    elementRef,
    isVisible,
    isLoaded,
    markAsLoaded
  }
}

/**
 * 懒加载组件 Hook
 */
export const useLazyComponent = <T = any>(
  importFn: () => Promise<{ default: React.ComponentType<T> }>,
  options: UseLazyLoadOptions = {}
) => {
  const [Component, setComponent] = useState<React.ComponentType<T> | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<Error | null>(null)
  const { isVisible, elementRef } = useLazyLoad(options)

  useEffect(() => {
    if (isVisible && !Component && !loading) {
      setLoading(true)
      setError(null)

      importFn()
        .then((module) => {
          setComponent(() => module.default)
        })
        .catch((err) => {
          setError(err)
          console.error('懒加载组件失败:', err)
        })
        .finally(() => {
          setLoading(false)
        })
    }
  }, [isVisible, Component, loading, importFn])

  return {
    Component,
    loading,
    error,
    elementRef
  }
}

/**
 * 懒加载图片 Hook
 */
export const useLazyImage = (src: string, options: UseLazyLoadOptions = {}) => {
  const [imageSrc, setImageSrc] = useState<string>('')
  const [imageLoaded, setImageLoaded] = useState(false)
  const [imageError, setImageError] = useState(false)
  const { isVisible, elementRef } = useLazyLoad(options)

  useEffect(() => {
    if (isVisible && src && !imageSrc) {
      const img = new Image()
      
      img.onload = () => {
        setImageSrc(src)
        setImageLoaded(true)
      }
      
      img.onerror = () => {
        setImageError(true)
      }
      
      img.src = src
    }
  }, [isVisible, src, imageSrc])

  return {
    elementRef,
    imageSrc,
    imageLoaded,
    imageError,
    isVisible
  }
}

/**
 * 懒加载内容 Hook
 */
export const useLazyContent = (
  loadFn: () => Promise<any>,
  options: UseLazyLoadOptions = {}
) => {
  const [content, setContent] = useState<any>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<Error | null>(null)
  const { isVisible, elementRef } = useLazyLoad(options)

  useEffect(() => {
    if (isVisible && !content && !loading) {
      setLoading(true)
      setError(null)

      loadFn()
        .then((data) => {
          setContent(data)
        })
        .catch((err) => {
          setError(err)
          console.error('懒加载内容失败:', err)
        })
        .finally(() => {
          setLoading(false)
        })
    }
  }, [isVisible, content, loading, loadFn])

  return {
    content,
    loading,
    error,
    elementRef,
    isVisible
  }
}