import React, { Component, ErrorInfo, ReactNode } from 'react'
import { Alert<PERSON>riangle, RefreshCw, Home, Bug } from 'lucide-react'

interface ErrorBoundaryState {
  hasError: boolean
  errorMessage: string
  errorStack?: string
  errorInfo?: ErrorInfo
  retryCount: number
}

interface PageErrorBoundaryProps {
  children: ReactNode
  fallback?: ReactNode
  onError?: (error: Error, errorInfo: ErrorInfo) => void
  maxRetries?: number
}

/**
 * 页面错误边界组件
 * 捕获并处理页面渲染错误，提供错误恢复机制
 */
class PageErrorBoundary extends Component<PageErrorBoundaryProps, ErrorBoundaryState> {
  private retryTimeoutId: NodeJS.Timeout | null = null

  constructor(props: PageErrorBoundaryProps) {
    super(props)
    
    this.state = {
      hasError: false,
      errorMessage: '',
      errorStack: '',
      errorInfo: undefined,
      retryCount: 0
    }
  }

  static getDerivedStateFromError(error: Error): Partial<ErrorBoundaryState> {
    // 更新状态以显示错误UI
    return {
      hasError: true,
      errorMessage: error.message || '发生了未知错误',
      errorStack: error.stack
    }
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    // 记录错误信息
    console.error('页面错误边界捕获到错误:', error, errorInfo)
    
    this.setState({
      errorInfo
    })
    
    // 调用外部错误处理函数
    if (this.props.onError) {
      this.props.onError(error, errorInfo)
    }
    
    // 发送错误报告（如果需要）
    this.reportError(error, errorInfo)
  }

  componentWillUnmount() {
    if (this.retryTimeoutId) {
      clearTimeout(this.retryTimeoutId)
    }
  }

  // 发送错误报告
  private reportError = (error: Error, errorInfo: ErrorInfo) => {
    try {
      // 这里可以集成错误报告服务
      const errorReport = {
        message: error.message,
        stack: error.stack,
        componentStack: errorInfo.componentStack,
        timestamp: new Date().toISOString(),
        userAgent: navigator.userAgent,
        url: window.location.href
      }
      
      console.log('错误报告:', errorReport)
      
      // 可以发送到错误监控服务
      // sendErrorReport(errorReport)
    } catch (reportError) {
      console.error('发送错误报告失败:', reportError)
    }
  }

  // 重试渲染
  private handleRetry = () => {
    const { maxRetries = 3 } = this.props
    const { retryCount } = this.state
    
    if (retryCount < maxRetries) {
      this.setState(prevState => ({
        hasError: false,
        errorMessage: '',
        errorStack: '',
        errorInfo: undefined,
        retryCount: prevState.retryCount + 1
      }))
      
      // 延迟重试，给组件一些时间恢复
      this.retryTimeoutId = setTimeout(() => {
        // 强制重新渲染
        this.forceUpdate()
      }, 100)
    }
  }

  // 重置错误状态
  private handleReset = () => {
    this.setState({
      hasError: false,
      errorMessage: '',
      errorStack: '',
      errorInfo: undefined,
      retryCount: 0
    })
  }

  // 刷新页面
  private handleRefresh = () => {
    window.location.reload()
  }

  // 返回首页
  private handleGoHome = () => {
    window.location.hash = '#bookmarks'
    this.handleReset()
  }

  // 复制错误信息
  private handleCopyError = async () => {
    const { errorMessage, errorStack, errorInfo } = this.state
    
    const errorText = `
错误信息: ${errorMessage}

错误堆栈:
${errorStack}

组件堆栈:
${errorInfo?.componentStack}

时间: ${new Date().toISOString()}
URL: ${window.location.href}
用户代理: ${navigator.userAgent}
    `.trim()
    
    try {
      await navigator.clipboard.writeText(errorText)
      alert('错误信息已复制到剪贴板')
    } catch (error) {
      console.error('复制错误信息失败:', error)
      // 降级到选择文本
      const textArea = document.createElement('textarea')
      textArea.value = errorText
      document.body.appendChild(textArea)
      textArea.select()
      document.execCommand('copy')
      document.body.removeChild(textArea)
      alert('错误信息已复制到剪贴板')
    }
  }

  render() {
    const { hasError, errorMessage, retryCount } = this.state
    const { children, fallback, maxRetries = 3 } = this.props

    if (hasError) {
      // 如果提供了自定义fallback，使用它
      if (fallback) {
        return fallback
      }

      // 默认错误UI
      return (
        <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
          <div className="max-w-lg w-full bg-white rounded-lg shadow-lg border border-gray-200 p-6">
            <div className="text-center">
              {/* 错误图标 */}
              <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <AlertTriangle className="w-8 h-8 text-red-600" />
              </div>
              
              {/* 错误标题 */}
              <h2 className="text-xl font-semibold text-gray-900 mb-2">
                页面出现错误
              </h2>
              
              {/* 错误描述 */}
              <p className="text-gray-600 mb-4">
                抱歉，页面渲染时发生了错误。您可以尝试以下操作来解决问题。
              </p>
              
              {/* 错误信息 */}
              <div className="bg-gray-50 border border-gray-200 rounded-lg p-3 mb-4 text-left">
                <p className="text-sm font-medium text-gray-700 mb-1">错误详情：</p>
                <p className="text-sm text-red-600 font-mono break-all">
                  {errorMessage}
                </p>
              </div>
              
              {/* 重试信息 */}
              {retryCount > 0 && (
                <p className="text-sm text-gray-500 mb-4">
                  已重试 {retryCount} 次，剩余 {maxRetries - retryCount} 次机会
                </p>
              )}
              
              {/* 操作按钮 */}
              <div className="flex flex-col sm:flex-row gap-3 justify-center">
                {/* 重试按钮 */}
                {retryCount < maxRetries && (
                  <button
                    onClick={this.handleRetry}
                    className="flex items-center justify-center space-x-2 px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors"
                  >
                    <RefreshCw className="w-4 h-4" />
                    <span>重试</span>
                  </button>
                )}
                
                {/* 返回首页按钮 */}
                <button
                  onClick={this.handleGoHome}
                  className="flex items-center justify-center space-x-2 px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
                >
                  <Home className="w-4 h-4" />
                  <span>返回首页</span>
                </button>
                
                {/* 刷新页面按钮 */}
                <button
                  onClick={this.handleRefresh}
                  className="flex items-center justify-center space-x-2 px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors"
                >
                  <RefreshCw className="w-4 h-4" />
                  <span>刷新页面</span>
                </button>
              </div>
              
              {/* 高级选项 */}
              <details className="mt-6 text-left">
                <summary className="cursor-pointer text-sm text-gray-500 hover:text-gray-700">
                  高级选项和错误详情
                </summary>
                <div className="mt-3 space-y-3">
                  {/* 复制错误信息 */}
                  <button
                    onClick={this.handleCopyError}
                    className="flex items-center space-x-2 text-sm text-gray-600 hover:text-gray-800"
                  >
                    <Bug className="w-4 h-4" />
                    <span>复制错误信息</span>
                  </button>
                  
                  {/* 错误堆栈 */}
                  {this.state.errorStack && (
                    <div className="bg-gray-100 border border-gray-300 rounded p-3">
                      <p className="text-xs font-medium text-gray-700 mb-2">错误堆栈：</p>
                      <pre className="text-xs text-gray-600 overflow-auto max-h-32 whitespace-pre-wrap">
                        {this.state.errorStack}
                      </pre>
                    </div>
                  )}
                  
                  {/* 组件堆栈 */}
                  {this.state.errorInfo?.componentStack && (
                    <div className="bg-gray-100 border border-gray-300 rounded p-3">
                      <p className="text-xs font-medium text-gray-700 mb-2">组件堆栈：</p>
                      <pre className="text-xs text-gray-600 overflow-auto max-h-32 whitespace-pre-wrap">
                        {this.state.errorInfo.componentStack}
                      </pre>
                    </div>
                  )}
                </div>
              </details>
              
              {/* 联系支持 */}
              <div className="mt-6 pt-4 border-t border-gray-200">
                <p className="text-xs text-gray-500">
                  如果问题持续存在，请联系技术支持：
                  <a 
                    href="mailto:<EMAIL>" 
                    className="text-primary-600 hover:text-primary-800 underline ml-1"
                  >
                    <EMAIL>
                  </a>
                </p>
              </div>
            </div>
          </div>
        </div>
      )
    }

    return children
  }
}

export default PageErrorBoundary