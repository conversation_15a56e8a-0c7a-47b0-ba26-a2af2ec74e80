// 标签页内容渲染器 - 使用静态导入避免动态导入冲突
import React, { Suspense } from 'react'
import PageErrorBoundary from './PageErrorBoundary'
import { Card, CardContent } from '../../components/ui/card'

// 静态导入组件
import BookmarksTab from '../../components/BookmarksTab'
import CategoryManagementTab from '../../components/CategoryManagementTab'
import TagsTab from '../../components/TagsTab'
import ImportExportTab from '../../components/ImportExportTab'
import AIIntegrationTab from '../../components/AIIntegrationTab'
import MCPSettingsTab from '../../components/MCPSettingsTab'
import DefaultAIModelsTab from '../../components/DefaultAIModelsTab'
import NotionSyncTab from '../../components/NotionSyncTab'
import SuperMarketTab from '../../components/SuperMarketTab'
import ObsidianIntegrationTab from '../../components/ObsidianIntegrationTab'
import AboutTab from '../components/AboutTab'
import HelpCenterTab from '../components/HelpCenterTab'
import ShadcnModalTest from '../../components/test/ShadcnModalTest'
import DeleteConfirmModalTestPage from '../../components/test/DeleteConfirmModalTestPage'
import VirtualBookmarkListTestPage from '../../components/test/VirtualBookmarkListTestPage'
import PopupAppTestPage from '../../components/test/PopupAppTestPage'
import BookmarksTabTestPage from '../../components/test/BookmarksTabTestPage'
import LocalAIServiceTestPage from '../../components/test/LocalAIServiceTestPage'

// 同步加载的轻量级组件
import SyncTab from './SyncTab'
import AIAssistantTab from './AIAssistantTab'
import SettingsTab from './SettingsTab'

interface TabContentRendererProps {
  activeTab: string
  onError: (error: Error, errorInfo: React.ErrorInfo) => void
}

// 加载状态组件
const TabLoadingFallback: React.FC = () => (
  <Card className="p-8">
    <CardContent className="text-center">
      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
      <p className="text-muted-foreground">正在加载...</p>
    </CardContent>
  </Card>
)

const TabContentRenderer: React.FC<TabContentRendererProps> = ({ activeTab, onError }) => {
  const renderTabContent = () => {
    const content = (() => {
      switch (activeTab) {
        case 'bookmarks':
          return <BookmarksTab />
        case 'categories':
          return <CategoryManagementTab />
        case 'tags':
          return <TagsTab />
        case 'import-export':
          return <ImportExportTab />
        case 'ai-integration':
          return <AIIntegrationTab />
        case 'mcp-settings':
          return <MCPSettingsTab />
        case 'default-ai-models':
          return <DefaultAIModelsTab />
        case 'sync':
          return <SyncTab />
        case 'notion-sync':
          return <NotionSyncTab />
        case 'super-market':
          return <SuperMarketTab />
        case 'obsidian-integration':
          return <ObsidianIntegrationTab />
        case 'ai-assistant':
          return <AIAssistantTab />
        case 'settings':
          return <SettingsTab />
        case 'shadcn-test':
          return <ShadcnModalTest />
        case 'delete-test':
          return <DeleteConfirmModalTestPage />
        case 'virtualbookmarklist-test':
          return <VirtualBookmarkListTestPage />
        case 'popupapp-test':
          return <PopupAppTestPage />
        case 'bookmarkstab-test':
          return <BookmarksTabTestPage />
        case 'local-ai-test':
          return <LocalAIServiceTestPage />
        case 'about':
          return <AboutTab />
        case 'help':
          return <HelpCenterTab />
        default:
          return <BookmarksTab />
      }
    })()

    // 为每个标签页包装错误边界和懒加载
    return (
      <PageErrorBoundary
        key={activeTab} // 确保切换标签页时重置错误状态
        onError={onError}
        maxRetries={2}
      >
        <Suspense fallback={<TabLoadingFallback />}>
          {content}
        </Suspense>
      </PageErrorBoundary>
    )
  }

  return renderTabContent()
}

export default TabContentRenderer