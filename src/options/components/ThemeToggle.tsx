import React, { useMemo } from 'react'
import { <PERSON>, Moon, Monitor } from 'lucide-react'
import { useTheme, type Theme } from '../hooks/useTheme'

interface ThemeToggleProps {
  className?: string
  showLabel?: boolean
  size?: 'sm' | 'md' | 'lg'
}

/**
 * 主题切换组件
 * 支持浅色、深色和系统主题
 */
const ThemeToggle: React.FC<ThemeToggleProps> = ({ 
  className = '', 
  showLabel = false,
  size = 'md'
}) => {
  const { theme, actualTheme, setTheme } = useTheme()

  // 尺寸配置
  const sizeConfig = {
    sm: {
      button: 'w-8 h-8',
      icon: 'w-4 h-4',
      text: 'text-xs'
    },
    md: {
      button: 'w-10 h-10',
      icon: 'w-5 h-5',
      text: 'text-sm'
    },
    lg: {
      button: 'w-12 h-12',
      icon: 'w-6 h-6',
      text: 'text-base'
    }
  }

  const config = sizeConfig[size]

  // 主题选项
  const themeOptions: Array<{
    value: Theme
    label: string
    icon: React.ComponentType<{ className?: string }>
    description: string
  }> = [
    {
      value: 'light',
      label: '浅色',
      icon: Sun,
      description: '始终使用浅色主题'
    },
    {
      value: 'dark',
      label: '深色',
      icon: Moon,
      description: '始终使用深色主题'
    },
    {
      value: 'system',
      label: '系统',
      icon: Monitor,
      description: '跟随系统主题设置'
    }
  ]

  // 使用useMemo优化性能，避免不必要的重新计算
  const currentOption = useMemo(
    () => themeOptions.find(option => option.value === theme),
    [theme]
  )
  
  const CurrentIcon = useMemo(
    () => currentOption?.icon || Sun,
    [currentOption]
  )

  // 循环切换主题
  const handleToggle = () => {
    const currentIndex = themeOptions.findIndex(option => option.value === theme)
    const nextIndex = (currentIndex + 1) % themeOptions.length
    setTheme(themeOptions[nextIndex].value)
  }

  if (showLabel) {
    return (
      <div className={`flex items-center space-x-2 ${className}`}>
        <span className={`text-foreground ${config.text}`}>
          主题:
        </span>
        <div className="relative">
          <select
            value={theme}
            onChange={(e) => setTheme(e.target.value as Theme)}
            className={`
              appearance-none bg-background border border-border
              text-foreground rounded-lg px-3 py-2 pr-8 ${config.text}
              focus:ring-2 focus:ring-ring focus:border-ring
              hover:bg-accent hover:text-accent-foreground transition-colors
            `}
          >
            {themeOptions.map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
          <div className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
            <CurrentIcon className={`${config.icon} text-muted-foreground`} />
          </div>
        </div>
      </div>
    )
  }

  return (
    <button
      onClick={handleToggle}
      className={`
        ${config.button} ${className}
        flex items-center justify-center rounded-lg
        bg-secondary hover:bg-secondary/80
        text-secondary-foreground
        border border-border
        transition-all duration-200 ease-in-out
        focus:ring-2 focus:ring-ring focus:outline-none
        group
      `}
      title={`当前: ${currentOption?.label} (${actualTheme === 'dark' ? '深色' : '浅色'})`}
      aria-label={`切换主题，当前: ${currentOption?.label}`}
    >
      <CurrentIcon 
        className={`
          ${config.icon} 
          transition-transform duration-200 
          group-hover:scale-110
          ${actualTheme === 'dark' ? 'text-yellow-400' : 'text-orange-500'}
          /* TODO: 考虑使用shadcn颜色变量替换硬编码颜色，如 text-accent-foreground */
        `} 
      />
    </button>
  )
}

export default ThemeToggle