// 同步标签页组件
import React from 'react'
import { RefreshCw } from 'lucide-react'
import { Button } from '../../components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../components/ui/card'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../../components/ui/select'
import { Checkbox } from '../../components/ui/checkbox'

const SyncTab: React.FC = () => {
  return (
    <div className="p-6">
      {/* 头部区域 */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="flex items-center text-2xl">
            <RefreshCw className="w-6 h-6 mr-3 text-primary-600" />
            同步
          </CardTitle>
          <CardDescription className="mt-1">
            管理您的收藏数据同步，确保多设备间数据一致性
          </CardDescription>
        </CardHeader>
      </Card>
      
      <div className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">同步状态</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <label className="text-sm font-medium text-foreground">自动同步</label>
                <p className="text-sm text-muted-foreground">启用后将自动同步您的收藏数据</p>
              </div>
              <Checkbox defaultChecked />
            </div>
            
            <div className="flex items-center justify-between">
              <div>
                <label className="text-sm font-medium text-foreground">同步频率</label>
                <p className="text-sm text-muted-foreground">设置自动同步的时间间隔</p>
              </div>
              <Select defaultValue="15min">
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="选择频率" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="5min">每5分钟</SelectItem>
                  <SelectItem value="15min">每15分钟</SelectItem>
                  <SelectItem value="30min">每30分钟</SelectItem>
                  <SelectItem value="1hour">每小时</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="pt-4">
              <Button className="w-full">
                <RefreshCw className="w-4 h-4 mr-2" />
                立即同步
              </Button>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">同步服务</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="text-center py-8">
              <RefreshCw className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-medium text-foreground mb-2">同步功能开发中</h3>
              <p className="text-muted-foreground">
                我们正在开发强大的同步功能，敬请期待！
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

export default SyncTab