import React, { useState, useMemo, useEffect } from 'react'
import { Book, HelpCircle, Settings, ChevronDown, ChevronRight, ExternalLink, Hash } from 'lucide-react'
import type { HelpContent, HelpSearchResult } from '../data/helpContent'
import { defaultHelpContent } from '../data/helpContent'
import HelpSearchBox from './HelpSearchBox'
import { Button } from '../../components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../components/ui/card'
import { Badge } from '../../components/ui/badge'
import { Alert, AlertDescription } from '../../components/ui/alert'

interface HelpCenterTabProps {
  helpContent?: HelpContent
}

/**
 * 帮助中心页面组件
 * 提供使用指南、FAQ和故障排除信息
 */
const HelpCenterTab: React.FC<HelpCenterTabProps> = ({ helpContent: propHelpContent }) => {
  const [helpContent, setHelpContent] = useState<HelpContent | null>(propHelpContent || null)
  const [loading, setLoading] = useState(!propHelpContent)
  const [searchQuery, setSearchQuery] = useState('')
  const [searchResults, setSearchResults] = useState<HelpSearchResult[]>([])
  const [selectedCategory, setSelectedCategory] = useState<string>('all')
  const [expandedSections, setExpandedSections] = useState<Set<string>>(new Set())
  const [activeAnchor, setActiveAnchor] = useState<string | null>(null)
  const [isSearchMode, setIsSearchMode] = useState(false)

  // 组件挂载时加载帮助内容
  useEffect(() => {
    if (!propHelpContent) {
      const loadHelpContent = async () => {
        try {
          setLoading(true)
          // 模拟异步加载（实际项目中可能从API获取）
          await new Promise(resolve => setTimeout(resolve, 500))
          setHelpContent(defaultHelpContent)
        } catch (error) {
          console.error('加载帮助内容时发生错误:', error)
          
          // 确保即使出错也能显示基本的帮助内容
          const fallbackContent = {
            sections: [
              {
                id: 'error-fallback',
                title: '加载错误',
                content: '抱歉，帮助内容加载失败。请刷新页面重试，或联系技术支持获取帮助。',
                category: 'troubleshooting' as const,
                keywords: ['错误', '加载失败', '支持']
              }
            ],
            categories: [
              {
                id: 'troubleshooting',
                name: '故障排除',
                description: '问题诊断和解决方案',
                icon: 'tool',
                order: 1
              }
            ]
          }
          
          setHelpContent(fallbackContent)
        } finally {
          setLoading(false)
        }
      }
      
      loadHelpContent()
    }
  }, [propHelpContent])

  // 处理URL锚点
  useEffect(() => {
    const hash = window.location.hash.substring(1)
    if (hash && helpContent) {
      const section = helpContent.sections.find(s => s.id === hash)
      if (section) {
        setActiveAnchor(hash)
        setExpandedSections(prev => new Set([...prev, hash]))
        // 滚动到对应位置
        setTimeout(() => {
          const element = document.getElementById(hash)
          if (element) {
            element.scrollIntoView({ behavior: 'smooth' })
          }
        }, 100)
      }
    }
  }, [helpContent])

  const { sections, categories } = helpContent || { sections: [], categories: [] }

  // 获取分类图标
  const getCategoryIcon = (iconName: string) => {
    switch (iconName) {
      case 'book':
        return Book
      case 'help-circle':
        return HelpCircle
      case 'tool':
        return Settings
      default:
        return HelpCircle
    }
  }

  // 处理搜索结果
  const handleSearchResults = (results: HelpSearchResult[]) => {
    setSearchResults(results)
    setIsSearchMode(results.length > 0 || searchQuery.trim().length > 0)
    
    // 如果有搜索结果，自动展开第一个结果
    if (results.length > 0) {
      const firstResultId = results[0].section.id
      setExpandedSections(prev => new Set([...prev, firstResultId]))
    }
  }

  // 处理搜索查询变化
  const handleQueryChange = (query: string) => {
    setSearchQuery(query)
    if (!query.trim()) {
      setIsSearchMode(false)
      setSearchResults([])
    }
  }

  // 筛选和搜索帮助内容
  const filteredSections = useMemo(() => {
    // 如果在搜索模式，返回搜索结果
    if (isSearchMode) {
      return searchResults.map(result => result.section)
    }

    // 否则按分类筛选
    let filtered = sections
    if (selectedCategory !== 'all') {
      filtered = filtered.filter(section => section.category === selectedCategory)
    }

    return filtered
  }, [sections, selectedCategory, isSearchMode, searchResults])

  // 切换内容展开/折叠
  const toggleSection = (sectionId: string) => {
    const newExpanded = new Set(expandedSections)
    if (newExpanded.has(sectionId)) {
      newExpanded.delete(sectionId)
      setActiveAnchor(null)
    } else {
      newExpanded.add(sectionId)
      setActiveAnchor(sectionId)
      // 更新URL锚点
      window.history.replaceState(null, '', `#${sectionId}`)
    }
    setExpandedSections(newExpanded)
  }

  // 跳转到指定章节
  const jumpToSection = (sectionId: string) => {
    setExpandedSections(prev => new Set([...prev, sectionId]))
    setActiveAnchor(sectionId)
    window.history.replaceState(null, '', `#${sectionId}`)
    
    setTimeout(() => {
      const element = document.getElementById(sectionId)
      if (element) {
        element.scrollIntoView({ behavior: 'smooth' })
      }
    }, 100)
  }

  // 展开所有章节
  const expandAll = () => {
    const allSectionIds = new Set(filteredSections.map(s => s.id))
    setExpandedSections(allSectionIds)
  }

  // 折叠所有章节
  const collapseAll = () => {
    setExpandedSections(new Set())
    setActiveAnchor(null)
    window.history.replaceState(null, '', window.location.pathname)
  }

  // 渲染帮助内容
  const renderContent = (content: string) => {
    const lines = content.trim().split('\n')
    const elements: React.ReactNode[] = []
    let listItems: string[] = []
    let inList = false
    
    const flushList = () => {
      if (listItems.length > 0) {
        elements.push(
          <ul key={`list-${elements.length}`} className="list-disc list-inside space-y-1 mb-4 ml-4">
            {listItems.map((item, idx) => (
              <li key={idx} className="text-muted-foreground">
                {renderInlineContent(item)}
              </li>
            ))}
          </ul>
        )
        listItems = []
        inList = false
      }
    }
    
    lines.forEach((line, index) => {
      const trimmedLine = line.trim()
      
      if (trimmedLine.startsWith('# ')) {
        flushList()
        elements.push(
          <h3 key={index} className="text-lg font-semibold text-foreground mt-6 mb-3 border-b pb-2">
            {trimmedLine.substring(2)}
          </h3>
        )
      } else if (trimmedLine.startsWith('## ')) {
        flushList()
        elements.push(
          <h4 key={index} className="text-base font-medium text-foreground mt-4 mb-2">
            {trimmedLine.substring(3)}
          </h4>
        )
      } else if (trimmedLine.startsWith('- ') || trimmedLine.match(/^\d+\. /)) {
        const listContent = trimmedLine.replace(/^[-\d]+\.\s*/, '')
        listItems.push(listContent)
        inList = true
      } else if (trimmedLine.startsWith('**') && trimmedLine.endsWith('**')) {
        flushList()
        elements.push(
          <p key={index} className="font-semibold text-foreground mb-2 mt-3">
            {trimmedLine.slice(2, -2)}
          </p>
        )
      } else if (trimmedLine) {
        flushList()
        elements.push(
          <p key={index} className="text-muted-foreground mb-3 leading-relaxed">
            {renderInlineContent(trimmedLine)}
          </p>
        )
      } else if (!inList) {
        flushList()
        elements.push(<div key={index} className="mb-2" />)
      }
    })
    
    flushList() // 处理最后的列表
    return elements
  }

  // 渲染行内内容（支持粗体、链接等）
  const renderInlineContent = (text: string) => {
    // 如果在搜索模式，高亮搜索关键词
    let processedText = text
    if (isSearchMode && searchQuery.trim()) {
      const regex = new RegExp(`(${searchQuery.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi')
      processedText = text.replace(regex, '<mark class="bg-yellow-200 px-1 rounded">$1</mark>')
    }
    
    // 简单的行内格式处理
    const parts = processedText.split(/(\*\*[^*]+\*\*|`[^`]+`|https?:\/\/[^\s]+|<mark[^>]*>[^<]*<\/mark>)/g)
    
    return parts.map((part, index) => {
      if (part.startsWith('**') && part.endsWith('**')) {
        return (
          <strong key={index} className="font-semibold text-foreground">
            {part.slice(2, -2)}
          </strong>
        )
      } else if (part.startsWith('`') && part.endsWith('`')) {
        return (
          <code key={index} className="bg-muted text-muted-foreground px-1 py-0.5 rounded text-sm font-mono">
            {part.slice(1, -1)}
          </code>
        )
      } else if (part.match(/^https?:\/\//)) {
        return (
          <a 
            key={index}
            href={part}
            target="_blank"
            rel="noopener noreferrer"
            className="text-primary-600 hover:text-primary-800 underline inline-flex items-center"
          >
            {part}
            <ExternalLink className="w-3 h-3 ml-1" />
          </a>
        )
      } else if (part.startsWith('<mark')) {
        return (
          <span 
            key={index}
            dangerouslySetInnerHTML={{ __html: part }}
          />
        )
      }
      return part
    })
  }

  // 如果正在加载，显示加载状态
  if (loading || !helpContent) {
    return (
      <div className="p-6">
        <Card>
          <CardContent className="text-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto mb-4"></div>
            <p className="text-muted-foreground">加载帮助内容中...</p>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="p-6">
      <CardTitle className="text-2xl mb-6">帮助中心</CardTitle>
      
      {/* 搜索和筛选区域 */}
      <div className="mb-6 space-y-4">
        {/* 搜索框和操作按钮 */}
        <div className="flex flex-col sm:flex-row gap-4">
          <HelpSearchBox
            sections={sections}
            onSearchResults={handleSearchResults}
            onQueryChange={handleQueryChange}
            className="flex-1"
          />
          
          {/* 操作按钮 */}
          <div className="flex gap-2">
            <Button
              variant="outline"
              onClick={expandAll}
              size="sm"
            >
              展开全部
            </Button>
            <Button
              variant="outline"
              onClick={collapseAll}
              size="sm"
            >
              折叠全部
            </Button>
          </div>
        </div>

        {/* 分类筛选或搜索结果统计 */}
        {isSearchMode ? (
          <div className="flex items-center justify-between">
            <div className="text-sm text-muted-foreground">
              找到 <Badge variant="secondary">{filteredSections.length}</Badge> 个相关结果
              {searchQuery.trim() && (
                <span className="ml-2">
                  关键词: <Badge variant="outline">"{searchQuery}"</Badge>
                </span>
              )}
            </div>
            <Button
              variant="link"
              onClick={() => {
                setSearchQuery('')
                setIsSearchMode(false)
                setSearchResults([])
              }}
              size="sm"
            >
              清除搜索
            </Button>
          </div>
        ) : (
          <div className="flex flex-wrap gap-2">
            <Button
              variant={selectedCategory === 'all' ? "default" : "outline"}
              onClick={() => setSelectedCategory('all')}
              size="sm"
            >
              全部 ({sections.length})
            </Button>
            {categories.map((category) => {
              const Icon = getCategoryIcon(category.icon)
              const categoryCount = sections.filter(s => s.category === category.id).length
              return (
                <Button
                  key={category.id}
                  variant={selectedCategory === category.id ? "default" : "outline"}
                  onClick={() => setSelectedCategory(category.id)}
                  size="sm"
                  className="flex items-center space-x-2"
                >
                  <Icon className="w-4 h-4" />
                  <span>{category.name} ({categoryCount})</span>
                </Button>
              )
            })}
          </div>
        )}

        {/* 快速导航 */}
        {filteredSections.length > 0 && (
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm flex items-center">
                <Hash className="w-4 h-4 mr-1" />
                快速导航
              </CardTitle>
            </CardHeader>
            <CardContent className="pt-0">
              <div className="flex flex-wrap gap-2">
                {filteredSections.slice(0, 6).map((section) => (
                  <Button
                    key={section.id}
                    variant={activeAnchor === section.id ? "default" : "outline"}
                    onClick={() => jumpToSection(section.id)}
                    size="sm"
                    className="text-xs"
                  >
                    {section.title}
                  </Button>
                ))}
                {filteredSections.length > 6 && (
                  <Badge variant="secondary" className="text-xs">
                    +{filteredSections.length - 6} 更多
                  </Badge>
                )}
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      {/* 帮助内容列表 */}
      <div className="space-y-4">
        {filteredSections.length === 0 ? (
          <Card>
            <CardContent className="text-center py-12 text-muted-foreground">
              <HelpCircle className="w-12 h-12 mx-auto mb-4 text-muted-foreground/50" />
              <CardTitle className="text-lg mb-2">没有找到相关内容</CardTitle>
              <CardDescription>尝试调整搜索关键词或选择不同的分类</CardDescription>
            </CardContent>
          </Card>
        ) : (
          filteredSections.map((section) => {
            const isExpanded = expandedSections.has(section.id)
            const categoryInfo = categories.find(cat => cat.id === section.category)
            const CategoryIcon = categoryInfo ? getCategoryIcon(categoryInfo.icon) : HelpCircle

            return (
              <Card 
                key={section.id} 
                id={section.id}
                className={`transition-all duration-200 ${
                  activeAnchor === section.id 
                    ? 'border-primary-500 shadow-lg' 
                    : ''
                }`}
              >
                <CardHeader 
                  className="cursor-pointer hover:bg-muted/50 transition-colors"
                  onClick={() => toggleSection(section.id)}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <CategoryIcon className="w-5 h-5 text-primary-600" />
                      <div className="flex-1">
                        <CardTitle className="text-base">{section.title}</CardTitle>
                        {categoryInfo && (
                          <CardDescription>{categoryInfo.name}</CardDescription>
                        )}
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation()
                          jumpToSection(section.id)
                        }}
                        title="复制链接"
                      >
                        <Hash className="w-4 h-4" />
                      </Button>
                      {isExpanded ? (
                        <ChevronDown className="w-5 h-5 text-muted-foreground" />
                      ) : (
                        <ChevronRight className="w-5 h-5 text-muted-foreground" />
                      )}
                    </div>
                  </div>
                </CardHeader>
                
                {isExpanded && (
                  <CardContent className="pt-0">
                    {renderContent(section.content)}
                  </CardContent>
                )}
              </Card>
            )
          })
        )}
      </div>

      {/* 联系信息 */}
      <Alert className="mt-8">
        <AlertDescription>
          <div>
            <h3 className="font-medium mb-2">需要更多帮助？</h3>
            <p className="text-sm mb-3">
              如果您没有找到需要的信息，欢迎联系我们的支持团队。
            </p>
            <div className="flex flex-wrap gap-4 text-sm">
              <a 
                href="mailto:<EMAIL>"
                className="text-primary-600 hover:text-primary-800 underline"
              >
                发送邮件
              </a>
              <a 
                href="https://universebag.com"
                target="_blank"
                rel="noopener noreferrer"
                className="text-primary-600 hover:text-primary-800 underline"
              >
                访问官网
              </a>
            </div>
          </div>
        </AlertDescription>
      </Alert>
    </div>
  )
}

export default HelpCenterTab