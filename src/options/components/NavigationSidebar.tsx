// 导航侧边栏组件
import React from 'react'
import { Button } from '../../components/ui/button'
import { Card } from '../../components/ui/card'

interface Tab {
  id: string
  name: string
  icon: React.ComponentType<{ className?: string }>
  isTestPage?: boolean
}

interface NavigationSidebarProps {
  tabs: Tab[]
  activeTab: string
  onTabChange: (tabId: string) => void
  isMobile: boolean
  getResponsiveValue: (values: Record<string, string>, defaultValue: string) => string
}

const NavigationSidebar: React.FC<NavigationSidebarProps> = ({
  tabs,
  activeTab,
  onTabChange,
  isMobile,
  getResponsiveValue
}) => {
  return (
    <nav 
      className={`${
        isMobile 
          ? 'w-full' 
          : getResponsiveValue({ sm: 'w-56', md: 'w-64', lg: 'w-72' }, 'w-64')
      } flex-shrink-0`} 
      aria-label="主导航"
    >
      <Card className="p-4">
        {!isMobile && (
          <div className="mb-3">
            <h2 className="text-sm font-medium text-muted-foreground uppercase tracking-wide">
              功能导航
            </h2>
            <p className="text-xs text-muted-foreground/70 mt-1">
              使用 Alt + 数字键快速切换
            </p>
          </div>
        )}
        <ul 
          className={`${
            isMobile 
              ? 'flex flex-wrap gap-2' 
              : 'space-y-2'
          }`} 
          role="tablist"
        >
          {tabs.map((tab, index) => {
            const Icon = tab.icon
            // 在"关于我们"菜单项之前添加分割线
            const shouldShowDivider = !isMobile && tab.id === 'about'
            
            return (
              <React.Fragment key={tab.id}>
                {shouldShowDivider && (
                  <li className="my-4">
                    <div className="border-t border-border mx-2"></div>
                  </li>
                )}
                <li>
                  <Button
                    variant={activeTab === tab.id ? "default" : "ghost"}
                    onClick={() => onTabChange(tab.id)}
                    className={`
                      ${isMobile ? 'flex-1 min-w-0' : 'w-full'} 
                      flex items-center ${isMobile ? 'justify-center' : 'space-x-3'} 
                      px-3 py-2 text-left justify-start
                      ${activeTab === tab.id ? 'bg-primary text-primary-foreground' : ''}
                    `}
                    aria-current={activeTab === tab.id ? 'page' : undefined}
                    aria-selected={activeTab === tab.id}
                    role="tab"
                    tabIndex={0}
                    title={`${tab.name} ${!isMobile ? `(Alt+${index + 1})` : ''}`}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter' || e.key === ' ') {
                        e.preventDefault()
                        onTabChange(tab.id)
                      } else if (!isMobile) {
                        if (e.key === 'ArrowDown') {
                          e.preventDefault()
                          const nextIndex = (index + 1) % tabs.length
                          const nextButton = document.querySelector(`[role="tab"][title*="Alt+${nextIndex + 1}"]`) as HTMLButtonElement
                          nextButton?.focus()
                        } else if (e.key === 'ArrowUp') {
                          e.preventDefault()
                          const prevIndex = index === 0 ? tabs.length - 1 : index - 1
                          const prevButton = document.querySelector(`[role="tab"][title*="Alt+${prevIndex + 1}"]`) as HTMLButtonElement
                          prevButton?.focus()
                        }
                      }
                    }}
                  >
                    <Icon className="w-5 h-5" aria-hidden="true" />
                    {!isMobile && (
                      <>
                        <span className="font-medium">{tab.name}</span>
                        <span className="text-xs text-muted-foreground ml-auto">
                          Alt+{index + 1}
                        </span>
                      </>
                    )}
                    {isMobile && (
                      <span className="sr-only">{tab.name}</span>
                    )}
                  </Button>
                </li>
              </React.Fragment>
            )
          })}
        </ul>
      </Card>
    </nav>
  )
}

export default NavigationSidebar