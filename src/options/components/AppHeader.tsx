// 应用头部组件
import React from 'react'
import { Star, Zap } from 'lucide-react'
import { Button } from '../../components/ui/button'
import { Card, CardContent } from '../../components/ui/card'
import ThemeToggle from './ThemeToggle'

interface AppHeaderProps {
  isMobile: boolean
}

const AppHeader: React.FC<AppHeaderProps> = ({ isMobile }) => {
  return (
    <Card className="rounded-none border-x-0 border-t-0">
      <CardContent className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-0">
        <div className="flex items-center justify-between h-16">
          <div className="flex items-center space-x-3">
            <Star className="w-8 h-8 text-primary" />
            <div>
              <h1 className="text-xl font-bold text-foreground">Universe Bag</h1>
              <p className="text-sm text-muted-foreground">智能收藏管理工具</p>
            </div>
          </div>
          <div className="flex items-center space-x-4">
            {/* 主题切换 */}
            <ThemeToggle size="md" />
            
            {/* AI 辅助按钮 - 在移动端隐藏文字 */}
            <Button className="flex items-center space-x-2">
              <Zap className="w-4 h-4" />
              {!isMobile && <span>AI 辅助</span>}
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

export default AppHeader