import React, { useState, useEffect, useRef } from 'react'
import { Search, X, Clock } from 'lucide-react'
import { searchHelpContent, getSearchSuggestions, debounce } from '../utils/helpSearch'
import { useSearchCache } from '../hooks/useCache'
import type { HelpSection, HelpSearchResult } from '../data/helpContent'

interface HelpSearchBoxProps {
  sections: HelpSection[]
  onSearchResults: (results: HelpSearchResult[]) => void
  onQueryChange: (query: string) => void
  placeholder?: string
  className?: string
}

/**
 * 帮助内容搜索框组件
 * 提供实时搜索、搜索建议和历史记录功能
 */
const HelpSearchBox: React.FC<HelpSearchBoxProps> = ({
  sections,
  onSearchResults,
  onQueryChange,
  placeholder = '搜索帮助内容...',
  className = ''
}) => {
  const [query, setQuery] = useState('')
  const [suggestions, setSuggestions] = useState<string[]>([])
  const [searchHistory, setSearchHistory] = useState<string[]>([])
  const [showSuggestions, setShowSuggestions] = useState(false)
  const [isSearching, setIsSearching] = useState(false)
  const [searchTime, setSearchTime] = useState(0)
  
  const inputRef = useRef<HTMLInputElement>(null)
  const suggestionsRef = useRef<HTMLDivElement>(null)

  // 搜索缓存
  const { getCachedResults, setCachedResults } = useSearchCache()

  // 从本地存储加载搜索历史
  useEffect(() => {
    try {
      const savedHistory = localStorage.getItem('help-search-history')
      if (savedHistory) {
        setSearchHistory(JSON.parse(savedHistory))
      }
    } catch (error) {
      console.error('加载搜索历史失败:', error)
    }
  }, [])

  // 防抖搜索函数
  const debouncedSearch = debounce((searchQuery: string) => {
    if (!searchQuery.trim()) {
      onSearchResults([])
      setSuggestions([])
      setIsSearching(false)
      return
    }

    // 先检查缓存
    const cachedResults = getCachedResults(searchQuery)
    if (cachedResults) {
      onSearchResults(cachedResults.results)
      setSuggestions(cachedResults.suggestions)
      setSearchTime(cachedResults.searchTime)
      setIsSearching(false)
      return
    }

    setIsSearching(true)
    const startTime = performance.now()
    
    try {
      // 执行搜索
      const results = searchHelpContent(sections, searchQuery, {
        fuzzySearch: true,
        maxResults: 20,
        weights: {
          title: 3,
          content: 1,
          keywords: 2
        }
      })
      
      // 获取搜索建议
      const searchSuggestions = getSearchSuggestions(sections, searchQuery, 5)
      
      const endTime = performance.now()
      const searchDuration = endTime - startTime
      setSearchTime(searchDuration)
      
      // 缓存搜索结果
      setCachedResults(searchQuery, {
        results,
        suggestions: searchSuggestions,
        searchTime: searchDuration
      })
      
      onSearchResults(results)
      setSuggestions(searchSuggestions)
    } catch (error) {
      console.error('搜索时发生错误:', error)
      onSearchResults([])
      setSuggestions([])
    } finally {
      setIsSearching(false)
    }
  }, 300)

  // 处理输入变化
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newQuery = e.target.value
    setQuery(newQuery)
    onQueryChange(newQuery)
    
    if (newQuery.trim()) {
      setShowSuggestions(true)
      debouncedSearch(newQuery)
    } else {
      setShowSuggestions(false)
      onSearchResults([])
      setSuggestions([])
    }
  }

  // 处理搜索提交
  const handleSearch = (searchQuery: string = query) => {
    if (!searchQuery.trim()) return
    
    // 添加到搜索历史
    const newHistory = [searchQuery, ...searchHistory.filter(h => h !== searchQuery)].slice(0, 10)
    setSearchHistory(newHistory)
    
    try {
      localStorage.setItem('help-search-history', JSON.stringify(newHistory))
    } catch (error) {
      console.error('保存搜索历史失败:', error)
    }
    
    setShowSuggestions(false)
    debouncedSearch(searchQuery)
  }

  // 处理建议点击
  const handleSuggestionClick = (suggestion: string) => {
    setQuery(suggestion)
    onQueryChange(suggestion)
    handleSearch(suggestion)
  }

  // 清除搜索
  const clearSearch = () => {
    setQuery('')
    onQueryChange('')
    onSearchResults([])
    setSuggestions([])
    setShowSuggestions(false)
    inputRef.current?.focus()
  }

  // 处理键盘事件
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSearch()
    } else if (e.key === 'Escape') {
      setShowSuggestions(false)
    }
  }

  // 点击外部关闭建议
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        suggestionsRef.current && 
        !suggestionsRef.current.contains(event.target as Node) &&
        !inputRef.current?.contains(event.target as Node)
      ) {
        setShowSuggestions(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  // 合并建议和历史记录
  const displaySuggestions = query.trim() 
    ? suggestions 
    : searchHistory.slice(0, 5)

  return (
    <div className={`relative ${className}`}>
      {/* 搜索输入框 */}
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
        <input
          ref={inputRef}
          type="text"
          placeholder={placeholder}
          className="w-full pl-10 pr-10 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors"
          value={query}
          onChange={handleInputChange}
          onKeyDown={handleKeyDown}
          onFocus={() => setShowSuggestions(true)}
        />
        
        {/* 右侧图标 */}
        <div className="absolute right-3 top-1/2 transform -translate-y-1/2 flex items-center space-x-1">
          {isSearching && (
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary-600"></div>
          )}
          {query && (
            <button
              onClick={clearSearch}
              className="text-gray-400 hover:text-gray-600 transition-colors"
              title="清除搜索"
            >
              <X className="w-4 h-4" />
            </button>
          )}
        </div>
      </div>

      {/* 搜索建议下拉框 */}
      {showSuggestions && displaySuggestions.length > 0 && (
        <div 
          ref={suggestionsRef}
          className="absolute top-full left-0 right-0 mt-1 bg-white border border-gray-300 rounded-lg shadow-lg z-10 max-h-64 overflow-y-auto"
        >
          {/* 建议标题 */}
          <div className="px-3 py-2 text-xs font-medium text-gray-500 bg-gray-50 border-b border-gray-200">
            {query.trim() ? '搜索建议' : '搜索历史'}
          </div>
          
          {/* 建议列表 */}
          {displaySuggestions.map((suggestion, index) => (
            <button
              key={index}
              className="w-full text-left px-3 py-2 hover:bg-gray-50 text-sm border-b border-gray-100 last:border-b-0 flex items-center space-x-2"
              onClick={() => handleSuggestionClick(suggestion)}
            >
              {!query.trim() && <Clock className="w-3 h-3 text-gray-400" />}
              <span>{suggestion}</span>
            </button>
          ))}
          
          {/* 清除历史记录 */}
          {!query.trim() && searchHistory.length > 0 && (
            <div className="border-t border-gray-200">
              <button
                onClick={() => {
                  setSearchHistory([])
                  localStorage.removeItem('help-search-history')
                  setShowSuggestions(false)
                }}
                className="w-full text-left px-3 py-2 text-xs text-gray-500 hover:bg-gray-50"
              >
                清除搜索历史
              </button>
            </div>
          )}
        </div>
      )}

      {/* 搜索统计信息 */}
      {query.trim() && searchTime > 0 && (
        <div className="mt-2 text-xs text-gray-500">
          搜索耗时: {searchTime.toFixed(1)}ms
        </div>
      )}
    </div>
  )
}

export default HelpSearchBox