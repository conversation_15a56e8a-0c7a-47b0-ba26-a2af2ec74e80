// 标签页配置常量
import { 
  Bookmark, 
  Folder, 
  Tag, 
  Globe, 
  Database, 
  FileText, 
  Bot, 
  RefreshCw, 
  Download, 
  Settings, 
  Zap, 
  Trash2, 
  Star, 
  Info, 
  HelpCircle 
} from 'lucide-react'

export interface TabConfig {
  id: string
  name: string
  icon: React.ComponentType<{ className?: string }>
  isTestPage?: boolean
}

// 开发模式配置 - 控制测试页面的显示
export const DEV_CONFIG = {
  // 设置为 true 显示测试页面，false 隐藏测试页面
  showTestPages: true,
  // 可以通过环境变量或URL参数启用
  enableTestPagesViaUrl: true
}

// 检查是否应该显示测试页面
export const shouldShowTestPages = (): boolean => {
  if (DEV_CONFIG.showTestPages) return true
  
  // 通过URL参数启用测试页面: ?dev=true 或 ?showTests=true
  if (DEV_CONFIG.enableTestPagesViaUrl) {
    const urlParams = new URLSearchParams(window.location.search)
    return urlParams.get('dev') === 'true' || urlParams.get('showTests') === 'true'
  }
  
  return false
}

// 所有标签页配置
export const ALL_TABS: TabConfig[] = [
  { id: 'bookmarks', name: '收藏管理', icon: Bookmark },
  { id: 'categories', name: '分类管理', icon: Folder },
  { id: 'tags', name: '标签管理', icon: Tag },
  { id: 'super-market', name: '超级市场', icon: Globe },
  { id: 'notion-sync', name: 'Notion集成', icon: Database },
  { id: 'obsidian-integration', name: 'Obsidian集成', icon: FileText },
  { id: 'ai-assistant', name: 'AI辅助', icon: Bot },
  { id: 'sync', name: '同步', icon: RefreshCw },
  { id: 'import-export', name: '导入导出', icon: Download },
  { id: 'ai-integration', name: 'AI集成', icon: Bot },
  { id: 'mcp-settings', name: 'MCP设置', icon: Settings },
  { id: 'default-ai-models', name: '默认AI模型', icon: Zap },
  { id: 'settings', name: '设置', icon: Settings },
  // 测试页面 - 仅在开发模式下显示
  { id: 'shadcn-test', name: 'shadcn测试', icon: Zap, isTestPage: true },
  { id: 'delete-test', name: '删除测试', icon: Trash2, isTestPage: true },
  { id: 'virtualbookmarklist-test', name: 'VirtualBookmarkList测试', icon: Bookmark, isTestPage: true },
  { id: 'popupapp-test', name: 'PopupApp测试', icon: Star, isTestPage: true },
  { id: 'bookmarkstab-test', name: 'BookmarksTab测试', icon: Bookmark, isTestPage: true },
  { id: 'local-ai-test', name: '本地AI服务测试', icon: Bot, isTestPage: true },
  // 信息页面
  { id: 'about', name: '关于我们', icon: Info },
  { id: 'help', name: '帮助中心', icon: HelpCircle },
]

// 获取过滤后的标签页列表
export const getFilteredTabs = (): TabConfig[] => {
  return ALL_TABS.filter(tab => {
    if (tab.isTestPage) {
      return shouldShowTestPages()
    }
    return true
  })
}