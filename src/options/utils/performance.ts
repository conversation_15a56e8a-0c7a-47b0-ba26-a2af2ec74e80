/**
 * 性能监控工具
 * 用于监控和优化应用性能
 */

interface PerformanceMetric {
  name: string
  value: number
  timestamp: number
  type: 'timing' | 'counter' | 'gauge'
  tags?: Record<string, string>
}

interface PerformanceConfig {
  enableLogging?: boolean
  enableReporting?: boolean
  sampleRate?: number
  maxMetrics?: number
}

/**
 * 性能监控类
 */
class PerformanceMonitor {
  private metrics: PerformanceMetric[] = []
  private config: Required<PerformanceConfig>
  private timers = new Map<string, number>()

  constructor(config: PerformanceConfig = {}) {
    this.config = {
      enableLogging: config.enableLogging ?? true,
      enableReporting: config.enableReporting ?? false,
      sampleRate: config.sampleRate ?? 1.0,
      maxMetrics: config.maxMetrics ?? 1000
    }
  }

  /**
   * 记录性能指标
   */
  record(name: string, value: number, type: PerformanceMetric['type'] = 'gauge', tags?: Record<string, string>) {
    // 采样控制
    if (Math.random() > this.config.sampleRate) {
      return
    }

    const metric: PerformanceMetric = {
      name,
      value,
      timestamp: Date.now(),
      type,
      tags
    }

    this.metrics.push(metric)

    // 限制指标数量
    if (this.metrics.length > this.config.maxMetrics) {
      this.metrics.shift()
    }

    if (this.config.enableLogging) {
      console.log(`[Performance] ${name}: ${value}${type === 'timing' ? 'ms' : ''}`, tags)
    }

    if (this.config.enableReporting) {
      this.reportMetric(metric)
    }
  }

  /**
   * 开始计时
   */
  startTimer(name: string) {
    this.timers.set(name, performance.now())
  }

  /**
   * 结束计时并记录
   */
  endTimer(name: string, tags?: Record<string, string>) {
    const startTime = this.timers.get(name)
    if (startTime !== undefined) {
      const duration = performance.now() - startTime
      this.record(name, duration, 'timing', tags)
      this.timers.delete(name)
      return duration
    }
    return 0
  }

  /**
   * 计数器递增
   */
  increment(name: string, value = 1, tags?: Record<string, string>) {
    this.record(name, value, 'counter', tags)
  }

  /**
   * 测量函数执行时间
   */
  async measure<T>(name: string, fn: () => T | Promise<T>, tags?: Record<string, string>): Promise<T> {
    this.startTimer(name)
    try {
      const result = await fn()
      this.endTimer(name, tags)
      return result
    } catch (error) {
      this.endTimer(name, { ...tags, error: 'true' })
      throw error
    }
  }

  /**
   * 获取指标统计
   */
  getStats(name?: string) {
    const filteredMetrics = name 
      ? this.metrics.filter(m => m.name === name)
      : this.metrics

    if (filteredMetrics.length === 0) {
      return null
    }

    const values = filteredMetrics.map(m => m.value)
    const sum = values.reduce((a, b) => a + b, 0)
    const avg = sum / values.length
    const min = Math.min(...values)
    const max = Math.max(...values)

    // 计算百分位数
    const sorted = [...values].sort((a, b) => a - b)
    const p50 = sorted[Math.floor(sorted.length * 0.5)]
    const p90 = sorted[Math.floor(sorted.length * 0.9)]
    const p95 = sorted[Math.floor(sorted.length * 0.95)]
    const p99 = sorted[Math.floor(sorted.length * 0.99)]

    return {
      count: filteredMetrics.length,
      sum,
      avg,
      min,
      max,
      p50,
      p90,
      p95,
      p99
    }
  }

  /**
   * 获取所有指标
   */
  getAllMetrics() {
    return [...this.metrics]
  }

  /**
   * 清除指标
   */
  clear() {
    this.metrics.length = 0
    this.timers.clear()
  }

  /**
   * 报告指标（可扩展到外部服务）
   */
  private reportMetric(metric: PerformanceMetric) {
    // 这里可以集成外部监控服务
    // 例如：发送到 Google Analytics、Sentry 等
    console.debug('[Performance Report]', metric)
  }
}

// 全局性能监控实例
export const performanceMonitor = new PerformanceMonitor({
  enableLogging: process.env.NODE_ENV === 'development',
  enableReporting: process.env.NODE_ENV === 'production',
  sampleRate: 0.1 // 10% 采样率
})

/**
 * 性能监控装饰器
 */
export function measurePerformance(name?: string) {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value
    const metricName = name || `${target.constructor.name}.${propertyKey}`

    descriptor.value = async function (...args: any[]) {
      return performanceMonitor.measure(metricName, () => originalMethod.apply(this, args))
    }

    return descriptor
  }
}

/**
 * React 组件性能监控 Hook
 */
export const usePerformanceMonitor = (componentName: string) => {
  const renderStartTime = performance.now()

  // 注意：这里需要在使用时导入 React
  // React.useEffect(() => {
  //   const renderTime = performance.now() - renderStartTime
  //   performanceMonitor.record(`${componentName}.render`, renderTime, 'timing')
  // })

  // const measureAction = React.useCallback((actionName: string, fn: () => void | Promise<void>) => {
  //   return performanceMonitor.measure(`${componentName}.${actionName}`, fn)
  // }, [componentName])

  // const recordMetric = React.useCallback((metricName: string, value: number, type: PerformanceMetric['type'] = 'gauge') => {
  //   performanceMonitor.record(`${componentName}.${metricName}`, value, type)
  // }, [componentName])

  return {
    measureAction: (actionName: string, fn: () => void | Promise<void>) => {
      return performanceMonitor.measure(`${componentName}.${actionName}`, fn)
    },
    recordMetric: (metricName: string, value: number, type: PerformanceMetric['type'] = 'gauge') => {
      performanceMonitor.record(`${componentName}.${metricName}`, value, type)
    }
  }
}

/**
 * Web Vitals 监控
 */
export const initWebVitalsMonitoring = () => {
  // 监控 Largest Contentful Paint (LCP)
  if ('PerformanceObserver' in window) {
    try {
      const observer = new PerformanceObserver((list) => {
        const entries = list.getEntries()
        const lastEntry = entries[entries.length - 1]
        performanceMonitor.record('web_vitals.lcp', lastEntry.startTime, 'timing')
      })
      observer.observe({ entryTypes: ['largest-contentful-paint'] })
    } catch (error) {
      console.warn('LCP monitoring failed:', error)
    }

    // 监控 First Input Delay (FID)
    try {
      const observer = new PerformanceObserver((list) => {
        const entries = list.getEntries()
        entries.forEach((entry: any) => {
          performanceMonitor.record('web_vitals.fid', entry.processingStart - entry.startTime, 'timing')
        })
      })
      observer.observe({ entryTypes: ['first-input'] })
    } catch (error) {
      console.warn('FID monitoring failed:', error)
    }

    // 监控 Cumulative Layout Shift (CLS)
    try {
      let clsValue = 0
      const observer = new PerformanceObserver((list) => {
        const entries = list.getEntries()
        entries.forEach((entry: any) => {
          if (!entry.hadRecentInput) {
            clsValue += entry.value
          }
        })
        performanceMonitor.record('web_vitals.cls', clsValue, 'gauge')
      })
      observer.observe({ entryTypes: ['layout-shift'] })
    } catch (error) {
      console.warn('CLS monitoring failed:', error)
    }
  }

  // 监控页面加载时间
  window.addEventListener('load', () => {
    const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming
    if (navigation) {
      performanceMonitor.record('page.load_time', navigation.loadEventEnd - navigation.fetchStart, 'timing')
      performanceMonitor.record('page.dom_content_loaded', navigation.domContentLoadedEventEnd - navigation.fetchStart, 'timing')
      performanceMonitor.record('page.first_paint', navigation.responseEnd - navigation.fetchStart, 'timing')
    }
  })
}

/**
 * 内存使用监控
 */
export const monitorMemoryUsage = () => {
  if ('memory' in performance) {
    const memory = (performance as any).memory
    performanceMonitor.record('memory.used', memory.usedJSHeapSize, 'gauge')
    performanceMonitor.record('memory.total', memory.totalJSHeapSize, 'gauge')
    performanceMonitor.record('memory.limit', memory.jsHeapSizeLimit, 'gauge')
  }
}

/**
 * 定期监控系统性能
 */
export const startPerformanceMonitoring = (interval = 30000) => {
  const monitor = () => {
    monitorMemoryUsage()
    
    // 监控 FPS
    let lastTime = performance.now()
    let frames = 0
    
    const countFPS = () => {
      frames++
      const currentTime = performance.now()
      if (currentTime >= lastTime + 1000) {
        performanceMonitor.record('performance.fps', frames, 'gauge')
        frames = 0
        lastTime = currentTime
      }
      requestAnimationFrame(countFPS)
    }
    requestAnimationFrame(countFPS)
  }

  monitor()
  return setInterval(monitor, interval)
}

// 在开发环境中自动启动监控
if (process.env.NODE_ENV === 'development') {
  initWebVitalsMonitoring()
  startPerformanceMonitoring()
}