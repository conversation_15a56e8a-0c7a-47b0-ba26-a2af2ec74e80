/**
 * 帮助内容搜索工具
 * 提供本地搜索功能和搜索结果排序
 */

import type { HelpSection, HelpSearchResult } from '../data/helpContent'

/**
 * 搜索配置接口
 */
interface SearchConfig {
  caseSensitive?: boolean
  fuzzySearch?: boolean
  maxResults?: number
  weights?: {
    title: number
    content: number
    keywords: number
  }
}

/**
 * 默认搜索配置
 */
const defaultSearchConfig: Required<SearchConfig> = {
  caseSensitive: false,
  fuzzySearch: true,
  maxResults: 20,
  weights: {
    title: 3,
    content: 1,
    keywords: 2
  }
}

/**
 * 计算字符串相似度（简单的编辑距离算法）
 */
const calculateSimilarity = (str1: string, str2: string): number => {
  const len1 = str1.length
  const len2 = str2.length
  
  if (len1 === 0) return len2
  if (len2 === 0) return len1
  
  const matrix = Array(len1 + 1).fill(null).map(() => Array(len2 + 1).fill(null))
  
  for (let i = 0; i <= len1; i++) matrix[i][0] = i
  for (let j = 0; j <= len2; j++) matrix[0][j] = j
  
  for (let i = 1; i <= len1; i++) {
    for (let j = 1; j <= len2; j++) {
      const cost = str1[i - 1] === str2[j - 1] ? 0 : 1
      matrix[i][j] = Math.min(
        matrix[i - 1][j] + 1,
        matrix[i][j - 1] + 1,
        matrix[i - 1][j - 1] + cost
      )
    }
  }
  
  const maxLen = Math.max(len1, len2)
  return (maxLen - matrix[len1][len2]) / maxLen
}

/**
 * 检查文本是否匹配查询
 */
const isTextMatch = (text: string, query: string, config: Required<SearchConfig>): boolean => {
  const searchText = config.caseSensitive ? text : text.toLowerCase()
  const searchQuery = config.caseSensitive ? query : query.toLowerCase()
  
  if (config.fuzzySearch) {
    // 模糊搜索：检查相似度或包含关系
    return searchText.includes(searchQuery) || calculateSimilarity(searchText, searchQuery) > 0.6
  } else {
    // 精确搜索：只检查包含关系
    return searchText.includes(searchQuery)
  }
}

/**
 * 计算搜索相关性得分
 */
const calculateRelevance = (
  section: HelpSection, 
  query: string, 
  config: Required<SearchConfig>
): { score: number; matchedKeywords: string[] } => {
  let score = 0
  const matchedKeywords: string[] = []
  
  // 标题匹配
  if (isTextMatch(section.title, query, config)) {
    score += config.weights.title
    matchedKeywords.push('title')
  }
  
  // 内容匹配
  if (isTextMatch(section.content, query, config)) {
    score += config.weights.content
    matchedKeywords.push('content')
  }
  
  // 关键词匹配
  section.keywords.forEach(keyword => {
    if (isTextMatch(keyword, query, config)) {
      score += config.weights.keywords
      matchedKeywords.push(keyword)
    }
  })
  
  return { score, matchedKeywords }
}

/**
 * 搜索帮助内容
 */
export const searchHelpContent = (
  sections: HelpSection[],
  query: string,
  config: Partial<SearchConfig> = {}
): HelpSearchResult[] => {
  if (!query.trim()) {
    return []
  }
  
  const searchConfig = { ...defaultSearchConfig, ...config }
  const results: HelpSearchResult[] = []
  
  sections.forEach(section => {
    const { score, matchedKeywords } = calculateRelevance(section, query, searchConfig)
    
    if (score > 0) {
      results.push({
        section,
        relevance: score,
        matchedKeywords
      })
    }
  })
  
  // 按相关性排序并限制结果数量
  return results
    .sort((a, b) => b.relevance - a.relevance)
    .slice(0, searchConfig.maxResults)
}

/**
 * 高亮搜索关键词
 */
export const highlightSearchTerms = (text: string, query: string): string => {
  if (!query.trim()) return text
  
  const regex = new RegExp(`(${query.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi')
  return text.replace(regex, '<mark class="bg-yellow-200">$1</mark>')
}

/**
 * 获取搜索建议
 */
export const getSearchSuggestions = (
  sections: HelpSection[],
  query: string,
  maxSuggestions: number = 5
): string[] => {
  if (!query.trim()) return []
  
  const suggestions = new Set<string>()
  const queryLower = query.toLowerCase()
  
  sections.forEach(section => {
    // 从标题中提取建议
    if (section.title.toLowerCase().includes(queryLower)) {
      suggestions.add(section.title)
    }
    
    // 从关键词中提取建议
    section.keywords.forEach(keyword => {
      if (keyword.toLowerCase().includes(queryLower)) {
        suggestions.add(keyword)
      }
    })
  })
  
  return Array.from(suggestions).slice(0, maxSuggestions)
}

/**
 * 防抖函数
 */
export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  delay: number
): ((...args: Parameters<T>) => void) => {
  let timeoutId: NodeJS.Timeout
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeoutId)
    timeoutId = setTimeout(() => func(...args), delay)
  }
}