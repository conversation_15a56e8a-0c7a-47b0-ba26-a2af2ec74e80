/**
 * manifest.json 读取工具
 * 用于从扩展的 manifest.json 文件中获取扩展信息
 */

import type { ExtensionInfo } from '../data/aboutInfo'

interface ManifestData {
  name: string
  version: string
  description: string
  manifest_version: number
  permissions?: string[]
  author?: string
}

/**
 * 从 Chrome 扩展 API 获取 manifest 信息
 */
export const getManifestFromChrome = (): ManifestData | null => {
  try {
    // 检查 Chrome 扩展 API 是否可用
    if (typeof chrome === 'undefined') {
      console.warn('Chrome API 不可用，可能不在扩展环境中运行')
      return null
    }

    if (!chrome.runtime) {
      console.warn('chrome.runtime 不可用')
      return null
    }

    if (!chrome.runtime.getManifest) {
      console.warn('chrome.runtime.getManifest 不可用')
      return null
    }

    const manifest = chrome.runtime.getManifest() as ManifestData
    
    // 验证 manifest 数据的完整性
    if (!validateManifestData(manifest)) {
      console.error('获取到的 manifest 数据无效或不完整')
      return null
    }

    console.log('成功获取 manifest 信息:', {
      name: manifest.name,
      version: manifest.version,
      manifest_version: manifest.manifest_version
    })

    return manifest
  } catch (error) {
    console.error('获取 manifest 信息时发生异常:', error)
    return null
  }
}

/**
 * 将 manifest 数据转换为 ExtensionInfo 格式
 */
export const convertManifestToExtensionInfo = (manifest: ManifestData): ExtensionInfo => {
  return {
    name: manifest.name || 'Universe Bag（乾坤袋）',
    version: manifest.version || '1.0.0',
    description: manifest.description || '智能收藏管理工具，支持AI自动分类和云端同步',
    developer: manifest.author || 'coffeebean'
  }
}

/**
 * 获取扩展信息，包含错误处理和默认值
 */
export const getExtensionInfo = (): ExtensionInfo => {
  try {
    const manifest = getManifestFromChrome()
    if (manifest) {
      const extensionInfo = convertManifestToExtensionInfo(manifest)
      console.log('成功获取扩展信息:', extensionInfo)
      return extensionInfo
    } else {
      console.warn('无法获取 manifest 数据，使用默认扩展信息')
    }
  } catch (error) {
    console.error('读取扩展信息时发生错误:', error)
  }

  // 返回默认信息
  const defaultInfo = {
    name: 'Universe Bag（乾坤袋）',
    version: '1.0.0',
    description: '智能收藏管理工具，支持AI自动分类和云端同步',
    developer: 'coffeebean'
  }

  console.log('使用默认扩展信息:', defaultInfo)
  return defaultInfo
}

/**
 * 检查扩展环境是否可用
 */
export const isExtensionEnvironment = (): boolean => {
  return typeof chrome !== 'undefined' && 
         chrome.runtime !== undefined && 
         chrome.runtime.getManifest !== undefined
}

/**
 * 验证 manifest 数据的完整性
 */
export const validateManifestData = (manifest: any): manifest is ManifestData => {
  if (!manifest || typeof manifest !== 'object') {
    return false
  }

  // 检查必需字段
  const requiredFields = ['name', 'version', 'manifest_version']
  for (const field of requiredFields) {
    if (!manifest[field]) {
      console.warn(`Manifest 缺少必需字段: ${field}`)
      return false
    }
  }

  // 检查版本格式
  const versionRegex = /^\d+\.\d+\.\d+$/
  if (!versionRegex.test(manifest.version)) {
    console.warn(`Manifest 版本格式无效: ${manifest.version}`)
  }

  // 检查 manifest 版本
  if (![2, 3].includes(manifest.manifest_version)) {
    console.warn(`不支持的 manifest 版本: ${manifest.manifest_version}`)
  }

  return true
}

/**
 * 获取扩展权限列表
 */
export const getExtensionPermissions = (): string[] => {
  try {
    const manifest = getManifestFromChrome()
    if (manifest && manifest.permissions) {
      return manifest.permissions
    }
    return []
  } catch (error) {
    console.error('获取扩展权限时发生错误:', error)
    return []
  }
}

/**
 * 获取扩展的构建信息
 */
export const getBuildInfo = () => {
  try {
    const manifest = getManifestFromChrome()
    const buildInfo = {
      buildDate: new Date().toISOString().split('T')[0], // 当前日期作为构建日期
      buildVersion: manifest?.version || '1.0.0',
      manifestVersion: manifest?.manifest_version || 3
    }
    
    console.log('获取构建信息:', buildInfo)
    return buildInfo
  } catch (error) {
    console.error('获取构建信息时发生错误:', error)
    
    // 返回默认构建信息
    const defaultBuildInfo = {
      buildDate: new Date().toISOString().split('T')[0],
      buildVersion: '1.0.0',
      manifestVersion: 3
    }
    
    console.log('使用默认构建信息:', defaultBuildInfo)
    return defaultBuildInfo
  }
}