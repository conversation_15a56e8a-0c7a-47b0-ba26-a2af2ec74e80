// CategoryModal组件演示

import React, { useState } from 'react'
import CategoryModal from '../components/CategoryModal'
import type { Category, CategoryInput, CategoryUpdate } from '../types'

// 模拟分类数据
const mockCategory: Category = {
  id: 'demo-category-1',
  name: '技术文档',
  description: '编程和技术相关的书签分类',
  color: '#3B82F6',
  createdAt: new Date('2024-01-01'),
  updatedAt: new Date('2024-01-01'),
  bookmarkCount: 15
}

/**
 * CategoryModal组件演示
 * 展示分类模态窗口的各种使用场景
 */
const CategoryModalDemo: React.FC = () => {
  const [modalState, setModalState] = useState<{
    isOpen: boolean
    type: 'create' | 'edit' | 'delete'
    category?: Category
  }>({
    isOpen: false,
    type: 'create'
  })
  
  const [loading, setLoading] = useState(false)
  const [message, setMessage] = useState('')

  // 打开创建模态窗口
  const openCreateModal = () => {
    setModalState({
      isOpen: true,
      type: 'create'
    })
    setMessage('')
  }

  // 打开编辑模态窗口
  const openEditModal = () => {
    setModalState({
      isOpen: true,
      type: 'edit',
      category: mockCategory
    })
    setMessage('')
  }

  // 打开删除模态窗口
  const openDeleteModal = () => {
    setModalState({
      isOpen: true,
      type: 'delete',
      category: mockCategory
    })
    setMessage('')
  }

  // 关闭模态窗口
  const closeModal = () => {
    setModalState(prev => ({
      ...prev,
      isOpen: false
    }))
  }

  // 处理保存操作
  const handleSave = async (data: CategoryInput | CategoryUpdate) => {
    setLoading(true)
    setMessage('')
    
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1500))
      
      console.log('保存分类数据:', data)
      setMessage(`${modalState.type === 'create' ? '创建' : '编辑'}分类成功！`)
      closeModal()
    } catch (error) {
      console.error('保存失败:', error)
      setMessage('保存失败，请重试')
    } finally {
      setLoading(false)
    }
  }

  // 处理删除操作
  const handleDelete = async () => {
    setLoading(true)
    setMessage('')
    
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      console.log('删除分类:', modalState.category?.id)
      setMessage('删除分类成功！')
      closeModal()
    } catch (error) {
      console.error('删除失败:', error)
      setMessage('删除失败，请重试')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="p-8 max-w-4xl mx-auto">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-4">
          CategoryModal 组件演示
        </h1>
        <p className="text-gray-600">
          演示分类模态窗口组件的各种使用场景，包括创建、编辑和删除分类。
        </p>
      </div>

      {/* 操作按钮 */}
      <div className="mb-8">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">操作演示</h2>
        <div className="flex flex-wrap gap-4">
          <button
            onClick={openCreateModal}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors"
          >
            创建新分类
          </button>
          <button
            onClick={openEditModal}
            className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transition-colors"
          >
            编辑分类
          </button>
          <button
            onClick={openDeleteModal}
            className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 transition-colors"
          >
            删除分类
          </button>
        </div>
      </div>

      {/* 状态消息 */}
      {message && (
        <div className={`mb-6 p-4 rounded-lg ${
          message.includes('成功') 
            ? 'bg-green-50 text-green-800 border border-green-200' 
            : 'bg-red-50 text-red-800 border border-red-200'
        }`}>
          {message}
        </div>
      )}

      {/* 模拟分类信息 */}
      <div className="mb-8">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">模拟分类数据</h2>
        <div className="bg-gray-50 rounded-lg p-4">
          <div className="flex items-center space-x-3 mb-3">
            <div 
              className="w-4 h-4 rounded-full"
              style={{ backgroundColor: mockCategory.color }}
            />
            <div>
              <h3 className="font-medium text-gray-900">{mockCategory.name}</h3>
              <p className="text-sm text-gray-600">{mockCategory.description}</p>
            </div>
          </div>
          <div className="text-sm text-gray-500">
            <p>ID: {mockCategory.id}</p>
            <p>书签数量: {mockCategory.bookmarkCount}</p>
            <p>创建时间: {mockCategory.createdAt.toLocaleDateString()}</p>
            <p>更新时间: {mockCategory.updatedAt.toLocaleDateString()}</p>
          </div>
        </div>
      </div>

      {/* 功能说明 */}
      <div className="mb-8">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">功能特性</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="bg-white border border-gray-200 rounded-lg p-4">
            <h3 className="font-medium text-gray-900 mb-2">创建分类</h3>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• 表单验证</li>
              <li>• 颜色选择</li>
              <li>• 名称唯一性检查</li>
              <li>• 加载状态显示</li>
            </ul>
          </div>
          <div className="bg-white border border-gray-200 rounded-lg p-4">
            <h3 className="font-medium text-gray-900 mb-2">编辑分类</h3>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• 预填充现有数据</li>
              <li>• 实时验证</li>
              <li>• 修改保存</li>
              <li>• 错误处理</li>
            </ul>
          </div>
          <div className="bg-white border border-gray-200 rounded-lg p-4">
            <h3 className="font-medium text-gray-900 mb-2">删除分类</h3>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• 删除确认对话框</li>
              <li>• 影响书签数量显示</li>
              <li>• 安全提示</li>
              <li>• 操作不可撤销警告</li>
            </ul>
          </div>
        </div>
      </div>

      {/* 交互说明 */}
      <div>
        <h2 className="text-xl font-semibold text-gray-900 mb-4">交互说明</h2>
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <ul className="text-sm text-blue-800 space-y-2">
            <li>• 点击按钮打开对应的模态窗口</li>
            <li>• 可以通过点击背景或按ESC键关闭模态窗口</li>
            <li>• 表单提交时会显示加载状态</li>
            <li>• 操作完成后会显示成功或失败消息</li>
            <li>• 删除操作会显示详细的确认信息</li>
          </ul>
        </div>
      </div>

      {/* CategoryModal组件 */}
      <CategoryModal
        isOpen={modalState.isOpen}
        type={modalState.type}
        category={modalState.category}
        bookmarkCount={modalState.category?.bookmarkCount}
        onSave={handleSave}
        onDelete={handleDelete}
        onClose={closeModal}
        loading={loading}
      />
    </div>
  )
}

export default CategoryModalDemo