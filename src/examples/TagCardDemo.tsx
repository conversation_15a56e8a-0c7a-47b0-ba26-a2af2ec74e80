// 标签卡片组件演示

import React, { useState } from 'react'
import TagCard from '../components/TagCard'
import type { Tag } from '../types'

/**
 * 标签卡片演示组件
 * 展示TagCard组件的各种使用场景和状态
 */
const TagCardDemo: React.FC = () => {
  const [selectedTag, setSelectedTag] = useState<string | null>(null)

  // 示例标签数据
  const sampleTags: Array<Tag & { usageCount: number }> = [
    {
      id: 'tag1',
      name: '技术',
      color: '#3B82F6',
      usageCount: 25,
      createdAt: new Date('2024-01-01'),
      updatedAt: new Date('2024-01-02')
    },
    {
      id: 'tag2',
      name: '学习',
      color: '#10B981',
      usageCount: 18,
      createdAt: new Date('2024-01-02'),
      updatedAt: new Date('2024-01-02')
    },
    {
      id: 'tag3',
      name: '工具',
      color: '#F59E0B',
      usageCount: 12,
      createdAt: new Date('2024-01-03'),
      updatedAt: new Date('2024-01-04')
    },
    {
      id: 'tag4',
      name: '新闻',
      color: '#EF4444',
      usageCount: 8,
      createdAt: new Date('2024-01-04'),
      updatedAt: new Date('2024-01-04')
    },
    {
      id: 'tag5',
      name: '娱乐',
      color: '#8B5CF6',
      usageCount: 3,
      createdAt: new Date('2024-01-05'),
      updatedAt: new Date('2024-01-05')
    },
    {
      id: 'tag6',
      name: '未使用标签',
      color: '#6B7280',
      usageCount: 0,
      createdAt: new Date('2024-01-06'),
      updatedAt: new Date('2024-01-06')
    },
    {
      id: 'tag7',
      name: '无颜色标签',
      color: undefined,
      usageCount: 5,
      createdAt: new Date('2024-01-07'),
      updatedAt: new Date('2024-01-07')
    },
    {
      id: 'tag8',
      name: '超长标签名称示例这是一个非常长的标签名称用于测试截断效果',
      color: '#EC4899',
      usageCount: 15,
      createdAt: new Date('2024-01-08'),
      updatedAt: new Date('2024-01-08')
    }
  ]

  // 处理标签编辑
  const handleEdit = (tag: Tag & { usageCount: number }) => {
    alert(`编辑标签: ${tag.name}`)
  }

  // 处理标签删除
  const handleDelete = (tag: Tag & { usageCount: number }) => {
    if (confirm(`确定要删除标签"${tag.name}"吗？`)) {
      alert(`删除标签: ${tag.name}`)
    }
  }

  // 处理标签点击
  const handleClick = (tag: Tag & { usageCount: number }) => {
    setSelectedTag(selectedTag === tag.id ? null : tag.id)
  }

  return (
    <div className="p-8 bg-gray-50 min-h-screen">
      <div className="max-w-7xl mx-auto">
        {/* 标题 */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            标签卡片组件演示
          </h1>
          <p className="text-gray-600">
            展示TagCard组件的各种使用场景，包括不同的使用频率、颜色和状态
          </p>
        </div>

        {/* 选中状态提示 */}
        {selectedTag && (
          <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <p className="text-blue-800">
              已选中标签: <strong>{sampleTags.find(t => t.id === selectedTag)?.name}</strong>
            </p>
          </div>
        )}

        {/* 使用频率分组展示 */}
        <div className="space-y-8">
          {/* 高频标签 */}
          <section>
            <h2 className="text-xl font-semibold text-gray-900 mb-4">
              高频标签 (20+ 次使用)
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
              {sampleTags
                .filter(tag => tag.usageCount >= 20)
                .map(tag => (
                  <TagCard
                    key={tag.id}
                    tag={tag}
                    onEdit={() => handleEdit(tag)}
                    onDelete={() => handleDelete(tag)}
                    onClick={() => handleClick(tag)}
                    className={selectedTag === tag.id ? 'ring-2 ring-blue-500' : ''}
                  />
                ))
              }
            </div>
          </section>

          {/* 中频标签 */}
          <section>
            <h2 className="text-xl font-semibold text-gray-900 mb-4">
              中频标签 (5-19 次使用)
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
              {sampleTags
                .filter(tag => tag.usageCount >= 5 && tag.usageCount < 20)
                .map(tag => (
                  <TagCard
                    key={tag.id}
                    tag={tag}
                    onEdit={() => handleEdit(tag)}
                    onDelete={() => handleDelete(tag)}
                    onClick={() => handleClick(tag)}
                    className={selectedTag === tag.id ? 'ring-2 ring-blue-500' : ''}
                  />
                ))
              }
            </div>
          </section>

          {/* 低频标签 */}
          <section>
            <h2 className="text-xl font-semibold text-gray-900 mb-4">
              低频标签 (1-4 次使用)
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
              {sampleTags
                .filter(tag => tag.usageCount >= 1 && tag.usageCount < 5)
                .map(tag => (
                  <TagCard
                    key={tag.id}
                    tag={tag}
                    onEdit={() => handleEdit(tag)}
                    onDelete={() => handleDelete(tag)}
                    onClick={() => handleClick(tag)}
                    className={selectedTag === tag.id ? 'ring-2 ring-blue-500' : ''}
                  />
                ))
              }
            </div>
          </section>

          {/* 未使用标签 */}
          <section>
            <h2 className="text-xl font-semibold text-gray-900 mb-4">
              未使用标签 (0 次使用)
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
              {sampleTags
                .filter(tag => tag.usageCount === 0)
                .map(tag => (
                  <TagCard
                    key={tag.id}
                    tag={tag}
                    onEdit={() => handleEdit(tag)}
                    onDelete={() => handleDelete(tag)}
                    onClick={() => handleClick(tag)}
                    className={selectedTag === tag.id ? 'ring-2 ring-blue-500' : ''}
                  />
                ))
              }
            </div>
          </section>
        </div>

        {/* 特殊情况展示 */}
        <section className="mt-12">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">
            特殊情况展示
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {/* 无颜色标签 */}
            {sampleTags
              .filter(tag => !tag.color)
              .map(tag => (
                <TagCard
                  key={tag.id}
                  tag={tag}
                  onEdit={() => handleEdit(tag)}
                  onDelete={() => handleDelete(tag)}
                  onClick={() => handleClick(tag)}
                  className={selectedTag === tag.id ? 'ring-2 ring-blue-500' : ''}
                />
              ))
            }

            {/* 长名称标签 */}
            {sampleTags
              .filter(tag => tag.name.length > 20)
              .map(tag => (
                <TagCard
                  key={tag.id}
                  tag={tag}
                  onEdit={() => handleEdit(tag)}
                  onDelete={() => handleDelete(tag)}
                  onClick={() => handleClick(tag)}
                  className={selectedTag === tag.id ? 'ring-2 ring-blue-500' : ''}
                />
              ))
            }
          </div>
        </section>

        {/* 无点击事件的标签 */}
        <section className="mt-12">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">
            无点击事件的标签
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
            {sampleTags.slice(0, 2).map(tag => (
              <TagCard
                key={`no-click-${tag.id}`}
                tag={tag}
                onEdit={() => handleEdit(tag)}
                onDelete={() => handleDelete(tag)}
                // 没有onClick属性
              />
            ))}
          </div>
        </section>

        {/* 使用说明 */}
        <section className="mt-12 p-6 bg-white rounded-lg border border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">
            使用说明
          </h2>
          <div className="space-y-4 text-sm text-gray-600">
            <div>
              <strong>颜色指示：</strong>
              <ul className="list-disc list-inside mt-2 space-y-1">
                <li>标签卡片背景色基于标签颜色生成，带有透明度</li>
                <li>标签图标使用对比色确保可读性</li>
                <li>右下角显示颜色预览和十六进制值</li>
              </ul>
            </div>
            <div>
              <strong>使用频率指示：</strong>
              <ul className="list-disc list-inside mt-2 space-y-1">
                <li>🔴 高频 (20+): 红色指示器</li>
                <li>🟡 中频 (5-19): 黄色指示器</li>
                <li>🟢 低频 (1-4): 绿色指示器</li>
                <li>⚪ 未使用 (0): 灰色指示器</li>
              </ul>
            </div>
            <div>
              <strong>交互功能：</strong>
              <ul className="list-disc list-inside mt-2 space-y-1">
                <li>悬停显示编辑和删除按钮</li>
                <li>点击卡片可选中标签（如果提供了onClick）</li>
                <li>编辑和删除按钮点击不会触发卡片点击事件</li>
              </ul>
            </div>
          </div>
        </section>
      </div>
    </div>
  )
}

export default TagCardDemo