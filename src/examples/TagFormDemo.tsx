import React, { useState } from 'react'
import { TagForm, TagFormData } from '../components/TagForm'
import { ColorUtils } from '../utils/colorUtils'

/**
 * TagForm组件演示
 */
export const TagFormDemo: React.FC = () => {
  const [showCreateForm, setShowCreateForm] = useState(false)
  const [showEditForm, setShowEditForm] = useState(false)
  const [loading, setLoading] = useState(false)
  const [result, setResult] = useState<string>('')

  // 模拟现有标签数据
  const existingTags = [
    { id: '1', name: '技术', color: '#3B82F6' },
    { id: '2', name: '学习', color: '#10B981' },
    { id: '3', name: '工具', color: '#F59E0B' },
    { id: '4', name: '新闻', color: '#EF4444' },
    { id: '5', name: '娱乐', color: '#8B5CF6' }
  ]

  // 编辑的标签数据
  const editTag = {
    id: '2',
    name: '学习',
    color: '#10B981'
  }

  /**
   * 处理创建标签
   */
  const handleCreateTag = async (tagData: TagFormData) => {
    setLoading(true)
    setResult('')

    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1500))
      
      // 模拟随机失败
      if (Math.random() < 0.1) {
        throw new Error('网络错误，请重试')
      }

      console.log('创建标签:', tagData)
      setResult(`成功创建标签: ${tagData.name} (${tagData.color})`)
      setShowCreateForm(false)
    } catch (error) {
      console.error('创建标签失败:', error)
      throw error // 让表单组件处理错误
    } finally {
      setLoading(false)
    }
  }

  /**
   * 处理编辑标签
   */
  const handleEditTag = async (tagData: TagFormData) => {
    setLoading(true)
    setResult('')

    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      console.log('编辑标签:', tagData)
      setResult(`成功编辑标签: ${tagData.name} (${tagData.color})`)
      setShowEditForm(false)
    } catch (error) {
      console.error('编辑标签失败:', error)
      throw error
    } finally {
      setLoading(false)
    }
  }

  /**
   * 处理取消操作
   */
  const handleCancel = () => {
    setShowCreateForm(false)
    setShowEditForm(false)
    setResult('')
  }

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">TagForm 组件演示</h1>
      
      <div className="space-y-8">
        {/* 操作按钮 */}
        <div className="bg-white p-6 rounded-lg shadow-md">
          <h2 className="text-lg font-semibold mb-4">操作演示</h2>
          <div className="flex space-x-4">
            <button
              onClick={() => setShowCreateForm(true)}
              disabled={showCreateForm || showEditForm}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              创建新标签
            </button>
            <button
              onClick={() => setShowEditForm(true)}
              disabled={showCreateForm || showEditForm}
              className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              编辑标签
            </button>
          </div>

          {/* 结果显示 */}
          {result && (
            <div className="mt-4 p-3 bg-green-50 border border-green-200 rounded-md">
              <div className="flex">
                <svg className="w-5 h-5 text-green-400 mr-2 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
                <span className="text-sm text-green-700">{result}</span>
              </div>
            </div>
          )}
        </div>

        {/* 创建标签表单 */}
        {showCreateForm && (
          <div className="bg-white p-6 rounded-lg shadow-md">
            <TagForm
              onSave={handleCreateTag}
              onCancel={handleCancel}
              loading={loading}
              existingTags={existingTags}
            />
          </div>
        )}

        {/* 编辑标签表单 */}
        {showEditForm && (
          <div className="bg-white p-6 rounded-lg shadow-md">
            <TagForm
              tag={editTag}
              onSave={handleEditTag}
              onCancel={handleCancel}
              loading={loading}
              existingTags={existingTags}
            />
          </div>
        )}

        {/* 现有标签列表 */}
        <div className="bg-white p-6 rounded-lg shadow-md">
          <h2 className="text-lg font-semibold mb-4">现有标签列表</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {existingTags.map(tag => (
              <div key={tag.id} className="p-3 border border-gray-200 rounded-md">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <div 
                      className="w-4 h-4 rounded-full border"
                      style={{ backgroundColor: tag.color }}
                    />
                    <span className="text-sm font-medium">{tag.name}</span>
                  </div>
                  <span className="text-xs text-gray-500">ID: {tag.id}</span>
                </div>
                <div className="mt-2">
                  <span 
                    className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium"
                    style={{
                      backgroundColor: tag.color,
                      color: ColorUtils.getContrastColor(tag.color)
                    }}
                  >
                    {tag.name}
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* 表单验证演示 */}
        <div className="bg-white p-6 rounded-lg shadow-md">
          <h2 className="text-lg font-semibold mb-4">表单验证规则</h2>
          <div className="space-y-3 text-sm">
            <div className="flex items-start space-x-2">
              <span className="text-red-500 mt-1">•</span>
              <div>
                <strong>标签名称必填：</strong>不能为空或只包含空格
              </div>
            </div>
            <div className="flex items-start space-x-2">
              <span className="text-red-500 mt-1">•</span>
              <div>
                <strong>长度限制：</strong>标签名称最多50个字符
              </div>
            </div>
            <div className="flex items-start space-x-2">
              <span className="text-red-500 mt-1">•</span>
              <div>
                <strong>名称唯一性：</strong>不能与现有标签重复（忽略大小写和空格）
              </div>
            </div>
            <div className="flex items-start space-x-2">
              <span className="text-blue-500 mt-1">•</span>
              <div>
                <strong>颜色自动生成：</strong>如果未选择颜色，系统会根据标签名称自动生成
              </div>
            </div>
            <div className="flex items-start space-x-2">
              <span className="text-blue-500 mt-1">•</span>
              <div>
                <strong>实时验证：</strong>输入时会实时检查名称格式和重复性
              </div>
            </div>
            <div className="flex items-start space-x-2">
              <span className="text-green-500 mt-1">•</span>
              <div>
                <strong>标签预览：</strong>实时显示标签在书签中的显示效果
              </div>
            </div>
          </div>
        </div>

        {/* 测试用例 */}
        <div className="bg-white p-6 rounded-lg shadow-md">
          <h2 className="text-lg font-semibold mb-4">测试用例</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h3 className="font-medium mb-2 text-green-600">有效输入</h3>
              <ul className="space-y-1 text-sm">
                <li>• "前端开发" - 新的标签名称</li>
                <li>• "React" - 简短名称</li>
                <li>• "JavaScript & TypeScript" - 包含特殊字符</li>
                <li>• "人工智能与机器学习" - 较长名称</li>
              </ul>
            </div>
            <div>
              <h3 className="font-medium mb-2 text-red-600">无效输入</h3>
              <ul className="space-y-1 text-sm">
                <li>• "" - 空字符串</li>
                <li>• "   " - 只包含空格</li>
                <li>• "技术" - 与现有标签重复</li>
                <li>• "学习" - 与现有标签重复（编辑时除外）</li>
              </ul>
            </div>
          </div>
        </div>

        {/* 功能特性 */}
        <div className="bg-white p-6 rounded-lg shadow-md">
          <h2 className="text-lg font-semibold mb-4">功能特性</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h3 className="font-medium mb-2">表单功能</h3>
              <ul className="space-y-1 text-sm text-gray-600">
                <li>✓ 创建和编辑模式</li>
                <li>✓ 实时表单验证</li>
                <li>✓ 颜色选择器集成</li>
                <li>✓ 标签预览功能</li>
                <li>✓ 加载状态显示</li>
                <li>✓ 错误处理和提示</li>
              </ul>
            </div>
            <div>
              <h3 className="font-medium mb-2">用户体验</h3>
              <ul className="space-y-1 text-sm text-gray-600">
                <li>✓ 防抖验证（300ms）</li>
                <li>✓ 字符计数显示</li>
                <li>✓ 修改确认提示</li>
                <li>✓ 键盘导航支持</li>
                <li>✓ 无障碍性支持</li>
                <li>✓ 响应式设计</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default TagFormDemo