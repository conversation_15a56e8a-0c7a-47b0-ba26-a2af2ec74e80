// CategoryForm组件演示页面

import React, { useState } from 'react'
import CategoryForm from '../components/CategoryForm'
import CategoryCard from '../components/CategoryCard'
import type { Category, CategoryInput, CategoryUpdate } from '../types'

// 演示数据
const initialCategories: Category[] = [
  {
    id: 'category-1',
    name: '技术',
    description: '编程、开发工具、技术文档等相关内容',
    color: '#3B82F6',
    createdAt: new Date('2024-01-01T10:00:00Z'),
    updatedAt: new Date('2024-01-02T10:00:00Z'),
    bookmarkCount: 0
  },
  {
    id: 'category-2',
    name: '学习',
    description: '在线课程、教程、学习资源',
    color: '#10B981',
    createdAt: new Date('2024-01-01T10:00:00Z'),
    updatedAt: new Date('2024-01-01T10:00:00Z'),
    bookmarkCount: 0
  }
]

const CategoryFormDemo: React.FC = () => {
  const [categories, setCategories] = useState<Category[]>(initialCategories)
  const [showCreateForm, setShowCreateForm] = useState(false)
  const [editingCategory, setEditingCategory] = useState<Category | null>(null)
  const [loading, setLoading] = useState(false)

  // 处理创建分类
  const handleCreateSubmit = async (data: CategoryInput) => {
    setLoading(true)
    
    // 模拟API调用延迟
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    const newCategory: Category = {
      id: `category-${Date.now()}`,
      name: data.name,
      description: data.description,
      color: data.color || '#3B82F6',
      parentId: data.parentId,
      createdAt: new Date(),
      updatedAt: new Date(),
      bookmarkCount: 0
    }
    
    setCategories(prev => [...prev, newCategory])
    setShowCreateForm(false)
    setLoading(false)
    
    console.log('创建分类:', newCategory)
    alert(`分类"${newCategory.name}"创建成功！`)
  }

  // 处理编辑分类
  const handleEditSubmit = async (data: CategoryUpdate) => {
    if (!editingCategory) return
    
    setLoading(true)
    
    // 模拟API调用延迟
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    const updatedCategory: Category = {
      ...editingCategory,
      name: data.name || editingCategory.name,
      description: data.description !== undefined ? data.description : editingCategory.description,
      color: data.color || editingCategory.color,
      parentId: data.parentId !== undefined ? data.parentId : editingCategory.parentId,
      updatedAt: new Date()
    }
    
    setCategories(prev => 
      prev.map(cat => cat.id === editingCategory.id ? updatedCategory : cat)
    )
    setEditingCategory(null)
    setLoading(false)
    
    console.log('更新分类:', updatedCategory)
    alert(`分类"${updatedCategory.name}"更新成功！`)
  }

  // 处理取消操作
  const handleCancel = () => {
    setShowCreateForm(false)
    setEditingCategory(null)
  }

  // 处理编辑分类
  const handleEditCategory = (category: Category) => {
    setEditingCategory(category)
  }

  // 处理删除分类
  const handleDeleteCategory = (categoryId: string) => {
    const category = categories.find(c => c.id === categoryId)
    if (category) {
      const confirmed = confirm(`确定要删除分类"${category.name}"吗？`)
      if (confirmed) {
        setCategories(prev => prev.filter(c => c.id !== categoryId))
        console.log('删除分类:', categoryId)
        alert(`分类"${category.name}"已删除！`)
      }
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-7xl mx-auto">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">CategoryForm 组件演示</h1>
          <p className="text-gray-600">
            展示分类表单组件的创建和编辑功能，包括表单验证、颜色选择等特性。
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* 左侧：表单区域 */}
          <div className="space-y-6">
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-xl font-semibold text-gray-900">
                  {editingCategory ? '编辑分类' : showCreateForm ? '创建分类' : '分类表单'}
                </h2>
                {!showCreateForm && !editingCategory && (
                  <button
                    onClick={() => setShowCreateForm(true)}
                    className="px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors"
                  >
                    创建新分类
                  </button>
                )}
              </div>

              {(showCreateForm || editingCategory) ? (
                <CategoryForm
                  mode={editingCategory ? 'edit' : 'create'}
                  category={editingCategory || undefined}
                  onSubmit={editingCategory ? handleEditSubmit : handleCreateSubmit}
                  onCancel={handleCancel}
                  loading={loading}
                />
              ) : (
                <div className="text-center py-12 text-gray-500">
                  <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                    </svg>
                  </div>
                  <p className="text-lg font-medium mb-2">准备创建分类</p>
                  <p className="text-sm">点击"创建新分类"按钮开始，或编辑现有分类</p>
                </div>
              )}
            </div>

            {/* 功能说明 */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">表单功能</h3>
              <div className="space-y-3 text-sm text-gray-600">
                <div>
                  <h4 className="font-medium text-gray-900 mb-1">表单验证</h4>
                  <ul className="list-disc list-inside space-y-1 ml-4">
                    <li>分类名称：必填，2-50字符，唯一性检查</li>
                    <li>分类描述：可选，最多200字符</li>
                    <li>分类颜色：必填，支持预定义和自定义颜色</li>
                  </ul>
                </div>
                
                <div>
                  <h4 className="font-medium text-gray-900 mb-1">颜色选择</h4>
                  <ul className="list-disc list-inside space-y-1 ml-4">
                    <li>10种预定义颜色快速选择</li>
                    <li>自定义颜色选择器</li>
                    <li>手动输入十六进制颜色值</li>
                    <li>实时颜色名称显示</li>
                  </ul>
                </div>
                
                <div>
                  <h4 className="font-medium text-gray-900 mb-1">用户体验</h4>
                  <ul className="list-disc list-inside space-y-1 ml-4">
                    <li>实时字符计数显示</li>
                    <li>表单验证错误提示</li>
                    <li>加载状态和禁用处理</li>
                    <li>键盘导航和可访问性支持</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>

          {/* 右侧：分类列表 */}
          <div className="space-y-6">
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-6">现有分类</h2>
              
              {categories.length > 0 ? (
                <div className="space-y-4">
                  {categories.map((category) => (
                    <CategoryCard
                      key={category.id}
                      category={category}
                      bookmarkCount={Math.floor(Math.random() * 20)} // 随机书签数量用于演示
                      onEdit={() => handleEditCategory(category)}
                      onDelete={() => handleDeleteCategory(category.id)}
                      onClick={() => console.log('查看分类:', category.name)}
                    />
                  ))}
                </div>
              ) : (
                <div className="text-center py-12 text-gray-500">
                  <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                    </svg>
                  </div>
                  <p className="text-lg font-medium mb-2">暂无分类</p>
                  <p className="text-sm">创建第一个分类开始管理您的书签</p>
                </div>
              )}
            </div>

            {/* 操作日志 */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">操作提示</h3>
              <div className="space-y-2 text-sm text-gray-600">
                <p>• 点击"创建新分类"开始创建</p>
                <p>• 点击分类卡片上的编辑按钮进行编辑</p>
                <p>• 点击分类卡片上的删除按钮删除分类</p>
                <p>• 表单会实时验证输入内容</p>
                <p>• 所有操作都会在控制台输出日志</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default CategoryFormDemo