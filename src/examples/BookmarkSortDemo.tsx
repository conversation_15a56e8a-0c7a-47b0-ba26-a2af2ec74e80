// 收藏排序功能演示组件

import React, { useState, useMemo } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../components/ui/card'
import { Badge } from '../components/ui/badge'
import BookmarkSortSelector from '../components/BookmarkSortSelector'
import BookmarkRow from '../components/BookmarkRow'
import BookmarkCompact from '../components/BookmarkCompact'
import BookmarkSortUtils from '../utils/bookmarkSortUtils'
import type { BookmarkSortOption } from '../components/BookmarkSortSelector'
import type { Bookmark } from '../types'

/**
 * 创建演示用的收藏数据
 */
const createDemoBookmarks = (): Bookmark[] => {
  const now = new Date()
  
  return [
    {
      id: '1',
      type: 'url',
      title: 'React 官方文档',
      url: 'https://react.dev',
      description: 'React 官方文档，学习现代 React 开发',
      tags: ['React', 'JavaScript', '前端开发'],
      category: '技术文档',
      favicon: 'https://react.dev/favicon.ico',
      metadata: {
        pageTitle: 'React',
        siteName: 'React',
        aiGenerated: false
      },
      createdAt: new Date(now.getTime() - 86400000 * 1), // 1天前
      updatedAt: new Date(now.getTime() - 43200000) // 12小时前更新
    },
    {
      id: '2',
      type: 'url',
      title: 'Vue.js 指南',
      url: 'https://vuejs.org',
      description: 'Vue.js 渐进式 JavaScript 框架',
      tags: ['Vue', 'JavaScript', '前端开发'],
      category: '技术文档',
      favicon: 'https://vuejs.org/logo.svg',
      metadata: {
        pageTitle: 'Vue.js',
        siteName: 'Vue.js',
        aiGenerated: false
      },
      createdAt: new Date(now.getTime() - 86400000 * 3), // 3天前
      updatedAt: new Date(now.getTime() - 86400000 * 3) // 3天前，未更新
    },
    {
      id: '3',
      type: 'text',
      title: 'Angular 学习笔记',
      content: 'Angular 是一个强大的前端框架...',
      description: 'Angular 框架学习笔记和最佳实践',
      tags: ['Angular', 'TypeScript', '前端开发'],
      category: '学习笔记',
      metadata: {
        wordCount: 1200,
        language: 'zh-CN',
        aiGenerated: false
      },
      createdAt: new Date(now.getTime() - 86400000 * 2), // 2天前
      updatedAt: new Date(now.getTime() - 3600000) // 1小时前更新
    },
    {
      id: '4',
      type: 'url',
      title: 'Node.js 最佳实践',
      url: 'https://nodejs.org/en/docs/guides/',
      description: 'Node.js 开发最佳实践和指南',
      tags: ['Node.js', 'JavaScript', '后端开发'],
      category: '技术文档',
      favicon: 'https://nodejs.org/static/images/favicons/favicon.ico',
      metadata: {
        pageTitle: 'Node.js Guides',
        siteName: 'Node.js',
        aiGenerated: false
      },
      createdAt: new Date(now.getTime() - 86400000 * 5), // 5天前
      updatedAt: new Date(now.getTime() - 86400000 * 5) // 5天前，未更新
    },
    {
      id: '5',
      type: 'url',
      title: 'CSS Grid 完整指南',
      url: 'https://css-tricks.com/snippets/css/complete-guide-grid/',
      description: 'CSS Grid 布局的完整指南和示例',
      tags: ['CSS', 'Grid', '前端开发'],
      category: '设计资源',
      favicon: 'https://css-tricks.com/favicon.ico',
      metadata: {
        pageTitle: 'A Complete Guide to Grid',
        siteName: 'CSS-Tricks',
        aiGenerated: false
      },
      createdAt: new Date(now.getTime() - 86400000 * 4), // 4天前
      updatedAt: new Date(now.getTime() - 7200000) // 2小时前更新
    },
    {
      id: '6',
      type: 'text',
      title: 'Webpack 配置优化',
      content: 'Webpack 配置优化技巧和最佳实践...',
      description: 'Webpack 打包工具的配置优化方法',
      tags: ['Webpack', 'JavaScript', '构建工具'],
      category: '学习笔记',
      metadata: {
        wordCount: 800,
        language: 'zh-CN',
        aiGenerated: false
      },
      createdAt: new Date(now.getTime() - 86400000 * 6), // 6天前
      updatedAt: new Date(now.getTime() - 86400000 * 1) // 1天前更新
    }
  ]
}

/**
 * 收藏排序功能演示组件
 */
const BookmarkSortDemo: React.FC = () => {
  const [sortOption, setSortOption] = useState<BookmarkSortOption>('created-desc')
  const [viewMode, setViewMode] = useState<'row' | 'compact'>('row')
  
  const demoBookmarks = useMemo(() => createDemoBookmarks(), [])
  
  // 应用排序
  const sortedBookmarks = useMemo(() => {
    return BookmarkSortUtils.sortBookmarks(demoBookmarks, sortOption)
  }, [demoBookmarks, sortOption])

  const handleEdit = (bookmark: Bookmark) => {
    console.log('编辑收藏:', bookmark.title)
  }

  const handleDelete = (bookmark: Bookmark) => {
    console.log('删除收藏:', bookmark.title)
  }

  const handleClick = (bookmark: Bookmark) => {
    console.log('点击收藏:', bookmark.title)
  }

  return (
    <div className="container mx-auto p-6 max-w-6xl">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">收藏排序功能演示</h1>
        <p className="text-muted-foreground mb-4">
          展示收藏管理页面的排序功能，支持多种排序方式，默认按创建时间降序（最新在前）
        </p>
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h3 className="font-semibold text-blue-800 mb-2">排序功能特点：</h3>
          <ul className="text-sm text-blue-700 space-y-1">
            <li>• 默认按创建时间降序排列（最新添加的在前面）</li>
            <li>• 支持按创建时间、更新时间、标题、分类等多种方式排序</li>
            <li>• 每种排序方式都支持升序和降序</li>
            <li>• 分类排序时，同分类内按创建时间降序排列</li>
            <li>• 排序状态会保持，不会因为搜索或筛选而重置</li>
          </ul>
        </div>
      </div>

      <div className="space-y-6">
        {/* 控制面板 */}
        <Card>
          <CardHeader>
            <CardTitle>排序控制</CardTitle>
            <CardDescription>
              选择不同的排序方式查看效果，当前排序：{BookmarkSortUtils.getSortOptionLabel(sortOption)}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                <label className="text-sm font-medium">排序方式：</label>
                <BookmarkSortSelector
                  value={sortOption}
                  onChange={setSortOption}
                />
              </div>
              
              <div className="flex items-center gap-2">
                <label className="text-sm font-medium">视图模式：</label>
                <div className="flex gap-2">
                  <button
                    onClick={() => setViewMode('row')}
                    className={`px-3 py-1 text-sm rounded ${
                      viewMode === 'row' 
                        ? 'bg-primary text-primary-foreground' 
                        : 'bg-secondary text-secondary-foreground'
                    }`}
                  >
                    行视图
                  </button>
                  <button
                    onClick={() => setViewMode('compact')}
                    className={`px-3 py-1 text-sm rounded ${
                      viewMode === 'compact' 
                        ? 'bg-primary text-primary-foreground' 
                        : 'bg-secondary text-secondary-foreground'
                    }`}
                  >
                    紧凑视图
                  </button>
                </div>
              </div>
            </div>
            
            <div className="mt-4 p-3 bg-muted rounded-lg">
              <p className="text-sm text-muted-foreground">
                <strong>当前排序说明：</strong> {BookmarkSortUtils.getSortOptionDescription(sortOption)}
              </p>
            </div>
          </CardContent>
        </Card>

        {/* 排序结果展示 */}
        <Card>
          <CardHeader>
            <CardTitle>排序结果</CardTitle>
            <CardDescription>
              共 {sortedBookmarks.length} 个收藏，按 "{BookmarkSortUtils.getSortOptionLabel(sortOption)}" 排序
            </CardDescription>
          </CardHeader>
          <CardContent>
            {viewMode === 'row' ? (
              <div className="space-y-2">
                {sortedBookmarks.map((bookmark, index) => (
                  <div key={bookmark.id} className="relative">
                    <div className="absolute -left-8 top-1/2 transform -translate-y-1/2 text-xs text-muted-foreground font-mono">
                      {index + 1}
                    </div>
                    <BookmarkRow
                      bookmark={bookmark}
                      onEdit={handleEdit}
                      onDelete={handleDelete}
                      onClick={handleClick}
                    />
                  </div>
                ))}
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {sortedBookmarks.map((bookmark, index) => (
                  <div key={bookmark.id} className="relative">
                    <Badge 
                      variant="secondary" 
                      className="absolute -top-2 -left-2 z-10 text-xs"
                    >
                      {index + 1}
                    </Badge>
                    <BookmarkCompact
                      bookmark={bookmark}
                      onEdit={handleEdit}
                      onDelete={handleDelete}
                      onClick={handleClick}
                    />
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        {/* 排序选项说明 */}
        <Card>
          <CardHeader>
            <CardTitle>排序选项说明</CardTitle>
            <CardDescription>
              各种排序方式的详细说明
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <h4 className="font-medium mb-2">时间排序</h4>
                <ul className="text-sm text-muted-foreground space-y-1">
                  <li>• <strong>最新添加：</strong>按创建时间降序，最新添加的在前</li>
                  <li>• <strong>最早添加：</strong>按创建时间升序，最早添加的在前</li>
                  <li>• <strong>最近更新：</strong>按更新时间降序，最近更新的在前</li>
                  <li>• <strong>最久未更新：</strong>按更新时间升序，最久未更新的在前</li>
                </ul>
              </div>
              <div>
                <h4 className="font-medium mb-2">字母排序</h4>
                <ul className="text-sm text-muted-foreground space-y-1">
                  <li>• <strong>标题 A-Z：</strong>按标题字母顺序升序排列</li>
                  <li>• <strong>标题 Z-A：</strong>按标题字母顺序降序排列</li>
                  <li>• <strong>分类 A-Z：</strong>按分类升序，同分类内按创建时间降序</li>
                  <li>• <strong>分类 Z-A：</strong>按分类降序，同分类内按创建时间降序</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

export default BookmarkSortDemo