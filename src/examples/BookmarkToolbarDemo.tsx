// 收藏工具栏组件演示

import React, { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../components/ui/card'
import BookmarkToolbar from '../components/BookmarkToolbar'
import BookmarkSortUtils from '../utils/bookmarkSortUtils'
import type { BookmarkSortOption } from '../components/BookmarkSortSelector'
import type { ViewMode } from '../components/ViewModeSelector'

/**
 * 收藏工具栏组件演示
 */
const BookmarkToolbarDemo: React.FC = () => {
  // 工具栏状态
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('all')
  const [sortOption, setSortOption] = useState<BookmarkSortOption>('created-desc')
  const [viewMode, setViewMode] = useState<ViewMode>('row')
  const [isSearching, setIsSearching] = useState(false)
  
  // 模拟数据
  const categories = ['技术文档', '学习笔记', '设计资源', '工具软件']
  const suggestions = ['React', 'Vue', 'Angular', 'TypeScript', 'JavaScript']
  
  // 处理搜索变化
  const handleSearchChange = (query: string) => {
    setSearchQuery(query)
    // 模拟搜索延迟
    if (query.trim()) {
      setIsSearching(true)
      setTimeout(() => setIsSearching(false), 1000)
    }
  }
  
  // 处理建议点击
  const handleSuggestionClick = (suggestion: string) => {
    setSearchQuery(suggestion)
  }
  
  // 处理添加收藏
  const handleAddClick = () => {
    console.log('添加收藏')
    alert('添加收藏功能')
  }
  
  // 处理刷新
  const handleRefreshClick = () => {
    console.log('刷新数据')
    alert('刷新数据功能')
  }

  return (
    <div className="container mx-auto p-6 max-w-6xl">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">收藏工具栏组件演示</h1>
        <p className="text-muted-foreground mb-4">
          展示可复用的收藏工具栏组件，包含搜索、筛选、排序、视图切换等功能
        </p>
        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
          <h3 className="font-semibold text-green-800 mb-2">组件特点：</h3>
          <ul className="text-sm text-green-700 space-y-1">
            <li>• 统一的工具栏组件，可在多个页面复用</li>
            <li>• 支持水平和垂直两种布局模式</li>
            <li>• 集成搜索、分类筛选、排序、视图切换功能</li>
            <li>• 支持紧凑模式，适应不同的空间需求</li>
            <li>• 使用shadcn/ui组件，保持设计一致性</li>
          </ul>
        </div>
      </div>

      <div className="space-y-8">
        {/* 垂直布局演示 */}
        <Card>
          <CardHeader>
            <CardTitle>垂直布局模式</CardTitle>
            <CardDescription>
              适合在页面头部使用，搜索框和控件分两行显示，空间利用更合理
            </CardDescription>
          </CardHeader>
          <CardContent>
            <BookmarkToolbar
              searchQuery={searchQuery}
              onSearchChange={handleSearchChange}
              isSearching={isSearching}
              suggestions={suggestions}
              onSuggestionClick={handleSuggestionClick}
              
              selectedCategory={selectedCategory}
              onCategoryChange={setSelectedCategory}
              categories={categories}
              
              sortOption={sortOption}
              onSortChange={setSortOption}
              
              viewMode={viewMode}
              onViewModeChange={setViewMode}
              viewModeLoading={false}
              
              onAddClick={handleAddClick}
              onRefreshClick={handleRefreshClick}
              showAddButton={true}
              showRefreshButton={true}
              
              layout="vertical"
            />
          </CardContent>
        </Card>

        {/* 水平布局演示 */}
        <Card>
          <CardHeader>
            <CardTitle>水平布局模式</CardTitle>
            <CardDescription>
              适合在侧边栏或紧凑空间使用，所有控件在一行显示
            </CardDescription>
          </CardHeader>
          <CardContent>
            <BookmarkToolbar
              searchQuery={searchQuery}
              onSearchChange={handleSearchChange}
              isSearching={isSearching}
              suggestions={suggestions}
              onSuggestionClick={handleSuggestionClick}
              
              selectedCategory={selectedCategory}
              onCategoryChange={setSelectedCategory}
              categories={categories}
              
              sortOption={sortOption}
              onSortChange={setSortOption}
              
              viewMode={viewMode}
              onViewModeChange={setViewMode}
              viewModeLoading={false}
              
              onAddClick={handleAddClick}
              onRefreshClick={handleRefreshClick}
              showAddButton={true}
              showRefreshButton={true}
              
              layout="horizontal"
            />
          </CardContent>
        </Card>

        {/* 紧凑模式演示 */}
        <Card>
          <CardHeader>
            <CardTitle>紧凑模式</CardTitle>
            <CardDescription>
              适合在移动端或空间受限的场景使用，隐藏部分文字，只显示图标
            </CardDescription>
          </CardHeader>
          <CardContent>
            <BookmarkToolbar
              searchQuery={searchQuery}
              onSearchChange={handleSearchChange}
              isSearching={isSearching}
              
              selectedCategory={selectedCategory}
              onCategoryChange={setSelectedCategory}
              categories={categories}
              
              sortOption={sortOption}
              onSortChange={setSortOption}
              
              viewMode={viewMode}
              onViewModeChange={setViewMode}
              viewModeLoading={false}
              
              onAddClick={handleAddClick}
              onRefreshClick={handleRefreshClick}
              showAddButton={true}
              showRefreshButton={true}
              
              layout="horizontal"
              compact={true}
            />
          </CardContent>
        </Card>

        {/* 自定义配置演示 */}
        <Card>
          <CardHeader>
            <CardTitle>自定义配置</CardTitle>
            <CardDescription>
              可以选择性显示功能，比如只显示搜索和排序，隐藏操作按钮
            </CardDescription>
          </CardHeader>
          <CardContent>
            <BookmarkToolbar
              searchQuery={searchQuery}
              onSearchChange={handleSearchChange}
              isSearching={isSearching}
              
              selectedCategory={selectedCategory}
              onCategoryChange={setSelectedCategory}
              categories={categories}
              
              sortOption={sortOption}
              onSortChange={setSortOption}
              
              viewMode={viewMode}
              onViewModeChange={setViewMode}
              viewModeLoading={false}
              
              showAddButton={false}
              showRefreshButton={false}
              
              layout="horizontal"
            />
          </CardContent>
        </Card>

        {/* 当前状态显示 */}
        <Card>
          <CardHeader>
            <CardTitle>当前状态</CardTitle>
            <CardDescription>
              显示工具栏的当前状态值，用于调试和验证
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
              <div>
                <strong>搜索关键词：</strong>
                <div className="text-muted-foreground">{searchQuery || '无'}</div>
              </div>
              <div>
                <strong>选中分类：</strong>
                <div className="text-muted-foreground">{selectedCategory === 'all' ? '所有分类' : selectedCategory}</div>
              </div>
              <div>
                <strong>排序方式：</strong>
                <div className="text-muted-foreground">{BookmarkSortUtils.getSortOptionLabel(sortOption)}</div>
              </div>
              <div>
                <strong>视图模式：</strong>
                <div className="text-muted-foreground">
                  {viewMode === 'row' ? '行视图' : viewMode === 'compact' ? '紧凑视图' : '卡片视图'}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 使用说明 */}
        <Card>
          <CardHeader>
            <CardTitle>使用说明</CardTitle>
            <CardDescription>
              如何在其他页面中使用BookmarkToolbar组件
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <h4 className="font-medium mb-2">基本用法</h4>
                <pre className="bg-muted p-3 rounded text-sm overflow-x-auto">
{`import BookmarkToolbar from './components/BookmarkToolbar'

<BookmarkToolbar
  searchQuery={searchQuery}
  onSearchChange={setSearchQuery}
  selectedCategory={selectedCategory}
  onCategoryChange={setSelectedCategory}
  categories={categories}
  sortOption={sortOption}
  onSortChange={setSortOption}
  viewMode={viewMode}
  onViewModeChange={setViewMode}
  layout="vertical"
/>`}
                </pre>
              </div>
              
              <div>
                <h4 className="font-medium mb-2">属性说明</h4>
                <ul className="text-sm text-muted-foreground space-y-1">
                  <li>• <strong>layout:</strong> 'horizontal' | 'vertical' - 布局模式</li>
                  <li>• <strong>compact:</strong> boolean - 是否使用紧凑模式</li>
                  <li>• <strong>showAddButton:</strong> boolean - 是否显示添加按钮</li>
                  <li>• <strong>showRefreshButton:</strong> boolean - 是否显示刷新按钮</li>
                  <li>• <strong>suggestions:</strong> string[] - 搜索建议列表</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

export default BookmarkToolbarDemo