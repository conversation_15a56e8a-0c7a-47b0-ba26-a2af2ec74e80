<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>下拉框视图选择器演示</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        /* 自定义样式 */
        .dropdown-demo {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        
        /* 模拟收藏管理页面头部样式 */
        .bookmark-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            min-height: 64px;
            position: relative;
            margin-bottom: 1.5rem;
        }
        
        .bookmark-header-title {
            flex-shrink: 0;
            min-width: 120px;
        }
        
        .bookmark-header-controls {
            display: flex;
            align-items: center;
            flex-wrap: wrap;
            min-height: 40px;
            gap: 0.75rem;
        }
        
        .bookmark-header-controls > * {
            flex-shrink: 0;
        }
        
        .view-mode-selector-container {
            display: flex;
            align-items: center;
            flex-shrink: 0;
            min-width: 140px;
        }
        
        /* 响应式布局 */
        @media (max-width: 768px) {
            .bookmark-header {
                flex-direction: column;
                align-items: stretch;
                gap: 1rem;
                min-height: auto;
            }
            
            .bookmark-header-title {
                text-align: center;
                min-width: auto;
            }
            
            .bookmark-header-controls {
                justify-content: center;
                gap: 0.75rem;
            }
        }
        
        @media (max-width: 640px) {
            .bookmark-header-controls {
                flex-direction: column;
                align-items: stretch;
                gap: 0.75rem;
            }
            
            .bookmark-header-controls > * {
                width: 100%;
                justify-content: center;
            }
            
            .view-mode-selector-container {
                width: 100%;
            }
            
            .bookmark-header-controls select,
            .bookmark-header-controls button {
                width: 100%;
            }
        }
    </style>
</head>
<body class="bg-gray-50 dropdown-demo">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <!-- 模拟收藏管理页面头部 -->
            <div class="bookmark-header">
                <!-- 标题区域 -->
                <div class="bookmark-header-title">
                    <h2 class="text-2xl font-bold text-gray-900">收藏管理</h2>
                </div>
                
                <!-- 控制按钮区域 -->
                <div class="bookmark-header-controls">
                    <!-- 添加收藏按钮 -->
                    <button class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors text-sm flex items-center">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        添加收藏
                    </button>
                    
                    <!-- 视图模式选择器 - 下拉框版本 -->
                    <div class="view-mode-selector-container">
                        <div class="relative">
                            <select 
                                id="viewModeSelect"
                                class="appearance-none bg-white border border-gray-300 rounded-lg px-4 py-2 pr-8 text-sm font-medium text-gray-700 hover:border-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200"
                                style="min-width: 140px;"
                                aria-label="选择视图模式"
                            >
                                <option value="card">卡片视图</option>
                                <option value="row">行视图</option>
                                <option value="compact">紧凑视图</option>
                            </select>
                            
                            <!-- 自定义下拉箭头 -->
                            <div class="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
                                <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </div>
                            
                            <!-- 当前模式图标显示 -->
                            <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                                <span class="text-gray-500" id="currentIcon">
                                    <!-- 卡片视图图标 -->
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <rect x="3" y="3" width="7" height="7" rx="1"></rect>
                                        <rect x="14" y="3" width="7" height="7" rx="1"></rect>
                                        <rect x="3" y="14" width="7" height="7" rx="1"></rect>
                                        <rect x="14" y="14" width="7" height="7" rx="1"></rect>
                                    </svg>
                                </span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 分类选择器 -->
                    <select class="border border-gray-300 rounded-lg px-3 py-2 text-sm min-w-[100px]">
                        <option value="all">所有分类</option>
                        <option value="work">工作</option>
                        <option value="study">学习</option>
                        <option value="entertainment">娱乐</option>
                    </select>
                    
                    <!-- 搜索框 -->
                    <div class="relative">
                        <input
                            type="text"
                            placeholder="搜索收藏..."
                            class="border border-gray-300 rounded-lg px-3 py-2 text-sm w-64 pr-8"
                        />
                        <div class="absolute right-2 top-1/2 transform -translate-y-1/2">
                            <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                        </div>
                    </div>
                    
                    <!-- 刷新按钮 -->
                    <button class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors text-sm">
                        刷新
                    </button>
                </div>
            </div>
            
            <!-- 演示内容区域 -->
            <div class="border-t border-gray-200 pt-6">
                <div class="text-center py-12 text-gray-500">
                    <div class="w-12 h-12 mx-auto mb-4 text-gray-300">
                        <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z"></path>
                        </svg>
                    </div>
                    <p class="text-lg font-medium mb-2">视图选择器演示</p>
                    <p class="text-sm">当前选择的视图模式: <span id="currentMode" class="font-medium text-blue-600">卡片视图</span></p>
                    <p class="text-sm mt-2">下拉框版本解决了布局错位问题，在所有屏幕尺寸下都能稳定显示</p>
                </div>
            </div>
        </div>
        
        <!-- 优势说明 -->
        <div class="mt-8 bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">下拉框版本的优势</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div class="flex items-start space-x-3">
                    <div class="flex-shrink-0 w-5 h-5 text-green-500 mt-0.5">
                        <svg fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <div>
                        <h4 class="font-medium text-gray-900">布局稳定</h4>
                        <p class="text-sm text-gray-600">避免了复杂的flex布局问题，在所有屏幕尺寸下都能保持稳定</p>
                    </div>
                </div>
                
                <div class="flex items-start space-x-3">
                    <div class="flex-shrink-0 w-5 h-5 text-green-500 mt-0.5">
                        <svg fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <div>
                        <h4 class="font-medium text-gray-900">空间节省</h4>
                        <p class="text-sm text-gray-600">占用更少的水平空间，为其他控件留出更多空间</p>
                    </div>
                </div>
                
                <div class="flex items-start space-x-3">
                    <div class="flex-shrink-0 w-5 h-5 text-green-500 mt-0.5">
                        <svg fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <div>
                        <h4 class="font-medium text-gray-900">用户熟悉</h4>
                        <p class="text-sm text-gray-600">下拉框是用户熟悉的UI模式，操作直观简单</p>
                    </div>
                </div>
                
                <div class="flex items-start space-x-3">
                    <div class="flex-shrink-0 w-5 h-5 text-green-500 mt-0.5">
                        <svg fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <div>
                        <h4 class="font-medium text-gray-900">可访问性好</h4>
                        <p class="text-sm text-gray-600">原生select元素具有良好的键盘导航和屏幕阅读器支持</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 视图模式图标配置
        const viewModeIcons = {
            card: `<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <rect x="3" y="3" width="7" height="7" rx="1"></rect>
                <rect x="14" y="3" width="7" height="7" rx="1"></rect>
                <rect x="3" y="14" width="7" height="7" rx="1"></rect>
                <rect x="14" y="14" width="7" height="7" rx="1"></rect>
            </svg>`,
            row: `<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <line x1="3" y1="6" x2="21" y2="6"></line>
                <line x1="3" y1="12" x2="21" y2="12"></line>
                <line x1="3" y1="18" x2="21" y2="18"></line>
            </svg>`,
            compact: `<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <rect x="3" y="3" width="18" height="4" rx="1"></rect>
                <rect x="3" y="9" width="18" height="4" rx="1"></rect>
                <rect x="3" y="15" width="18" height="4" rx="1"></rect>
            </svg>`
        }

        const viewModeNames = {
            card: '卡片视图',
            row: '行视图',
            compact: '紧凑视图'
        }

        // 处理视图模式切换
        document.getElementById('viewModeSelect').addEventListener('change', function(e) {
            const selectedMode = e.target.value
            
            // 更新图标
            document.getElementById('currentIcon').innerHTML = viewModeIcons[selectedMode]
            
            // 更新显示的模式名称
            document.getElementById('currentMode').textContent = viewModeNames[selectedMode]
            
            console.log('视图模式切换到:', selectedMode)
        })

        // 演示响应式效果
        function updateScreenSize() {
            const width = window.innerWidth
            let screenType = '大屏幕'
            
            if (width <= 640) {
                screenType = '小屏幕 (移动设备)'
            } else if (width <= 768) {
                screenType = '中等屏幕 (平板)'
            }
            
            console.log(`当前屏幕尺寸: ${width}px (${screenType})`)
        }

        window.addEventListener('resize', updateScreenSize)
        updateScreenSize()
    </script>
</body>
</html>