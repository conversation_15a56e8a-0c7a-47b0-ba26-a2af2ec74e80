<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TagModal 组件演示</title>
    <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    <script src="https://unpkg.com/lucide-react@latest/dist/umd/lucide-react.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .tag-form {
            /* 基础样式 */
        }
        .tag-color-picker {
            /* 基础样式 */
        }
    </style>
</head>
<body class="bg-gray-50">
    <div id="root"></div>

    <script type="text/babel">
        const { useState, useEffect } = React;
        const { X, AlertTriangle } = lucideReact;

        // 模拟TagForm组件
        const TagForm = ({ tag, onSave, onCancel, loading, existingTags }) => {
            const [name, setName] = useState(tag?.name || '');
            const [color, setColor] = useState(tag?.color || '');

            const handleSubmit = (e) => {
                e.preventDefault();
                onSave({ name, color });
            };

            return (
                <form onSubmit={handleSubmit} className="space-y-4">
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                            标签名称 <span className="text-red-500">*</span>
                        </label>
                        <input
                            type="text"
                            value={name}
                            onChange={(e) => setName(e.target.value)}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                            placeholder="输入标签名称"
                            disabled={loading}
                            required
                        />
                    </div>
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                            标签颜色
                        </label>
                        <input
                            type="color"
                            value={color || '#3B82F6'}
                            onChange={(e) => setColor(e.target.value)}
                            className="w-16 h-8 border border-gray-300 rounded cursor-pointer"
                            disabled={loading}
                        />
                    </div>
                    <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
                        <button
                            type="button"
                            onClick={onCancel}
                            className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50"
                            disabled={loading}
                        >
                            取消
                        </button>
                        <button
                            type="submit"
                            className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-lg hover:bg-blue-700 disabled:opacity-50"
                            disabled={loading}
                        >
                            {loading ? '保存中...' : '保存'}
                        </button>
                    </div>
                </form>
            );
        };

        // TagModal组件
        const TagModal = ({
            isOpen,
            type,
            tag,
            bookmarkCount = 0,
            onSave,
            onDelete,
            onClose,
            loading = false,
            existingTags = []
        }) => {
            if (!isOpen) return null;

            const handleBackdropClick = (e) => {
                if (e.target === e.currentTarget && !loading) {
                    onClose();
                }
            };

            useEffect(() => {
                const handleEscKey = (e) => {
                    if (e.key === 'Escape' && !loading) {
                        onClose();
                    }
                };

                if (isOpen) {
                    document.addEventListener('keydown', handleEscKey);
                    document.body.style.overflow = 'hidden';
                }

                return () => {
                    document.removeEventListener('keydown', handleEscKey);
                    document.body.style.overflow = 'unset';
                };
            }, [isOpen, loading, onClose]);

            const getModalTitle = () => {
                switch (type) {
                    case 'create': return '创建新标签';
                    case 'edit': return '编辑标签';
                    case 'delete': return '删除标签';
                    default: return '标签管理';
                }
            };

            const handleDeleteConfirm = async () => {
                if (onDelete) {
                    await onDelete();
                }
            };

            const handleFormSave = async (formData) => {
                if (onSave) {
                    await onSave(formData);
                }
            };

            const renderDeleteContent = () => {
                if (!tag) return null;

                return (
                    <div className="space-y-4">
                        <div className="flex items-center space-x-3">
                            <div className="flex-shrink-0 w-10 h-10 bg-red-100 rounded-full flex items-center justify-center">
                                <AlertTriangle className="w-5 h-5 text-red-600" />
                            </div>
                            <div>
                                <h3 className="text-lg font-medium text-gray-900">确认删除标签</h3>
                                <p className="text-sm text-gray-500">此操作无法撤销</p>
                            </div>
                        </div>

                        <div className="bg-gray-50 rounded-lg p-4">
                            <div className="flex items-center space-x-3">
                                <div 
                                    className="w-4 h-4 rounded-full"
                                    style={{ backgroundColor: tag.color || '#6B7280' }}
                                />
                                <div>
                                    <p className="font-medium text-gray-900">{tag.name}</p>
                                    <p className="text-sm text-gray-600">使用次数: {tag.usageCount || 0}</p>
                                </div>
                            </div>
                        </div>

                        <div className="space-y-2">
                            <p className="text-sm text-gray-700">删除此标签将会：</p>
                            <ul className="text-sm text-gray-600 space-y-1 ml-4">
                                <li>• 永久删除标签 "{tag.name}"</li>
                                {bookmarkCount > 0 ? (
                                    <li>• 从 {bookmarkCount} 个书签中移除此标签</li>
                                ) : (
                                    <li>• 不会影响任何书签（此标签未被使用）</li>
                                )}
                                <li>• 此操作无法撤销</li>
                            </ul>
                        </div>

                        <div className="flex items-center justify-end space-x-3 pt-4 border-t border-gray-200">
                            <button
                                type="button"
                                onClick={onClose}
                                className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50"
                                disabled={loading}
                            >
                                取消
                            </button>
                            <button
                                type="button"
                                onClick={handleDeleteConfirm}
                                className="px-4 py-2 text-sm font-medium text-white bg-red-600 border border-transparent rounded-lg hover:bg-red-700 disabled:opacity-50"
                                disabled={loading}
                            >
                                {loading ? (
                                    <div className="flex items-center">
                                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                                        删除中...
                                    </div>
                                ) : (
                                    '确认删除'
                                )}
                            </button>
                        </div>
                    </div>
                );
            };

            const renderFormContent = () => {
                if (type === 'delete') {
                    return renderDeleteContent();
                }

                if (!onSave) {
                    console.error('TagModal: onSave is required for create/edit mode');
                    return null;
                }

                return (
                    <TagForm
                        tag={tag}
                        onSave={handleFormSave}
                        onCancel={onClose}
                        loading={loading}
                        existingTags={existingTags}
                    />
                );
            };

            return (
                <div 
                    className="fixed inset-0 z-50 overflow-y-auto"
                    role="dialog"
                    aria-modal="true"
                >
                    <div 
                        className="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0"
                        onClick={handleBackdropClick}
                    >
                        <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
                        <span className="hidden sm:inline-block sm:align-middle sm:h-screen">&#8203;</span>
                        <div className="relative inline-block align-bottom bg-white rounded-lg px-4 pt-5 pb-4 text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full sm:p-6">
                            <div className="flex items-center justify-between mb-4">
                                <h3 className="text-lg font-medium text-gray-900">
                                    {getModalTitle()}
                                </h3>
                                <button
                                    type="button"
                                    onClick={onClose}
                                    className="text-gray-400 hover:text-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500 rounded-lg p-1"
                                    disabled={loading}
                                >
                                    <X className="w-5 h-5" />
                                </button>
                            </div>
                            <div>
                                {renderFormContent()}
                            </div>
                        </div>
                    </div>
                </div>
            );
        };

        // 演示应用
        const TagModalDemo = () => {
            const [isOpen, setIsOpen] = useState(false);
            const [modalType, setModalType] = useState('create');
            const [loading, setLoading] = useState(false);

            const sampleTag = {
                id: '1',
                name: '前端开发',
                color: '#3B82F6',
                usageCount: 15,
                createdAt: new Date('2024-01-01'),
                updatedAt: new Date('2024-01-15')
            };

            const existingTags = [
                { id: '1', name: '前端开发' },
                { id: '2', name: '后端开发' },
                { id: '3', name: '数据库' },
                { id: '4', name: '算法' }
            ];

            const handleSave = async (data) => {
                setLoading(true);
                console.log('保存标签数据:', data);
                await new Promise(resolve => setTimeout(resolve, 1500));
                setLoading(false);
                setIsOpen(false);
                alert(`标签${modalType === 'create' ? '创建' : '更新'}成功！`);
            };

            const handleDelete = async () => {
                setLoading(true);
                console.log('删除标签:', sampleTag.id);
                await new Promise(resolve => setTimeout(resolve, 1500));
                setLoading(false);
                setIsOpen(false);
                alert('标签删除成功！');
            };

            const handleClose = () => {
                if (!loading) {
                    setIsOpen(false);
                }
            };

            const openModal = (type) => {
                setModalType(type);
                setIsOpen(true);
            };

            return (
                <div className="min-h-screen bg-gray-50 py-8">
                    <div className="max-w-4xl mx-auto px-4">
                        <div className="mb-8">
                            <h1 className="text-3xl font-bold text-gray-900 mb-2">
                                TagModal 组件演示
                            </h1>
                            <p className="text-gray-600">
                                展示标签模态窗口的创建、编辑和删除功能
                            </p>
                        </div>

                        <div className="bg-white rounded-lg shadow-sm p-6 mb-8">
                            <h2 className="text-xl font-semibold text-gray-900 mb-4">
                                模态窗口操作
                            </h2>
                            <div className="flex flex-wrap gap-4">
                                <button
                                    onClick={() => openModal('create')}
                                    className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                                >
                                    创建标签
                                </button>
                                <button
                                    onClick={() => openModal('edit')}
                                    className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700"
                                >
                                    编辑标签
                                </button>
                                <button
                                    onClick={() => openModal('delete')}
                                    className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700"
                                >
                                    删除标签
                                </button>
                            </div>
                        </div>

                        <div className="bg-white rounded-lg shadow-sm p-6">
                            <h2 className="text-xl font-semibold text-gray-900 mb-4">
                                示例标签信息
                            </h2>
                            <div className="bg-gray-50 rounded-lg p-4">
                                <div className="flex items-center space-x-3">
                                    <div 
                                        className="w-4 h-4 rounded-full"
                                        style={{ backgroundColor: sampleTag.color }}
                                    />
                                    <div>
                                        <p className="font-medium text-gray-900">{sampleTag.name}</p>
                                        <p className="text-sm text-gray-600">
                                            使用次数: {sampleTag.usageCount} | 
                                            创建时间: {sampleTag.createdAt.toLocaleDateString()}
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <TagModal
                            isOpen={isOpen}
                            type={modalType}
                            tag={modalType !== 'create' ? sampleTag : undefined}
                            bookmarkCount={modalType === 'delete' ? sampleTag.usageCount : undefined}
                            onSave={handleSave}
                            onDelete={handleDelete}
                            onClose={handleClose}
                            loading={loading}
                            existingTags={existingTags}
                        />
                    </div>
                </div>
            );
        };

        ReactDOM.render(<TagModalDemo />, document.getElementById('root'));
    </script>
</body>
</html>