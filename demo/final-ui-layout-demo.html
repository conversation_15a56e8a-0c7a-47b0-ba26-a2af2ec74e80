<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>最终UI布局统一规范演示</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .demo-section {
            background: white;
            border-radius: 8px;
            padding: 24px;
            margin-bottom: 24px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        
        .demo-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 16px 24px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            background: white;
            margin-bottom: 16px;
        }
        
        .demo-header-center {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
            padding: 16px 24px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            background: white;
            margin-bottom: 16px;
        }
        
        .demo-title {
            display: flex;
            align-items: center;
        }
        
        .demo-title-center {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 8px;
        }
        
        .demo-icon {
            width: 24px;
            height: 24px;
            margin-right: 12px;
            color: #2563eb;
        }
        
        .demo-title h2, .demo-title-center h2 {
            margin: 0;
            font-size: 24px;
            font-weight: 600;
            color: #111827;
        }
        
        .demo-description {
            margin: 4px 0 0 0;
            color: #6b7280;
            font-size: 14px;
        }
        
        .demo-description-center {
            margin: 8px 0 16px 0;
            color: #6b7280;
            font-size: 14px;
        }
        
        .demo-actions {
            display: flex;
            gap: 12px;
        }
        
        .demo-actions-center {
            display: flex;
            gap: 12px;
            justify-content: center;
        }
        
        .btn {
            display: inline-flex;
            align-items: center;
            padding: 8px 16px;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            text-decoration: none;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .btn-primary {
            background: #2563eb;
            color: white;
            border: none;
        }
        
        .btn-primary:hover {
            background: #1d4ed8;
        }
        
        .btn-outline {
            background: white;
            color: #374151;
            border: 1px solid #d1d5db;
        }
        
        .btn-outline:hover {
            background: #f9fafb;
        }
        
        .btn-icon {
            width: 16px;
            height: 16px;
            margin-right: 8px;
        }
        
        .layout-comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 24px;
            margin-top: 24px;
        }
        
        .layout-type {
            padding: 16px;
            border-radius: 8px;
            border: 2px solid #e5e7eb;
        }
        
        .layout-type h3 {
            margin: 0 0 16px 0;
            font-size: 18px;
            color: #111827;
        }
        
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 24px;
        }
        
        .feature-card {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 16px;
        }
        
        .feature-card h4 {
            margin: 0 0 8px 0;
            color: #1e293b;
            font-size: 16px;
        }
        
        .feature-list {
            list-style: none;
            padding: 0;
            margin: 8px 0;
        }
        
        .feature-list li {
            padding: 4px 0;
            display: flex;
            align-items: center;
            font-size: 14px;
        }
        
        .feature-list li::before {
            content: "✓";
            color: #10b981;
            font-weight: bold;
            margin-right: 8px;
        }
        
        .status-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
            margin-left: 8px;
        }
        
        .status-completed {
            background: #dcfce7;
            color: #166534;
        }
        
        .page-icon {
            width: 20px;
            height: 20px;
            margin-right: 8px;
            vertical-align: middle;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="demo-section">
            <h1>🎉 最终UI布局统一规范演示</h1>
            <p>展示插件所有主要页面的统一UI布局效果，包括最新的改进。</p>
        </div>

        <!-- 收藏管理页面 -->
        <div class="demo-section">
            <h2>
                <svg class="page-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z"></path>
                </svg>
                收藏管理页面
                <span class="status-badge status-completed">✅ 已优化</span>
            </h2>
            <div class="demo-header">
                <div>
                    <div class="demo-title">
                        <svg class="demo-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z"></path>
                        </svg>
                        <h2>收藏管理</h2>
                    </div>
                    <p class="demo-description">管理您的收藏内容，支持搜索、分类和多种视图模式</p>
                </div>
                <div class="demo-actions">
                    <button class="btn btn-primary">
                        <svg class="btn-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        添加收藏
                    </button>
                    <button class="btn btn-outline">刷新</button>
                </div>
            </div>
            <p><strong>改进内容：</strong>添加了书签图标，统一了标题格式</p>
        </div>

        <!-- 分类管理页面 -->
        <div class="demo-section">
            <h2>
                <svg class="page-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2V7zm0 0V5a2 2 0 012-2h6l2 2h6a2 2 0 012 2v2M7 13h10M7 17h10"></path>
                </svg>
                分类管理页面
                <span class="status-badge status-completed">✅ 已优化</span>
            </h2>
            <div class="demo-header">
                <div>
                    <div class="demo-title">
                        <svg class="demo-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2V7zm0 0V5a2 2 0 012-2h6l2 2h6a2 2 0 012 2v2M7 13h10M7 17h10"></path>
                        </svg>
                        <h2>分类管理</h2>
                    </div>
                    <p class="demo-description">管理您的书签分类，更好地组织收藏内容</p>
                </div>
                <div class="demo-actions">
                    <button class="btn btn-outline">
                        <svg class="btn-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                        </svg>
                        刷新
                    </button>
                    <button class="btn btn-primary">
                        <svg class="btn-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        新建分类
                    </button>
                </div>
            </div>
            <p><strong>布局模式：</strong>标准模式 - 标题在左，操作按钮在右</p>
        </div>

        <!-- 标签管理页面 -->
        <div class="demo-section">
            <h2>
                <svg class="page-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
                </svg>
                标签管理页面
                <span class="status-badge status-completed">✅ 已优化</span>
            </h2>
            <div class="demo-header">
                <div>
                    <div class="demo-title">
                        <svg class="demo-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
                        </svg>
                        <h2>标签管理</h2>
                    </div>
                    <p class="demo-description">管理您的书签标签，更好地分类和查找内容</p>
                </div>
                <div class="demo-actions">
                    <button class="btn btn-outline">
                        <svg class="btn-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                        </svg>
                        刷新
                    </button>
                    <button class="btn btn-primary">
                        <svg class="btn-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        新建标签
                    </button>
                </div>
            </div>
            <p><strong>布局模式：</strong>标准模式 - 与分类管理页面保持一致的左右布局</p>
        </div>

        <!-- 导入导出页面 -->
        <div class="demo-section">
            <h2>
                <svg class="page-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4"></path>
                </svg>
                导入导出页面
                <span class="status-badge status-completed">✅ 已完成</span>
            </h2>
            <div class="demo-header">
                <div>
                    <div class="demo-title">
                        <svg class="demo-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4"></path>
                        </svg>
                        <h2>导入导出</h2>
                    </div>
                    <p class="demo-description">导出您的收藏数据或从其他来源导入收藏</p>
                </div>
            </div>
            <p><strong>布局模式：</strong>标准模式 - 作为参考模板</p>
        </div>

        <!-- 设置页面 -->
        <div class="demo-section">
            <h2>
                <svg class="page-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                </svg>
                设置页面
                <span class="status-badge status-completed">✅ 已优化</span>
            </h2>
            <div class="demo-header">
                <div>
                    <div class="demo-title">
                        <svg class="demo-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        </svg>
                        <h2>设置</h2>
                    </div>
                    <p class="demo-description">配置应用程序的各项设置，个性化您的使用体验</p>
                </div>
            </div>
            <p><strong>改进内容：</strong>添加了设置图标，统一了Card布局结构</p>
        </div>

        <!-- 布局模式对比 -->
        <div class="demo-section">
            <h2>布局模式对比</h2>
            <div class="layout-comparison">
                <div class="layout-type">
                    <h3>标准模式</h3>
                    <p>适用于：收藏管理、分类管理、标签管理、导入导出、设置</p>
                    <ul class="feature-list">
                        <li>标题和描述在左侧</li>
                        <li>操作按钮在右侧</li>
                        <li>水平布局，充分利用空间</li>
                        <li>所有管理页面统一使用此模式</li>
                    </ul>
                </div>
                
                <div class="layout-type">
                    <h3>统一性优势</h3>
                    <p>所有页面现在都使用相同的布局模式</p>
                    <ul class="feature-list">
                        <li>用户体验一致性</li>
                        <li>视觉风格统一</li>
                        <li>操作习惯一致</li>
                        <li>维护成本降低</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- 技术规范总结 -->
        <div class="demo-section">
            <h2>技术规范总结</h2>
            <div class="feature-grid">
                <div class="feature-card">
                    <h4>图标规范</h4>
                    <ul class="feature-list">
                        <li>尺寸：w-6 h-6 (24px)</li>
                        <li>间距：mr-3 (12px)</li>
                        <li>颜色：text-primary-600</li>
                        <li>来源：lucide-react</li>
                    </ul>
                </div>
                
                <div class="feature-card">
                    <h4>标题规范</h4>
                    <ul class="feature-list">
                        <li>字体：text-2xl (24px)</li>
                        <li>权重：font-semibold</li>
                        <li>颜色：默认文本色</li>
                        <li>布局：flex items-center</li>
                    </ul>
                </div>
                
                <div class="feature-card">
                    <h4>组件使用</h4>
                    <ul class="feature-list">
                        <li>Card, CardHeader, CardTitle</li>
                        <li>CardDescription, CardContent</li>
                        <li>Button (default/outline)</li>
                        <li>shadcn/ui 设计系统</li>
                    </ul>
                </div>
                
                <div class="feature-card">
                    <h4>布局规范</h4>
                    <ul class="feature-list">
                        <li>外层：p-6 (24px padding)</li>
                        <li>Card间距：mb-6 (24px)</li>
                        <li>按钮间距：space-x-3 (12px)</li>
                        <li>响应式设计支持</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- 完成状态 -->
        <div class="demo-section">
            <h2>🎉 优化完成状态</h2>
            <div class="feature-grid">
                <div class="feature-card">
                    <h4>✅ 收藏管理页面</h4>
                    <p>添加了书签图标，统一了标题格式</p>
                </div>
                
                <div class="feature-card">
                    <h4>✅ 分类管理页面</h4>
                    <p>已有文件夹树图标，布局规范</p>
                </div>
                
                <div class="feature-card">
                    <h4>✅ 标签管理页面</h4>
                    <p>调整为标准布局，与分类管理页面保持一致</p>
                </div>
                
                <div class="feature-card">
                    <h4>✅ 导入导出页面</h4>
                    <p>已有数据库图标，作为参考模板</p>
                </div>
                
                <div class="feature-card">
                    <h4>✅ 设置页面</h4>
                    <p>添加了设置图标，统一了布局</p>
                </div>
                
                <div class="feature-card">
                    <h4>✅ 构建验证</h4>
                    <p>所有检查通过，代码质量良好</p>
                </div>
            </div>
        </div>
    </div>
</body>
</html>