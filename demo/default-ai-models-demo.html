<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>默认AI模型功能演示</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
            padding: 30px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }
        
        .demo-section {
            background: white;
            margin: 20px 0;
            padding: 25px;
            border-radius: 12px;
            box-shadow: 0 4px 16px rgba(0,0,0,0.1);
        }
        
        .demo-section h3 {
            color: #333;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 2px solid #e0e0e0;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .stat-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            border-left: 4px solid #667eea;
        }
        
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 5px;
        }
        
        .stat-label {
            color: #666;
            font-size: 0.9em;
        }
        
        .usage-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .usage-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #28a745;
        }
        
        .usage-card.unconfigured {
            border-left-color: #ffc107;
        }
        
        .usage-card.disabled {
            border-left-color: #dc3545;
            opacity: 0.7;
        }
        
        .usage-title {
            font-weight: bold;
            color: #333;
            margin-bottom: 8px;
        }
        
        .usage-description {
            color: #666;
            font-size: 0.9em;
            margin-bottom: 10px;
        }
        
        .model-info {
            background: #e9ecef;
            padding: 10px;
            border-radius: 6px;
            font-size: 0.85em;
        }
        
        .model-primary {
            color: #28a745;
            font-weight: bold;
        }
        
        .model-fallback {
            color: #ffc107;
            font-weight: bold;
        }
        
        .button-group {
            display: flex;
            gap: 10px;
            margin: 20px 0;
            flex-wrap: wrap;
        }
        
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: #667eea;
            color: white;
        }
        
        .btn-primary:hover {
            background: #5a6fd8;
        }
        
        .btn-success {
            background: #28a745;
            color: white;
        }
        
        .btn-success:hover {
            background: #218838;
        }
        
        .btn-warning {
            background: #ffc107;
            color: #212529;
        }
        
        .btn-warning:hover {
            background: #e0a800;
        }
        
        .test-area {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        
        .test-input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 6px;
            margin-bottom: 10px;
            font-size: 14px;
        }
        
        .test-output {
            background: #e9ecef;
            padding: 15px;
            border-radius: 6px;
            min-height: 100px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 13px;
        }
        
        .loading {
            color: #667eea;
            font-style: italic;
        }
        
        .error {
            color: #dc3545;
            background: #f8d7da;
            padding: 10px;
            border-radius: 6px;
            margin: 10px 0;
        }
        
        .success {
            color: #155724;
            background: #d4edda;
            padding: 10px;
            border-radius: 6px;
            margin: 10px 0;
        }
        
        .category-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.75em;
            font-weight: bold;
            margin-right: 8px;
        }
        
        .category-chat {
            background: #cce5ff;
            color: #0066cc;
        }
        
        .category-translation {
            background: #d4edda;
            color: #155724;
        }
        
        .category-analysis {
            background: #e2d9f3;
            color: #6f42c1;
        }
        
        .category-generation {
            background: #fff3cd;
            color: #856404;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🤖 默认AI模型功能演示</h1>
        <p>展示默认AI模型配置和调用功能</p>
    </div>

    <!-- 统计信息 -->
    <div class="demo-section">
        <h3>📊 统计信息</h3>
        <div class="stats-grid" id="statsGrid">
            <div class="stat-card">
                <div class="stat-number" id="totalUsages">-</div>
                <div class="stat-label">使用场景总数</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="configuredUsages">-</div>
                <div class="stat-label">已配置场景</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="availableModels">-</div>
                <div class="stat-label">可用模型数</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="enabledUsages">-</div>
                <div class="stat-label">启用场景数</div>
            </div>
        </div>
    </div>

    <!-- 操作按钮 -->
    <div class="demo-section">
        <h3>🛠️ 操作功能</h3>
        <div class="button-group">
            <button class="btn btn-primary" onclick="loadConfiguration()">🔄 刷新配置</button>
            <button class="btn btn-success" onclick="setRecommendedConfig()">✨ 一键推荐配置</button>
            <button class="btn btn-warning" onclick="resetToDefaults()">🔄 重置为默认</button>
        </div>
        <div id="operationResult"></div>
    </div>

    <!-- 使用场景列表 -->
    <div class="demo-section">
        <h3>📋 使用场景配置</h3>
        <div class="usage-list" id="usageList">
            <!-- 动态生成使用场景卡片 -->
        </div>
    </div>

    <!-- 模型调用测试 -->
    <div class="demo-section">
        <h3>🧪 模型调用测试</h3>
        <div class="test-area">
            <select class="test-input" id="testUsageSelect">
                <option value="">选择使用场景</option>
            </select>
            <input type="text" class="test-input" id="testPrompt" placeholder="输入测试提示词..." value="你好，请介绍一下你自己">
            <button class="btn btn-primary" onclick="testModelCall()">🚀 测试调用</button>
            <div class="test-output" id="testOutput">等待测试...</div>
        </div>
    </div>

    <!-- 配置导入导出 -->
    <div class="demo-section">
        <h3>💾 配置管理</h3>
        <div class="button-group">
            <button class="btn btn-primary" onclick="exportConfiguration()">📤 导出配置</button>
            <button class="btn btn-primary" onclick="importConfiguration()">📥 导入配置</button>
        </div>
        <textarea class="test-input" id="configData" rows="10" placeholder="配置JSON数据..."></textarea>
    </div>

    <script>
        // 模拟的默认AI模型服务
        class MockDefaultAIModelService {
            constructor() {
                this.usages = [
                    {
                        id: 'default-chat',
                        name: '默认聊天',
                        description: '通用对话和问答场景',
                        category: 'chat',
                        selectedModelId: 'openai_gpt-4',
                        fallbackModelId: 'claude_claude-3',
                        enabled: true,
                        priority: 1
                    },
                    {
                        id: 'translation',
                        name: '翻译模型',
                        description: '文本翻译和多语言处理',
                        category: 'translation',
                        selectedModelId: 'openai_gpt-4',
                        fallbackModelId: null,
                        enabled: true,
                        priority: 2
                    },
                    {
                        id: 'tag-naming',
                        name: '标签命名',
                        description: '为收藏内容生成合适的标签名称',
                        category: 'generation',
                        selectedModelId: null,
                        fallbackModelId: null,
                        enabled: true,
                        priority: 3
                    },
                    {
                        id: 'folder-naming',
                        name: '文件夹命名',
                        description: '为收藏分类生成合适的文件夹名称',
                        category: 'generation',
                        selectedModelId: 'claude_claude-3',
                        fallbackModelId: 'openai_gpt-4',
                        enabled: false,
                        priority: 4
                    },
                    {
                        id: 'content-analysis',
                        name: '内容分析',
                        description: '分析网页内容并提取关键信息',
                        category: 'analysis',
                        selectedModelId: 'openai_gpt-4',
                        fallbackModelId: 'claude_claude-3',
                        enabled: true,
                        priority: 5
                    },
                    {
                        id: 'summary-generation',
                        name: '摘要生成',
                        description: '为收藏内容生成简洁摘要',
                        category: 'generation',
                        selectedModelId: 'claude_claude-3',
                        fallbackModelId: null,
                        enabled: true,
                        priority: 6
                    }
                ];

                this.availableModels = [
                    {
                        id: 'openai_gpt-4',
                        name: 'gpt-4',
                        displayName: 'GPT-4',
                        provider: 'OpenAI',
                        providerId: 'openai',
                        modelType: 'chat',
                        enabled: true,
                        status: 'connected',
                        isRecommended: true,
                        isPopular: true
                    },
                    {
                        id: 'claude_claude-3',
                        name: 'claude-3',
                        displayName: 'Claude 3',
                        provider: 'Anthropic',
                        providerId: 'claude',
                        modelType: 'chat',
                        enabled: true,
                        status: 'connected',
                        isRecommended: true,
                        isPopular: false
                    },
                    {
                        id: 'deepseek_chat',
                        name: 'deepseek-chat',
                        displayName: 'DeepSeek Chat',
                        provider: 'DeepSeek',
                        providerId: 'deepseek',
                        modelType: 'chat',
                        enabled: true,
                        status: 'connected',
                        isRecommended: false,
                        isPopular: true
                    }
                ];
            }

            async getDefaultModelUsages() {
                await this.delay(500);
                return [...this.usages];
            }

            async getAvailableModels() {
                await this.delay(300);
                return [...this.availableModels];
            }

            async updateUsageModel(usageId, selectedModelId, fallbackModelId) {
                await this.delay(200);
                const usage = this.usages.find(u => u.id === usageId);
                if (usage) {
                    usage.selectedModelId = selectedModelId;
                    usage.fallbackModelId = fallbackModelId;
                }
            }

            async setRecommendedConfiguration() {
                await this.delay(1000);
                const primaryModel = this.availableModels.find(m => m.isRecommended && m.isPopular);
                const fallbackModel = this.availableModels.find(m => m.isRecommended && !m.isPopular);
                
                this.usages.forEach(usage => {
                    usage.selectedModelId = primaryModel?.id || null;
                    usage.fallbackModelId = fallbackModel?.id || null;
                });
            }

            async resetToDefaults() {
                await this.delay(500);
                this.usages.forEach(usage => {
                    usage.selectedModelId = null;
                    usage.fallbackModelId = null;
                });
            }

            async getUsageStats() {
                await this.delay(200);
                const total = this.usages.length;
                const configured = this.usages.filter(u => u.selectedModelId).length;
                const enabled = this.usages.filter(u => u.enabled).length;
                return { total, configured, enabled };
            }

            async callModel(usageId, prompt) {
                await this.delay(1500);
                const usage = this.usages.find(u => u.id === usageId);
                if (!usage || !usage.selectedModelId) {
                    throw new Error(`使用场景 ${usageId} 未配置模型`);
                }

                const model = this.availableModels.find(m => m.id === usage.selectedModelId);
                if (!model) {
                    throw new Error(`模型 ${usage.selectedModelId} 不可用`);
                }

                return `[${model.displayName}] 针对"${prompt}"的回复：\n\n这是一个模拟的AI响应。在实际应用中，这里会调用真实的AI模型API来生成回复。当前使用的是${model.displayName}模型，来自${model.provider}提供商。\n\n使用场景：${usage.name}\n模型类型：${model.modelType}\n响应时间：${Date.now() % 1000}ms`;
            }

            async exportConfiguration() {
                await this.delay(300);
                return JSON.stringify({
                    version: '1.0',
                    exportedAt: new Date().toISOString(),
                    usages: this.usages
                }, null, 2);
            }

            async importConfiguration(configJson) {
                await this.delay(500);
                const config = JSON.parse(configJson);
                if (config.usages && Array.isArray(config.usages)) {
                    this.usages = config.usages;
                } else {
                    throw new Error('无效的配置格式');
                }
            }

            delay(ms) {
                return new Promise(resolve => setTimeout(resolve, ms));
            }
        }

        // 初始化服务
        const mockService = new MockDefaultAIModelService();

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadConfiguration();
        });

        // 加载配置
        async function loadConfiguration() {
            try {
                showLoading('正在加载配置...');
                
                const [usages, models, stats] = await Promise.all([
                    mockService.getDefaultModelUsages(),
                    mockService.getAvailableModels(),
                    mockService.getUsageStats()
                ]);

                updateStats(stats, models.length);
                updateUsageList(usages, models);
                updateTestSelect(usages);
                
                showSuccess('配置加载成功');
            } catch (error) {
                showError('加载配置失败: ' + error.message);
            }
        }

        // 更新统计信息
        function updateStats(stats, availableCount) {
            document.getElementById('totalUsages').textContent = stats.total;
            document.getElementById('configuredUsages').textContent = stats.configured;
            document.getElementById('availableModels').textContent = availableCount;
            document.getElementById('enabledUsages').textContent = stats.enabled;
        }

        // 更新使用场景列表
        function updateUsageList(usages, models) {
            const container = document.getElementById('usageList');
            container.innerHTML = '';

            usages.forEach(usage => {
                const card = document.createElement('div');
                card.className = `usage-card ${!usage.selectedModelId ? 'unconfigured' : ''} ${!usage.enabled ? 'disabled' : ''}`;
                
                const primaryModel = models.find(m => m.id === usage.selectedModelId);
                const fallbackModel = models.find(m => m.id === usage.fallbackModelId);
                
                card.innerHTML = `
                    <div class="usage-title">
                        <span class="category-badge category-${usage.category}">${getCategoryName(usage.category)}</span>
                        ${usage.name}
                        ${!usage.enabled ? ' (已禁用)' : ''}
                    </div>
                    <div class="usage-description">${usage.description}</div>
                    <div class="model-info">
                        <div><strong>主要模型:</strong> 
                            ${primaryModel ? `<span class="model-primary">${primaryModel.displayName} (${primaryModel.provider})</span>` : '未配置'}
                        </div>
                        <div><strong>备用模型:</strong> 
                            ${fallbackModel ? `<span class="model-fallback">${fallbackModel.displayName} (${fallbackModel.provider})</span>` : '未配置'}
                        </div>
                        <div><strong>优先级:</strong> ${usage.priority}</div>
                    </div>
                `;
                
                container.appendChild(card);
            });
        }

        // 更新测试选择框
        function updateTestSelect(usages) {
            const select = document.getElementById('testUsageSelect');
            select.innerHTML = '<option value="">选择使用场景</option>';
            
            usages.filter(u => u.enabled && u.selectedModelId).forEach(usage => {
                const option = document.createElement('option');
                option.value = usage.id;
                option.textContent = `${usage.name} (${getCategoryName(usage.category)})`;
                select.appendChild(option);
            });
        }

        // 一键推荐配置
        async function setRecommendedConfig() {
            try {
                showLoading('正在设置推荐配置...');
                await mockService.setRecommendedConfiguration();
                showSuccess('推荐配置设置成功');
                await loadConfiguration();
            } catch (error) {
                showError('设置推荐配置失败: ' + error.message);
            }
        }

        // 重置为默认
        async function resetToDefaults() {
            try {
                showLoading('正在重置配置...');
                await mockService.resetToDefaults();
                showSuccess('配置重置成功');
                await loadConfiguration();
            } catch (error) {
                showError('重置配置失败: ' + error.message);
            }
        }

        // 测试模型调用
        async function testModelCall() {
            const usageId = document.getElementById('testUsageSelect').value;
            const prompt = document.getElementById('testPrompt').value;
            const output = document.getElementById('testOutput');
            
            if (!usageId) {
                showError('请选择使用场景');
                return;
            }
            
            if (!prompt.trim()) {
                showError('请输入测试提示词');
                return;
            }
            
            try {
                output.textContent = '正在调用AI模型...';
                output.className = 'test-output loading';
                
                const response = await mockService.callModel(usageId, prompt);
                
                output.textContent = response;
                output.className = 'test-output';
            } catch (error) {
                output.textContent = '调用失败: ' + error.message;
                output.className = 'test-output error';
            }
        }

        // 导出配置
        async function exportConfiguration() {
            try {
                showLoading('正在导出配置...');
                const config = await mockService.exportConfiguration();
                document.getElementById('configData').value = config;
                showSuccess('配置导出成功');
            } catch (error) {
                showError('导出配置失败: ' + error.message);
            }
        }

        // 导入配置
        async function importConfiguration() {
            const configData = document.getElementById('configData').value;
            
            if (!configData.trim()) {
                showError('请输入配置数据');
                return;
            }
            
            try {
                showLoading('正在导入配置...');
                await mockService.importConfiguration(configData);
                showSuccess('配置导入成功');
                await loadConfiguration();
            } catch (error) {
                showError('导入配置失败: ' + error.message);
            }
        }

        // 获取分类名称
        function getCategoryName(category) {
            const names = {
                'chat': '对话',
                'translation': '翻译',
                'analysis': '分析',
                'generation': '生成'
            };
            return names[category] || '其他';
        }

        // 显示消息
        function showMessage(message, type) {
            const result = document.getElementById('operationResult');
            result.innerHTML = `<div class="${type}">${message}</div>`;
            setTimeout(() => {
                result.innerHTML = '';
            }, 3000);
        }

        function showLoading(message) {
            showMessage(message, 'loading');
        }

        function showSuccess(message) {
            showMessage(message, 'success');
        }

        function showError(message) {
            showMessage(message, 'error');
        }
    </script>
</body>
</html>