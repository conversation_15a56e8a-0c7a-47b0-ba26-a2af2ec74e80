<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>UI布局统一规范演示</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .demo-section {
            background: white;
            border-radius: 8px;
            padding: 24px;
            margin-bottom: 24px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        
        .demo-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 16px 24px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            background: white;
            margin-bottom: 16px;
        }
        
        .demo-title {
            display: flex;
            align-items: center;
        }
        
        .demo-icon {
            width: 24px;
            height: 24px;
            margin-right: 12px;
            color: #2563eb;
        }
        
        .demo-title h2 {
            margin: 0;
            font-size: 24px;
            font-weight: 600;
            color: #111827;
        }
        
        .demo-description {
            margin: 4px 0 0 0;
            color: #6b7280;
            font-size: 14px;
        }
        
        .demo-actions {
            display: flex;
            gap: 12px;
        }
        
        .btn {
            display: inline-flex;
            align-items: center;
            padding: 8px 16px;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            text-decoration: none;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .btn-primary {
            background: #2563eb;
            color: white;
            border: none;
        }
        
        .btn-primary:hover {
            background: #1d4ed8;
        }
        
        .btn-outline {
            background: white;
            color: #374151;
            border: 1px solid #d1d5db;
        }
        
        .btn-outline:hover {
            background: #f9fafb;
        }
        
        .btn-icon {
            width: 16px;
            height: 16px;
            margin-right: 8px;
        }
        
        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 24px;
            margin-top: 24px;
        }
        
        .before, .after {
            padding: 16px;
            border-radius: 8px;
            border: 2px solid #e5e7eb;
        }
        
        .before {
            border-color: #ef4444;
            background: #fef2f2;
        }
        
        .after {
            border-color: #10b981;
            background: #f0fdf4;
        }
        
        .comparison h3 {
            margin: 0 0 16px 0;
            font-size: 18px;
        }
        
        .before h3 {
            color: #dc2626;
        }
        
        .after h3 {
            color: #059669;
        }
        
        .old-header {
            padding: 16px 0;
            border-bottom: 1px solid #e5e7eb;
            margin-bottom: 16px;
        }
        
        .old-header h2 {
            margin: 0 0 4px 0;
            font-size: 24px;
            color: #111827;
        }
        
        .old-header p {
            margin: 0;
            color: #6b7280;
            font-size: 14px;
        }
        
        .old-actions {
            display: flex;
            gap: 12px;
            margin-top: 16px;
        }
        
        .old-btn {
            padding: 8px 16px;
            border-radius: 6px;
            font-size: 14px;
            text-decoration: none;
            cursor: pointer;
        }
        
        .old-btn-primary {
            background: #3b82f6;
            color: white;
            border: none;
        }
        
        .old-btn-secondary {
            background: white;
            color: #374151;
            border: 1px solid #d1d5db;
        }
        
        .feature-list {
            list-style: none;
            padding: 0;
            margin: 16px 0;
        }
        
        .feature-list li {
            padding: 8px 0;
            display: flex;
            align-items: center;
        }
        
        .feature-list li::before {
            content: "✓";
            color: #10b981;
            font-weight: bold;
            margin-right: 8px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="demo-section">
            <h1>UI布局统一规范演示</h1>
            <p>本演示展示了插件管理页面UI布局统一规范的改进效果。</p>
        </div>

        <!-- 分类管理页面演示 -->
        <div class="demo-section">
            <h2>分类管理页面</h2>
            <div class="demo-header">
                <div>
                    <div class="demo-title">
                        <svg class="demo-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2V7zm0 0V5a2 2 0 012-2h6l2 2h6a2 2 0 012 2v2M7 13h10M7 17h10"></path>
                        </svg>
                        <h2>分类管理</h2>
                    </div>
                    <p class="demo-description">管理您的书签分类，更好地组织收藏内容</p>
                </div>
                <div class="demo-actions">
                    <button class="btn btn-outline">
                        <svg class="btn-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                        </svg>
                        刷新
                    </button>
                    <button class="btn btn-primary">
                        <svg class="btn-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        新建分类
                    </button>
                </div>
            </div>
        </div>

        <!-- 标签管理页面演示 -->
        <div class="demo-section">
            <h2>标签管理页面</h2>
            <div class="demo-header">
                <div>
                    <div class="demo-title">
                        <svg class="demo-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
                        </svg>
                        <h2>标签管理</h2>
                    </div>
                    <p class="demo-description">管理您的书签标签，更好地分类和查找内容</p>
                </div>
                <div class="demo-actions">
                    <button class="btn btn-outline">
                        <svg class="btn-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                        </svg>
                        刷新
                    </button>
                    <button class="btn btn-primary">
                        <svg class="btn-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        新建标签
                    </button>
                </div>
            </div>
        </div>

        <!-- 导入导出页面演示 -->
        <div class="demo-section">
            <h2>导入导出页面</h2>
            <div class="demo-header">
                <div>
                    <div class="demo-title">
                        <svg class="demo-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4"></path>
                        </svg>
                        <h2>导入导出</h2>
                    </div>
                    <p class="demo-description">导出您的收藏数据或从其他来源导入收藏</p>
                </div>
            </div>
        </div>

        <!-- 改进对比 -->
        <div class="demo-section">
            <h2>改进对比</h2>
            <div class="comparison">
                <div class="before">
                    <h3>改进前</h3>
                    <div class="old-header">
                        <h2>标签管理</h2>
                        <p>管理您的书签标签，更好地分类和查找内容</p>
                        <div class="old-actions">
                            <button class="old-btn old-btn-secondary">刷新</button>
                            <button class="old-btn old-btn-primary">新建标签</button>
                        </div>
                    </div>
                    <ul class="feature-list">
                        <li style="color: #dc2626;">❌ 没有统一的Card容器</li>
                        <li style="color: #dc2626;">❌ 缺少识别性图标</li>
                        <li style="color: #dc2626;">❌ 按钮样式不统一</li>
                        <li style="color: #dc2626;">❌ 布局不一致</li>
                    </ul>
                </div>
                
                <div class="after">
                    <h3>改进后</h3>
                    <div class="demo-header">
                        <div>
                            <div class="demo-title">
                                <svg class="demo-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
                                </svg>
                                <h2>标签管理</h2>
                            </div>
                            <p class="demo-description">管理您的书签标签，更好地分类和查找内容</p>
                        </div>
                        <div class="demo-actions">
                            <button class="btn btn-outline">刷新</button>
                            <button class="btn btn-primary">新建标签</button>
                        </div>
                    </div>
                    <ul class="feature-list">
                        <li>统一的Card容器布局</li>
                        <li>每个页面都有独特图标</li>
                        <li>shadcn/ui统一按钮样式</li>
                        <li>一致的视觉层次结构</li>
                        <li>更好的用户体验</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- 技术实现 -->
        <div class="demo-section">
            <h2>技术实现要点</h2>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 24px;">
                <div>
                    <h3>组件统一</h3>
                    <ul class="feature-list">
                        <li>使用shadcn/ui Card组件</li>
                        <li>统一Button组件样式</li>
                        <li>lucide-react图标库</li>
                        <li>一致的颜色主题</li>
                    </ul>
                </div>
                
                <div>
                    <h3>布局规范</h3>
                    <ul class="feature-list">
                        <li>图标 + 标题 + 描述格式</li>
                        <li>右侧操作按钮区域</li>
                        <li>统一的间距和尺寸</li>
                        <li>响应式设计</li>
                    </ul>
                </div>
                
                <div>
                    <h3>用户体验</h3>
                    <ul class="feature-list">
                        <li>视觉一致性</li>
                        <li>更好的可识别性</li>
                        <li>统一的交互模式</li>
                        <li>无障碍访问支持</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</body>
</html>