<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI文本生成组件演示</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
            color: white;
            padding: 40px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 15px;
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .content {
            padding: 40px;
        }

        .demo-section {
            margin-bottom: 40px;
            padding: 30px;
            border: 2px solid #e5e7eb;
            border-radius: 15px;
            background: #f9fafb;
        }

        .demo-section h2 {
            color: #4f46e5;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .demo-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-top: 20px;
        }

        .demo-card {
            background: white;
            padding: 25px;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.05);
            border: 1px solid #e5e7eb;
        }

        .demo-card h3 {
            color: #374151;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #374151;
        }

        .form-group input,
        .form-group textarea,
        .form-group select {
            width: 100%;
            padding: 12px;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.2s;
        }

        .form-group input:focus,
        .form-group textarea:focus,
        .form-group select:focus {
            outline: none;
            border-color: #4f46e5;
        }

        .ai-generator {
            position: relative;
        }

        .ai-button {
            position: absolute;
            top: 8px;
            right: 8px;
            background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 6px;
            font-size: 12px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 4px;
            transition: transform 0.2s;
        }

        .ai-button:hover {
            transform: translateY(-1px);
        }

        .ai-button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        .ai-preview {
            margin-top: 15px;
            padding: 15px;
            background: #f0f9ff;
            border: 1px solid #0ea5e9;
            border-radius: 8px;
            display: none;
        }

        .ai-preview.show {
            display: block;
        }

        .ai-preview-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 10px;
        }

        .ai-preview-content {
            color: #374151;
            font-size: 14px;
            line-height: 1.5;
        }

        .ai-actions {
            display: flex;
            gap: 8px;
            margin-top: 10px;
        }

        .ai-actions button {
            padding: 4px 8px;
            border: none;
            border-radius: 4px;
            font-size: 12px;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .accept-btn {
            background: #10b981;
            color: white;
        }

        .reject-btn {
            background: #ef4444;
            color: white;
        }

        .regenerate-btn {
            background: #6b7280;
            color: white;
        }

        .suggestions {
            margin-top: 15px;
        }

        .suggestions-title {
            font-size: 12px;
            color: #6b7280;
            margin-bottom: 8px;
        }

        .suggestion-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 6px;
        }

        .suggestion-tag {
            background: #e0e7ff;
            color: #4f46e5;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .suggestion-tag:hover {
            background: #c7d2fe;
        }

        .feature-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }

        .feature-item {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
            border-left: 4px solid #4f46e5;
        }

        .feature-item h4 {
            color: #4f46e5;
            margin-bottom: 10px;
        }

        .feature-item p {
            color: #6b7280;
            font-size: 14px;
        }

        .status-indicator {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-ready {
            background: #10b981;
        }

        .status-generating {
            background: #f59e0b;
            animation: pulse 1.5s infinite;
        }

        .status-error {
            background: #ef4444;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        .spinning {
            animation: spin 1s linear infinite;
        }

        @media (max-width: 768px) {
            .demo-grid {
                grid-template-columns: 1fr;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .content {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>
                ✨ AI文本生成组件演示
            </h1>
            <p>为收藏管理页面提供智能文本生成功能</p>
        </div>

        <div class="content">
            <!-- 功能介绍 -->
            <div class="demo-section">
                <h2>🎯 功能特性</h2>
                <div class="feature-list">
                    <div class="feature-item">
                        <h4>智能描述生成</h4>
                        <p>基于标题、网址和分类信息，自动生成准确的收藏描述</p>
                    </div>
                    <div class="feature-item">
                        <h4>个性化笔记</h4>
                        <p>根据收藏内容生成有用的笔记和使用建议</p>
                    </div>
                    <div class="feature-item">
                        <h4>多种建议</h4>
                        <p>提供多个生成选项，用户可以选择最合适的内容</p>
                    </div>
                    <div class="feature-item">
                        <h4>实时预览</h4>
                        <p>生成内容实时预览，支持接受、拒绝或重新生成</p>
                    </div>
                </div>
            </div>

            <!-- 描述生成演示 -->
            <div class="demo-section">
                <h2>📝 描述生成演示</h2>
                <div class="demo-grid">
                    <div class="demo-card">
                        <h3>📋 收藏信息</h3>
                        <div class="form-group">
                            <label>标题</label>
                            <input type="text" id="desc-title" value="React官方文档" placeholder="请输入收藏标题">
                        </div>
                        <div class="form-group">
                            <label>网址</label>
                            <input type="url" id="desc-url" value="https://react.dev" placeholder="https://example.com">
                        </div>
                        <div class="form-group">
                            <label>分类</label>
                            <select id="desc-category">
                                <option value="开发工具">开发工具</option>
                                <option value="学习资源">学习资源</option>
                                <option value="工作">工作</option>
                                <option value="娱乐">娱乐</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="demo-card">
                        <h3>🤖 AI生成描述</h3>
                        <div class="form-group ai-generator">
                            <label>
                                <span class="status-indicator status-ready"></span>
                                描述内容
                            </label>
                            <textarea id="desc-content" rows="4" placeholder="点击AI生成按钮自动生成描述..."></textarea>
                            <button class="ai-button" onclick="generateDescription()">
                                <span id="desc-icon">✨</span>
                                <span id="desc-text">AI生成</span>
                            </button>
                        </div>
                        
                        <div id="desc-preview" class="ai-preview">
                            <div class="ai-preview-header">
                                <strong>🎯 AI生成的描述</strong>
                            </div>
                            <div class="ai-preview-content" id="desc-preview-content"></div>
                            <div class="ai-actions">
                                <button class="accept-btn" onclick="acceptDescription()">✅ 接受</button>
                                <button class="reject-btn" onclick="rejectDescription()">❌ 拒绝</button>
                                <button class="regenerate-btn" onclick="generateDescription()">🔄 重新生成</button>
                            </div>
                        </div>
                        
                        <div class="suggestions">
                            <div class="suggestions-title">💡 AI建议：</div>
                            <div class="suggestion-tags">
                                <span class="suggestion-tag" onclick="applySuggestion('desc-content', '简化版：React官方开发文档和学习资源')">简化版</span>
                                <span class="suggestion-tag" onclick="applySuggestion('desc-content', '详细版：React官方文档，包含完整的API参考和最佳实践指南')">详细版</span>
                                <span class="suggestion-tag" onclick="applySuggestion('desc-content', '推荐：React开发者必备的官方文档资源')">推荐版</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 笔记生成演示 -->
            <div class="demo-section">
                <h2>📓 笔记生成演示</h2>
                <div class="demo-grid">
                    <div class="demo-card">
                        <h3>📋 收藏上下文</h3>
                        <div class="form-group">
                            <label>标题</label>
                            <input type="text" id="notes-title" value="GitHub" placeholder="请输入收藏标题">
                        </div>
                        <div class="form-group">
                            <label>描述</label>
                            <textarea id="notes-desc" rows="2" placeholder="收藏的描述信息">全球最大的代码托管平台，提供Git版本控制和协作开发功能</textarea>
                        </div>
                        <div class="form-group">
                            <label>分类</label>
                            <select id="notes-category">
                                <option value="开发工具">开发工具</option>
                                <option value="学习资源">学习资源</option>
                                <option value="工作">工作</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="demo-card">
                        <h3>🤖 AI生成笔记</h3>
                        <div class="form-group ai-generator">
                            <label>
                                <span class="status-indicator status-ready"></span>
                                个人笔记
                            </label>
                            <textarea id="notes-content" rows="5" placeholder="点击AI生成按钮自动生成笔记..."></textarea>
                            <button class="ai-button" onclick="generateNotes()">
                                <span id="notes-icon">✨</span>
                                <span id="notes-text">AI生成</span>
                            </button>
                        </div>
                        
                        <div id="notes-preview" class="ai-preview">
                            <div class="ai-preview-header">
                                <strong>📝 AI生成的笔记</strong>
                            </div>
                            <div class="ai-preview-content" id="notes-preview-content"></div>
                            <div class="ai-actions">
                                <button class="accept-btn" onclick="acceptNotes()">✅ 接受</button>
                                <button class="reject-btn" onclick="rejectNotes()">❌ 拒绝</button>
                                <button class="regenerate-btn" onclick="generateNotes()">🔄 重新生成</button>
                            </div>
                        </div>
                        
                        <div class="suggestions">
                            <div class="suggestions-title">💡 使用建议：</div>
                            <div class="suggestion-tags">
                                <span class="suggestion-tag" onclick="applySuggestion('notes-content', '记得定期备份重要代码到GitHub')">备份提醒</span>
                                <span class="suggestion-tag" onclick="applySuggestion('notes-content', '可以用来展示个人项目作品集')">作品集</span>
                                <span class="suggestion-tag" onclick="applySuggestion('notes-content', '学习开源项目的好地方')">学习资源</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 集成测试演示 -->
            <div class="demo-section">
                <h2>🔧 完整功能测试</h2>
                <p style="margin-bottom: 20px; color: #6b7280;">
                    这个演示展示了AI生成组件在实际收藏编辑场景中的完整工作流程
                </p>
                
                <div class="demo-card">
                    <h3>🎯 收藏编辑模拟</h3>
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                        <div>
                            <div class="form-group">
                                <label>收藏标题</label>
                                <input type="text" id="full-title" value="MDN Web Docs" placeholder="请输入标题">
                            </div>
                            <div class="form-group">
                                <label>网址</label>
                                <input type="url" id="full-url" value="https://developer.mozilla.org" placeholder="https://example.com">
                            </div>
                            <div class="form-group">
                                <label>分类</label>
                                <select id="full-category">
                                    <option value="学习资源">学习资源</option>
                                    <option value="开发工具">开发工具</option>
                                    <option value="参考文档">参考文档</option>
                                </select>
                            </div>
                        </div>
                        
                        <div>
                            <div class="form-group ai-generator">
                                <label>
                                    <span class="status-indicator status-ready"></span>
                                    描述 (AI辅助)
                                </label>
                                <textarea id="full-desc" rows="3" placeholder="AI将根据上述信息生成描述..."></textarea>
                                <button class="ai-button" onclick="generateFullDescription()">
                                    <span>✨</span> AI生成
                                </button>
                            </div>
                            
                            <div class="form-group ai-generator">
                                <label>
                                    <span class="status-indicator status-ready"></span>
                                    笔记 (AI辅助)
                                </label>
                                <textarea id="full-notes" rows="3" placeholder="AI将生成有用的笔记和建议..."></textarea>
                                <button class="ai-button" onclick="generateFullNotes()">
                                    <span>✨</span> AI生成
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <div style="margin-top: 20px; padding: 15px; background: #f0f9ff; border-radius: 8px;">
                        <strong>💡 使用提示：</strong>
                        <ul style="margin: 10px 0 0 20px; color: #374151;">
                            <li>先填写基础信息（标题、网址、分类）</li>
                            <li>点击"AI生成"按钮自动生成描述</li>
                            <li>基于描述内容再生成个人笔记</li>
                            <li>可以多次生成直到满意为止</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 模拟AI生成延迟
        const AI_DELAY = 1500;

        // 模拟AI响应数据
        const mockResponses = {
            description: {
                'react.dev': 'React官方文档，提供完整的API参考、教程和最佳实践指南，是React开发者必备的学习和参考资源。',
                'github.com': '全球最大的代码托管和协作开发平台，支持Git版本控制，提供项目管理、代码审查和持续集成功能。',
                'developer.mozilla.org': 'MDN Web文档，权威的Web开发技术文档，涵盖HTML、CSS、JavaScript等前端技术的详细说明和示例。'
            },
            notes: {
                'react.dev': '学习React的最佳起点，建议从基础概念开始，逐步深入Hooks和状态管理。定期查看更新内容，跟上React生态发展。',
                'github.com': '除了代码托管，还可以用来：1) 展示个人项目作品集 2) 参与开源项目贡献 3) 学习优秀代码实现 4) 建立技术影响力',
                'developer.mozilla.org': '前端开发的权威参考，遇到技术问题时首选查询地。建议收藏常用API页面，利用搜索功能快速定位信息。'
            }
        };

        function getAIResponse(type, url) {
            const domain = new URL(url).hostname.replace('www.', '');
            return mockResponses[type][domain] || `这是一个关于${domain}的${type === 'description' ? '优质资源' : '使用建议'}。`;
        }

        function setGeneratingState(buttonId, iconId, textId) {
            const button = document.getElementById(buttonId);
            const icon = document.getElementById(iconId);
            const text = document.getElementById(textId);
            
            button.disabled = true;
            icon.textContent = '⏳';
            icon.classList.add('spinning');
            text.textContent = '生成中...';
            
            // 更新状态指示器
            const indicator = button.parentElement.querySelector('.status-indicator');
            if (indicator) {
                indicator.className = 'status-indicator status-generating';
            }
        }

        function resetGeneratingState(buttonId, iconId, textId) {
            const button = document.getElementById(buttonId);
            const icon = document.getElementById(iconId);
            const text = document.getElementById(textId);
            
            button.disabled = false;
            icon.textContent = '✨';
            icon.classList.remove('spinning');
            text.textContent = 'AI生成';
            
            // 更新状态指示器
            const indicator = button.parentElement.querySelector('.status-indicator');
            if (indicator) {
                indicator.className = 'status-indicator status-ready';
            }
        }

        function showPreview(previewId, content) {
            const preview = document.getElementById(previewId);
            const previewContent = document.getElementById(previewId + '-content');
            
            previewContent.textContent = content;
            preview.classList.add('show');
        }

        function hidePreview(previewId) {
            const preview = document.getElementById(previewId);
            preview.classList.remove('show');
        }

        function applySuggestion(targetId, content) {
            document.getElementById(targetId).value = content;
        }

        // 描述生成功能
        async function generateDescription() {
            setGeneratingState('desc-text', 'desc-icon', 'desc-text');
            
            const title = document.getElementById('desc-title').value;
            const url = document.getElementById('desc-url').value;
            
            setTimeout(() => {
                const content = getAIResponse('description', url);
                showPreview('desc-preview', content);
                resetGeneratingState('desc-text', 'desc-icon', 'desc-text');
            }, AI_DELAY);
        }

        function acceptDescription() {
            const content = document.getElementById('desc-preview-content').textContent;
            document.getElementById('desc-content').value = content;
            hidePreview('desc-preview');
        }

        function rejectDescription() {
            hidePreview('desc-preview');
        }

        // 笔记生成功能
        async function generateNotes() {
            setGeneratingState('notes-text', 'notes-icon', 'notes-text');
            
            const title = document.getElementById('notes-title').value;
            const url = 'https://github.com'; // 模拟URL
            
            setTimeout(() => {
                const content = getAIResponse('notes', url);
                showPreview('notes-preview', content);
                resetGeneratingState('notes-text', 'notes-icon', 'notes-text');
            }, AI_DELAY);
        }

        function acceptNotes() {
            const content = document.getElementById('notes-preview-content').textContent;
            document.getElementById('notes-content').value = content;
            hidePreview('notes-preview');
        }

        function rejectNotes() {
            hidePreview('notes-preview');
        }

        // 完整功能测试
        async function generateFullDescription() {
            const button = event.target;
            const originalText = button.innerHTML;
            button.innerHTML = '<span>⏳</span> 生成中...';
            button.disabled = true;
            
            const url = document.getElementById('full-url').value;
            
            setTimeout(() => {
                const content = getAIResponse('description', url);
                document.getElementById('full-desc').value = content;
                button.innerHTML = originalText;
                button.disabled = false;
            }, AI_DELAY);
        }

        async function generateFullNotes() {
            const button = event.target;
            const originalText = button.innerHTML;
            button.innerHTML = '<span>⏳</span> 生成中...';
            button.disabled = true;
            
            const url = document.getElementById('full-url').value;
            
            setTimeout(() => {
                const content = getAIResponse('notes', url);
                document.getElementById('full-notes').value = content;
                button.innerHTML = originalText;
                button.disabled = false;
            }, AI_DELAY);
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('AI文本生成组件演示页面已加载');
        });
    </script>
</body>
</html>