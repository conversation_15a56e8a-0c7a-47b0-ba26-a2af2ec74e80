<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI推荐功能演示</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            background: white;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .header h1 {
            color: #1a1a1a;
            margin-bottom: 8px;
        }
        
        .header p {
            color: #666;
            font-size: 16px;
        }
        
        .demo-section {
            margin-bottom: 30px;
        }
        
        .demo-section h2 {
            color: #333;
            border-bottom: 2px solid #e0e0e0;
            padding-bottom: 8px;
            margin-bottom: 20px;
        }
        
        .input-group {
            margin-bottom: 16px;
        }
        
        .input-group label {
            display: block;
            margin-bottom: 4px;
            font-weight: 500;
            color: #333;
        }
        
        .input-group input,
        .input-group textarea {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 14px;
            box-sizing: border-box;
        }
        
        .input-group textarea {
            resize: vertical;
            min-height: 80px;
        }
        
        .button-group {
            display: flex;
            gap: 12px;
            margin: 20px 0;
        }
        
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.2s;
        }
        
        .btn-primary {
            background: #007bff;
            color: white;
        }
        
        .btn-primary:hover {
            background: #0056b3;
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #545b62;
        }
        
        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }
        
        .results {
            margin-top: 20px;
        }
        
        .result-card {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 16px;
        }
        
        .result-card h3 {
            margin: 0 0 12px 0;
            color: #495057;
            font-size: 16px;
        }
        
        .tag-list {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-bottom: 12px;
        }
        
        .tag {
            background: #e3f2fd;
            color: #1976d2;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .tag.new {
            background: #fff3e0;
            color: #f57c00;
        }
        
        .folder-list {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }
        
        .folder-item {
            display: flex;
            justify-content: between;
            align-items: center;
            background: #e8f5e8;
            color: #2e7d32;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 14px;
        }
        
        .confidence {
            font-size: 12px;
            opacity: 0.8;
            margin-left: auto;
        }
        
        .loading {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
            color: #666;
        }
        
        .loading::before {
            content: '';
            width: 20px;
            height: 20px;
            border: 2px solid #e0e0e0;
            border-top: 2px solid #007bff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 8px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .error {
            background: #ffebee;
            color: #c62828;
            padding: 12px;
            border-radius: 6px;
            margin: 12px 0;
        }
        
        .info {
            background: #e3f2fd;
            color: #1976d2;
            padding: 12px;
            border-radius: 6px;
            margin: 12px 0;
        }
        
        .example-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-bottom: 16px;
        }
        
        .example-btn {
            background: #f0f0f0;
            border: 1px solid #ddd;
            padding: 6px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.2s;
        }
        
        .example-btn:hover {
            background: #e0e0e0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 AI智能推荐功能演示</h1>
            <p>体验基于AI的标签和文件夹推荐功能</p>
        </div>

        <div class="info">
            <strong>功能说明：</strong>
            <ul>
                <li>🏷️ <strong>标签推荐</strong>：优先推荐现有标签，避免重复标签</li>
                <li>📁 <strong>文件夹推荐</strong>：只推荐现有文件夹，不创建新文件夹</li>
                <li>🧠 <strong>智能分析</strong>：基于标题、URL、内容进行AI分析</li>
                <li>⚡ <strong>降级策略</strong>：AI不可用时使用本地规则推荐</li>
            </ul>
        </div>

        <div class="demo-section">
            <h2>📝 输入内容</h2>
            
            <div class="example-buttons">
                <button class="example-btn" onclick="loadExample('tech')">技术文档示例</button>
                <button class="example-btn" onclick="loadExample('learning')">学习资源示例</button>
                <button class="example-btn" onclick="loadExample('tool')">工具软件示例</button>
                <button class="example-btn" onclick="loadExample('news')">新闻资讯示例</button>
                <button class="example-btn" onclick="clearInputs()">清空</button>
            </div>
            
            <div class="input-group">
                <label for="title">标题</label>
                <input type="text" id="title" placeholder="请输入收藏标题">
            </div>
            
            <div class="input-group">
                <label for="url">网址</label>
                <input type="url" id="url" placeholder="https://example.com">
            </div>
            
            <div class="input-group">
                <label for="description">描述</label>
                <textarea id="description" placeholder="请输入内容描述"></textarea>
            </div>
            
            <div class="input-group">
                <label for="content">详细内容</label>
                <textarea id="content" placeholder="请输入详细内容（可选）"></textarea>
            </div>
            
            <div class="button-group">
                <button class="btn btn-primary" onclick="getRecommendations()" id="recommendBtn">
                    🤖 获取AI推荐
                </button>
                <button class="btn btn-secondary" onclick="getTagsOnly()">
                    🏷️ 仅推荐标签
                </button>
                <button class="btn btn-secondary" onclick="getFoldersOnly()">
                    📁 仅推荐文件夹
                </button>
            </div>
        </div>

        <div class="demo-section">
            <h2>🎯 推荐结果</h2>
            <div id="results"></div>
        </div>
    </div>

    <script>
        // 示例数据
        const examples = {
            tech: {
                title: 'React 18 新特性详解',
                url: 'https://react.dev/blog/2022/03/29/react-v18',
                description: 'React 18 引入了并发渲染、自动批处理、Suspense 改进等重要特性',
                content: 'React 18 是一个重要的版本更新，引入了并发渲染功能，允许 React 在后台准备新的 UI，而不会阻塞主线程。新的自动批处理功能可以减少渲染次数，提高性能。Suspense 组件也得到了改进，现在支持服务端渲染。'
            },
            learning: {
                title: 'JavaScript 基础教程',
                url: 'https://javascript.info/',
                description: '从零开始学习 JavaScript 编程语言的完整教程',
                content: '这是一个全面的 JavaScript 学习资源，涵盖了从基础语法到高级概念的所有内容。包括变量、函数、对象、异步编程、DOM 操作等核心知识点。适合初学者和有经验的开发者参考。'
            },
            tool: {
                title: 'VS Code 插件推荐',
                url: 'https://marketplace.visualstudio.com/',
                description: '提高开发效率的 VS Code 插件集合',
                content: '推荐一些实用的 VS Code 插件，包括代码格式化工具 Prettier、代码检查工具 ESLint、Git 管理工具 GitLens、主题插件等。这些插件可以显著提高开发效率和代码质量。'
            },
            news: {
                title: 'AI 技术最新发展动态',
                url: 'https://news.example.com/ai-trends',
                description: '2024年人工智能技术发展趋势和最新突破',
                content: '人工智能技术在2024年取得了重大突破，包括大语言模型的进步、多模态AI的发展、AI在各行业的应用等。本文分析了当前AI技术的发展趋势和未来展望。'
            }
        }

        // 模拟现有标签和分类数据
        const mockExistingTags = [
            '技术', '学习', '工具', '前端', 'JavaScript', 'React', 'Vue', 
            '后端', 'Python', 'Java', '数据库', 'AI', '机器学习',
            '设计', 'UI', 'UX', '产品', '管理', '创业', '投资',
            '健康', '旅游', '美食', '电影', '音乐', '游戏', '体育'
        ]

        const mockExistingCategories = [
            '技术文档', '学习资源', '工具软件', '设计素材', '新闻资讯',
            '娱乐休闲', '生活服务', '商业财经', '健康医疗', '教育培训'
        ]

        // 加载示例数据
        function loadExample(type) {
            const example = examples[type]
            if (example) {
                document.getElementById('title').value = example.title
                document.getElementById('url').value = example.url
                document.getElementById('description').value = example.description
                document.getElementById('content').value = example.content
            }
        }

        // 清空输入
        function clearInputs() {
            document.getElementById('title').value = ''
            document.getElementById('url').value = ''
            document.getElementById('description').value = ''
            document.getElementById('content').value = ''
            document.getElementById('results').innerHTML = ''
        }

        // 显示加载状态
        function showLoading() {
            document.getElementById('results').innerHTML = '<div class="loading">AI正在分析内容...</div>'
            document.getElementById('recommendBtn').disabled = true
        }

        // 隐藏加载状态
        function hideLoading() {
            document.getElementById('recommendBtn').disabled = false
        }

        // 显示错误
        function showError(message) {
            document.getElementById('results').innerHTML = `<div class="error">❌ ${message}</div>`
        }

        // 模拟AI推荐服务
        function simulateAIRecommendation(request) {
            return new Promise((resolve) => {
                setTimeout(() => {
                    const content = `${request.title || ''} ${request.description || ''} ${request.content || ''}`.toLowerCase()
                    
                    // 模拟标签推荐
                    const existingTags = []
                    const newTags = []
                    
                    // 基于关键词匹配现有标签
                    mockExistingTags.forEach(tag => {
                        if (content.includes(tag.toLowerCase()) || 
                            (request.title && request.title.toLowerCase().includes(tag.toLowerCase()))) {
                            existingTags.push(tag)
                        }
                    })
                    
                    // 基于内容生成新标签
                    if (content.includes('react') && !existingTags.includes('React')) newTags.push('React组件')
                    if (content.includes('javascript') && !existingTags.includes('JavaScript')) newTags.push('JS开发')
                    if (content.includes('教程') && !existingTags.includes('教程')) newTags.push('教程')
                    if (content.includes('插件') && !existingTags.includes('插件')) newTags.push('插件')
                    if (content.includes('ai') && !existingTags.includes('AI')) newTags.push('人工智能')
                    
                    // 限制数量
                    const finalExistingTags = existingTags.slice(0, 4)
                    const finalNewTags = newTags.slice(0, 2)
                    
                    // 模拟文件夹推荐
                    const recommendedFolders = []
                    
                    mockExistingCategories.forEach((category, index) => {
                        let confidence = 0
                        let reason = ''
                        
                        if (content.includes('技术') || content.includes('javascript') || content.includes('react')) {
                            if (category === '技术文档') {
                                confidence = 0.9
                                reason = '内容包含技术相关信息'
                            } else if (category === '学习资源') {
                                confidence = 0.7
                                reason = '适合技术学习参考'
                            }
                        } else if (content.includes('学习') || content.includes('教程')) {
                            if (category === '学习资源') {
                                confidence = 0.9
                                reason = '包含学习相关内容'
                            } else if (category === '教育培训') {
                                confidence = 0.8
                                reason = '适合教育培训使用'
                            }
                        } else if (content.includes('工具') || content.includes('插件')) {
                            if (category === '工具软件') {
                                confidence = 0.9
                                reason = '包含工具软件信息'
                            }
                        } else if (content.includes('新闻') || content.includes('动态')) {
                            if (category === '新闻资讯') {
                                confidence = 0.9
                                reason = '新闻资讯类内容'
                            }
                        }
                        
                        if (confidence > 0) {
                            recommendedFolders.push({
                                name: category,
                                confidence,
                                reason
                            })
                        }
                    })
                    
                    // 按置信度排序并限制数量
                    recommendedFolders.sort((a, b) => b.confidence - a.confidence)
                    const finalFolders = recommendedFolders.slice(0, 3)
                    
                    resolve({
                        tags: {
                            existingTags: finalExistingTags,
                            newTags: finalNewTags,
                            confidence: 0.85,
                            reasoning: '基于内容关键词分析和现有标签匹配'
                        },
                        folders: {
                            recommendedFolders: finalFolders,
                            reasoning: '基于内容特征和分类匹配度分析'
                        }
                    })
                }, 1500) // 模拟网络延迟
            })
        }

        // 获取完整推荐
        async function getRecommendations() {
            const request = {
                title: document.getElementById('title').value,
                url: document.getElementById('url').value,
                description: document.getElementById('description').value,
                content: document.getElementById('content').value
            }
            
            if (!request.title && !request.description && !request.content) {
                showError('请至少填写标题、描述或内容中的一项')
                return
            }
            
            showLoading()
            
            try {
                const result = await simulateAIRecommendation(request)
                displayResults(result)
            } catch (error) {
                showError('获取推荐失败: ' + error.message)
            } finally {
                hideLoading()
            }
        }

        // 仅获取标签推荐
        async function getTagsOnly() {
            const request = {
                title: document.getElementById('title').value,
                description: document.getElementById('description').value,
                content: document.getElementById('content').value
            }
            
            if (!request.title && !request.description && !request.content) {
                showError('请至少填写标题、描述或内容中的一项')
                return
            }
            
            showLoading()
            
            try {
                const result = await simulateAIRecommendation(request)
                displayResults({ tags: result.tags })
            } catch (error) {
                showError('获取标签推荐失败: ' + error.message)
            } finally {
                hideLoading()
            }
        }

        // 仅获取文件夹推荐
        async function getFoldersOnly() {
            const request = {
                title: document.getElementById('title').value,
                description: document.getElementById('description').value,
                content: document.getElementById('content').value
            }
            
            if (!request.title && !request.description && !request.content) {
                showError('请至少填写标题、描述或内容中的一项')
                return
            }
            
            showLoading()
            
            try {
                const result = await simulateAIRecommendation(request)
                displayResults({ folders: result.folders })
            } catch (error) {
                showError('获取文件夹推荐失败: ' + error.message)
            } finally {
                hideLoading()
            }
        }

        // 显示推荐结果
        function displayResults(result) {
            let html = ''
            
            if (result.tags) {
                html += '<div class="result-card">'
                html += '<h3>🏷️ 推荐标签</h3>'
                
                if (result.tags.existingTags && result.tags.existingTags.length > 0) {
                    html += '<div style="margin-bottom: 12px;"><strong>现有标签：</strong></div>'
                    html += '<div class="tag-list">'
                    result.tags.existingTags.forEach(tag => {
                        html += `<span class="tag">${tag}</span>`
                    })
                    html += '</div>'
                }
                
                if (result.tags.newTags && result.tags.newTags.length > 0) {
                    html += '<div style="margin-bottom: 12px;"><strong>新标签建议：</strong></div>'
                    html += '<div class="tag-list">'
                    result.tags.newTags.forEach(tag => {
                        html += `<span class="tag new">${tag}</span>`
                    })
                    html += '</div>'
                }
                
                if (result.tags.reasoning) {
                    html += `<div style="font-size: 12px; color: #666; margin-top: 12px;">💡 ${result.tags.reasoning}</div>`
                }
                
                html += '</div>'
            }
            
            if (result.folders) {
                html += '<div class="result-card">'
                html += '<h3>📁 推荐文件夹</h3>'
                
                if (result.folders.recommendedFolders && result.folders.recommendedFolders.length > 0) {
                    html += '<div class="folder-list">'
                    result.folders.recommendedFolders.forEach(folder => {
                        const confidenceText = folder.confidence >= 0.8 ? '高' : folder.confidence >= 0.6 ? '中' : '低'
                        html += `<div class="folder-item">
                            <span>📁 ${folder.name}</span>
                            <span class="confidence">置信度: ${confidenceText}</span>
                        </div>`
                        if (folder.reason) {
                            html += `<div style="font-size: 12px; color: #666; margin-left: 20px; margin-top: -4px;">理由: ${folder.reason}</div>`
                        }
                    })
                    html += '</div>'
                } else {
                    html += '<div style="color: #666;">暂无推荐的文件夹</div>'
                }
                
                if (result.folders.reasoning) {
                    html += `<div style="font-size: 12px; color: #666; margin-top: 12px;">💡 ${result.folders.reasoning}</div>`
                }
                
                html += '</div>'
            }
            
            if (!html) {
                html = '<div class="info">暂无推荐结果</div>'
            }
            
            document.getElementById('results').innerHTML = html
        }

        // 页面加载完成后加载默认示例
        window.addEventListener('load', () => {
            loadExample('tech')
        })
    </script>
</body>
</html>