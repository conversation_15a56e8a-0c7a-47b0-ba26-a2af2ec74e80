<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Obsidian集成功能演示 - Universe Bag</title>
    <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    <script src="https://unpkg.com/lucide-react@latest/dist/umd/lucide-react.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f8fafc;
        }
        .demo-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        .demo-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 24px;
            text-align: center;
        }
        .demo-content {
            padding: 24px;
        }
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .feature-card {
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 20px;
            background: #f8fafc;
        }
        .feature-card h3 {
            margin: 0 0 12px 0;
            color: #1e293b;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .feature-card p {
            margin: 0 0 16px 0;
            color: #64748b;
            line-height: 1.6;
        }
        .feature-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        .feature-list li {
            padding: 4px 0;
            color: #475569;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .feature-list li:before {
            content: "✓";
            color: #10b981;
            font-weight: bold;
        }
        .demo-section {
            margin: 32px 0;
            padding: 24px;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            background: white;
        }
        .demo-section h2 {
            margin: 0 0 16px 0;
            color: #1e293b;
            display: flex;
            align-items: center;
            gap: 12px;
        }
        .code-block {
            background: #1e293b;
            color: #e2e8f0;
            padding: 16px;
            border-radius: 6px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 14px;
            line-height: 1.5;
            overflow-x: auto;
            margin: 16px 0;
        }
        .settings-demo {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        .setting-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            background: #f8fafc;
        }
        .setting-label {
            font-weight: 500;
            color: #374151;
        }
        .setting-description {
            font-size: 12px;
            color: #6b7280;
            margin-top: 4px;
        }
        .toggle-switch {
            width: 44px;
            height: 24px;
            background: #10b981;
            border-radius: 12px;
            position: relative;
            cursor: pointer;
        }
        .toggle-switch:before {
            content: '';
            position: absolute;
            width: 20px;
            height: 20px;
            background: white;
            border-radius: 50%;
            top: 2px;
            right: 2px;
            transition: transform 0.2s;
        }
        .toggle-switch.off {
            background: #d1d5db;
        }
        .toggle-switch.off:before {
            transform: translateX(-20px);
        }
        .sync-result {
            background: #f0f9ff;
            border: 1px solid #0ea5e9;
            border-radius: 8px;
            padding: 16px;
            margin: 16px 0;
        }
        .sync-stats {
            display: flex;
            justify-content: space-around;
            margin: 16px 0;
        }
        .sync-stat {
            text-align: center;
        }
        .sync-stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #10b981;
        }
        .sync-stat-label {
            font-size: 12px;
            color: #6b7280;
            margin-top: 4px;
        }
        .template-editor {
            border: 1px solid #d1d5db;
            border-radius: 6px;
            padding: 12px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 14px;
            line-height: 1.5;
            background: #f9fafb;
            min-height: 200px;
            resize: vertical;
        }
        .vault-selector {
            display: flex;
            align-items: center;
            gap: 12px;
            margin: 16px 0;
        }
        .vault-option {
            flex: 1;
            padding: 12px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            background: white;
            cursor: pointer;
            transition: all 0.2s;
        }
        .vault-option:hover {
            border-color: #3b82f6;
            background: #f0f9ff;
        }
        .vault-option.selected {
            border-color: #3b82f6;
            background: #eff6ff;
        }
        .vault-name {
            font-weight: 500;
            color: #1f2937;
        }
        .vault-path {
            font-size: 12px;
            color: #6b7280;
            margin-top: 4px;
        }
        .action-buttons {
            display: flex;
            gap: 12px;
            margin: 20px 0;
            flex-wrap: wrap;
        }
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .btn-primary {
            background: #3b82f6;
            color: white;
        }
        .btn-primary:hover {
            background: #2563eb;
        }
        .btn-secondary {
            background: #f3f4f6;
            color: #374151;
            border: 1px solid #d1d5db;
        }
        .btn-secondary:hover {
            background: #e5e7eb;
        }
        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
        .format-selector {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 16px;
            margin: 20px 0;
        }
        .format-option {
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            padding: 20px;
            cursor: pointer;
            transition: all 0.2s;
            text-align: center;
        }
        .format-option:hover {
            border-color: #3b82f6;
        }
        .format-option.selected {
            border-color: #3b82f6;
            background: #eff6ff;
        }
        .format-icon {
            width: 48px;
            height: 48px;
            margin: 0 auto 12px;
            background: #f3f4f6;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .format-title {
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 8px;
        }
        .format-description {
            font-size: 14px;
            color: #6b7280;
            line-height: 1.4;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <div class="demo-header">
            <h1>🔗 Obsidian集成功能演示</h1>
            <p>将您的书签数据无缝同步到Obsidian笔记库</p>
        </div>
        
        <div class="demo-content">
            <!-- 功能概览 -->
            <div class="demo-section">
                <h2>
                    <span>📋</span>
                    功能概览
                </h2>
                <div class="feature-grid">
                    <div class="feature-card">
                        <h3>
                            <span>📝</span>
                            标准Markdown导出
                        </h3>
                        <p>将每个书签转换为独立的Markdown文件，包含完整的元数据和内容。</p>
                        <ul class="feature-list">
                            <li>自动生成YAML前置元数据</li>
                            <li>支持自定义模板</li>
                            <li>包含Dataview查询代码</li>
                            <li>自动生成双向链接</li>
                        </ul>
                    </div>
                    
                    <div class="feature-card">
                        <h3>
                            <span>🗃️</span>
                            Bases数据库集成
                        </h3>
                        <p>创建结构化的数据库表格，支持高级查询和视图管理。</p>
                        <ul class="feature-list">
                            <li>自动创建数据库表格</li>
                            <li>支持筛选、排序、分组</li>
                            <li>预设多种视图</li>
                            <li>评分和状态管理</li>
                        </ul>
                    </div>
                    
                    <div class="feature-card">
                        <h3>
                            <span>🔄</span>
                            智能同步
                        </h3>
                        <p>支持手动和自动同步，确保数据始终保持最新状态。</p>
                        <ul class="feature-list">
                            <li>增量同步支持</li>
                            <li>冲突检测和解决</li>
                            <li>批量操作支持</li>
                            <li>同步状态监控</li>
                        </ul>
                    </div>
                    
                    <div class="feature-card">
                        <h3>
                            <span>🎨</span>
                            自定义模板
                        </h3>
                        <p>完全可定制的Markdown模板，支持变量替换和高级格式化。</p>
                        <ul class="feature-list">
                            <li>丰富的变量支持</li>
                            <li>预设模板库</li>
                            <li>实时预览</li>
                            <li>模板导入导出</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Obsidian库选择演示 -->
            <div class="demo-section">
                <h2>
                    <span>📁</span>
                    Obsidian库选择
                </h2>
                <p>自动检测系统中的Obsidian库，或手动添加库路径。</p>
                
                <div class="vault-selector">
                    <div class="vault-option selected">
                        <div class="vault-name">我的知识库</div>
                        <div class="vault-path">C:\Users\<USER>\Documents\我的知识库</div>
                    </div>
                    <div class="vault-option">
                        <div class="vault-name">工作笔记</div>
                        <div class="vault-path">D:\Obsidian\工作笔记</div>
                    </div>
                </div>
                
                <div class="action-buttons">
                    <button class="btn btn-secondary">
                        <span>🔄</span>
                        刷新库列表
                    </button>
                    <button class="btn btn-secondary">
                        <span>➕</span>
                        添加自定义路径
                    </button>
                </div>
            </div>

            <!-- 导出格式选择演示 -->
            <div class="demo-section">
                <h2>
                    <span>📄</span>
                    导出格式选择
                </h2>
                <p>选择最适合您工作流程的导出格式。</p>
                
                <div class="format-selector">
                    <div class="format-option selected">
                        <div class="format-icon">📝</div>
                        <div class="format-title">标准Markdown</div>
                        <div class="format-description">
                            每个书签生成独立的.md文件，包含完整的元数据和Dataview查询支持
                        </div>
                    </div>
                    <div class="format-option">
                        <div class="format-icon">🗃️</div>
                        <div class="format-title">Bases数据库</div>
                        <div class="format-description">
                            创建结构化的数据库表格，支持高级筛选、排序和视图管理
                        </div>
                    </div>
                </div>
            </div>

            <!-- 同步设置演示 -->
            <div class="demo-section">
                <h2>
                    <span>⚙️</span>
                    同步设置
                </h2>
                <p>配置同步行为和输出格式。</p>
                
                <div class="settings-demo">
                    <div class="setting-item">
                        <div>
                            <div class="setting-label">包含Dataview查询</div>
                            <div class="setting-description">在生成的文件中包含Dataview查询代码</div>
                        </div>
                        <div class="toggle-switch"></div>
                    </div>
                    
                    <div class="setting-item">
                        <div>
                            <div class="setting-label">自动同步</div>
                            <div class="setting-description">新增书签时自动同步到Obsidian</div>
                        </div>
                        <div class="toggle-switch off"></div>
                    </div>
                    
                    <div class="setting-item">
                        <div>
                            <div class="setting-label">创建预设视图</div>
                            <div class="setting-description">自动创建按分类、状态等分组的视图</div>
                        </div>
                        <div class="toggle-switch"></div>
                    </div>
                    
                    <div class="setting-item">
                        <div>
                            <div class="setting-label">启用评分字段</div>
                            <div class="setting-description">在数据库中添加1-5星评分字段</div>
                        </div>
                        <div class="toggle-switch"></div>
                    </div>
                </div>
            </div>

            <!-- 模板编辑器演示 -->
            <div class="demo-section">
                <h2>
                    <span>🎨</span>
                    自定义模板编辑器
                </h2>
                <p>创建个性化的Markdown模板，支持丰富的变量替换。</p>
                
                <div style="margin: 16px 0;">
                    <strong>可用变量：</strong>
                    <span style="display: inline-flex; gap: 8px; flex-wrap: wrap; margin-top: 8px;">
                        <span style="background: #e5e7eb; padding: 4px 8px; border-radius: 4px; font-size: 12px;">{{title}}</span>
                        <span style="background: #e5e7eb; padding: 4px 8px; border-radius: 4px; font-size: 12px;">{{url}}</span>
                        <span style="background: #e5e7eb; padding: 4px 8px; border-radius: 4px; font-size: 12px;">{{description}}</span>
                        <span style="background: #e5e7eb; padding: 4px 8px; border-radius: 4px; font-size: 12px;">{{tags}}</span>
                        <span style="background: #e5e7eb; padding: 4px 8px; border-radius: 4px; font-size: 12px;">{{category}}</span>
                        <span style="background: #e5e7eb; padding: 4px 8px; border-radius: 4px; font-size: 12px;">{{date}}</span>
                    </span>
                </div>
                
                <textarea class="template-editor" readonly># {{title}}

🔗 **链接**: [{{title}}]({{url}})

📝 **描述**: {{description}}

🏷️ **标签**: {{tags}}

📁 **分类**: [[{{category}}]]

## 笔记

<!-- 在这里添加你的笔记 -->

## 相关链接

<!-- 添加相关的双向链接 -->

## 相关书签

```dataview
TABLE title, url, category
FROM #bookmark
WHERE contains(tags, "{{tags}}")
SORT created DESC
LIMIT 10
```</textarea>
                
                <div class="action-buttons">
                    <button class="btn btn-secondary">
                        <span>🔄</span>
                        恢复默认模板
                    </button>
                    <button class="btn btn-secondary">
                        <span>👁️</span>
                        预览模板
                    </button>
                </div>
            </div>

            <!-- 同步操作演示 -->
            <div class="demo-section">
                <h2>
                    <span>🚀</span>
                    同步操作
                </h2>
                <p>执行各种同步操作，将书签数据导出到Obsidian。</p>
                
                <div class="action-buttons">
                    <button class="btn btn-primary">
                        <span>📤</span>
                        导出当前书签
                    </button>
                    <button class="btn btn-primary">
                        <span>📦</span>
                        同步所有书签
                    </button>
                    <button class="btn btn-primary">
                        <span>🗃️</span>
                        创建Bases数据库
                    </button>
                </div>
                
                <!-- 同步结果演示 -->
                <div class="sync-result">
                    <h4 style="margin: 0 0 12px 0; color: #0ea5e9;">✅ 同步完成</h4>
                    <div class="sync-stats">
                        <div class="sync-stat">
                            <div class="sync-stat-number">156</div>
                            <div class="sync-stat-label">成功同步</div>
                        </div>
                        <div class="sync-stat">
                            <div class="sync-stat-number">2</div>
                            <div class="sync-stat-label">同步失败</div>
                        </div>
                        <div class="sync-stat">
                            <div class="sync-stat-number">3.2s</div>
                            <div class="sync-stat-label">用时</div>
                        </div>
                    </div>
                    <details style="margin-top: 12px;">
                        <summary style="cursor: pointer; color: #6b7280;">查看错误详情</summary>
                        <div style="margin-top: 8px; font-size: 12px; color: #ef4444;">
                            • 书签 "示例页面1" 同步失败：文件名包含非法字符<br>
                            • 书签 "示例页面2" 同步失败：目标文件夹不存在
                        </div>
                    </details>
                </div>
            </div>

            <!-- 生成的文件示例 -->
            <div class="demo-section">
                <h2>
                    <span>📄</span>
                    生成的文件示例
                </h2>
                <p>查看同步后在Obsidian中生成的文件格式。</p>
                
                <h4>标准Markdown文件示例：</h4>
                <div class="code-block">---
title: "React官方文档"
url: "https://react.dev/"
tags: ["前端开发", "React", "JavaScript"]
category: "技术文档"
created: "2024-01-15T10:30:00.000Z"
updated: "2024-01-15T10:30:00.000Z"
type: "bookmark"
source: "browser-extension"
---

# React官方文档

🔗 **链接**: [React官方文档](https://react.dev/)

📝 **描述**: React是用于构建用户界面的JavaScript库

🏷️ **标签**: #前端开发 #React #JavaScript

📁 **分类**: [[技术文档]]

## 笔记

<!-- 在这里添加你的笔记 -->

## 相关链接

<!-- 添加相关的双向链接 -->

## 相关书签

```dataview
TABLE title, url, category
FROM #bookmark
WHERE contains(tags, "React")
SORT created DESC
LIMIT 10
```</div>

                <h4>Bases数据库表格示例：</h4>
                <div class="code-block">```bases-database
name: 书签管理
columns:
  - name: 标题
    type: text
    primary: true
  - name: URL
    type: url
  - name: 分类
    type: select
    options: [开发工具, 学习资源, 新闻资讯, 娱乐, 购物, 社交, 工作, 设计, 技术文档]
  - name: 标签
    type: multi-select
  - name: 描述
    type: long-text
  - name: 创建时间
    type: date
  - name: 评分
    type: number
    min: 1
    max: 5
  - name: 状态
    type: select
    options: [未读, 已读, 收藏, 归档]
```

| 标题 | URL | 分类 | 标签 | 描述 | 创建时间 | 评分 | 状态 |
|------|-----|------|------|------|----------|------|------|
| React官方文档 | https://react.dev/ | 技术文档 | 前端开发, React | React是用于构建用户界面的JavaScript库 | 2024-01-15 | 5 | 收藏 |
| Vue.js指南 | https://vuejs.org/ | 技术文档 | 前端开发, Vue | 渐进式JavaScript框架 | 2024-01-14 | 4 | 已读 |</div>
            </div>

            <!-- 高级功能说明 -->
            <div class="demo-section">
                <h2>
                    <span>⭐</span>
                    高级功能
                </h2>
                <div class="feature-grid">
                    <div class="feature-card">
                        <h3>
                            <span>🔄</span>
                            双向同步
                        </h3>
                        <p>不仅可以将书签同步到Obsidian，还能检测Obsidian中的修改并同步回书签管理器。</p>
                    </div>
                    
                    <div class="feature-card">
                        <h3>
                            <span>🎯</span>
                            智能分类
                        </h3>
                        <p>基于内容自动推荐分类和标签，支持AI增强的内容分析。</p>
                    </div>
                    
                    <div class="feature-card">
                        <h3>
                            <span>📊</span>
                            数据分析
                        </h3>
                        <p>提供书签收集统计、分类分布分析、使用频率追踪等数据洞察。</p>
                    </div>
                    
                    <div class="feature-card">
                        <h3>
                            <span>🔗</span>
                            关系图谱
                        </h3>
                        <p>在Obsidian图谱视图中展示书签之间的关联关系，发现知识连接。</p>
                    </div>
                </div>
            </div>

            <!-- 使用场景 -->
            <div class="demo-section">
                <h2>
                    <span>💡</span>
                    使用场景
                </h2>
                <div class="feature-grid">
                    <div class="feature-card">
                        <h3>
                            <span>🎓</span>
                            学术研究
                        </h3>
                        <p>将研究资料整理到Obsidian中，建立知识网络，支持文献管理和引用。</p>
                    </div>
                    
                    <div class="feature-card">
                        <h3>
                            <span>💼</span>
                            项目管理
                        </h3>
                        <p>将项目相关的资源和文档集中管理，支持团队协作和知识共享。</p>
                    </div>
                    
                    <div class="feature-card">
                        <h3>
                            <span>📚</span>
                            个人学习</span>
                        </h3>
                        <p>构建个人知识库，将学习资源与笔记关联，形成完整的学习体系。</p>
                    </div>
                    
                    <div class="feature-card">
                        <h3>
                            <span>✍️</span>
                            内容创作
                        </h3>
                        <p>收集创作素材和灵感，在Obsidian中组织和发展创意想法。</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script type="text/babel">
        // 添加一些交互效果
        document.addEventListener('DOMContentLoaded', function() {
            // 切换开关交互
            const toggles = document.querySelectorAll('.toggle-switch');
            toggles.forEach(toggle => {
                toggle.addEventListener('click', function() {
                    this.classList.toggle('off');
                });
            });

            // 格式选择交互
            const formatOptions = document.querySelectorAll('.format-option');
            formatOptions.forEach(option => {
                option.addEventListener('click', function() {
                    formatOptions.forEach(opt => opt.classList.remove('selected'));
                    this.classList.add('selected');
                });
            });

            // 库选择交互
            const vaultOptions = document.querySelectorAll('.vault-option');
            vaultOptions.forEach(option => {
                option.addEventListener('click', function() {
                    vaultOptions.forEach(opt => opt.classList.remove('selected'));
                    this.classList.add('selected');
                });
            });

            // 按钮点击效果
            const buttons = document.querySelectorAll('.btn');
            buttons.forEach(button => {
                button.addEventListener('click', function() {
                    // 模拟加载状态
                    const originalText = this.innerHTML;
                    this.innerHTML = '<span>⏳</span> 处理中...';
                    this.disabled = true;
                    
                    setTimeout(() => {
                        this.innerHTML = originalText;
                        this.disabled = false;
                    }, 2000);
                });
            });
        });
    </script>
</body>
</html>