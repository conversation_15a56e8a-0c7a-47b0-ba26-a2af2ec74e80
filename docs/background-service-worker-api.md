# Background Service Worker API 文档

## 概述

Background Service Worker 是 Universe Bag Chrome 扩展的核心后台服务，负责处理扩展的生命周期管理、右键菜单创建、消息通信和数据存储初始化等功能。

## 主要功能模块

### 1. 扩展生命周期管理
- 扩展安装时的初始化处理
- 右键菜单的创建和管理
- 存储系统的初始化

### 2. 右键菜单系统
- 创建上下文相关的右键菜单项
- 处理用户的右键菜单点击事件
- 支持链接收藏、文字摘录和页面收藏

### 3. 消息通信系统
- 处理来自 Content Script 和 Popup 的消息
- 提供异步消息处理机制
- 支持设置的获取和更新

### 4. 数据存储管理
- 初始化默认配置和数据结构
- 管理用户设置、分类、标签和收藏数据
- 提供存储操作的错误处理

## API 参考

### 生命周期函数

#### chrome.runtime.onInstalled 监听器
```typescript
chrome.runtime.onInstalled.addListener((details: chrome.runtime.InstalledDetails) => void)
```

**功能**: 监听扩展安装事件，执行初始化操作  
**参数**: 
- `details.reason`: 安装原因（'install' | 'update' | 'chrome_update' | 'shared_module_update'）

**执行流程**:
1. 创建右键菜单
2. 初始化存储系统

### 右键菜单管理

#### createContextMenus()
```typescript
function createContextMenus(): void
```

**功能**: 创建扩展的右键菜单项  
**返回值**: `void`  
**副作用**: 在浏览器右键菜单中添加收藏相关选项

**创建的菜单项**:
- `bookmark-link`: 收藏链接（在链接上右键时显示）
- `bookmark-selection`: 收藏摘录（选中文字后右键时显示）
- `bookmark-page`: 收藏当前页面（在页面空白处右键时显示）

**使用示例**:
```typescript
// 扩展安装时自动调用
chrome.runtime.onInstalled.addListener(() => {
  createContextMenus()
})
```

#### chrome.contextMenus.onClicked 监听器
```typescript
chrome.contextMenus.onClicked.addListener((info: chrome.contextMenus.OnClickData, tab?: chrome.tabs.Tab) => void)
```

**功能**: 处理右键菜单点击事件  
**参数**:
- `info`: 点击信息，包含菜单项ID、链接URL、选中文字等
- `tab`: 当前标签页信息

### 收藏处理函数

#### handleBookmarkLink()
```typescript
async function handleBookmarkLink(
  info: chrome.contextMenus.OnClickData, 
  tab?: chrome.tabs.Tab
): Promise<void>
```

**功能**: 处理收藏链接操作  
**参数**:
- `info`: 右键菜单点击信息，必须包含 `linkUrl`
- `tab`: 当前标签页信息

**返回值**: `Promise<void>`  
**副作用**: 向 Content Script 发送消息获取链接详细信息

**使用示例**:
```typescript
// 用户右键点击链接并选择"收藏链接"时自动调用
chrome.contextMenus.onClicked.addListener((info, tab) => {
  if (info.menuItemId === 'bookmark-link') {
    handleBookmarkLink(info, tab)
  }
})
```

#### handleBookmarkSelection()
```typescript
async function handleBookmarkSelection(
  info: chrome.contextMenus.OnClickData, 
  tab?: chrome.tabs.Tab
): Promise<void>
```

**功能**: 处理收藏选中文字操作  
**参数**:
- `info`: 右键菜单点击信息，必须包含 `selectionText`
- `tab`: 当前标签页信息

**返回值**: `Promise<void>`  
**副作用**: 向 Content Script 发送消息获取页面和选中文字信息

**使用示例**:
```typescript
// 用户选中文字后右键选择"收藏摘录"时自动调用
chrome.contextMenus.onClicked.addListener((info, tab) => {
  if (info.menuItemId === 'bookmark-selection') {
    handleBookmarkSelection(info, tab)
  }
})
```

#### handleBookmarkPage()
```typescript
async function handleBookmarkPage(
  _info: chrome.contextMenus.OnClickData, 
  tab?: chrome.tabs.Tab
): Promise<void>
```

**功能**: 处理收藏当前页面操作  
**参数**:
- `_info`: 右键菜单点击信息（此函数中未使用）
- `tab`: 当前标签页信息

**返回值**: `Promise<void>`  
**副作用**: 向 Content Script 发送消息获取页面信息

### 存储管理

#### initializeStorage()
```typescript
async function initializeStorage(): Promise<void>
```

**功能**: 初始化扩展的存储系统  
**返回值**: `Promise<void>`  
**副作用**: 在 Chrome Storage 中设置默认配置和数据结构

**初始化的数据结构**:
```typescript
{
  initialized: true,
  settings: {
    theme: 'light',
    language: 'zh-CN',
    autoTagging: true,
    floatingWidget: false,
    syncEnabled: false
  },
  categories: [
    {
      id: 'default',
      name: '默认分类',
      description: '未分类的收藏内容',
      color: '#3b82f6',
      createdAt: string // ISO 时间戳
    }
  ],
  tags: [],
  bookmarks: []
}
```

**使用示例**:
```typescript
// 扩展安装时自动调用
chrome.runtime.onInstalled.addListener(() => {
  initializeStorage()
})
```

### 消息通信

#### chrome.runtime.onMessage 监听器
```typescript
chrome.runtime.onMessage.addListener((
  message: any, 
  sender: chrome.runtime.MessageSender, 
  sendResponse: (response?: any) => void
) => boolean)
```

**功能**: 监听来自其他脚本的消息  
**参数**:
- `message`: 消息对象，包含 `type` 和 `data` 字段
- `sender`: 消息发送者信息
- `sendResponse`: 响应回调函数

**返回值**: `true` 表示异步响应

#### handleMessage()
```typescript
async function handleMessage(
  message: any, 
  _sender: chrome.runtime.MessageSender
): Promise<any>
```

**功能**: 处理消息的主函数  
**参数**:
- `message`: 消息对象，必须包含 `type` 字段
- `_sender`: 消息发送者信息（此函数中未使用）

**返回值**: `Promise<any>` - 根据消息类型返回相应数据

**支持的消息类型**:
- `PING`: 连接测试，返回 `{ status: 'pong' }`
- `GET_SETTINGS`: 获取用户设置
- `UPDATE_SETTINGS`: 更新用户设置

**使用示例**:
```typescript
// 从 Popup 发送消息获取设置
chrome.runtime.sendMessage({
  type: 'GET_SETTINGS'
}).then(settings => {
  console.log('当前设置:', settings)
})

// 从 Popup 发送消息更新设置
chrome.runtime.sendMessage({
  type: 'UPDATE_SETTINGS',
  data: { theme: 'dark' }
}).then(updatedSettings => {
  console.log('更新后的设置:', updatedSettings)
})
```

### 设置管理

#### getSettings()
```typescript
async function getSettings(): Promise<any>
```

**功能**: 获取用户设置  
**返回值**: `Promise<any>` - 用户设置对象，如果不存在则返回空对象  
**异常**: 存储访问失败时抛出错误

**使用示例**:
```typescript
try {
  const settings = await getSettings()
  console.log('用户设置:', settings)
} catch (error) {
  console.error('获取设置失败:', error)
}
```

#### updateSettings()
```typescript
async function updateSettings(newSettings: any): Promise<any>
```

**功能**: 更新用户设置  
**参数**:
- `newSettings`: 要更新的设置对象（会与现有设置合并）

**返回值**: `Promise<any>` - 更新后的完整设置对象  
**异常**: 存储访问失败时抛出错误

**使用示例**:
```typescript
try {
  const updatedSettings = await updateSettings({
    theme: 'dark',
    autoTagging: false
  })
  console.log('更新后的设置:', updatedSettings)
} catch (error) {
  console.error('更新设置失败:', error)
}
```

## 错误处理

所有异步函数都包含完善的错误处理机制：

### 消息通信错误
- Content Script 不可用时的错误捕获
- 消息发送超时的处理
- 未知消息类型的错误响应

### 存储操作错误
- Chrome Storage API 访问失败的处理
- 数据序列化/反序列化错误
- 存储配额超限的处理

### 示例错误处理
```typescript
try {
  const response = await chrome.tabs.sendMessage(tab.id!, {
    type: 'GET_PAGE_INFO',
    data: {}
  })
  console.log('页面信息:', response)
} catch (error) {
  console.error('获取页面信息失败:', error)
  // 可以在这里添加用户友好的错误提示
}
```

## 扩展性

### 添加新的右键菜单项
```typescript
function createContextMenus() {
  chrome.contextMenus.removeAll(() => {
    // 现有菜单项...
    
    // 添加新菜单项
    chrome.contextMenus.create({
      id: 'new-feature',
      title: '新功能',
      contexts: ['page']
    })
  })
}

// 在点击处理器中添加对应逻辑
chrome.contextMenus.onClicked.addListener((info, tab) => {
  switch (info.menuItemId) {
    // 现有处理...
    case 'new-feature':
      handleNewFeature(info, tab)
      break
  }
})
```

### 添加新的消息类型
```typescript
async function handleMessage(message: any, sender: chrome.runtime.MessageSender) {
  switch (message.type) {
    // 现有消息类型...
    case 'NEW_MESSAGE_TYPE':
      return await handleNewMessageType(message.data)
    
    default:
      throw new Error(`未知的消息类型: ${message.type}`)
  }
}
```

## 性能考虑

### 内存使用
- Service Worker 在不活跃时会被浏览器暂停
- 避免在全局作用域存储大量数据
- 使用 Chrome Storage API 而非内存存储持久数据

### 消息通信优化
- 使用异步消息处理避免阻塞
- 实现消息超时机制
- 批量处理相关消息以减少通信开销

### 存储优化
- 使用增量更新而非全量替换
- 定期清理过期数据
- 实现数据压缩以节省存储空间

## 调试指南

### 查看 Service Worker 日志
1. 打开 Chrome 扩展管理页面 (`chrome://extensions/`)
2. 找到 Universe Bag 扩展
3. 点击"检查视图"中的"Service Worker"链接
4. 在开发者工具控制台中查看日志

### 常见调试场景
```typescript
// 添加调试日志
console.log('Background Service Worker 已启动')
console.log('右键菜单被点击:', info.menuItemId)
console.log('收到消息:', message, '来自:', sender)

// 检查存储状态
chrome.storage.local.get(null, (result) => {
  console.log('当前存储状态:', result)
})
```

## 依赖关系

### Chrome APIs
- `chrome.runtime`: 扩展运行时管理
- `chrome.contextMenus`: 右键菜单管理
- `chrome.storage`: 数据存储
- `chrome.tabs`: 标签页管理

### 内部依赖
- Content Script (`src/content/index.ts`): 页面信息提取
- Popup Script (`src/popup/PopupApp.tsx`): 用户界面交互
- 类型定义 (`src/types/index.ts`): TypeScript 类型支持

### 外部依赖
- 无直接外部依赖，仅使用 Chrome Extension APIs

## 安全考虑

### 权限最小化
- 仅请求必要的 Chrome 权限
- 限制 host_permissions 的范围
- 验证消息来源的合法性

### 数据安全
- 敏感数据的加密存储
- API 密钥的安全管理
- 用户隐私数据的保护

### 示例安全实践
```typescript
// 验证消息来源
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  // 确保消息来自扩展内部
  if (sender.id !== chrome.runtime.id) {
    console.warn('收到来自未知来源的消息:', sender)
    return
  }
  
  // 处理消息...
})
```