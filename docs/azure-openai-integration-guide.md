# Azure OpenAI集成指南

## 概述

本指南详细介绍如何在Universe Bag（乾坤袋）Chrome扩展中集成和使用Azure OpenAI服务。Azure OpenAI是Microsoft Azure云平台上部署的OpenAI模型服务，提供企业级的安全性、合规性和可靠性。

## 新增功能

### v1.1.0 更新内容

- ✅ **新增Azure OpenAI提供商类型**: 在AI类型定义中添加了 `azure-openai` 支持
- ✅ **Azure OpenAI连接测试**: 实现了专门的Azure OpenAI连接测试功能
- ✅ **Azure OpenAI模型获取**: 支持获取Azure部署的模型列表
- ✅ **统一API接口**: 与其他AI提供商保持一致的调用接口
- ✅ **完整类型支持**: 提供完整的TypeScript类型定义和智能提示

## 配置要求

### 1. Azure OpenAI资源

在使用前，您需要：

1. **Azure订阅**: 拥有有效的Microsoft Azure订阅
2. **Azure OpenAI资源**: 在Azure门户中创建Azure OpenAI资源
3. **模型部署**: 在Azure OpenAI Studio中部署所需的模型
4. **API密钥**: 获取Azure OpenAI资源的API密钥

### 2. 必需信息

- **资源端点**: `https://your-resource-name.openai.azure.com`
- **API密钥**: 从Azure门户获取的API密钥
- **API版本**: 推荐使用 `2024-02-15-preview` 或更新版本
- **部署名称**: 在Azure OpenAI Studio中创建的模型部署名称

## 配置示例

### 基本配置

```typescript
import { AIProviderConfig } from '../types/ai'

const azureOpenAIConfig: AIProviderConfig = {
  id: 'azure_openai_main',
  name: 'Azure OpenAI主服务',
  type: 'azure-openai',
  baseUrl: 'https://your-resource-name.openai.azure.com',
  apiKey: 'your-azure-openai-api-key',
  headers: {
    'api-version': '2024-02-15-preview'
  },
  timeout: 30000,
  enabled: true,
  createdAt: new Date(),
  updatedAt: new Date()
}
```

### 多环境配置

```typescript
// 开发环境配置
const azureDevConfig: AIProviderConfig = {
  id: 'azure_openai_dev',
  name: 'Azure OpenAI开发环境',
  type: 'azure-openai',
  baseUrl: 'https://dev-resource.openai.azure.com',
  apiKey: process.env.AZURE_OPENAI_DEV_KEY,
  headers: {
    'api-version': '2024-02-15-preview'
  },
  timeout: 15000,
  enabled: true,
  createdAt: new Date(),
  updatedAt: new Date()
}

// 生产环境配置
const azureProdConfig: AIProviderConfig = {
  id: 'azure_openai_prod',
  name: 'Azure OpenAI生产环境',
  type: 'azure-openai',
  baseUrl: 'https://prod-resource.openai.azure.com',
  apiKey: process.env.AZURE_OPENAI_PROD_KEY,
  headers: {
    'api-version': '2024-02-15-preview',
    'User-Agent': 'Universe-Bag-Extension/1.0'
  },
  timeout: 30000,
  enabled: true,
  createdAt: new Date(),
  updatedAt: new Date()
}
```

## 使用示例

### 1. 连接测试

```typescript
import { aiProviderService } from '../services/aiProviderService'

async function testAzureOpenAIConnection() {
  const config: AIProviderConfig = {
    id: 'azure_test',
    name: 'Azure OpenAI测试',
    type: 'azure-openai',
    baseUrl: 'https://your-resource.openai.azure.com',
    apiKey: 'your-api-key',
    headers: {
      'api-version': '2024-02-15-preview'
    },
    enabled: true,
    createdAt: new Date(),
    updatedAt: new Date()
  }

  try {
    const result = await aiProviderService.testConnection(config)
    
    if (result.success) {
      console.log('✅ Azure OpenAI连接成功')
      console.log(`📊 响应时间: ${result.responseTime}ms`)
      console.log(`🚀 可用部署: ${result.modelCount}个`)
    } else {
      console.error('❌ Azure OpenAI连接失败:', result.error)
    }
  } catch (error) {
    console.error('连接测试异常:', error.message)
  }
}
```

### 2. 模型列表获取

```typescript
import { aiModelService } from '../services/aiModelService'

async function getAzureOpenAIModels() {
  const config = azureOpenAIConfig // 使用上面定义的配置
  
  try {
    const models = await aiModelService.getModels(config)
    
    console.log(`发现 ${models.length} 个Azure OpenAI部署:`)
    
    models.forEach(model => {
      console.log(`📦 ${model.displayName}`)
      console.log(`   ID: ${model.id}`)
      console.log(`   描述: ${model.description}`)
      console.log(`   能力: ${model.capabilities?.join(', ')}`)
      console.log(`   推荐: ${model.isRecommended ? '是' : '否'}`)
      console.log('---')
    })
  } catch (error) {
    console.error('获取模型列表失败:', error.message)
  }
}
```

### 3. AI对话功能

```typescript
import { aiChatService, ChatMessage } from '../services/aiChatService'

async function chatWithAzureOpenAI() {
  const config = azureOpenAIConfig
  const deploymentName = 'gpt-4' // 您在Azure中创建的部署名称
  
  const messages: ChatMessage[] = [
    {
      role: 'system',
      content: '你是一个有用的AI助手，请用中文回答问题。'
    },
    {
      role: 'user',
      content: '请介绍一下Azure OpenAI服务的优势。'
    }
  ]

  try {
    const response = await aiChatService.chatWithCloudService(
      config,
      deploymentName,
      messages,
      {
        temperature: 0.7,
        maxTokens: 1000
      }
    )
    
    console.log('🤖 AI回复:', response.content)
    console.log('📊 使用统计:', response.usage)
  } catch (error) {
    console.error('对话失败:', error.message)
  }
}
```

### 4. 批量操作

```typescript
async function batchTestAzureConfigs() {
  const configs = [azureDevConfig, azureProdConfig]
  const results = []
  
  for (const config of configs) {
    console.log(`测试 ${config.name}...`)
    
    try {
      const result = await aiProviderService.testConnection(config)
      results.push({
        name: config.name,
        success: result.success,
        responseTime: result.responseTime,
        error: result.error
      })
    } catch (error) {
      results.push({
        name: config.name,
        success: false,
        error: error.message
      })
    }
  }
  
  // 输出测试结果
  console.log('\n📊 批量测试结果:')
  results.forEach(result => {
    const status = result.success ? '✅ 成功' : '❌ 失败'
    console.log(`${result.name}: ${status}`)
    if (result.responseTime) {
      console.log(`   响应时间: ${result.responseTime}ms`)
    }
    if (result.error) {
      console.log(`   错误: ${result.error}`)
    }
  })
}
```

## 高级配置

### 1. 自定义请求头

```typescript
const advancedConfig: AIProviderConfig = {
  id: 'azure_openai_advanced',
  name: 'Azure OpenAI高级配置',
  type: 'azure-openai',
  baseUrl: 'https://your-resource.openai.azure.com',
  apiKey: 'your-api-key',
  headers: {
    'api-version': '2024-02-15-preview',
    'User-Agent': 'Universe-Bag-Extension/1.0',
    'X-Custom-Header': 'custom-value',
    'Content-Type': 'application/json'
  },
  timeout: 45000, // 45秒超时
  enabled: true,
  createdAt: new Date(),
  updatedAt: new Date()
}
```

### 2. 错误处理和重试

```typescript
async function robustAzureConnection(config: AIProviderConfig, maxRetries = 3) {
  let lastError: Error | null = null
  
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      console.log(`尝试连接 Azure OpenAI (第${attempt}次)...`)
      
      const result = await aiProviderService.testConnection(config)
      
      if (result.success) {
        console.log(`✅ 连接成功 (第${attempt}次尝试)`)
        return result
      } else {
        throw new Error(result.error || '连接失败')
      }
    } catch (error) {
      lastError = error
      console.warn(`⚠️ 第${attempt}次尝试失败: ${error.message}`)
      
      if (attempt < maxRetries) {
        const delay = Math.pow(2, attempt) * 1000 // 指数退避
        console.log(`等待 ${delay}ms 后重试...`)
        await new Promise(resolve => setTimeout(resolve, delay))
      }
    }
  }
  
  throw new Error(`所有重试都失败了。最后错误: ${lastError?.message}`)
}
```

### 3. 配置验证

```typescript
function validateAzureOpenAIConfig(config: AIProviderConfig): boolean {
  // 检查必需字段
  if (!config.baseUrl || !config.apiKey) {
    console.error('❌ 缺少必需的baseUrl或apiKey')
    return false
  }
  
  // 检查URL格式
  if (!config.baseUrl.includes('.openai.azure.com')) {
    console.error('❌ baseUrl不是有效的Azure OpenAI端点')
    return false
  }
  
  // 检查API版本
  const apiVersion = config.headers?.['api-version']
  if (!apiVersion) {
    console.warn('⚠️ 建议设置api-version请求头')
  }
  
  // 检查API密钥格式
  if (config.apiKey.length < 32) {
    console.warn('⚠️ API密钥长度可能不正确')
  }
  
  console.log('✅ Azure OpenAI配置验证通过')
  return true
}

// 使用示例
if (validateAzureOpenAIConfig(azureOpenAIConfig)) {
  await testAzureOpenAIConnection()
}
```

## 常见问题和解决方案

### 1. 连接超时

**问题**: 连接Azure OpenAI时出现超时错误

**解决方案**:
```typescript
// 增加超时时间
const config = {
  ...azureOpenAIConfig,
  timeout: 60000 // 60秒
}

// 或者使用重试机制
await robustAzureConnection(config, 5)
```

### 2. API版本不兼容

**问题**: 使用了不支持的API版本

**解决方案**:
```typescript
// 使用推荐的API版本
const config = {
  ...azureOpenAIConfig,
  headers: {
    'api-version': '2024-02-15-preview' // 或更新版本
  }
}
```

### 3. 部署名称错误

**问题**: 模型部署名称不正确

**解决方案**:
```typescript
// 首先获取可用的部署列表
const models = await aiModelService.getModels(config)
console.log('可用部署:', models.map(m => m.id))

// 使用正确的部署名称
const deploymentName = models[0].id // 使用第一个可用部署
```

### 4. 权限问题

**问题**: API密钥权限不足

**解决方案**:
1. 检查Azure门户中的API密钥权限
2. 确保资源具有适当的访问控制设置
3. 验证订阅状态和配额限制

### 5. 网络连接问题

**问题**: 网络连接被阻止或限制

**解决方案**:
```typescript
// 添加自定义User-Agent
const config = {
  ...azureOpenAIConfig,
  headers: {
    ...azureOpenAIConfig.headers,
    'User-Agent': 'Universe-Bag-Extension/1.0'
  }
}

// 检查网络连接
async function checkNetworkConnectivity() {
  try {
    const response = await fetch('https://azure.microsoft.com', {
      method: 'HEAD',
      signal: AbortSignal.timeout(5000)
    })
    return response.ok
  } catch {
    return false
  }
}
```

## 最佳实践

### 1. 安全性

- **API密钥保护**: 不要在代码中硬编码API密钥
- **环境变量**: 使用环境变量存储敏感信息
- **权限最小化**: 只授予必要的权限
- **定期轮换**: 定期更换API密钥

```typescript
// 推荐的安全配置
const secureConfig: AIProviderConfig = {
  id: 'azure_openai_secure',
  name: 'Azure OpenAI安全配置',
  type: 'azure-openai',
  baseUrl: process.env.AZURE_OPENAI_ENDPOINT!, // 从环境变量读取
  apiKey: process.env.AZURE_OPENAI_API_KEY!,   // 从环境变量读取
  headers: {
    'api-version': '2024-02-15-preview'
  },
  timeout: 30000,
  enabled: true,
  createdAt: new Date(),
  updatedAt: new Date()
}
```

### 2. 性能优化

- **连接池**: 复用HTTP连接
- **缓存**: 缓存模型列表和配置
- **超时设置**: 合理设置超时时间
- **重试策略**: 实现指数退避重试

### 3. 监控和日志

```typescript
import { performanceMonitor } from '../utils/performance'

async function monitoredAzureCall() {
  performanceMonitor.startTimer('azure-openai-call')
  
  try {
    const result = await aiProviderService.testConnection(azureOpenAIConfig)
    
    const duration = performanceMonitor.endTimer('azure-openai-call')
    console.log(`Azure OpenAI调用耗时: ${duration}ms`)
    
    return result
  } catch (error) {
    performanceMonitor.endTimer('azure-openai-call')
    console.error('Azure OpenAI调用失败:', error)
    throw error
  }
}
```

### 4. 配置管理

```typescript
class AzureOpenAIConfigManager {
  private configs: Map<string, AIProviderConfig> = new Map()
  
  addConfig(id: string, config: AIProviderConfig) {
    if (validateAzureOpenAIConfig(config)) {
      this.configs.set(id, config)
    }
  }
  
  getConfig(id: string): AIProviderConfig | undefined {
    return this.configs.get(id)
  }
  
  async testAllConfigs(): Promise<Map<string, boolean>> {
    const results = new Map<string, boolean>()
    
    for (const [id, config] of this.configs) {
      try {
        const result = await aiProviderService.testConnection(config)
        results.set(id, result.success)
      } catch {
        results.set(id, false)
      }
    }
    
    return results
  }
}
```

## 相关文档

- [AI类型定义API文档](./ai-types-api.md)
- [AI提供商服务API文档](./aiProviderService-api.md)
- [AI对话服务API文档](./aiChatService-api.md)
- [AI集成服务API文档](./aiIntegrationService-api.md)

## 版本历史

### v1.1.0 (当前版本)
- ✅ 新增Azure OpenAI提供商类型支持
- ✅ 实现Azure OpenAI连接测试功能
- ✅ 添加Azure OpenAI模型获取功能
- ✅ 完善类型定义和文档

### v1.0.0 (基础版本)
- 支持OpenAI、Claude、Gemini等主流AI服务
- 本地AI服务支持（Ollama、LM Studio等）

## 注意事项

1. **Azure订阅**: 使用Azure OpenAI需要有效的Azure订阅
2. **配额限制**: 注意Azure OpenAI的使用配额和限制
3. **地区可用性**: Azure OpenAI服务在不同地区的可用性可能不同
4. **成本控制**: 监控API调用成本，设置适当的限制
5. **合规性**: 确保使用符合您组织的合规要求

通过本指南，您应该能够成功在Universe Bag扩展中集成和使用Azure OpenAI服务。如有问题，请参考相关API文档或提交Issue。