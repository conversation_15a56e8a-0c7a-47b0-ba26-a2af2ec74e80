# 性能监控工具 API 文档

## 概览

性能监控工具提供了完整的性能监控和优化功能，包括性能指标收集、内存使用监控、防抖节流工具等。

- **文件路径**: src/utils/performance.ts
- **主要类**: PerformanceMonitor, MemoryMonitor
- **工具函数**: 
- **导出实例**: performanceMonitor, memoryMonitor

## PerformanceMonitor 类

性能监控类

### 静态方法

#### getInstance

**签名**: `static getInstance(): PerformanceMonitor`

---

### 实例方法

#### startTimer

**功能**: 开始计时

**签名**: `startTimer(label: string): void`

**参数**:
- `label` (`string`) - 计时标签

---

#### endTimer

**功能**: 结束计时并记录

**签名**: `endTimer(label: string): number`

**参数**:
- `label` (`string`) - 计时标签

**返回值**: 耗时（毫秒）

---

#### recordMetric

**功能**: 记录单次指标

**签名**: `recordMetric(label: string, value: number): void`

**参数**:
- `label` (`string`) - 指标标签
- `value` (`number`) - 指标值

---

#### getMetricStats

**功能**: 获取指标统计

**签名**: `getMetricStats(label: string): `

**参数**:
- `label` (`string`) - 指标标签

**返回值**: 统计信息

---

#### getAllStats

**功能**: 获取所有指标统计

**签名**: `getAllStats(): Record<string, any>`

**返回值**: 所有指标的统计信息

---

#### clearMetrics

**功能**: 清除指标数据

**签名**: `clearMetrics(label?: string): void`

**参数**:
- `label` (`string`) (可选) - 可选的指标标签，不提供则清除所有

---

#### printReport

**功能**: 输出性能报告

**签名**: `printReport(): void`

---

## MemoryMonitor 类

内存使用监控

### 静态方法

#### getInstance

**签名**: `static getInstance(): MemoryMonitor`

---

### 实例方法

#### startMonitoring

**功能**: 开始监控内存使用

**签名**: `startMonitoring(interval: number = 30000): void`

**参数**:
- `interval` (`number = 30000`) - 监控间隔（毫秒）

---

#### stopMonitoring

**功能**: 停止监控内存使用

**签名**: `stopMonitoring(): void`

---

#### getCurrentMemoryUsage

**功能**: 获取当前内存使用情况

**签名**: `getCurrentMemoryUsage(): `

**返回值**: 内存使用信息

---

## 导出内容

- **class**: `PerformanceMonitor`
- **function**: `debounce`
- **function**: `throttle`
- **class**: `MemoryMonitor`
- **const**: `performanceMonitor`
- **const**: `memoryMonitor`
- **const**: `performanceMonitor`
- **const**: `memoryMonitor`

