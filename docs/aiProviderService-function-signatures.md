# AI提供商服务函数签名文档

## 概述

本文档提供 `src/services/aiProviderService.ts` 中所有公共和私有方法的详细函数签名。

## 文件信息

- **文件路径**: `src/services/aiProviderService.ts`
- **类名**: `AIProviderService`
- **导出**: `aiProviderService` (单例实例)

## 公共方法

### 核心API方法

#### `testConnection(config: AIProviderConfig)`
```typescript
async testConnection(config: AIProviderConfig): Promise<AIConnectionResult>
```
**功能**: 测试AI提供商连接状态  
**参数**:
- `config` (AIProviderConfig): 提供商配置信息
**返回值**: Promise<AIConnectionResult> - 连接测试结果  
**异常**: 连接失败时返回包含错误信息的结果对象

#### `getModels(config: AIProviderConfig)`
```typescript
async getModels(config: AIProviderConfig): Promise<AIModel[]>
```
**功能**: 获取提供商的模型列表  
**参数**:
- `config` (AIProviderConfig): 提供商配置信息
**返回值**: Promise<AIModel[]> - 模型列表数组  
**异常**: 获取失败时返回空数组

## Ollama 相关方法

### 公共方法

#### `validateOllamaService(baseUrl: string)`
```typescript
async validateOllamaService(baseUrl: string): Promise<{
  isRunning: boolean,
  version?: string,
  error?: string
}>
```
**功能**: 验证Ollama服务状态  
**参数**:
- `baseUrl` (string): Ollama服务地址
**返回值**: 服务状态信息对象

#### `testOllamaConnection(baseUrl: string)`
```typescript
async testOllamaConnection(baseUrl: string): Promise<{
  success: boolean,
  modelCount?: number,
  error?: string
}>
```
**功能**: 测试Ollama连接  
**参数**:
- `baseUrl` (string): Ollama服务地址
**返回值**: 连接测试结果对象

#### `getOllamaModels(baseUrl: string)`
```typescript
async getOllamaModels(baseUrl: string): Promise<AIModel[]>
```
**功能**: 获取Ollama模型列表  
**参数**:
- `baseUrl` (string): Ollama服务地址
**返回值**: Promise<AIModel[]> - Ollama模型数组

### 私有方法

#### `formatOllamaModelName(modelName: string, tag?: string)`
```typescript
private formatOllamaModelName(modelName: string, tag?: string): string
```
**功能**: 格式化Ollama模型名称  
**参数**:
- `modelName` (string): 原始模型名称
- `tag` (string, 可选): 模型标签
**返回值**: string - 格式化后的显示名称

#### `generateOllamaModelDescription(model: OllamaModel)`
```typescript
private generateOllamaModelDescription(model: OllamaModel): string
```
**功能**: 生成Ollama模型描述  
**参数**:
- `model` (OllamaModel): Ollama模型信息
**返回值**: string - 模型描述文本

#### `determineOllamaCapabilities(model: OllamaModel)`
```typescript
private determineOllamaCapabilities(model: OllamaModel): string[]
```
**功能**: 确定Ollama模型能力  
**参数**:
- `model` (OllamaModel): Ollama模型信息
**返回值**: string[] - 能力列表

#### `extractOllamaModelTags(model: OllamaModel)`
```typescript
private extractOllamaModelTags(model: OllamaModel): string[]
```
**功能**: 提取Ollama模型标签  
**参数**:
- `model` (OllamaModel): Ollama模型信息
**返回值**: string[] - 标签列表

#### `isOllamaModelRecommended(modelName: string)`
```typescript
private isOllamaModelRecommended(modelName: string): boolean
```
**功能**: 判断是否为推荐的Ollama模型  
**参数**:
- `modelName` (string): 模型名称
**返回值**: boolean - 是否推荐

#### `isOllamaModelPopular(modelName: string)`
```typescript
private isOllamaModelPopular(modelName: string): boolean
```
**功能**: 判断是否为热门的Ollama模型  
**参数**:
- `modelName` (string): 模型名称
**返回值**: boolean - 是否热门

## LM Studio 相关方法

### 公共方法

#### `validateLMStudioService(baseUrl: string)`
```typescript
async validateLMStudioService(baseUrl: string): Promise<{
  isRunning: boolean,
  version?: string,
  error?: string
}>
```
**功能**: 验证LM Studio服务状态  
**参数**:
- `baseUrl` (string): LM Studio服务地址
**返回值**: 服务状态信息对象

#### `testLMStudioConnection(baseUrl: string)`
```typescript
async testLMStudioConnection(baseUrl: string): Promise<{
  success: boolean,
  modelCount?: number,
  error?: string
}>
```
**功能**: 测试LM Studio连接  
**参数**:
- `baseUrl` (string): LM Studio服务地址
**返回值**: 连接测试结果对象

#### `getLMStudioModels(baseUrl: string)`
```typescript
async getLMStudioModels(baseUrl: string): Promise<AIModel[]>
```
**功能**: 获取LM Studio模型列表  
**参数**:
- `baseUrl` (string): LM Studio服务地址
**返回值**: Promise<AIModel[]> - LM Studio模型数组

### 私有方法

#### `formatLMStudioModelName(modelId: string)`
```typescript
private formatLMStudioModelName(modelId: string): string
```
**功能**: 格式化LM Studio模型名称  
**参数**:
- `modelId` (string): 模型ID
**返回值**: string - 格式化后的显示名称

#### `generateLMStudioModelDescription(model: LMStudioModel)`
```typescript
private generateLMStudioModelDescription(model: LMStudioModel): string
```
**功能**: 生成LM Studio模型描述  
**参数**:
- `model` (LMStudioModel): LM Studio模型信息
**返回值**: string - 模型描述文本

#### `determineLMStudioCapabilities(model: LMStudioModel)`
```typescript
private determineLMStudioCapabilities(model: LMStudioModel): string[]
```
**功能**: 确定LM Studio模型能力  
**参数**:
- `model` (LMStudioModel): LM Studio模型信息
**返回值**: string[] - 能力列表

#### `extractLMStudioModelTags(model: LMStudioModel)`
```typescript
private extractLMStudioModelTags(model: LMStudioModel): string[]
```
**功能**: 提取LM Studio模型标签  
**参数**:
- `model` (LMStudioModel): LM Studio模型信息
**返回值**: string[] - 标签列表

#### `isLMStudioModelRecommended(modelId: string)`
```typescript
private isLMStudioModelRecommended(modelId: string): boolean
```
**功能**: 判断是否为推荐的LM Studio模型  
**参数**:
- `modelId` (string): 模型ID
**返回值**: boolean - 是否推荐

#### `isLMStudioModelPopular(modelId: string)`
```typescript
private isLMStudioModelPopular(modelId: string): boolean
```
**功能**: 判断是否为热门的LM Studio模型  
**参数**:
- `modelId` (string): 模型ID
**返回值**: boolean - 是否热门

## Xinference 相关方法

### 公共方法

#### `validateXinferenceService(baseUrl: string)`
```typescript
async validateXinferenceService(baseUrl: string): Promise<{
  isRunning: boolean,
  version?: string,
  error?: string
}>
```
**功能**: 验证Xinference服务状态  
**参数**:
- `baseUrl` (string): Xinference服务地址
**返回值**: 服务状态信息对象

#### `testXinferenceConnection(baseUrl: string)`
```typescript
async testXinferenceConnection(baseUrl: string): Promise<{
  success: boolean,
  modelCount?: number,
  error?: string
}>
```
**功能**: 测试Xinference连接  
**参数**:
- `baseUrl` (string): Xinference服务地址
**返回值**: 连接测试结果对象

#### `getXinferenceModels(baseUrl: string)`
```typescript
async getXinferenceModels(baseUrl: string): Promise<AIModel[]>
```
**功能**: 获取Xinference模型列表  
**参数**:
- `baseUrl` (string): Xinference服务地址
**返回值**: Promise<AIModel[]> - Xinference模型数组

## OpenRouter 相关方法

### 公共方法

#### `testOpenRouterConnection(apiKey: string)`
```typescript
async testOpenRouterConnection(apiKey: string): Promise<{
  success: boolean,
  modelCount?: number,
  error?: string
}>
```
**功能**: 测试OpenRouter连接  
**参数**:
- `apiKey` (string): OpenRouter API密钥
**返回值**: 连接测试结果对象

#### `getOpenRouterModels(apiKey: string)`
```typescript
async getOpenRouterModels(apiKey: string): Promise<AIModel[]>
```
**功能**: 获取OpenRouter模型列表  
**参数**:
- `apiKey` (string): OpenRouter API密钥
**返回值**: Promise<AIModel[]> - OpenRouter模型数组

### 私有方法

#### `validateOpenRouterApiKey(apiKey: string)`
```typescript
private validateOpenRouterApiKey(apiKey: string): {
  isValid: boolean,
  error?: string
}
```
**功能**: 验证OpenRouter API密钥格式  
**参数**:
- `apiKey` (string): 要验证的API密钥
**返回值**: 验证结果对象

#### `formatOpenRouterModelName(model: OpenRouterModel)`
```typescript
private formatOpenRouterModelName(model: OpenRouterModel): string
```
**功能**: 格式化OpenRouter模型名称  
**参数**:
- `model` (OpenRouterModel): OpenRouter模型信息
**返回值**: string - 格式化后的显示名称

#### `generateOpenRouterModelDescription(model: OpenRouterModel)`
```typescript
private generateOpenRouterModelDescription(model: OpenRouterModel): string
```
**功能**: 生成OpenRouter模型描述  
**参数**:
- `model` (OpenRouterModel): OpenRouter模型信息
**返回值**: string - 模型描述文本

#### `determineOpenRouterCapabilities(model: OpenRouterModel)`
```typescript
private determineOpenRouterCapabilities(model: OpenRouterModel): string[]
```
**功能**: 确定OpenRouter模型能力  
**参数**:
- `model` (OpenRouterModel): OpenRouter模型信息
**返回值**: string[] - 能力列表

#### `extractOpenRouterModelTags(model: OpenRouterModel)`
```typescript
private extractOpenRouterModelTags(model: OpenRouterModel): string[]
```
**功能**: 提取OpenRouter模型标签  
**参数**:
- `model` (OpenRouterModel): OpenRouter模型信息
**返回值**: string[] - 标签列表

#### `formatOpenRouterPricing(model: OpenRouterModel)`
```typescript
private formatOpenRouterPricing(model: OpenRouterModel): string | null
```
**功能**: 格式化OpenRouter定价信息  
**参数**:
- `model` (OpenRouterModel): OpenRouter模型信息
**返回值**: string | null - 格式化的定价信息

#### `extractModelSize(modelId: string)`
```typescript
private extractModelSize(modelId: string): string | undefined
```
**功能**: 从模型ID中提取模型大小  
**参数**:
- `modelId` (string): 模型标识符
**返回值**: string | undefined - 模型大小

#### `isOpenRouterModelRecommended(model: OpenRouterModel)`
```typescript
private isOpenRouterModelRecommended(model: OpenRouterModel): boolean
```
**功能**: 判断是否为推荐的OpenRouter模型  
**参数**:
- `model` (OpenRouterModel): OpenRouter模型信息
**返回值**: boolean - 是否推荐

#### `isOpenRouterModelPopular(model: OpenRouterModel)`
```typescript
private isOpenRouterModelPopular(model: OpenRouterModel): boolean
```
**功能**: 判断是否为热门的OpenRouter模型  
**参数**:
- `model` (OpenRouterModel): OpenRouter模型信息
**返回值**: boolean - 是否热门

## OpenAI 相关方法

#### `testOpenAIConnection(baseUrl: string, apiKey: string)`
```typescript
async testOpenAIConnection(baseUrl: string, apiKey: string): Promise<{
  success: boolean,
  modelCount?: number,
  error?: string
}>
```
**功能**: 测试OpenAI连接  
**参数**:
- `baseUrl` (string): OpenAI API基础URL
- `apiKey` (string): OpenAI API密钥
**返回值**: 连接测试结果对象

#### `getOpenAIModels(baseUrl: string, apiKey: string)`
```typescript
async getOpenAIModels(baseUrl: string, apiKey: string): Promise<AIModel[]>
```
**功能**: 获取OpenAI模型列表  
**参数**:
- `baseUrl` (string): OpenAI API基础URL
- `apiKey` (string): OpenAI API密钥
**返回值**: Promise<AIModel[]> - OpenAI模型数组

## Azure OpenAI 相关方法

#### `testAzureOpenAIConnection(baseUrl: string, apiKey: string)`
```typescript
async testAzureOpenAIConnection(baseUrl: string, apiKey: string): Promise<{
  success: boolean,
  modelCount?: number,
  error?: string
}>
```
**功能**: 测试Azure OpenAI连接  
**参数**:
- `baseUrl` (string): Azure OpenAI端点URL
- `apiKey` (string): Azure OpenAI API密钥
**返回值**: 连接测试结果对象

#### `getAzureOpenAIModels(baseUrl: string, apiKey: string)`
```typescript
async getAzureOpenAIModels(baseUrl: string, apiKey: string): Promise<AIModel[]>
```
**功能**: 获取Azure OpenAI模型列表  
**参数**:
- `baseUrl` (string): Azure OpenAI端点URL
- `apiKey` (string): Azure OpenAI API密钥
**返回值**: Promise<AIModel[]> - Azure OpenAI模型数组

## Claude 相关方法

#### `testClaudeConnection(baseUrl: string, apiKey: string)`
```typescript
async testClaudeConnection(baseUrl: string, apiKey: string): Promise<{
  success: boolean,
  modelCount?: number,
  error?: string
}>
```
**功能**: 测试Claude连接  
**参数**:
- `baseUrl` (string): Claude API基础URL
- `apiKey` (string): Claude API密钥
**返回值**: 连接测试结果对象

#### `getClaudeModels()`
```typescript
async getClaudeModels(): Promise<AIModel[]>
```
**功能**: 获取Claude预设模型列表  
**返回值**: Promise<AIModel[]> - Claude模型数组

## Gemini 相关方法

#### `testGeminiConnection(baseUrl: string, apiKey: string)`
```typescript
async testGeminiConnection(baseUrl: string, apiKey: string): Promise<{
  success: boolean,
  modelCount?: number,
  error?: string
}>
```
**功能**: 测试Gemini连接  
**参数**:
- `baseUrl` (string): Gemini API基础URL
- `apiKey` (string): Gemini API密钥
**返回值**: 连接测试结果对象

#### `getGeminiModels(baseUrl: string, apiKey: string)`
```typescript
async getGeminiModels(baseUrl: string, apiKey: string): Promise<AIModel[]>
```
**功能**: 获取Gemini模型列表  
**参数**:
- `baseUrl` (string): Gemini API基础URL
- `apiKey` (string): Gemini API密钥
**返回值**: Promise<AIModel[]> - Gemini模型数组

## 其他提供商方法

### DeepSeek

#### `testDeepSeekConnection(baseUrl: string, apiKey: string)`
```typescript
async testDeepSeekConnection(baseUrl: string, apiKey: string): Promise<{
  success: boolean,
  modelCount?: number,
  error?: string
}>
```

#### `getDeepSeekModels()`
```typescript
async getDeepSeekModels(): Promise<AIModel[]>
```

### 智谱AI

#### `testZhipuConnection(baseUrl: string, apiKey: string)`
```typescript
async testZhipuConnection(baseUrl: string, apiKey: string): Promise<{
  success: boolean,
  modelCount?: number,
  error?: string
}>
```

#### `getZhipuModels()`
```typescript
async getZhipuModels(): Promise<AIModel[]>
```

### 通义千问

#### `testQwenConnection(baseUrl: string, apiKey: string)`
```typescript
async testQwenConnection(baseUrl: string, apiKey: string): Promise<{
  success: boolean,
  modelCount?: number,
  error?: string
}>
```

#### `getQwenModels()`
```typescript
async getQwenModels(): Promise<AIModel[]>
```

### Together AI

#### `testTogetherConnection(baseUrl: string, apiKey: string)`
```typescript
async testTogetherConnection(baseUrl: string, apiKey: string): Promise<{
  success: boolean,
  modelCount?: number,
  error?: string
}>
```

#### `getTogetherModels(baseUrl: string, apiKey: string)`
```typescript
async getTogetherModels(baseUrl: string, apiKey: string): Promise<AIModel[]>
```

### xAI Grok

#### `testGrokConnection(baseUrl: string, apiKey: string)`
```typescript
async testGrokConnection(baseUrl: string, apiKey: string): Promise<{
  success: boolean,
  modelCount?: number,
  error?: string
}>
```

#### `getGrokModels()`
```typescript
async getGrokModels(): Promise<AIModel[]>
```

### 自定义API

#### `testCustomAPI(config: CustomAPIConfig)`
```typescript
async testCustomAPI(config: CustomAPIConfig): Promise<{
  success: boolean,
  modelCount?: number,
  error?: string
}>
```

#### `getCustomModels(config: AIProviderConfig)`
```typescript
async getCustomModels(config: AIProviderConfig): Promise<AIModel[]>
```

## 工具方法

#### `formatBytes(bytes: number)`
```typescript
private formatBytes(bytes: number): string
```
**功能**: 格式化字节大小为可读格式  
**参数**:
- `bytes` (number): 字节数
**返回值**: string - 格式化的大小字符串（如 "3.54 GB"）

## 类型定义

### 核心接口

```typescript
interface AIProviderConfig {
  id: string
  name: string
  type: AIProvider
  baseUrl: string
  apiKey?: string
  headers?: Record<string, string>
  timeout?: number
  enabled: boolean
  createdAt: Date
  updatedAt: Date
}

interface AIConnectionResult {
  providerId: string
  success: boolean
  responseTime?: number
  modelCount?: number
  error?: string
  testedAt: Date
}

interface AIModel {
  id: string
  name: string
  displayName: string
  description?: string
  size?: string
  parameters?: string
  tags?: string[]
  capabilities?: string[]
  providerId: string
  isRecommended?: boolean
  isPopular?: boolean
}
```

### 提供商特定接口

```typescript
interface OllamaModel {
  name: string
  size: number
  digest: string
  details?: {
    format: string
    family: string
    families?: string[]
    parameter_size: string
    quantization_level: string
  }
}

interface LMStudioModel {
  id: string
  object: string
  created: number
  owned_by: string
}

interface OpenRouterModel {
  id: string
  name: string
  description?: string
  context_length: number
  architecture: {
    modality: string
    tokenizer: string
    instruct_type?: string
  }
  pricing: {
    prompt: string
    completion: string
    image?: string
    request?: string
  }
  top_provider: {
    context_length: number
    max_completion_tokens?: number
  }
}

interface XinferenceModel {
  model_name: string
  model_uid: string
  model_type: string
  model_size_in_billions?: number
  quantization?: string
  model_description?: string
  context_length?: number
  model_lang?: string[]
  model_ability?: string[]
}

interface CustomAPIConfig {
  baseUrl: string
  apiKey?: string
  headers?: Record<string, string>
  timeout?: number
}
```

## 使用示例

### 基本使用

```typescript
import { aiProviderService } from '../services/aiProviderService'

// 测试连接
const config: AIProviderConfig = {
  id: 'my-ollama',
  name: 'My Ollama',
  type: 'ollama',
  baseUrl: 'http://localhost:11434',
  enabled: true,
  createdAt: new Date(),
  updatedAt: new Date()
}

const result = await aiProviderService.testConnection(config)
if (result.success) {
  console.log('连接成功')
  
  // 获取模型列表
  const models = await aiProviderService.getModels(config)
  console.log(`找到 ${models.length} 个模型`)
}
```

### 错误处理

```typescript
try {
  const models = await aiProviderService.getOllamaModels('http://localhost:11434')
  // 处理模型列表
} catch (error) {
  console.error('获取模型失败:', error.message)
}
```

## 相关文档

- [AI提供商服务API文档](./aiProviderService-api.md)
- [OpenRouter API文档](./aiProviderService-openrouter-api.md)
- [AI集成服务文档](./aiIntegrationService-api.md)
- [AI模型服务文档](./aiModelService-api.md)