# 本地AI服务集成测试指南

## 概述

本指南将帮助您在Chrome扩展中测试本地AI服务集成功能。我们已经实现了对Ollama、LM Studio、Xinference等主流本地AI服务的支持。

## 安装和加载扩展

### 1. 构建扩展
```bash
npm run build
```

### 2. 加载到Chrome
1. 打开Chrome浏览器
2. 访问 `chrome://extensions/`
3. 开启右上角的"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择项目的 `dist` 文件夹
6. 扩展将被加载并显示在扩展列表中

## 访问测试页面

### 1. 打开选项页面
- 点击扩展图标，然后点击"选项"
- 或者在扩展管理页面点击"详细信息" → "扩展程序选项"

### 2. 导航到测试页面
- 在选项页面的左侧导航栏中找到"本地AI服务测试"
- 点击进入测试页面

## 测试功能

### 🔍 服务发现测试

**功能说明**: 自动扫描本地AI服务

**操作步骤**:
1. 在"自定义端口"输入框中可以添加额外的端口（可选）
   - 格式: `8888,9999,7777`
   - 留空则只扫描默认端口
2. 点击"发现服务"按钮开始扫描
3. 系统将自动扫描以下默认端口:
   - `11434` - Ollama
   - `1234` - LM Studio
   - `9997` - Xinference
   - `5000` - Text Generation WebUI
   - `8080` - LocalAI
   - `7860` - Gradio应用
   - `8000` - FastAPI服务

**预期结果**:
- 显示发现的服务数量
- 在日志中显示扫描过程
- 在"发现的服务"区域显示找到的服务

### 🧪 自定义服务测试

**功能说明**: 测试特定的AI服务配置

**操作步骤**:
1. 填写服务信息:
   - **服务名称**: 例如 "My AI Service"
   - **服务地址**: 例如 "http://localhost:8080"
   - **API路径**: 例如 "/v1" (可选)
   - **超时时间**: 例如 "10000" (毫秒)
2. 点击"测试自定义服务"按钮

**预期结果**:
- 显示连接测试结果
- 如果成功，显示响应时间和模型数量
- 如果失败，显示错误信息

### 📊 服务状态监控

**功能说明**: 查看发现服务的详细状态

**显示信息**:
- **状态图标**: 
  - 🟢 绿色勾号 = 在线
  - 🔴 红色叉号 = 离线
  - 🟡 黄色感叹号 = 未测试
- **状态徽章**: "在线"/"离线"/"未测试"
- **响应时间**: 连接延迟（毫秒）
- **模型数量**: 可用模型数量
- **最后检查**: 最后测试时间

**操作按钮**:
- **测试**: 重新测试服务连接
- **模型**: 获取服务的模型列表

### 📦 模型列表查看

**功能说明**: 查看AI服务的可用模型

**显示信息**:
- **模型名称**: 格式化的显示名称
- **描述**: 模型的详细描述
- **大小**: 模型文件大小
- **参数**: 模型参数数量
- **能力**: 支持的功能（chat、completion、coding等）
- **标签**: 推荐、热门等标签

### 📝 实时日志

**功能说明**: 查看测试过程的详细日志

**日志内容**:
- 服务发现过程
- 连接测试结果
- 模型获取状态
- 错误信息

**操作**:
- 点击"清空日志"清除所有日志记录

## 测试场景

### 场景1: 测试Ollama服务

**前提条件**: 
- 已安装并运行Ollama
- Ollama运行在默认端口11434

**测试步骤**:
1. 点击"发现服务"
2. 查看是否发现Ollama服务
3. 点击Ollama服务的"测试"按钮
4. 如果连接成功，点击"模型"按钮查看可用模型

**预期结果**:
- 发现Ollama服务
- 连接状态显示为"在线"
- 显示已安装的模型列表

### 场景2: 测试LM Studio服务

**前提条件**:
- 已安装并运行LM Studio
- 启用了本地服务器功能
- 运行在端口1234

**测试步骤**:
1. 点击"发现服务"
2. 查看是否发现LM Studio服务
3. 测试连接和获取模型列表

### 场景3: 测试自定义服务

**前提条件**:
- 有一个运行中的AI服务
- 知道服务的地址和端口

**测试步骤**:
1. 在自定义服务区域填写服务信息
2. 点击"测试自定义服务"
3. 查看连接结果

### 场景4: 测试端口扫描

**测试步骤**:
1. 在"自定义端口"中输入一些端口号
2. 点击"发现服务"
3. 查看扫描结果和日志

## 故障排除

### 问题1: 没有发现任何服务

**可能原因**:
- 没有运行任何AI服务
- 服务运行在非默认端口
- 防火墙阻止了连接

**解决方案**:
1. 确认AI服务正在运行
2. 检查服务运行的端口
3. 在自定义端口中添加正确的端口号
4. 检查防火墙设置

### 问题2: 服务显示离线

**可能原因**:
- 服务暂时不可用
- 网络连接问题
- 服务配置错误

**解决方案**:
1. 重启AI服务
2. 检查服务配置
3. 点击"测试"按钮重新测试
4. 查看日志中的错误信息

### 问题3: 无法获取模型列表

**可能原因**:
- 服务API不兼容
- 模型端点路径错误
- 权限问题

**解决方案**:
1. 检查服务的API文档
2. 尝试不同的API路径配置
3. 查看详细的错误日志

## 开发者信息

### 支持的服务类型

1. **Ollama** (端口11434)
   - API端点: `/api/version`, `/api/tags`
   - 模型格式: Ollama原生格式

2. **LM Studio** (端口1234)
   - API端点: `/v1/models`
   - 模型格式: OpenAI兼容格式

3. **Xinference** (端口9997)
   - API端点: `/v1/cluster/status`, `/v1/models`
   - 模型格式: Xinference格式

4. **通用OpenAI兼容服务**
   - API端点: `/v1/models`
   - 模型格式: OpenAI兼容格式

### 技术实现

- **服务发现**: 并发扫描多个端口
- **连接测试**: 带超时的HTTP请求
- **模型解析**: 智能识别不同服务的响应格式
- **错误处理**: 友好的错误信息和重试机制

### 扩展性

系统设计为可扩展的，可以轻松添加对新AI服务的支持：

1. 在`localAIServiceAdapter.ts`中添加新的服务配置
2. 实现服务特定的API调用逻辑
3. 添加模型格式解析器
4. 更新测试用例

## 反馈和支持

如果在测试过程中遇到问题或有改进建议，请：

1. 查看浏览器控制台的错误信息
2. 检查测试页面的日志输出
3. 记录具体的操作步骤和错误信息
4. 提供AI服务的版本和配置信息

测试愉快！🚀