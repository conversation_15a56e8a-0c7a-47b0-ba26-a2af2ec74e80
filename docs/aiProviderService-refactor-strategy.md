# AIProviderService 重构策略

## 问题分析
当前的 `AIProviderService` 类存在以下问题：
1. 超过4790行代码，违反单一职责原则
2. 包含所有AI提供商的逻辑，耦合度高
3. 难以测试和维护
4. 添加新提供商需要修改核心类

## 重构建议

### 1. 策略模式重构
创建抽象基类和具体实现类：

```typescript
// 抽象基类
abstract class BaseAIProvider {
  abstract testConnection(config: AIProviderConfig): Promise<AIConnectionResult>
  abstract getModels(config: AIProviderConfig): Promise<AIModel[]>
  abstract validateService(baseUrl: string): Promise<ServiceStatus>
}

// 具体实现
class OllamaProvider extends BaseAIProvider {
  async testConnection(config: AIProviderConfig): Promise<AIConnectionResult> {
    // Ollama特定逻辑
  }
  
  async getModels(config: AIProviderConfig): Promise<AIModel[]> {
    // Ollama模型获取逻辑
  }
}

class OpenAIProvider extends BaseAIProvider {
  // OpenAI特定实现
}
```

### 2. 工厂模式管理提供商
```typescript
class AIProviderFactory {
  private providers = new Map<string, BaseAIProvider>()
  
  constructor() {
    this.providers.set('ollama', new OllamaProvider())
    this.providers.set('openai', new OpenAIProvider())
    // 其他提供商...
  }
  
  getProvider(type: string): BaseAIProvider {
    const provider = this.providers.get(type)
    if (!provider) {
      throw new Error(`不支持的提供商类型: ${type}`)
    }
    return provider
  }
}
```

### 3. 重构后的主服务类
```typescript
export class AIProviderService {
  private factory = new AIProviderFactory()
  
  async testConnection(config: AIProviderConfig): Promise<AIConnectionResult> {
    const provider = this.factory.getProvider(config.type)
    return provider.testConnection(config)
  }
  
  async getModels(config: AIProviderConfig): Promise<AIModel[]> {
    const provider = this.factory.getProvider(config.type)
    return provider.getModels(config)
  }
}
```

## 优势
1. 符合开闭原则，添加新提供商无需修改现有代码
2. 每个提供商逻辑独立，便于测试和维护
3. 降低类的复杂度和耦合度
4. 提高代码可读性和可扩展性