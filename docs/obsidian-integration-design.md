# Obsidian 集成功能设计方案

## 功能概览

将书签数据同步到Obsidian笔记库，支持单个书签导出和批量同步，让用户可以在Obsidian中管理和关联书签内容。

## 核心功能

### 1. 单个书签导出
- 右键菜单"导出到Obsidian"
- 生成Markdown格式的书签笔记
- 包含元数据、标签、描述等信息

### 2. 批量同步
- 整个收藏夹同步到Obsidian
- 按分类创建文件夹结构
- 支持增量同步和全量同步

### 3. 智能模板
- 可自定义的Markdown模板
- 支持Obsidian插件格式（如Dataview）
- 自动生成双向链接

## 技术实现方案

### 1. Obsidian库检测
```typescript
class ObsidianDetector {
  async findObsidianVaults(): Promise<ObsidianVault[]> {
    const vaults: ObsidianVault[] = [];
    
    // Windows路径检测
    const commonPaths = [
      '%USERPROFILE%/Documents',
      '%USERPROFILE%/Desktop', 
      '%USERPROFILE%/OneDrive/Documents',
      'D:/Obsidian',
      'E:/Obsidian'
    ];
    
    for (const basePath of commonPaths) {
      try {
        const expandedPath = this.expandPath(basePath);
        const folders = await this.listDirectories(expandedPath);
        
        for (const folder of folders) {
          if (await this.isObsidianVault(folder)) {
            vaults.push({
              name: this.getFolderName(folder),
              path: folder,
              configPath: `${folder}/.obsidian`
            });
          }
        }
      } catch (error) {
        console.log(`无法访问路径: ${basePath}`);
      }
    }
    
    return vaults;
  }
  
  private async isObsidianVault(folderPath: string): Promise<boolean> {
    try {
      // 检查是否存在.obsidian配置文件夹
      const configExists = await this.pathExists(`${folderPath}/.obsidian`);
      return configExists;
    } catch {
      return false;
    }
  }
  
  private async pathExists(path: string): Promise<boolean> {
    // 使用File System Access API或Chrome扩展API检查路径
    return new Promise((resolve) => {
      chrome.runtime.sendMessage({
        action: 'checkPath',
        path: path
      }, (response) => {
        resolve(response.exists);
      });
    });
  }
}

interface ObsidianVault {
  name: string;
  path: string;
  configPath: string;
}
```

### 2. Markdown生成器
```typescript
class ObsidianMarkdownGenerator {
  private template: string;
  
  constructor(template?: string) {
    this.template = template || this.getDefaultTemplate();
  }
  
  generateBookmarkNote(bookmark: Bookmark, options: ExportOptions = {}): string {
    const frontmatter = this.generateFrontmatter(bookmark);
    const content = this.generateContent(bookmark, options);
    
    return `---\n${frontmatter}\n---\n\n${content}`;
  }
  
  private generateFrontmatter(bookmark: Bookmark): string {
    const metadata = {
      title: bookmark.title,
      url: bookmark.url,
      tags: bookmark.tags || [],
      category: bookmark.category || '未分类',
      created: new Date(bookmark.dateAdded).toISOString(),
      updated: new Date().toISOString(),
      type: 'bookmark',
      source: 'browser-extension'
    };
    
    return Object.entries(metadata)
      .map(([key, value]) => {
        if (Array.isArray(value)) {
          return `${key}: [${value.map(v => `"${v}"`).join(', ')}]`;
        }
        return `${key}: "${value}"`;
      })
      .join('\n');
  }
  
  private generateContent(bookmark: Bookmark, options: ExportOptions): string {
    let content = `# ${bookmark.title}\n\n`;
    
    // 添加链接
    content += `🔗 **链接**: [${bookmark.title}](${bookmark.url})\n\n`;
    
    // 添加描述
    if (bookmark.description) {
      content += `📝 **描述**: ${bookmark.description}\n\n`;
    }
    
    // 添加标签
    if (bookmark.tags && bookmark.tags.length > 0) {
      content += `🏷️ **标签**: ${bookmark.tags.map(tag => `#${tag}`).join(' ')}\n\n`;
    }
    
    // 添加分类
    if (bookmark.category) {
      content += `📁 **分类**: [[${bookmark.category}]]\n\n`;
    }
    
    // 添加AI分析结果（如果有）
    if (bookmark.aiAnalysis) {
      content += `## AI 分析\n\n`;
      content += `**主题**: ${bookmark.aiAnalysis.topics.join(', ')}\n\n`;
      content += `**推荐标签**: ${bookmark.aiAnalysis.tags.join(', ')}\n\n`;
    }
    
    // 添加笔记区域
    content += `## 笔记\n\n`;
    content += `<!-- 在这里添加你的笔记 -->\n\n`;
    
    // 添加相关链接区域
    content += `## 相关链接\n\n`;
    content += `<!-- 添加相关的双向链接 -->\n\n`;
    
    // 添加Dataview查询（如果启用）
    if (options.includeDataview) {
      content += this.generateDataviewQueries(bookmark);
    }
    
    return content;
  }
  
  private generateDataviewQueries(bookmark: Bookmark): string {
    let queries = `## 相关书签\n\n`;
    
    // 按标签查找相关书签
    if (bookmark.tags && bookmark.tags.length > 0) {
      queries += '```dataview\n';
      queries += 'TABLE title, url, category\n';
      queries += 'FROM #bookmark\n';
      queries += `WHERE contains(tags, "${bookmark.tags[0]}")\n`;
      queries += 'SORT created DESC\n';
      queries += 'LIMIT 10\n';
      queries += '```\n\n';
    }
    
    return queries;
  }
  
  generateBasesDatabase(bookmarks: Bookmark[]): string {
    // 生成Bases数据库格式的书签表格
    let basesContent = `# 书签数据库\n\n`;
    
    // 添加数据库表格头部
    basesContent += `\`\`\`bases-database\n`;
    basesContent += `name: 书签管理\n`;
    basesContent += `columns:\n`;
    basesContent += `  - name: 标题\n`;
    basesContent += `    type: text\n`;
    basesContent += `    primary: true\n`;
    basesContent += `  - name: URL\n`;
    basesContent += `    type: url\n`;
    basesContent += `  - name: 分类\n`;
    basesContent += `    type: select\n`;
    basesContent += `    options: [开发工具, 学习资源, 新闻资讯, 娱乐, 购物, 社交, 工作, 设计, 技术文档]\n`;
    basesContent += `  - name: 标签\n`;
    basesContent += `    type: multi-select\n`;
    basesContent += `  - name: 描述\n`;
    basesContent += `    type: long-text\n`;
    basesContent += `  - name: 创建时间\n`;
    basesContent += `    type: date\n`;
    basesContent += `  - name: 评分\n`;
    basesContent += `    type: number\n`;
    basesContent += `    min: 1\n`;
    basesContent += `    max: 5\n`;
    basesContent += `  - name: 状态\n`;
    basesContent += `    type: select\n`;
    basesContent += `    options: [未读, 已读, 收藏, 归档]\n`;
    basesContent += `\`\`\`\n\n`;
    
    // 添加数据行
    basesContent += `## 书签数据\n\n`;
    basesContent += `| 标题 | URL | 分类 | 标签 | 描述 | 创建时间 | 评分 | 状态 |\n`;
    basesContent += `|------|-----|------|------|------|----------|------|------|\n`;
    
    bookmarks.forEach(bookmark => {
      const title = bookmark.title.replace(/\|/g, '\\|');
      const url = bookmark.url;
      const category = bookmark.category || '未分类';
      const tags = bookmark.tags ? bookmark.tags.join(', ') : '';
      const description = (bookmark.description || '').replace(/\|/g, '\\|').substring(0, 50);
      const created = new Date(bookmark.dateAdded).toISOString().split('T')[0];
      const rating = bookmark.rating || '';
      const status = bookmark.status || '未读';
      
      basesContent += `| ${title} | ${url} | ${category} | ${tags} | ${description} | ${created} | ${rating} | ${status} |\n`;
    });
    
    return basesContent;
  }
  
  private getDefaultTemplate(): string {
    return `# {{title}}

🔗 **链接**: [{{title}}]({{url}})

📝 **描述**: {{description}}

🏷️ **标签**: {{tags}}

📁 **分类**: [[{{category}}]]

## 笔记

<!-- 在这里添加你的笔记 -->

## 相关链接

<!-- 添加相关的双向链接 -->
`;
  }
}

interface ExportOptions {
  includeDataview?: boolean;
  customTemplate?: string;
  folderPath?: string;
  filenamePattern?: string;
}
```

### 3. 文件系统操作
```typescript
class ObsidianFileManager {
  private vaultPath: string;
  
  constructor(vaultPath: string) {
    this.vaultPath = vaultPath;
  }
  
  async saveBookmarkNote(bookmark: Bookmark, markdown: string, options: ExportOptions = {}): Promise<boolean> {
    try {
      const folderPath = options.folderPath || 'Bookmarks';
      const filename = this.generateFilename(bookmark, options.filenamePattern);
      const fullPath = `${this.vaultPath}/${folderPath}/${filename}`;
      
      // 确保文件夹存在
      await this.ensureDirectoryExists(`${this.vaultPath}/${folderPath}`);
      
      // 写入文件
      await this.writeFile(fullPath, markdown);
      
      return true;
    } catch (error) {
      console.error('保存书签笔记失败:', error);
      return false;
    }
  }
  
  async syncBookmarkFolder(bookmarks: Bookmark[], folderName: string): Promise<SyncResult> {
    const result: SyncResult = {
      success: 0,
      failed: 0,
      errors: []
    };
    
    const generator = new ObsidianMarkdownGenerator();
    const targetFolder = `Bookmarks/${folderName}`;
    
    // 确保目标文件夹存在
    await this.ensureDirectoryExists(`${this.vaultPath}/${targetFolder}`);
    
    for (const bookmark of bookmarks) {
      try {
        const markdown = generator.generateBookmarkNote(bookmark, {
          includeDataview: true,
          folderPath: targetFolder
        });
        
        const success = await this.saveBookmarkNote(bookmark, markdown, {
          folderPath: targetFolder
        });
        
        if (success) {
          result.success++;
        } else {
          result.failed++;
        }
      } catch (error) {
        result.failed++;
        result.errors.push(`${bookmark.title}: ${error.message}`);
      }
    }
    
    return result;
  }
  
  private generateFilename(bookmark: Bookmark, pattern?: string): string {
    if (pattern) {
      return pattern
        .replace('{{title}}', this.sanitizeFilename(bookmark.title))
        .replace('{{date}}', new Date().toISOString().split('T')[0])
        .replace('{{id}}', bookmark.id);
    }
    
    // 默认文件名格式
    const sanitizedTitle = this.sanitizeFilename(bookmark.title);
    return `${sanitizedTitle}.md`;
  }
  
  private sanitizeFilename(filename: string): string {
    // 移除或替换不允许的文件名字符
    return filename
      .replace(/[<>:"/\\|?*]/g, '-')
      .replace(/\s+/g, ' ')
      .trim()
      .substring(0, 100); // 限制长度
  }
  
  private async writeFile(path: string, content: string): Promise<void> {
    // 使用Chrome扩展的文件API或File System Access API
    return new Promise((resolve, reject) => {
      chrome.runtime.sendMessage({
        action: 'writeFile',
        path: path,
        content: content
      }, (response) => {
        if (response.success) {
          resolve();
        } else {
          reject(new Error(response.error));
        }
      });
    });
  }
  
  private async ensureDirectoryExists(dirPath: string): Promise<void> {
    return new Promise((resolve, reject) => {
      chrome.runtime.sendMessage({
        action: 'ensureDirectory',
        path: dirPath
      }, (response) => {
        if (response.success) {
          resolve();
        } else {
          reject(new Error(response.error));
        }
      });
    });
  }
}

interface SyncResult {
  success: number;
  failed: number;
  errors: string[];
}

### 5. Bases数据库管理器
```typescript
class BasesManager {
  private vaultPath: string;
  private fileManager: ObsidianFileManager;
  
  constructor(vaultPath: string) {
    this.vaultPath = vaultPath;
    this.fileManager = new ObsidianFileManager(vaultPath);
  }
  
  async createBookmarkDatabase(bookmarks: Bookmark[], databaseName: string = '书签数据库'): Promise<boolean> {
    try {
      const generator = new ObsidianMarkdownGenerator();
      const databaseContent = generator.generateBasesDatabase(bookmarks);
      
      const filename = `${databaseName}.md`;
      const success = await this.fileManager.writeFile(
        `${this.vaultPath}/Databases/${filename}`,
        databaseContent
      );
      
      return success;
    } catch (error) {
      console.error('创建Bases数据库失败:', error);
      return false;
    }
  }
  
  async updateBookmarkInDatabase(bookmark: Bookmark, databasePath: string): Promise<boolean> {
    try {
      // 读取现有数据库文件
      const existingContent = await this.readDatabaseFile(databasePath);
      
      // 解析现有数据
      const { metadata, rows } = this.parseDatabaseContent(existingContent);
      
      // 更新或添加书签行
      const updatedRows = this.updateBookmarkRow(rows, bookmark);
      
      // 重新生成数据库内容
      const newContent = this.generateDatabaseContent(metadata, updatedRows);
      
      // 写回文件
      await this.fileManager.writeFile(databasePath, newContent);
      
      return true;
    } catch (error) {
      console.error('更新Bases数据库失败:', error);
      return false;
    }
  }
  
  async syncBookmarksToDatabase(bookmarks: Bookmark[], databasePath: string): Promise<SyncResult> {
    const result: SyncResult = {
      success: 0,
      failed: 0,
      errors: []
    };
    
    try {
      // 检查数据库是否存在，不存在则创建
      const databaseExists = await this.databaseExists(databasePath);
      
      if (!databaseExists) {
        const created = await this.createBookmarkDatabase(bookmarks, '书签数据库');
        if (created) {
          result.success = bookmarks.length;
        } else {
          result.failed = bookmarks.length;
          result.errors.push('创建数据库失败');
        }
        return result;
      }
      
      // 逐个同步书签
      for (const bookmark of bookmarks) {
        try {
          const success = await this.updateBookmarkInDatabase(bookmark, databasePath);
          if (success) {
            result.success++;
          } else {
            result.failed++;
          }
        } catch (error) {
          result.failed++;
          result.errors.push(`${bookmark.title}: ${error.message}`);
        }
      }
      
    } catch (error) {
      result.errors.push(`同步失败: ${error.message}`);
    }
    
    return result;
  }
  
  private async readDatabaseFile(path: string): Promise<string> {
    return new Promise((resolve, reject) => {
      chrome.runtime.sendMessage({
        action: 'readFile',
        path: path
      }, (response) => {
        if (response.success) {
          resolve(response.content);
        } else {
          reject(new Error(response.error));
        }
      });
    });
  }
  
  private parseDatabaseContent(content: string): { metadata: any, rows: any[] } {
    // 解析Bases数据库格式的内容
    const lines = content.split('\n');
    let inMetadata = false;
    let inTable = false;
    const metadata = {};
    const rows = [];
    
    for (const line of lines) {
      if (line.startsWith('```bases-database')) {
        inMetadata = true;
        continue;
      }
      
      if (line.startsWith('```') && inMetadata) {
        inMetadata = false;
        continue;
      }
      
      if (inMetadata) {
        // 解析元数据
        const [key, ...valueParts] = line.split(':');
        if (key && valueParts.length > 0) {
          metadata[key.trim()] = valueParts.join(':').trim();
        }
      }
      
      if (line.startsWith('|') && line.includes('标题')) {
        inTable = true;
        continue;
      }
      
      if (inTable && line.startsWith('|') && !line.includes('---')) {
        // 解析表格行
        const cells = line.split('|').map(cell => cell.trim()).filter(Boolean);
        if (cells.length > 0) {
          rows.push(cells);
        }
      }
    }
    
    return { metadata, rows };
  }
  
  private updateBookmarkRow(existingRows: any[], bookmark: Bookmark): any[] {
    // 查找是否已存在该书签
    const existingIndex = existingRows.findIndex(row => 
      row[1] === bookmark.url // 假设URL在第二列
    );
    
    const newRow = [
      bookmark.title,
      bookmark.url,
      bookmark.category || '未分类',
      bookmark.tags ? bookmark.tags.join(', ') : '',
      bookmark.description || '',
      new Date(bookmark.dateAdded).toISOString().split('T')[0],
      bookmark.rating || '',
      bookmark.status || '未读'
    ];
    
    if (existingIndex >= 0) {
      // 更新现有行
      existingRows[existingIndex] = newRow;
    } else {
      // 添加新行
      existingRows.push(newRow);
    }
    
    return existingRows;
  }
  
  private generateDatabaseContent(metadata: any, rows: any[]): string {
    const generator = new ObsidianMarkdownGenerator();
    
    // 重新构建书签对象数组
    const bookmarks = rows.map(row => ({
      title: row[0],
      url: row[1],
      category: row[2],
      tags: row[3] ? row[3].split(', ') : [],
      description: row[4],
      dateAdded: new Date(row[5]).getTime(),
      rating: row[6],
      status: row[7]
    }));
    
    return generator.generateBasesDatabase(bookmarks);
  }
  
  private async databaseExists(path: string): Promise<boolean> {
    return new Promise((resolve) => {
      chrome.runtime.sendMessage({
        action: 'checkPath',
        path: path
      }, (response) => {
        resolve(response.exists);
      });
    });
  }
  
  async createDatabaseViews(databasePath: string): Promise<boolean> {
    try {
      // 创建不同的视图文件
      const views = [
        {
          name: '按分类查看',
          filter: 'GROUP BY 分类',
          sort: '分类 ASC, 创建时间 DESC'
        },
        {
          name: '收藏书签',
          filter: 'WHERE 状态 = "收藏"',
          sort: '创建时间 DESC'
        },
        {
          name: '待读列表',
          filter: 'WHERE 状态 = "未读"',
          sort: '创建时间 ASC'
        },
        {
          name: '高评分书签',
          filter: 'WHERE 评分 >= 4',
          sort: '评分 DESC, 创建时间 DESC'
        }
      ];
      
      for (const view of views) {
        const viewContent = this.generateViewContent(databasePath, view);
        const viewPath = `${this.vaultPath}/Views/${view.name}.md`;
        await this.fileManager.writeFile(viewPath, viewContent);
      }
      
      return true;
    } catch (error) {
      console.error('创建数据库视图失败:', error);
      return false;
    }
  }
  
  private generateViewContent(databasePath: string, view: any): string {
    return `# ${view.name}

\`\`\`bases-view
database: [[${databasePath}]]
filter: ${view.filter}
sort: ${view.sort}
\`\`\`

## 说明

这是一个自动生成的书签数据库视图，显示${view.name}的内容。

### 筛选条件
- ${view.filter}

### 排序方式  
- ${view.sort}
`;
  }
}
```
```

### 4. 用户界面组件
```typescript
const ObsidianIntegrationComponent = () => {
  const [vaults, setVaults] = useState<ObsidianVault[]>([]);
  const [selectedVault, setSelectedVault] = useState<string>('');
  const [syncSettings, setSyncSettings] = useState({
    folderPath: 'Bookmarks',
    includeDataview: true,
    filenamePattern: '{{title}}',
    autoSync: false
  });
  
  useEffect(() => {
    loadObsidianVaults();
  }, []);
  
  const loadObsidianVaults = async () => {
    const detector = new ObsidianDetector();
    const foundVaults = await detector.findObsidianVaults();
    setVaults(foundVaults);
    
    if (foundVaults.length > 0) {
      setSelectedVault(foundVaults[0].path);
    }
  };
  
  const handleExportBookmark = async (bookmark: Bookmark) => {
    if (!selectedVault) {
      alert('请先选择Obsidian库');
      return;
    }
    
    const fileManager = new ObsidianFileManager(selectedVault);
    const generator = new ObsidianMarkdownGenerator();
    
    const markdown = generator.generateBookmarkNote(bookmark, {
      includeDataview: syncSettings.includeDataview,
      folderPath: syncSettings.folderPath
    });
    
    const success = await fileManager.saveBookmarkNote(bookmark, markdown, syncSettings);
    
    if (success) {
      showNotification('书签已导出到Obsidian', 'success');
    } else {
      showNotification('导出失败', 'error');
    }
  };
  
  const handleSyncFolder = async (folderName: string, bookmarks: Bookmark[]) => {
    if (!selectedVault) {
      alert('请先选择Obsidian库');
      return;
    }
    
    if (syncSettings.exportType === 'bases') {
      await handleCreateDatabase(bookmarks);
    } else {
      const fileManager = new ObsidianFileManager(selectedVault);
      const result = await fileManager.syncBookmarkFolder(bookmarks, folderName);
      
      showNotification(
        `同步完成: ${result.success} 成功, ${result.failed} 失败`,
        result.failed > 0 ? 'warning' : 'success'
      );
    }
  };
  
  const handleCreateDatabase = async (bookmarks: Bookmark[]) => {
    if (!selectedVault) {
      alert('请先选择Obsidian库');
      return;
    }
    
    const basesManager = new BasesManager(selectedVault);
    const databaseName = syncSettings.databaseName || '书签数据库';
    
    try {
      // 创建数据库
      const success = await basesManager.createBookmarkDatabase(bookmarks, databaseName);
      
      if (success) {
        showNotification(`成功创建数据库: ${databaseName}`, 'success');
        
        // 如果启用了视图创建
        if (syncSettings.createViews) {
          const databasePath = `${selectedVault}/Databases/${databaseName}.md`;
          await basesManager.createDatabaseViews(databasePath);
          showNotification('预设视图创建完成', 'success');
        }
      } else {
        showNotification('数据库创建失败', 'error');
      }
    } catch (error) {
      showNotification(`创建失败: ${error.message}`, 'error');
    }
  };
  
  const handleSyncToDatabase = async (bookmarks: Bookmark[]) => {
    if (!selectedVault) {
      alert('请先选择Obsidian库');
      return;
    }
    
    const basesManager = new BasesManager(selectedVault);
    const databaseName = syncSettings.databaseName || '书签数据库';
    const databasePath = `${selectedVault}/Databases/${databaseName}.md`;
    
    try {
      const result = await basesManager.syncBookmarksToDatabase(bookmarks, databasePath);
      
      showNotification(
        `同步完成: ${result.success} 成功, ${result.failed} 失败`,
        result.failed > 0 ? 'warning' : 'success'
      );
      
      if (result.errors.length > 0) {
        console.log('同步错误:', result.errors);
      }
    } catch (error) {
      showNotification(`同步失败: ${error.message}`, 'error');
    }
  };
  
  return (
    <div className="obsidian-integration">
      <h3>Obsidian 集成</h3>
      
      <div className="vault-selection">
        <label>选择Obsidian库:</label>
        <select 
          value={selectedVault}
          onChange={(e) => setSelectedVault(e.target.value)}
        >
          <option value="">请选择...</option>
          {vaults.map(vault => (
            <option key={vault.path} value={vault.path}>
              {vault.name} ({vault.path})
            </option>
          ))}
        </select>
        <button onClick={loadObsidianVaults}>刷新</button>
      </div>
      
      <div className="sync-settings">
        <h4>同步设置</h4>
        
        <div className="setting-item">
          <label>目标文件夹:</label>
          <input 
            type="text"
            value={syncSettings.folderPath}
            onChange={(e) => setSyncSettings({...syncSettings, folderPath: e.target.value})}
            placeholder="Bookmarks"
          />
        </div>
        
        <div className="setting-item">
          <label>文件名模式:</label>
          <input 
            type="text"
            value={syncSettings.filenamePattern}
            onChange={(e) => setSyncSettings({...syncSettings, filenamePattern: e.target.value})}
            placeholder="{{title}}"
          />
          <small>可用变量: {{title}}, {{date}}, {{id}}</small>
        </div>
        
        <div className="setting-item">
          <label>
            <input 
              type="checkbox"
              checked={syncSettings.includeDataview}
              onChange={(e) => setSyncSettings({...syncSettings, includeDataview: e.target.checked})}
            />
            包含Dataview查询
          </label>
        </div>
        
        <div className="setting-item">
          <label>
            <input 
              type="checkbox"
              checked={syncSettings.autoSync}
              onChange={(e) => setSyncSettings({...syncSettings, autoSync: e.target.checked})}
            />
            自动同步新书签
          </label>
        </div>
      </div>
      
      <div className="export-options">
        <h4>导出选项</h4>
        
        <div className="option-group">
          <label>
            <input 
              type="radio" 
              name="exportType" 
              value="markdown"
              checked={syncSettings.exportType === 'markdown'}
              onChange={(e) => setSyncSettings({...syncSettings, exportType: e.target.value})}
            />
            标准Markdown文件
          </label>
          
          <label>
            <input 
              type="radio" 
              name="exportType" 
              value="bases"
              checked={syncSettings.exportType === 'bases'}
              onChange={(e) => setSyncSettings({...syncSettings, exportType: e.target.value})}
            />
            Bases数据库表格
          </label>
        </div>
        
        {syncSettings.exportType === 'bases' && (
          <div className="bases-options">
            <div className="setting-item">
              <label>数据库名称:</label>
              <input 
                type="text"
                value={syncSettings.databaseName || '书签数据库'}
                onChange={(e) => setSyncSettings({...syncSettings, databaseName: e.target.value})}
                placeholder="书签数据库"
              />
            </div>
            
            <div className="setting-item">
              <label>
                <input 
                  type="checkbox"
                  checked={syncSettings.createViews}
                  onChange={(e) => setSyncSettings({...syncSettings, createViews: e.target.checked})}
                />
                创建预设视图（按分类、收藏、待读等）
              </label>
            </div>
            
            <div className="setting-item">
              <label>
                <input 
                  type="checkbox"
                  checked={syncSettings.enableRating}
                  onChange={(e) => setSyncSettings({...syncSettings, enableRating: e.target.checked})}
                />
                启用评分字段
              </label>
            </div>
            
            <div className="setting-item">
              <label>
                <input 
                  type="checkbox"
                  checked={syncSettings.enableStatus}
                  onChange={(e) => setSyncSettings({...syncSettings, enableStatus: e.target.checked})}
                />
                启用状态字段（未读/已读/收藏/归档）
              </label>
            </div>
          </div>
        )}
      </div>
      
      <div className="actions">
        <button onClick={() => handleExportBookmark(currentBookmark)}>
          导出当前书签
        </button>
        <button onClick={() => handleSyncFolder('全部书签', allBookmarks)}>
          同步所有书签
        </button>
        
        {syncSettings.exportType === 'bases' && (
          <>
            <button onClick={() => handleCreateDatabase(allBookmarks)}>
              创建Bases数据库
            </button>
            <button onClick={() => handleSyncToDatabase(allBookmarks)}>
              同步到现有数据库
            </button>
          </>
        )}
      </div>
      
      <ObsidianTemplateEditor 
        template={syncSettings.template}
        onTemplateChange={(template) => setSyncSettings({...syncSettings, template})}
      />
    </div>
  );
};
```

## 高级功能

### 1. 双向同步
- 检测Obsidian中书签笔记的修改
- 将笔记内容同步回书签描述
- 支持标签和分类的双向更新

### 2. 智能模板
- 支持自定义Markdown模板
- 预设多种模板样式
- 支持Obsidian插件格式

### 3. 批量操作
- 按分类批量导出
- 按标签筛选导出
- 支持增量同步

### 4. 集成增强
- 支持Obsidian插件（如Dataview、Templater）
- 自动生成MOC（Map of Content）
- 支持图谱视图的链接关系

### 5. Bases 数据库支持
- 自动创建书签数据库表格
- 支持Bases插件的数据库视图
- 提供结构化的书签管理界面
- 支持自定义字段和筛选条件

## 会员功能定位

将Obsidian集成作为**高级会员功能**，分层提供：

### 基础版功能 ($3.99)
- 单个书签导出为Markdown
- 基础模板支持
- 简单的文件夹同步

### 高级版功能 (完整版 $12.99)
- 批量书签同步
- **Bases数据库集成**
- 自定义数据库字段
- 预设视图创建（按分类、状态、评分等）
- 双向同步支持
- AI增强的标签和分类
- 自定义模板编辑器
- 智能链接生成

## Bases集成的独特价值

### 1. 结构化管理
- 将书签转换为结构化数据库
- 支持筛选、排序、分组等高级操作
- 提供类似Notion的表格体验

### 2. 多维度视图
- 按分类查看书签
- 收藏和待读列表
- 高评分书签筛选
- 自定义查询条件

### 3. 数据分析
- 书签收集统计
- 分类分布分析
- 使用频率追踪
- 标签云生成

### 4. 工作流集成
- 与Obsidian其他插件协同
- 支持模板和自动化
- 图谱视图中的关系展示

## 技术优势

### 1. 无缝集成
- 利用用户现有的Obsidian工作流
- 不需要学习新的工具
- 数据完全在用户控制之下

### 2. 灵活扩展
- 支持自定义字段
- 可以添加笔记和关联
- 与其他知识管理工具兼容

### 3. 离线可用
- 所有数据存储在本地
- 不依赖网络连接
- 完全的数据隐私保护

这个Bases集成功能将是知识管理用户的杀手级功能，特别适合研究人员、学者和重度信息收集者！