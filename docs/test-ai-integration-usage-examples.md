# AI集成服务测试脚本使用示例

## 快速开始

### 基本使用

```bash
# 直接运行完整测试
node scripts/test-ai-integration.js

# 通过npm脚本运行
npm run test:ai-integration
```

### 运行前准备

1. **确保项目已构建**：
```bash
npm run build
```

2. **启动本地AI服务**（可选）：
```bash
# 启动Ollama（如果已安装）
ollama serve

# 启动LM Studio（如果已安装）
# 在LM Studio中启用本地服务器

# 启动Xinference（如果已安装）
xinference-local --host 0.0.0.0 --port 9997
```

## 测试输出示例

### 成功运行输出

```
🚀 开始AI集成服务综合测试...
============================================================

🧪 测试AI集成服务基本功能...

📋 1. 获取支持的提供商列表
✅ 找到 14 个支持的提供商:
   - Ollama (ollama): 本地部署的开源AI模型服务
   - LM Studio (lm-studio): 本地AI模型运行环境
   - OpenRouter (openrouter): 多模型聚合API服务
   - OpenAI (openai): OpenAI GPT系列模型
   - Anthropic Claude (claude): Anthropic的Claude系列模型
   - Google Gemini (gemini): Google的Gemini系列模型
   - DeepSeek (deepseek): DeepSeek AI模型服务
   - 智谱AI (zhipu): 智谱AI GLM系列模型
   - 通义千问 (qwen): 阿里云通义千问模型
   - Together AI (together): Together AI模型聚合服务
   - xAI Grok (grok): xAI的Grok模型
   - Xinference (xinference): Xinference分布式推理引擎
   - Azure OpenAI (azure-openai): Microsoft Azure部署的OpenAI模型
   - 自定义API (custom): 自定义AI服务API

📋 2. 获取已配置的提供商列表
✅ 找到 2 个已配置的提供商:
   - 演示 OpenAI (openai): https://api.openai.com/v1
   - 演示 Ollama (ollama): http://localhost:11434

📋 3. 测试添加新提供商
✅ 成功添加新提供商

📋 4. 测试提供商连接
   测试 演示 OpenAI...
   ✅ 演示 OpenAI: 1250ms
   测试 演示 Ollama...
   ❌ 演示 Ollama: 连接超时或API密钥无效

📋 5. 测试获取模型列表
   获取 演示 OpenAI 模型...
   ✅ 演示 OpenAI: 找到 2 个模型
      - GPT-4: OpenAI最先进的大型语言模型
      - GPT-3.5 Turbo: OpenAI高效的对话模型
   获取 演示 Ollama 模型...
   ✅ 演示 Ollama: 找到 0 个模型

🎉 AI集成服务基本功能测试完成！

🔍 测试本地AI服务发现功能...

📋 1. 发现本地AI服务
✅ 发现 0 个本地服务:

⚠️  扫描错误 12 个:
   - 端口 1234: 无法连接到端口 1234
   - 端口 5000: 无法连接到端口 5000
   - 端口 7860: 无法连接到端口 7860

📋 2. 测试本地服务连接
（无可用服务进行连接测试）

📋 3. 测试获取本地服务模型
（无可用服务获取模型）

🎉 本地AI服务发现功能测试完成！

💬 测试AI对话功能...

📋 1. 测试本地服务对话
   模拟本地服务对话测试...
   ✅ 本地服务对话功能正常（模拟测试）

📋 2. 测试云端服务对话
   模拟 演示 OpenAI 对话测试...
   ✅ 云端服务对话功能正常（模拟测试）

📋 3. 测试模型连接
   ✅ 模型连接测试功能正常（模拟测试）

🎉 AI对话功能测试完成！

⚙️ 测试配置管理功能...

📋 1. 测试导出配置
✅ 导出配置成功: 3 个提供商

📋 2. 测试获取统计信息
✅ 统计信息: 总计 3, 启用 3, 连接 1

📋 3. 测试配置验证
✅ 配置验证: 支持 14 种提供商类型

🎉 配置管理功能测试完成！

============================================================
📊 测试结果汇总:
   ✅ AI集成服务基本功能
   ✅ 本地AI服务发现功能
   ✅ AI对话功能
   ✅ 配置管理功能

🎯 测试完成: 4/4 项通过
🎉 所有测试都通过了！聚合AI服务集成功能正常。

📝 测试说明:
   - 本测试使用模拟数据和环境
   - 实际功能需要在浏览器扩展环境中测试
   - 请访问插件选项页面进行完整测试
   - 测试页面: chrome-extension://[extension-id]/src/options/index.html#local-ai-test
```

### 部分测试失败的输出

```
🧪 测试AI集成服务基本功能...

📋 1. 获取支持的提供商列表
✅ 找到 14 个支持的提供商:
   - Ollama (ollama): 本地部署的开源AI模型服务
   ...

📋 2. 获取已配置的提供商列表
❌ AI集成服务测试失败: Cannot resolve module '../src/services/aiIntegrationService.ts'

============================================================
📊 测试结果汇总:
   ❌ AI集成服务基本功能
   ✅ 本地AI服务发现功能
   ✅ AI对话功能
   ✅ 配置管理功能

🎯 测试完成: 3/4 项通过
⚠️  部分测试未通过，请检查相关功能。
```

## 自定义使用场景

### 场景1: 只测试特定功能

```javascript
// 只测试AI集成基本功能
const { testAIIntegrationBasics } = require('./scripts/test-ai-integration.js')

async function testBasicOnly() {
  console.log('🧪 只测试AI集成基本功能...')
  const result = await testAIIntegrationBasics()
  console.log(`测试结果: ${result ? '✅ 通过' : '❌ 失败'}`)
}

testBasicOnly()
```

### 场景2: 自定义测试数据

```javascript
// 修改模拟数据
global.chrome = {
  storage: {
    sync: {
      get: (keys, callback) => {
        const customMockData = {
          ai_providers: [
            {
              id: 'custom_openai',
              type: 'openai',
              name: '自定义 OpenAI',
              baseUrl: 'https://api.openai.com/v1',
              apiKey: 'sk-custom-key',
              enabled: true,
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString()
            }
          ]
        }
        
        if (typeof keys === 'string') {
          callback({ [keys]: customMockData[keys] })
        } else {
          callback(customMockData)
        }
      },
      set: (data, callback) => {
        console.log('💾 保存自定义数据:', data)
        if (callback) callback()
      }
    }
  }
}

// 然后运行测试
const { runAllTests } = require('./scripts/test-ai-integration.js')
runAllTests()
```

### 场景3: 集成到开发流程

```javascript
// dev-test.js
const { testAIIntegrationBasics, testLocalAIServiceDiscovery } = require('./scripts/test-ai-integration.js')

async function quickDevTest() {
  console.log('🚀 快速开发测试...')
  
  // 只测试核心功能
  const basicResult = await testAIIntegrationBasics()
  const localResult = await testLocalAIServiceDiscovery()
  
  if (basicResult && localResult) {
    console.log('✅ 开发测试通过，可以继续开发')
    return true
  } else {
    console.log('❌ 开发测试失败，请检查代码')
    return false
  }
}

quickDevTest()
```

## 故障排除

### 问题1: 模块加载失败

**错误信息**：
```
❌ AI集成服务测试失败: Cannot resolve module '../src/services/aiIntegrationService.ts'
```

**解决方法**：
```bash
# 重新构建项目
npm run build

# 检查构建输出
ls dist/

# 确保TypeScript文件已编译为JavaScript
```

### 问题2: Chrome存储API错误

**错误信息**：
```
❌ 获取已配置提供商失败: chrome.storage is not defined
```

**解决方法**：
```javascript
// 确保Chrome API模拟已正确设置
if (typeof chrome === 'undefined') {
  // 模拟代码应该在这里
  global.chrome = { /* 模拟实现 */ }
}
```

### 问题3: 网络连接测试失败

**错误信息**：
```
❌ 演示 OpenAI: 连接超时或API密钥无效
```

**解决方法**：
```javascript
// 这是正常的，因为使用的是演示API密钥
// 如果需要真实测试，请提供有效的API密钥
const mockData = {
  ai_providers: [
    {
      // 使用真实的API密钥
      apiKey: 'sk-real-api-key-here'
    }
  ]
}
```

## 开发者扩展

### 添加新的测试功能

```javascript
/**
 * 测试新功能
 */
async function testNewFeature() {
  console.log('\n🆕 测试新功能...')
  
  try {
    // 添加测试逻辑
    console.log('\n📋 1. 测试新功能项目1')
    // 测试代码...
    
    console.log('\n🎉 新功能测试完成！')
    return true
  } catch (error) {
    console.error('❌ 新功能测试失败:', error)
    return false
  }
}

// 添加到主测试函数
async function runAllTestsExtended() {
  const results = []
  
  // 运行原有测试
  results.push(await testAIIntegrationBasics())
  results.push(await testLocalAIServiceDiscovery())
  results.push(await testAIChatFunctionality())
  results.push(await testConfigurationManagement())
  
  // 运行新测试
  results.push(await testNewFeature())
  
  // 汇总结果...
}
```

### 自定义输出格式

```javascript
// 自定义日志函数
function customLog(level, message) {
  const timestamp = new Date().toLocaleTimeString()
  const icons = {
    success: '✅',
    error: '❌', 
    warning: '⚠️',
    info: 'ℹ️'
  }
  
  console.log(`[${timestamp}] ${icons[level]} ${message}`)
}

// 在测试函数中使用
async function testWithCustomLogging() {
  customLog('info', '开始自定义测试...')
  
  try {
    // 测试逻辑
    customLog('success', '测试通过')
    return true
  } catch (error) {
    customLog('error', `测试失败: ${error.message}`)
    return false
  }
}
```

## package.json 脚本配置

```json
{
  "scripts": {
    "test:ai-integration": "node scripts/test-ai-integration.js",
    "test:ai-quick": "node -e \"require('./scripts/test-ai-integration.js').testAIIntegrationBasics()\"",
    "test:ai-local": "node -e \"require('./scripts/test-ai-integration.js').testLocalAIServiceDiscovery()\"",
    "test:ai-full": "npm run build && npm run test:ai-integration",
    "dev:ai-test": "npm run build && npm run test:ai-integration && npm run dev"
  }
}
```

## CI/CD 集成示例

```yaml
# .github/workflows/ai-integration-test.yml
name: AI Integration Test

on: [push, pull_request]

jobs:
  test-ai-integration:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v2
    
    - name: Setup Node.js
      uses: actions/setup-node@v2
      with:
        node-version: '18'
        
    - name: Install dependencies
      run: npm install
      
    - name: Build project
      run: npm run build
      
    - name: Test AI integration
      run: npm run test:ai-integration
```

## 相关文档

- [AI集成服务测试脚本API文档](./test-ai-integration-script-api.md)
- [AI集成服务API文档](./aiIntegrationService-api.md)
- [本地AI服务测试指南](./local-ai-service-test-guide.md)
- [AI集成任务完成总结](./ai-integration-task4-completion-summary.md)