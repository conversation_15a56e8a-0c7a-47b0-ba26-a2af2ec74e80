# AI类型定义API文档

## 概述

`src/types/ai.ts` 文件包含了AI集成功能的所有类型定义，为整个AI服务系统提供了统一的类型约束和接口规范。

## 文件位置

- **源文件**: `src/types/ai.ts`
- **相关测试**: `tests/aiIntegrationService.test.ts`, `tests/aiProviderService.test.ts`, `tests/aiModelService.test.ts`

## 核心类型定义

### AIProvider 类型

AI服务提供商类型枚举，定义了所有支持的AI服务提供商。

```typescript
export type AIProvider = 
  | 'openai'        // OpenAI GPT系列
  | 'azure-openai'  // Azure OpenAI服务 (新增)
  | 'claude'        // Anthropic Claude
  | 'gemini'        // Google Gemini
  | 'local'         // 本地AI服务
  | 'custom'        // 自定义API
  | 'ollama'        // Ollama本地部署
  | 'lm-studio'     // LM Studio
  | 'openrouter'    // OpenRouter聚合服务
  | 'xinference'    // Xinference分布式推理
  | 'deepseek'      // DeepSeek AI
  | 'zhipu'         // 智谱AI
  | 'qwen'          // 通义千问
  | 'bailian'       // 百炼大模型
  | 'hunyuan'       // 腾讯混元
  | 'volcengine'    // 火山引擎
  | 'together'      // Together AI
  | 'grok'          // xAI Grok
```

**最新更新**: 新增了 `azure-openai` 类型，支持Microsoft Azure部署的OpenAI模型。

## 核心接口

### AIProviderConfig 接口

AI提供商配置接口，用于存储和管理AI服务提供商的配置信息。

```typescript
export interface AIProviderConfig {
  id: string                        // 唯一标识符
  name: string                      // 显示名称
  type: AIProvider                  // 提供商类型
  baseUrl: string                   // API基础URL
  apiKey?: string                   // API密钥（可选）
  headers?: Record<string, string>  // 自定义请求头
  timeout?: number                  // 请求超时时间（毫秒）
  enabled: boolean                  // 是否启用
  createdAt: Date                   // 创建时间
  updatedAt: Date                   // 更新时间
}
```

**使用示例**:
```typescript
const ollamaConfig: AIProviderConfig = {
  id: 'ollama_local',
  name: 'Ollama本地服务',
  type: 'ollama',
  baseUrl: 'http://localhost:11434',
  enabled: true,
  createdAt: new Date(),
  updatedAt: new Date()
}

const azureOpenAIConfig: AIProviderConfig = {
  id: 'azure_openai_prod',
  name: 'Azure OpenAI生产环境',
  type: 'azure-openai',
  baseUrl: 'https://your-resource.openai.azure.com',
  apiKey: 'your-api-key',
  headers: {
    'api-version': '2024-02-15-preview'
  },
  timeout: 30000,
  enabled: true,
  createdAt: new Date(),
  updatedAt: new Date()
}
```

### AIProviderInfo 接口

AI提供商信息接口，包含提供商的静态信息和能力描述。

```typescript
export interface AIProviderInfo {
  id: string                    // 提供商ID
  name: string                  // 显示名称
  description: string           // 描述信息
  icon: string                  // 图标（emoji或URL）
  type: AIProvider              // 提供商类型
  defaultBaseUrl: string        // 默认API地址
  requiresApiKey: boolean       // 是否需要API密钥
  supportedFeatures: string[]   // 支持的功能列表
  documentationUrl?: string     // 文档链接
}
```

**使用示例**:
```typescript
const azureOpenAIInfo: AIProviderInfo = {
  id: 'azure-openai',
  name: 'Azure OpenAI',
  description: 'Microsoft Azure部署的OpenAI模型服务',
  icon: '☁️',
  type: 'azure-openai',
  defaultBaseUrl: 'https://your-resource.openai.azure.com',
  requiresApiKey: true,
  supportedFeatures: ['chat', 'completion', 'embedding', 'image-generation'],
  documentationUrl: 'https://docs.microsoft.com/azure/cognitive-services/openai/'
}
```

### AIModel 接口

AI模型接口，描述AI模型的详细信息。

```typescript
export interface AIModel {
  id: string              // 模型唯一标识
  name: string            // 模型名称
  displayName: string     // 显示名称
  description?: string    // 模型描述
  size?: string          // 模型大小
  parameters?: string    // 参数数量
  tags?: string[]        // 标签列表
  capabilities?: string[] // 能力列表
  providerId: string     // 所属提供商ID
  isRecommended?: boolean // 是否推荐
  isPopular?: boolean    // 是否热门
}
```

**使用示例**:
```typescript
const gpt4Model: AIModel = {
  id: 'gpt-4',
  name: 'gpt-4',
  displayName: 'GPT-4',
  description: 'OpenAI最先进的大型语言模型',
  parameters: '175B+',
  tags: ['openai', 'gpt', 'multimodal'],
  capabilities: ['chat', 'completion', 'reasoning', 'coding'],
  providerId: 'openai_main',
  isRecommended: true,
  isPopular: true
}
```

### AIConnectionResult 接口

AI连接测试结果接口。

```typescript
export interface AIConnectionResult {
  providerId: string      // 提供商ID
  success: boolean        // 连接是否成功
  responseTime?: number   // 响应时间（毫秒）
  error?: string         // 错误信息
  modelCount?: number    // 可用模型数量
  testedAt: Date         // 测试时间
}
```

**使用示例**:
```typescript
const connectionResult: AIConnectionResult = {
  providerId: 'azure_openai_prod',
  success: true,
  responseTime: 1250,
  modelCount: 5,
  testedAt: new Date()
}
```

### ModelFilter 接口

模型筛选器接口，用于模型搜索和筛选。

```typescript
export interface ModelFilter {
  size?: string[]         // 按大小筛选
  type?: string[]         // 按类型筛选
  capabilities?: string[] // 按能力筛选
  tags?: string[]         // 按标签筛选
  isRecommended?: boolean // 只显示推荐模型
  isPopular?: boolean     // 只显示热门模型
}
```

**使用示例**:
```typescript
const filter: ModelFilter = {
  capabilities: ['chat', 'coding'],
  isRecommended: true,
  size: ['7B', '13B']
}
```

## 扩展接口

### AIConfig 接口

完整的AI配置接口，包含所有配置选项。

```typescript
export interface AIConfig {
  // 基础配置
  provider: AIProvider
  baseUrl?: string
  apiKey?: string
  model: string
  
  // 功能开关
  autoTagging: boolean
  autoCategories: boolean
  autoDescription: boolean
  
  // 高级配置
  temperature?: number
  maxTokens?: number
  timeout?: number
  
  // 本地模型配置
  localModelPath?: string
  localModelType?: 'ollama' | 'llamacpp' | 'other'
  
  // 自定义配置
  customHeaders?: Record<string, string>
  customParams?: Record<string, any>
  
  // 状态信息
  isConnected: boolean
  lastTestDate?: Date
  availableModels?: string[]
  
  // 时间戳
  createdAt: Date
  updatedAt: Date
}
```

### AI功能相关接口

#### 标签生成

```typescript
// 请求接口
export interface AITagGenerationRequest {
  content: string           // 内容文本
  title?: string           // 标题
  url?: string            // URL地址
  existingTags?: string[] // 已有标签
  maxTags?: number        // 最大标签数
  language?: string       // 语言
}

// 响应接口
export interface AITagGenerationResponse {
  tags: string[]          // 生成的标签
  confidence: number      // 置信度
  reasoning?: string      // 推理过程
  processingTime: number  // 处理时间
}
```

#### 分类建议

```typescript
// 请求接口
export interface AICategoryRequest {
  content: string                    // 内容文本
  title?: string                    // 标题
  url?: string                     // URL地址
  existingCategories?: string[]    // 已有分类
  maxSuggestions?: number          // 最大建议数
}

// 响应接口
export interface AICategoryResponse {
  category: string                 // 推荐分类
  confidence: number              // 置信度
  alternatives?: Array<{          // 备选分类
    category: string
    confidence: number
  }>
  reasoning?: string              // 推理过程
}
```

#### 描述生成

```typescript
// 请求接口
export interface AIDescriptionRequest {
  content: string                           // 内容文本
  title?: string                           // 标题
  url?: string                            // URL地址
  maxLength?: number                      // 最大长度
  style?: 'brief' | 'detailed' | 'summary' // 风格
}

// 响应接口
export interface AIDescriptionResponse {
  description: string    // 生成的描述
  confidence: number     // 置信度
  wordCount: number      // 字数统计
}
```

## 统计和监控接口

### AIServiceStats 接口

AI服务统计信息接口。

```typescript
export interface AIServiceStats {
  totalRequests: number           // 总请求数
  successfulRequests: number      // 成功请求数
  failedRequests: number          // 失败请求数
  averageResponseTime: number     // 平均响应时间
  lastRequestDate?: Date          // 最后请求时间
  errorRate: number              // 错误率
  
  // 按功能分类的统计
  tagGenerationCount: number      // 标签生成次数
  categoryGenerationCount: number // 分类生成次数
  descriptionGenerationCount: number // 描述生成次数
  
  // 按时间段的统计
  dailyStats: Array<{
    date: string
    requests: number
    errors: number
  }>
}
```

### AIServiceError 接口

AI服务错误接口。

```typescript
export interface AIServiceError {
  code: string          // 错误代码
  message: string       // 错误消息
  details?: any        // 错误详情
  timestamp: Date      // 时间戳
  provider: AIProvider // 提供商
  operation: string    // 操作类型
}
```

## 批处理接口

### AIBatchRequest 接口

AI批处理请求接口。

```typescript
export interface AIBatchRequest {
  id: string                    // 批处理ID
  items: Array<{               // 处理项目
    id: string
    type: 'tags' | 'category' | 'description'
    data: AITagGenerationRequest | AICategoryRequest | AIDescriptionRequest
  }>
  priority: 'low' | 'normal' | 'high' // 优先级
  createdAt: Date              // 创建时间
}
```

### AIBatchResponse 接口

AI批处理响应接口。

```typescript
export interface AIBatchResponse {
  id: string                   // 批处理ID
  results: Array<{            // 处理结果
    id: string
    success: boolean
    data?: AITagGenerationResponse | AICategoryResponse | AIDescriptionResponse
    error?: string
  }>
  completedAt: Date           // 完成时间
  totalProcessingTime: number // 总处理时间
}
```

## 提示词模板接口

### AIPromptTemplate 接口

AI提示词模板接口。

```typescript
export interface AIPromptTemplate {
  id: string                                    // 模板ID
  name: string                                  // 模板名称
  description: string                           // 模板描述
  template: string                              // 模板内容
  variables: string[]                           // 变量列表
  category: 'tagging' | 'categorization' | 'description' | 'custom' // 分类
  language: string                              // 语言
  createdAt: Date                              // 创建时间
  updatedAt: Date                              // 更新时间
}
```

## 使用示例

### 基本配置示例

```typescript
import { 
  AIProviderConfig, 
  AIProvider, 
  AIModel, 
  AIConnectionResult 
} from '../types/ai'

// 配置Azure OpenAI提供商
const azureConfig: AIProviderConfig = {
  id: 'azure_openai_main',
  name: 'Azure OpenAI主服务',
  type: 'azure-openai',
  baseUrl: 'https://your-resource.openai.azure.com',
  apiKey: process.env.AZURE_OPENAI_API_KEY,
  headers: {
    'api-version': '2024-02-15-preview'
  },
  timeout: 30000,
  enabled: true,
  createdAt: new Date(),
  updatedAt: new Date()
}

// 配置本地Ollama服务
const ollamaConfig: AIProviderConfig = {
  id: 'ollama_local',
  name: 'Ollama本地服务',
  type: 'ollama',
  baseUrl: 'http://localhost:11434',
  enabled: true,
  createdAt: new Date(),
  updatedAt: new Date()
}
```

### 模型定义示例

```typescript
// Azure OpenAI模型
const azureGPT4: AIModel = {
  id: 'gpt-4-azure',
  name: 'gpt-4',
  displayName: 'GPT-4 (Azure)',
  description: 'Azure部署的GPT-4模型，支持多模态输入',
  parameters: '175B+',
  tags: ['azure', 'openai', 'gpt-4', 'multimodal'],
  capabilities: ['chat', 'completion', 'reasoning', 'coding', 'vision'],
  providerId: 'azure_openai_main',
  isRecommended: true,
  isPopular: true
}

// 本地Ollama模型
const llamaModel: AIModel = {
  id: 'llama2:7b',
  name: 'llama2:7b',
  displayName: 'Llama 2 7B',
  description: 'Meta开发的7B参数Llama 2模型',
  size: '3.8 GB',
  parameters: '7B',
  tags: ['ollama', 'llama', 'meta', 'local'],
  capabilities: ['chat', 'completion'],
  providerId: 'ollama_local',
  isRecommended: true,
  isPopular: true
}
```

### 连接测试示例

```typescript
// 测试Azure OpenAI连接
async function testAzureConnection(config: AIProviderConfig): Promise<AIConnectionResult> {
  const startTime = Date.now()
  
  try {
    const response = await fetch(`${config.baseUrl}/deployments/gpt-4/chat/completions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${config.apiKey}`,
        ...config.headers
      },
      body: JSON.stringify({
        messages: [{ role: 'user', content: 'Hello' }],
        max_tokens: 10
      })
    })
    
    const responseTime = Date.now() - startTime
    
    if (response.ok) {
      return {
        providerId: config.id,
        success: true,
        responseTime,
        testedAt: new Date()
      }
    } else {
      throw new Error(`HTTP ${response.status}`)
    }
  } catch (error) {
    return {
      providerId: config.id,
      success: false,
      responseTime: Date.now() - startTime,
      error: error.message,
      testedAt: new Date()
    }
  }
}
```

### AI功能使用示例

```typescript
// 标签生成请求
const tagRequest: AITagGenerationRequest = {
  content: '这是一篇关于人工智能和机器学习的技术文章',
  title: 'AI技术发展趋势',
  maxTags: 5,
  language: 'zh-CN'
}

// 分类建议请求
const categoryRequest: AICategoryRequest = {
  content: '介绍了最新的深度学习算法和应用',
  title: '深度学习技术综述',
  maxSuggestions: 3
}

// 描述生成请求
const descriptionRequest: AIDescriptionRequest = {
  content: '详细分析了Transformer架构的工作原理',
  title: 'Transformer架构解析',
  maxLength: 200,
  style: 'summary'
}
```

## 类型守卫函数

为了更好地使用这些类型，建议创建类型守卫函数：

```typescript
// 检查是否为有效的AI提供商类型
export function isValidAIProvider(provider: string): provider is AIProvider {
  const validProviders: AIProvider[] = [
    'openai', 'azure-openai', 'claude', 'gemini', 'local', 'custom',
    'ollama', 'lm-studio', 'openrouter', 'xinference', 'deepseek',
    'zhipu', 'qwen', 'bailian', 'hunyuan', 'volcengine', 'together', 'grok'
  ]
  return validProviders.includes(provider as AIProvider)
}

// 检查提供商是否需要API密钥
export function requiresApiKey(provider: AIProvider): boolean {
  const noKeyProviders: AIProvider[] = ['ollama', 'lm-studio', 'xinference', 'local']
  return !noKeyProviders.includes(provider)
}

// 检查提供商是否为本地服务
export function isLocalProvider(provider: AIProvider): boolean {
  const localProviders: AIProvider[] = ['ollama', 'lm-studio', 'xinference', 'local']
  return localProviders.includes(provider)
}
```

## 版本更新说明

### v1.1.0 (当前版本)
- **新增**: `azure-openai` 提供商类型
- **支持**: Microsoft Azure部署的OpenAI模型
- **兼容**: 保持与现有代码的完全兼容性

### v1.0.0 (基础版本)
- 初始类型定义
- 支持主流AI服务提供商
- 完整的接口规范

## 相关文档

- [AI集成服务API](./aiIntegrationService-api.md)
- [AI提供商服务API](./aiProviderService-api.md)
- [AI模型服务API](./aiModelService-api.md)
- [AI对话服务API](./aiChatService-api.md)
- [本地AI服务适配器API](./localAIServiceAdapter-api.md)

## 注意事项

1. **类型安全**: 所有接口都提供了完整的TypeScript类型支持
2. **向后兼容**: 新增类型不会破坏现有功能
3. **扩展性**: 接口设计支持未来功能扩展
4. **文档同步**: 类型变更时需要同步更新相关文档
5. **测试覆盖**: 所有类型都应该有对应的单元测试

## 最佳实践

1. **使用类型守卫**: 在运行时验证数据类型
2. **接口组合**: 通过接口继承和组合复用类型定义
3. **可选属性**: 合理使用可选属性提高接口灵活性
4. **枚举类型**: 使用联合类型代替枚举提高类型安全
5. **文档注释**: 为所有公共接口提供详细的JSDoc注释