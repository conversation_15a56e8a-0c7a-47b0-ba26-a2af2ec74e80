# Content Script 错误修复总结

## 问题描述

浏览器插件控制台出现以下错误：

1. **GET_SELECTED_TEXT 消息类型错误**：
   ```
   Content Script 处理消息失败: Error: 未知的消息类型: GET_SELECTED_TEXT
   ```

2. **右键菜单重复创建错误**：
   ```
   Unchecked runtime.lastError: Cannot create item with duplicate id universe-bag-main
   Unchecked runtime.lastError: Cannot create item with duplicate id bookmark-page
   Unchecked runtime.lastError: Cannot create item with duplicate id bookmark-selection
   Unchecked runtime.lastError: Cannot create item with duplicate id bookmark-link
   ```

## 问题分析

### 1. GET_SELECTED_TEXT 消息处理缺失

- **原因**：`src/popup/PopupApp.tsx` 中发送了 `GET_SELECTED_TEXT` 消息，但 `src/content/index.ts` 中的消息处理器没有对应的处理逻辑
- **影响**：导致获取页面选中文字功能失效，控制台报错

### 2. 右键菜单重复创建

- **原因**：`src/background/messageHandler.ts` 中的 `initializeContextMenus()` 方法在每次扩展启动时都会创建菜单项，但没有先清除已存在的菜单
- **影响**：导致菜单项重复创建，控制台报错

## 修复方案

### 1. 添加 GET_SELECTED_TEXT 消息处理

**文件**：`src/content/index.ts`

**修改内容**：
- 在 `handleContentMessage` 函数中添加 `GET_SELECTED_TEXT` 消息类型处理
- 新增 `handleGetSelectedText` 函数实现选中文字获取逻辑

```typescript
// 处理消息的主函数
async function handleContentMessage(message: any) {
  switch (message.type) {
    case 'GET_PAGE_INFO':
      return handleGetPageInfo(message.data)
    
    case 'GET_LINK_INFO':
      return handleGetLinkInfo(message.data)
    
    case 'GET_SELECTED_TEXT':  // 新增
      return handleGetSelectedText()
    
    case 'PING':
      return { status: 'pong', url: window.location.href }
    
    default:
      throw new Error(`未知的消息类型: ${message.type}`)
  }
}

// 处理获取选中文字请求 - 新增函数
async function handleGetSelectedText() {
  try {
    const selection = window.getSelection()
    const selectedText = selection ? selection.toString().trim() : ''
    
    console.log('获取选中文字成功:', selectedText)
    return { selectedText }
  } catch (error) {
    console.error('获取选中文字失败:', error)
    throw error
  }
}
```

### 2. 修复右键菜单重复创建

**文件**：`src/background/messageHandler.ts`

**修改内容**：
- 在创建菜单项前先调用 `chrome.contextMenus.removeAll()` 清除现有菜单
- 将菜单创建逻辑放在 `removeAll` 的回调中执行

```typescript
private initializeContextMenus(): void {
  try {
    // 先清除所有现有菜单项，避免重复创建
    chrome.contextMenus.removeAll(() => {
      // 创建主菜单项
      chrome.contextMenus.create({
        id: 'universe-bag-main',
        title: 'Universe Bag',
        contexts: ['page', 'selection', 'link']
      })

      // 收藏当前页面
      chrome.contextMenus.create({
        id: 'bookmark-page',
        parentId: 'universe-bag-main',
        title: '收藏当前页面',
        contexts: ['page']
      })

      // 收藏选中文字
      chrome.contextMenus.create({
        id: 'bookmark-selection',
        parentId: 'universe-bag-main',
        title: '收藏选中文字',
        contexts: ['selection']
      })

      // 收藏链接
      chrome.contextMenus.create({
        id: 'bookmark-link',
        parentId: 'universe-bag-main',
        title: '收藏链接',
        contexts: ['link']
      })

      console.log('右键菜单初始化完成')
    })

    // 监听右键菜单点击事件
    chrome.contextMenus.onClicked.addListener(this.handleContextMenuClick.bind(this))

  } catch (error) {
    console.error('初始化右键菜单失败:', error)
  }
}
```

## 功能特性

### GET_SELECTED_TEXT 消息处理

- **功能**：获取当前页面用户选中的文字内容
- **返回格式**：`{ selectedText: string }`
- **错误处理**：捕获 `window.getSelection()` 异常并记录日志
- **边界情况**：正确处理空选中和 null 选中的情况

### 右键菜单管理

- **防重复**：每次初始化前先清除现有菜单项
- **层级结构**：主菜单 + 三个子菜单项
- **上下文适配**：根据页面元素类型显示对应菜单项

## 测试验证

创建了 `verify-content-script-fix.js` 测试脚本，验证：

1. ✅ GET_SELECTED_TEXT 消息类型正确处理
2. ✅ 选中文字获取功能正常
3. ✅ 空选中文字处理正确
4. ✅ getSelection 异常处理正确
5. ✅ 右键菜单重复创建问题修复
6. ✅ 菜单项创建顺序和结构正确

## 影响范围

### 修复的功能
- 页面选中文字获取功能恢复正常
- 右键菜单不再重复创建
- 控制台错误消息消除

### 不受影响的功能
- 页面信息提取功能
- 链接信息提取功能
- 书签管理功能
- 其他扩展核心功能

## 部署建议

1. **测试环境验证**：在开发环境中加载修复后的扩展，验证错误是否消除
2. **功能测试**：测试选中文字收藏功能是否正常工作
3. **右键菜单测试**：验证右键菜单显示正常，无重复项
4. **回归测试**：确保其他功能未受影响

## 后续优化建议

1. **消息类型枚举**：考虑创建消息类型枚举，避免硬编码字符串
2. **错误处理统一**：统一消息处理的错误格式和日志记录
3. **菜单配置化**：将右键菜单配置提取到配置文件中
4. **单元测试**：为消息处理函数添加完整的单元测试覆盖

---

**修复完成时间**：2025年1月27日  
**修复文件**：
- `src/content/index.ts`
- `src/background/messageHandler.ts`
- `verify-content-script-fix.js` (测试文件)