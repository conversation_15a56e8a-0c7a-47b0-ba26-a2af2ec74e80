# shadcn/ui 高级交互组件安装文档

## 概述

本文档记录了 shadcn/ui 高级交互组件的安装过程和使用说明。这些组件为收藏夹页面重构提供了丰富的交互功能。

## 已安装的组件

### 1. DropdownMenu 组件
- **文件位置**: `src/components/ui/dropdown-menu.tsx`
- **功能**: 提供下拉菜单功能，支持多级菜单、分隔符、快捷键等
- **主要导出**:
  - `DropdownMenu` - 根组件
  - `DropdownMenuTrigger` - 触发器
  - `DropdownMenuContent` - 内容容器
  - `DropdownMenuItem` - 菜单项
  - `DropdownMenuSeparator` - 分隔符
  - `DropdownMenuLabel` - 标签

### 2. Badge 组件
- **文件位置**: `src/components/ui/badge.tsx`
- **功能**: 显示标签和状态信息
- **变体支持**:
  - `default` - 默认样式
  - `secondary` - 次要样式
  - `destructive` - 危险样式
  - `outline` - 轮廓样式

### 3. Select 组件
- **文件位置**: `src/components/ui/select.tsx`
- **功能**: 提供下拉选择功能
- **主要导出**:
  - `Select` - 根组件
  - `SelectTrigger` - 触发器
  - `SelectContent` - 内容容器
  - `SelectItem` - 选项
  - `SelectValue` - 值显示

### 4. Tooltip 组件
- **文件位置**: `src/components/ui/tooltip.tsx`
- **功能**: 提供悬停提示信息
- **主要导出**:
  - `TooltipProvider` - 提供者组件
  - `Tooltip` - 根组件
  - `TooltipTrigger` - 触发器
  - `TooltipContent` - 内容

## 安装过程

### 1. 组件安装命令
```bash
# 安装 DropdownMenu 组件
npx shadcn@latest add dropdown-menu

# 安装 Badge 组件
npx shadcn@latest add badge

# 安装 Select 组件
npx shadcn@latest add select

# 安装 Tooltip 组件
npx shadcn@latest add tooltip
```

### 2. 创建的文件
- `src/components/ui/dropdown-menu.tsx` - DropdownMenu 组件
- `src/components/ui/badge.tsx` - Badge 组件
- `src/components/ui/select.tsx` - Select 组件
- `src/components/ui/tooltip.tsx` - Tooltip 组件
- `src/components/examples/AdvancedComponentsDemo.tsx` - 演示组件
- `tests/shadcn-advanced-components.test.tsx` - 测试文件
- `scripts/verify-advanced-components.js` - 验证脚本

## 使用示例

### DropdownMenu 使用示例
```tsx
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

<DropdownMenu>
  <DropdownMenuTrigger asChild>
    <Button variant="outline">操作菜单</Button>
  </DropdownMenuTrigger>
  <DropdownMenuContent>
    <DropdownMenuItem>编辑</DropdownMenuItem>
    <DropdownMenuItem>删除</DropdownMenuItem>
  </DropdownMenuContent>
</DropdownMenu>
```

### Badge 使用示例
```tsx
import { Badge } from '@/components/ui/badge';

<Badge variant="default">默认标签</Badge>
<Badge variant="secondary">次要标签</Badge>
<Badge variant="destructive">重要标签</Badge>
<Badge variant="outline">轮廓标签</Badge>
```

### Select 使用示例
```tsx
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

<Select>
  <SelectTrigger>
    <SelectValue placeholder="选择选项" />
  </SelectTrigger>
  <SelectContent>
    <SelectItem value="option1">选项1</SelectItem>
    <SelectItem value="option2">选项2</SelectItem>
  </SelectContent>
</Select>
```

### Tooltip 使用示例
```tsx
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';

<TooltipProvider>
  <Tooltip>
    <TooltipTrigger asChild>
      <Button variant="outline">悬停查看</Button>
    </TooltipTrigger>
    <TooltipContent>
      <p>这是提示信息</p>
    </TooltipContent>
  </Tooltip>
</TooltipProvider>
```

## 验证安装

### 运行验证脚本
```bash
node scripts/verify-advanced-components.js
```

### 运行测试
```bash
npm test tests/shadcn-advanced-components.test.tsx
```

### 查看演示
演示组件位于 `src/components/examples/AdvancedComponentsDemo.tsx`，展示了所有组件的使用方法和组合使用场景。

## 在收藏夹页面中的应用

这些高级交互组件将在收藏夹页面重构中发挥重要作用：

1. **DropdownMenu**: 用于收藏夹项目的操作菜单（编辑、删除、移动等）
2. **Badge**: 用于显示收藏夹的标签和分类信息
3. **Select**: 用于分类选择和筛选功能
4. **Tooltip**: 用于提供操作按钮的提示信息

## 技术特性

- ✅ 基于 Radix UI 构建，具有良好的可访问性
- ✅ 支持键盘导航
- ✅ 响应式设计
- ✅ 支持主题定制
- ✅ TypeScript 类型支持
- ✅ 与现有设计系统集成

## 注意事项

1. **TooltipProvider**: 使用 Tooltip 组件时需要在应用根部或组件树中包含 `TooltipProvider`
2. **样式定制**: 所有组件都支持通过 `className` 属性进行样式定制
3. **可访问性**: 组件已内置 ARIA 属性，支持屏幕阅读器
4. **性能**: 组件使用了 React.forwardRef 和适当的优化策略

## 下一步

- 在收藏夹页面中集成这些组件
- 根据具体需求定制组件样式
- 编写更多的集成测试
- 优化组件性能和用户体验

---

*文档生成时间: 2025年1月11日*
*shadcn/ui 版本: latest*