# 本地AI服务测试脚本API文档

## 概述

`scripts/test-local-ai-services.js` 是一个专门用于测试本地AI服务集成功能的Node.js脚本。该脚本提供了完整的本地AI服务发现、连接测试、模型获取等功能的自动化测试。

## 脚本信息

- **文件路径**: `scripts/test-local-ai-services.js`
- **类型**: ES模块 (ESM)
- **运行环境**: Node.js
- **依赖**: 需要项目已构建完成

## 使用方法

### 直接运行
```bash
node scripts/test-local-ai-services.js
```

### 通过npm脚本运行
```bash
npm run test:local-ai
```

## 主要功能

### 1. 服务加载 (`loadServices`)

**函数签名**:
```javascript
async function loadServices(): Promise<{
  localAIServiceAdapter: LocalAIServiceAdapter,
  aiProviderService: AIProviderService
} | null>
```

**功能**: 动态导入本地AI服务相关的模块

**返回值**:
- 成功: 包含服务实例的对象
- 失败: `null`

**使用示例**:
```javascript
const services = await loadServices();
if (services) {
  const { localAIServiceAdapter, aiProviderService } = services;
  // 使用服务实例
}
```

### 2. 颜色输出工具

#### `colorLog(color, message)`
**功能**: 输出带颜色的控制台信息

**参数**:
- `color` (string): 颜色名称 ('red', 'green', 'yellow', 'blue', 'magenta', 'cyan')
- `message` (string): 要输出的消息

#### 便捷函数
- `success(message)`: 输出绿色成功信息 ✅
- `error(message)`: 输出红色错误信息 ❌
- `info(message)`: 输出蓝色信息 ℹ️
- `warning(message)`: 输出黄色警告信息 ⚠️
- `header(message)`: 输出带分隔线的标题

**使用示例**:
```javascript
success('操作成功完成');
error('发生了错误');
info('这是一条信息');
warning('这是一个警告');
header('测试开始');
```

### 3. 本地服务发现测试 (`testLocalServiceDiscovery`)

**函数签名**:
```javascript
async function testLocalServiceDiscovery(
  adapter: LocalAIServiceAdapter
): Promise<LocalServiceConfig[]>
```

**功能**: 测试本地AI服务的自动发现功能

**参数**:
- `adapter`: 本地AI服务适配器实例

**返回值**: 发现的服务配置列表

**测试内容**:
- 扫描预定义端口 [8888, 9999]
- 显示发现的服务详细信息
- 报告扫描过程中的错误

**使用示例**:
```javascript
const services = await testLocalServiceDiscovery(localAIServiceAdapter);
console.log(`发现了 ${services.length} 个服务`);
```

### 4. 服务连接测试 (`testServiceConnection`)

**函数签名**:
```javascript
async function testServiceConnection(
  adapter: LocalAIServiceAdapter,
  service: LocalServiceConfig
): Promise<AIConnectionResult>
```

**功能**: 测试指定服务的连接状态

**参数**:
- `adapter`: 本地AI服务适配器实例
- `service`: 要测试的服务配置

**返回值**: 连接测试结果

**测试内容**:
- 连接响应时间
- 服务可用性
- 模型数量统计

**使用示例**:
```javascript
const result = await testServiceConnection(adapter, serviceConfig);
if (result.success) {
  console.log(`连接成功，响应时间: ${result.responseTime}ms`);
}
```

### 5. 模型获取测试 (`testModelRetrieval`)

**函数签名**:
```javascript
async function testModelRetrieval(
  adapter: LocalAIServiceAdapter,
  service: LocalServiceConfig
): Promise<AIModel[]>
```

**功能**: 测试从指定服务获取模型列表

**参数**:
- `adapter`: 本地AI服务适配器实例
- `service`: 服务配置

**返回值**: 模型列表

**测试内容**:
- 模型数量统计
- 模型详细信息展示
- 模型能力和标签分析

**使用示例**:
```javascript
const models = await testModelRetrieval(adapter, serviceConfig);
models.forEach(model => {
  console.log(`模型: ${model.displayName}`);
});
```

### 6. 默认服务配置测试 (`testDefaultServices`)

**函数签名**:
```javascript
async function testDefaultServices(
  adapter: LocalAIServiceAdapter
): Promise<LocalServiceConfig[]>
```

**功能**: 测试获取默认服务配置

**参数**:
- `adapter`: 本地AI服务适配器实例

**返回值**: 默认服务配置列表

**测试内容**:
- 默认服务数量
- 服务配置详情
- 超时设置验证

### 7. 自定义服务配置测试 (`testCustomServiceConfig`)

**函数签名**:
```javascript
async function testCustomServiceConfig(
  adapter: LocalAIServiceAdapter
): Promise<void>
```

**功能**: 测试创建自定义服务配置

**参数**:
- `adapter`: 本地AI服务适配器实例

**测试内容**:
- HTTP服务配置
- HTTPS服务配置
- 自定义请求头设置
- 超时配置

**测试用例**:
```javascript
const testConfigs = [
  {
    name: 'Test HTTP Service',
    url: 'http://localhost:8080',
    options: { apiPath: '/api/v2', timeout: 15000 }
  },
  {
    name: 'Test HTTPS Service',
    url: 'https://api.example.com:8443',
    options: { headers: { 'Authorization': 'Bearer token' } }
  }
];
```

### 8. AI提供商服务集成测试 (`testAIProviderServiceIntegration`)

**函数签名**:
```javascript
async function testAIProviderServiceIntegration(
  aiProviderService: AIProviderService
): Promise<void>
```

**功能**: 测试AI提供商服务的集成功能

**参数**:
- `aiProviderService`: AI提供商服务实例

**测试内容**:
- 本地服务发现功能
- 默认服务获取
- 自定义服务创建

### 9. 主测试函数 (`runTests`)

**函数签名**:
```javascript
async function runTests(): Promise<void>
```

**功能**: 执行完整的测试流程

**测试流程**:
1. 加载服务模块
2. 测试默认服务配置
3. 测试自定义服务配置
4. 执行本地服务发现
5. 测试每个发现的服务
6. 测试AI提供商服务集成
7. 输出测试总结

**错误处理**:
- 模块加载失败时退出
- 测试过程中的异常捕获
- 详细的错误信息输出

## 输出格式

### 成功信息
```
✅ 操作成功完成
```

### 错误信息
```
❌ 操作失败: 错误详情
```

### 信息提示
```
ℹ️  这是一条信息
```

### 警告信息
```
⚠️  这是一个警告
```

### 标题分隔
```
============================================================
🤖 测试标题
============================================================
```

## 测试报告

脚本会生成详细的测试报告，包括：

### 服务发现报告
- 发现的服务数量
- 每个服务的详细配置
- 扫描过程中的错误

### 连接测试报告
- 连接成功/失败状态
- 响应时间统计
- 模型数量统计

### 模型信息报告
- 模型ID和显示名称
- 模型描述和大小
- 模型能力和标签
- 推荐和热门标记

### 测试总结
```
测试总结:
   - 发现的服务数量: 3
   - 在线服务数量: 2
   - 测试用例: 全部通过
```

## 错误处理

### 模块加载错误
```javascript
❌ 无法加载服务模块: 模块未找到
💡 提示: 请确保项目已正确构建
```

### 服务连接错误
```javascript
❌ Ollama 连接失败: 连接超时，请检查服务是否运行
```

### 模型获取错误
```javascript
❌ LM Studio 模型获取失败: HTTP 404: Not Found
```

## 依赖要求

### 系统要求
- Node.js (支持ES模块)
- Windows 10 (项目开发环境)

### 项目依赖
- 项目必须已构建 (`npm run build`)
- 相关服务模块必须可用

### 可选服务
- Ollama (端口 11434)
- LM Studio (端口 1234)
- Xinference (端口 9997)
- 其他自定义AI服务

## 扩展使用

### 自定义端口扫描
```javascript
// 修改 testLocalServiceDiscovery 函数中的端口列表
const result = await adapter.discoverLocalServices([8888, 9999, 7777]);
```

### 添加新的测试用例
```javascript
async function testCustomFeature(adapter) {
  header('自定义功能测试');
  // 添加测试逻辑
}
```

### 集成到CI/CD
```bash
# 在构建脚本中添加
npm run build
npm run test:local-ai
```

## 注意事项

1. **构建要求**: 运行前必须执行 `npm run build`
2. **服务依赖**: 测试结果取决于本地AI服务的可用性
3. **网络环境**: 某些测试可能需要网络连接
4. **权限要求**: 可能需要防火墙权限进行端口扫描
5. **超时设置**: 默认超时为10-15秒，可根据需要调整

## 相关文档

- [本地AI服务适配器API](./localAIServiceAdapter-api.md)
- [AI提供商服务API](./aiProviderService-api.md)
- [本地AI服务测试指南](./local-ai-service-test-guide.md)
- [AI集成任务2完成总结](./ai-integration-task2-completion-summary.md)