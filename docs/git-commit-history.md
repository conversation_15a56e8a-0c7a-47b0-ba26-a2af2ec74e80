# Git提交历史

## 提交规范

本项目采用[约定式提交](https://www.conventionalcommits.org/zh-hans/v1.0.0/)规范：

```
<类型>[可选 范围]: <描述>

[可选 正文]

[可选 脚注]
```

### 提交类型

- `feat`: 新功能
- `fix`: 修复bug
- `docs`: 文档更新
- `style`: 代码格式化（不影响代码运行的变动）
- `refactor`: 重构（既不是新增功能，也不是修复bug的代码变动）
- `perf`: 性能优化
- `test`: 增加测试
- `chore`: 构建过程或辅助工具的变动
- `ci`: CI/CD相关变动

## 提交历史

### d1c50ed - feat: 初始化Universe Bag智能收藏扩展项目

**提交时间**: 2025-01-21  
**作者**: Universe Bag Developer  
**类型**: feat (新功能)

**主要变更**:
- 搭建Chrome扩展Manifest V3项目架构
- 集成React 18 + TypeScript + Tailwind CSS技术栈  
- 配置Vite构建工具和开发环境
- 实现Background Service Worker基础框架
- 实现Content Script页面信息提取功能
- 创建Popup和Options页面基础组件
- 定义核心数据模型和类型系统
- 配置自动化构建和测试流程
- 修复CSS文件加载问题

**文件统计**:
- 新增文件: 50个
- 代码行数: 13,136行
- 涉及模块: 项目架构、构建配置、核心功能、测试框架

**完成任务**: 任务1 - 项目初始化和基础架构搭建

---

## 分支策略

当前使用简单的主分支策略：
- `master`: 主分支，包含稳定的代码

未来可以考虑采用Git Flow或GitHub Flow策略：
- `develop`: 开发分支
- `feature/*`: 功能分支
- `hotfix/*`: 热修复分支
- `release/*`: 发布分支

## 提交最佳实践

1. **原子性提交**: 每次提交只包含一个逻辑变更
2. **清晰的提交信息**: 使用约定式提交格式
3. **及时提交**: 完成一个功能点就提交
4. **代码审查**: 重要变更需要代码审查
5. **测试通过**: 提交前确保所有测试通过

## 版本标签

计划使用语义化版本标签：
- `v1.0.0`: 第一个正式版本
- `v1.1.0`: 新功能版本
- `v1.0.1`: 修复版本

## 贡献指南

1. Fork项目
2. 创建功能分支 (`git checkout -b feature/amazing-feature`)
3. 提交变更 (`git commit -m 'feat: add amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 创建Pull Request