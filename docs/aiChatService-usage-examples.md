# AI对话服务使用示例

## 概述

本文档提供 `AIChatService` 的详细使用示例，涵盖各种实际应用场景。

## 基础使用

### 导入服务

```typescript
import { aiChatService, ChatMessage, ChatSettings, ChatResponse } from '../services/aiChatService'
import { aiIntegrationService } from '../services/aiIntegrationService'
```

### 简单对话

```typescript
async function simpleChat() {
  const messages: ChatMessage[] = [
    { role: 'user', content: '你好，请介绍一下自己。' }
  ]
  
  try {
    const response = await aiChatService.chatWithLocalService(
      'http://localhost:11434',
      'llama2:7b',
      messages
    )
    
    console.log('AI回复:', response.content)
  } catch (error) {
    console.error('对话失败:', error.message)
  }
}
```

## 本地AI服务对话

### Ollama服务对话

```typescript
async function chatWithOllama() {
  const messages: ChatMessage[] = [
    { role: 'system', content: '你是一个专业的编程助手。' },
    { role: 'user', content: '请解释JavaScript中的闭包概念。' }
  ]
  
  const settings: ChatSettings = {
    temperature: 0.7,
    maxTokens: 1000,
    topP: 0.9
  }
  
  try {
    const response = await aiChatService.chatWithLocalService(
      'http://localhost:11434',
      'llama2:7b',
      messages,
      settings
    )
    
    console.log('Ollama回复:', response.content)
    console.log('使用模型:', response.model)
    
    if (response.usage) {
      console.log('令牌使用:', {
        输入: response.usage.promptTokens,
        输出: response.usage.completionTokens,
        总计: response.usage.totalTokens
      })
    }
  } catch (error) {
    console.error('Ollama对话失败:', error.message)
  }
}
```

### LM Studio服务对话

```typescript
async function chatWithLMStudio() {
  const messages: ChatMessage[] = [
    { role: 'user', content: '写一个Python函数来计算斐波那契数列。' }
  ]
  
  const settings: ChatSettings = {
    temperature: 0.3, // 代码生成使用较低温度
    maxTokens: 500,
    frequencyPenalty: 0.1,
    presencePenalty: 0.1
  }
  
  try {
    const response = await aiChatService.chatWithLocalService(
      'http://localhost:1234/v1',
      'codellama-7b-instruct',
      messages,
      settings
    )
    
    console.log('LM Studio回复:', response.content)
    console.log('完成原因:', response.finishReason)
  } catch (error) {
    console.error('LM Studio对话失败:', error.message)
  }
}
```

### LocalAI服务对话

```typescript
async function chatWithLocalAI() {
  const messages: ChatMessage[] = [
    { role: 'user', content: '请总结一下机器学习的主要算法类型。' }
  ]
  
  try {
    const response = await aiChatService.chatWithLocalService(
      'http://localhost:8080/v1',
      'gpt-3.5-turbo',
      messages,
      {
        temperature: 0.5,
        maxTokens: 800
      }
    )
    
    console.log('LocalAI回复:', response.content)
  } catch (error) {
    console.error('LocalAI对话失败:', error.message)
  }
}
```

## 云端AI服务对话

### OpenAI服务对话

```typescript
async function chatWithOpenAI() {
  // 获取配置的OpenAI提供商
  const providers = await aiIntegrationService.getConfiguredProviders()
  const openaiProvider = providers.find(p => p.type === 'openai')
  
  if (!openaiProvider) {
    console.error('未找到OpenAI提供商配置')
    return
  }
  
  const messages: ChatMessage[] = [
    { role: 'system', content: '你是一个创意写作助手。' },
    { role: 'user', content: '写一个关于未来城市的短故事开头。' }
  ]
  
  const settings: ChatSettings = {
    temperature: 0.8, // 创意写作使用较高温度
    maxTokens: 300,
    topP: 0.95
  }
  
  try {
    const response = await aiChatService.chatWithCloudService(
      openaiProvider,
      'gpt-3.5-turbo',
      messages,
      settings
    )
    
    console.log('OpenAI回复:', response.content)
  } catch (error) {
    console.error('OpenAI对话失败:', error.message)
  }
}
```

### Claude服务对话

```typescript
async function chatWithClaude() {
  const providers = await aiIntegrationService.getConfiguredProviders()
  const claudeProvider = providers.find(p => p.type === 'claude')
  
  if (!claudeProvider) {
    console.error('未找到Claude提供商配置')
    return
  }
  
  const messages: ChatMessage[] = [
    { role: 'user', content: '请分析一下人工智能在医疗领域的应用前景。' }
  ]
  
  try {
    const response = await aiChatService.chatWithCloudService(
      claudeProvider,
      'claude-3-sonnet-20240229',
      messages,
      {
        temperature: 0.4,
        maxTokens: 1000
      }
    )
    
    console.log('Claude回复:', response.content)
  } catch (error) {
    console.error('Claude对话失败:', error.message)
  }
}
```

## 高级使用场景

### 多轮对话管理

```typescript
class ConversationManager {
  private messages: ChatMessage[] = []
  private serviceUrl: string
  private modelId: string
  private defaultSettings: ChatSettings
  
  constructor(
    serviceUrl: string,
    modelId: string,
    systemPrompt?: string,
    settings?: ChatSettings
  ) {
    this.serviceUrl = serviceUrl
    this.modelId = modelId
    this.defaultSettings = settings || { temperature: 0.7, maxTokens: 1000 }
    
    if (systemPrompt) {
      this.messages.push({ role: 'system', content: systemPrompt })
    }
  }
  
  async sendMessage(content: string, settings?: ChatSettings): Promise<string> {
    // 添加用户消息
    this.messages.push({ role: 'user', content })
    
    try {
      const response = await aiChatService.chatWithLocalService(
        this.serviceUrl,
        this.modelId,
        this.messages,
        { ...this.defaultSettings, ...settings }
      )
      
      // 添加AI回复到历史
      this.messages.push({ role: 'assistant', content: response.content })
      
      return response.content
    } catch (error) {
      // 如果失败，移除用户消息
      this.messages.pop()
      throw error
    }
  }
  
  getHistory(): ChatMessage[] {
    return [...this.messages]
  }
  
  clearHistory(): void {
    // 保留系统消息
    this.messages = this.messages.filter(msg => msg.role === 'system')
  }
  
  getMessageCount(): number {
    return this.messages.filter(msg => msg.role !== 'system').length
  }
  
  exportHistory(): string {
    return JSON.stringify(this.messages, null, 2)
  }
  
  importHistory(historyJson: string): void {
    try {
      const history = JSON.parse(historyJson)
      if (Array.isArray(history)) {
        this.messages = history
      }
    } catch (error) {
      throw new Error('无效的历史记录格式')
    }
  }
}

// 使用示例
async function multiTurnConversation() {
  const conversation = new ConversationManager(
    'http://localhost:11434',
    'llama2:7b',
    '你是一个有用的AI助手，请用中文回答问题。'
  )
  
  try {
    const reply1 = await conversation.sendMessage('什么是机器学习？')
    console.log('回复1:', reply1)
    
    const reply2 = await conversation.sendMessage('能举个具体的例子吗？')
    console.log('回复2:', reply2)
    
    const reply3 = await conversation.sendMessage('这个例子中用到了哪些算法？')
    console.log('回复3:', reply3)
    
    console.log('对话轮数:', conversation.getMessageCount())
    
    // 导出对话历史
    const history = conversation.exportHistory()
    console.log('对话历史:', history)
    
  } catch (error) {
    console.error('对话过程中出错:', error.message)
  }
}
```

### 并行对话处理

```typescript
async function parallelChats() {
  const questions = [
    '什么是人工智能？',
    '机器学习有哪些类型？',
    '深度学习的原理是什么？',
    '自然语言处理的应用有哪些？'
  ]
  
  const chatPromises = questions.map(async (question, index) => {
    const messages: ChatMessage[] = [
      { role: 'user', content: question }
    ]
    
    try {
      const response = await aiChatService.chatWithLocalService(
        'http://localhost:11434',
        'llama2:7b',
        messages,
        { temperature: 0.5, maxTokens: 200 }
      )
      
      return {
        question,
        answer: response.content,
        index
      }
    } catch (error) {
      return {
        question,
        answer: `错误: ${error.message}`,
        index,
        error: true
      }
    }
  })
  
  try {
    const results = await Promise.all(chatPromises)
    
    results.forEach(result => {
      console.log(`\n问题 ${result.index + 1}: ${result.question}`)
      console.log(`回答: ${result.answer}`)
      if (result.error) {
        console.log('❌ 此问题处理失败')
      } else {
        console.log('✅ 处理成功')
      }
    })
  } catch (error) {
    console.error('并行对话处理失败:', error)
  }
}
```

### 智能服务选择

```typescript
class SmartChatService {
  private localServices = [
    { url: 'http://localhost:11434', model: 'llama2:7b', name: 'Ollama' },
    { url: 'http://localhost:1234/v1', model: 'gpt-3.5-turbo', name: 'LM Studio' }
  ]
  
  async findBestService(): Promise<{ url: string, model: string, name: string } | null> {
    for (const service of this.localServices) {
      try {
        const isConnected = await aiChatService.testModelConnection(service.url, service.model)
        if (isConnected) {
          console.log(`✅ ${service.name} 可用`)
          return service
        }
      } catch (error) {
        console.log(`❌ ${service.name} 不可用: ${error.message}`)
      }
    }
    
    return null
  }
  
  async smartChat(content: string): Promise<string> {
    const service = await this.findBestService()
    
    if (!service) {
      throw new Error('没有可用的AI服务')
    }
    
    const messages: ChatMessage[] = [
      { role: 'user', content }
    ]
    
    try {
      const response = await aiChatService.chatWithLocalService(
        service.url,
        service.model,
        messages
      )
      
      console.log(`使用服务: ${service.name}`)
      return response.content
    } catch (error) {
      throw new Error(`${service.name} 对话失败: ${error.message}`)
    }
  }
}

// 使用示例
async function smartChatExample() {
  const smartChat = new SmartChatService()
  
  try {
    const reply = await smartChat.smartChat('请解释一下区块链技术。')
    console.log('智能回复:', reply)
  } catch (error) {
    console.error('智能对话失败:', error.message)
  }
}
```

## 连接测试和诊断

### 服务健康检查

```typescript
async function healthCheck() {
  const services = [
    { name: 'Ollama', url: 'http://localhost:11434', model: 'llama2:7b' },
    { name: 'LM Studio', url: 'http://localhost:1234/v1', model: 'gpt-3.5-turbo' },
    { name: 'LocalAI', url: 'http://localhost:8080/v1', model: 'gpt-3.5-turbo' }
  ]
  
  console.log('🔍 开始AI服务健康检查...\n')
  
  for (const service of services) {
    console.log(`检查 ${service.name}...`)
    
    try {
      // 测试连接
      const startTime = Date.now()
      const isConnected = await aiChatService.testModelConnection(service.url, service.model)
      const responseTime = Date.now() - startTime
      
      if (isConnected) {
        console.log(`✅ ${service.name} 正常 (${responseTime}ms)`)
        
        // 获取模型列表
        try {
          const models = await aiChatService.getAvailableModels(service.url)
          console.log(`   可用模型: ${models.length} 个`)
          console.log(`   模型列表: ${models.slice(0, 3).join(', ')}${models.length > 3 ? '...' : ''}`)
        } catch (error) {
          console.log(`   ⚠️  无法获取模型列表: ${error.message}`)
        }
      } else {
        console.log(`❌ ${service.name} 连接失败`)
      }
    } catch (error) {
      console.log(`❌ ${service.name} 错误: ${error.message}`)
    }
    
    console.log('')
  }
}
```

### 性能基准测试

```typescript
async function performanceBenchmark() {
  const testMessage = '请用一句话解释人工智能。'
  const services = [
    { name: 'Ollama Llama2', url: 'http://localhost:11434', model: 'llama2:7b' },
    { name: 'LM Studio', url: 'http://localhost:1234/v1', model: 'gpt-3.5-turbo' }
  ]
  
  console.log('🚀 开始性能基准测试...\n')
  
  for (const service of services) {
    console.log(`测试 ${service.name}...`)
    
    const times: number[] = []
    const responses: string[] = []
    
    for (let i = 0; i < 3; i++) {
      try {
        const startTime = Date.now()
        
        const response = await aiChatService.chatWithLocalService(
          service.url,
          service.model,
          [{ role: 'user', content: testMessage }],
          { maxTokens: 50 }
        )
        
        const responseTime = Date.now() - startTime
        times.push(responseTime)
        responses.push(response.content)
        
        console.log(`  第${i + 1}次: ${responseTime}ms`)
      } catch (error) {
        console.log(`  第${i + 1}次: 失败 - ${error.message}`)
      }
    }
    
    if (times.length > 0) {
      const avgTime = times.reduce((a, b) => a + b, 0) / times.length
      const minTime = Math.min(...times)
      const maxTime = Math.max(...times)
      
      console.log(`  平均响应时间: ${avgTime.toFixed(0)}ms`)
      console.log(`  最快响应时间: ${minTime}ms`)
      console.log(`  最慢响应时间: ${maxTime}ms`)
      console.log(`  成功率: ${times.length}/3 (${(times.length / 3 * 100).toFixed(0)}%)`)
    }
    
    console.log('')
  }
}
```

## 错误处理和重试

### 智能重试机制

```typescript
class RobustChatService {
  private maxRetries = 3
  private retryDelay = 1000 // 1秒
  
  async chatWithRetry(
    serviceUrl: string,
    modelId: string,
    messages: ChatMessage[],
    settings?: ChatSettings
  ): Promise<ChatResponse> {
    let lastError: Error
    
    for (let attempt = 1; attempt <= this.maxRetries; attempt++) {
      try {
        console.log(`尝试 ${attempt}/${this.maxRetries}...`)
        
        const response = await aiChatService.chatWithLocalService(
          serviceUrl,
          modelId,
          messages,
          settings
        )
        
        console.log(`✅ 第${attempt}次尝试成功`)
        return response
        
      } catch (error) {
        lastError = error
        console.log(`❌ 第${attempt}次尝试失败: ${error.message}`)
        
        if (attempt < this.maxRetries) {
          const delay = this.retryDelay * attempt // 指数退避
          console.log(`等待 ${delay}ms 后重试...`)
          await new Promise(resolve => setTimeout(resolve, delay))
        }
      }
    }
    
    throw new Error(`所有重试都失败了，最后错误: ${lastError.message}`)
  }
}

// 使用示例
async function robustChatExample() {
  const robustChat = new RobustChatService()
  
  try {
    const response = await robustChat.chatWithRetry(
      'http://localhost:11434',
      'llama2:7b',
      [{ role: 'user', content: '你好' }]
    )
    
    console.log('最终回复:', response.content)
  } catch (error) {
    console.error('所有尝试都失败了:', error.message)
  }
}
```

### 降级策略

```typescript
class FallbackChatService {
  private services = [
    { url: 'http://localhost:11434', model: 'llama2:7b', name: 'Ollama' },
    { url: 'http://localhost:1234/v1', model: 'gpt-3.5-turbo', name: 'LM Studio' }
  ]
  
  async chatWithFallback(messages: ChatMessage[], settings?: ChatSettings): Promise<ChatResponse> {
    let lastError: Error
    
    for (const service of this.services) {
      try {
        console.log(`尝试使用 ${service.name}...`)
        
        const response = await aiChatService.chatWithLocalService(
          service.url,
          service.model,
          messages,
          settings
        )
        
        console.log(`✅ ${service.name} 成功响应`)
        return response
        
      } catch (error) {
        lastError = error
        console.log(`❌ ${service.name} 失败: ${error.message}`)
      }
    }
    
    throw new Error(`所有服务都不可用，最后错误: ${lastError.message}`)
  }
}

// 使用示例
async function fallbackChatExample() {
  const fallbackChat = new FallbackChatService()
  
  try {
    const response = await fallbackChat.chatWithFallback([
      { role: 'user', content: '请介绍一下TypeScript的优势。' }
    ])
    
    console.log('回复:', response.content)
  } catch (error) {
    console.error('所有服务都不可用:', error.message)
  }
}
```

## 实用工具函数

### 消息格式化

```typescript
function formatChatHistory(messages: ChatMessage[]): string {
  return messages.map(msg => {
    const role = msg.role === 'user' ? '👤 用户' : 
                 msg.role === 'assistant' ? '🤖 AI' : '⚙️ 系统'
    return `${role}: ${msg.content}`
  }).join('\n\n')
}

function truncateMessage(content: string, maxLength: number = 100): string {
  if (content.length <= maxLength) {
    return content
  }
  return content.substring(0, maxLength) + '...'
}

function countTokensApprox(text: string): number {
  // 简单的令牌估算（实际应该使用专门的tokenizer）
  return Math.ceil(text.length / 4)
}
```

### 配置管理

```typescript
interface ChatConfig {
  defaultService: string
  defaultModel: string
  defaultSettings: ChatSettings
  timeout: number
  maxRetries: number
}

class ChatConfigManager {
  private config: ChatConfig = {
    defaultService: 'http://localhost:11434',
    defaultModel: 'llama2:7b',
    defaultSettings: {
      temperature: 0.7,
      maxTokens: 1000,
      topP: 0.9
    },
    timeout: 60000,
    maxRetries: 3
  }
  
  getConfig(): ChatConfig {
    return { ...this.config }
  }
  
  updateConfig(updates: Partial<ChatConfig>): void {
    this.config = { ...this.config, ...updates }
  }
  
  resetToDefaults(): void {
    this.config = {
      defaultService: 'http://localhost:11434',
      defaultModel: 'llama2:7b',
      defaultSettings: {
        temperature: 0.7,
        maxTokens: 1000,
        topP: 0.9
      },
      timeout: 60000,
      maxRetries: 3
    }
  }
}
```

## 集成示例

### 与UI组件集成

```typescript
// React组件示例
import React, { useState, useCallback } from 'react'

interface ChatComponentProps {
  serviceUrl: string
  modelId: string
}

const ChatComponent: React.FC<ChatComponentProps> = ({ serviceUrl, modelId }) => {
  const [messages, setMessages] = useState<ChatMessage[]>([])
  const [input, setInput] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  
  const sendMessage = useCallback(async () => {
    if (!input.trim() || isLoading) return
    
    const userMessage: ChatMessage = { role: 'user', content: input.trim() }
    const newMessages = [...messages, userMessage]
    
    setMessages(newMessages)
    setInput('')
    setIsLoading(true)
    
    try {
      const response = await aiChatService.chatWithLocalService(
        serviceUrl,
        modelId,
        newMessages
      )
      
      const aiMessage: ChatMessage = { role: 'assistant', content: response.content }
      setMessages(prev => [...prev, aiMessage])
      
    } catch (error) {
      console.error('发送消息失败:', error)
      // 可以添加错误消息到聊天记录
      const errorMessage: ChatMessage = {
        role: 'assistant',
        content: `错误: ${error.message}`
      }
      setMessages(prev => [...prev, errorMessage])
    } finally {
      setIsLoading(false)
    }
  }, [input, messages, serviceUrl, modelId, isLoading])
  
  return (
    <div className="chat-component">
      <div className="messages">
        {messages.map((msg, index) => (
          <div key={index} className={`message ${msg.role}`}>
            <strong>{msg.role}:</strong> {msg.content}
          </div>
        ))}
      </div>
      
      <div className="input-area">
        <input
          type="text"
          value={input}
          onChange={(e) => setInput(e.target.value)}
          onKeyPress={(e) => e.key === 'Enter' && sendMessage()}
          disabled={isLoading}
          placeholder="输入消息..."
        />
        <button onClick={sendMessage} disabled={isLoading || !input.trim()}>
          {isLoading ? '发送中...' : '发送'}
        </button>
      </div>
    </div>
  )
}
```

## 最佳实践

### 1. 错误处理

```typescript
// ✅ 好的做法
try {
  const response = await aiChatService.chatWithLocalService(url, model, messages)
  return response.content
} catch (error) {
  if (error.message.includes('ECONNREFUSED')) {
    throw new Error('AI服务未运行，请检查服务状态')
  } else if (error.message.includes('HTTP 401')) {
    throw new Error('API密钥无效，请检查配置')
  } else {
    throw new Error(`对话失败: ${error.message}`)
  }
}

// ❌ 不好的做法
const response = await aiChatService.chatWithLocalService(url, model, messages)
// 没有错误处理
```

### 2. 参数验证

```typescript
// ✅ 好的做法
function validateChatInput(messages: ChatMessage[], settings?: ChatSettings) {
  if (!messages || messages.length === 0) {
    throw new Error('消息列表不能为空')
  }
  
  if (settings?.temperature && (settings.temperature < 0 || settings.temperature > 2)) {
    throw new Error('温度参数必须在0-2之间')
  }
  
  if (settings?.maxTokens && settings.maxTokens < 1) {
    throw new Error('最大令牌数必须大于0')
  }
}
```

### 3. 资源管理

```typescript
// ✅ 好的做法 - 使用超时和取消
const controller = new AbortController()
const timeoutId = setTimeout(() => controller.abort(), 30000)

try {
  const response = await fetch(url, {
    signal: controller.signal,
    // ... 其他选项
  })
  clearTimeout(timeoutId)
  return response
} catch (error) {
  clearTimeout(timeoutId)
  throw error
}
```

### 4. 性能优化

```typescript
// ✅ 好的做法 - 缓存模型列表
const modelCache = new Map<string, { models: string[], timestamp: number }>()
const CACHE_DURATION = 5 * 60 * 1000 // 5分钟

async function getCachedModels(serviceUrl: string): Promise<string[]> {
  const cached = modelCache.get(serviceUrl)
  const now = Date.now()
  
  if (cached && (now - cached.timestamp) < CACHE_DURATION) {
    return cached.models
  }
  
  const models = await aiChatService.getAvailableModels(serviceUrl)
  modelCache.set(serviceUrl, { models, timestamp: now })
  
  return models
}
```

## 调试技巧

### 启用详细日志

```typescript
class DebugChatService {
  private debug = true
  
  private log(message: string, data?: any) {
    if (this.debug) {
      console.log(`[ChatService] ${message}`, data || '')
    }
  }
  
  async debugChat(serviceUrl: string, modelId: string, messages: ChatMessage[]) {
    this.log('开始对话', { serviceUrl, modelId, messageCount: messages.length })
    
    try {
      const startTime = Date.now()
      const response = await aiChatService.chatWithLocalService(serviceUrl, modelId, messages)
      const duration = Date.now() - startTime
      
      this.log('对话成功', {
        duration: `${duration}ms`,
        responseLength: response.content.length,
        model: response.model,
        usage: response.usage
      })
      
      return response
    } catch (error) {
      this.log('对话失败', { error: error.message })
      throw error
    }
  }
}
```

### 请求/响应拦截

```typescript
// 可以扩展aiChatService来添加拦截器
class InterceptedChatService extends AIChatService {
  private requestInterceptors: Array<(config: any) => any> = []
  private responseInterceptors: Array<(response: any) => any> = []
  
  addRequestInterceptor(interceptor: (config: any) => any) {
    this.requestInterceptors.push(interceptor)
  }
  
  addResponseInterceptor(interceptor: (response: any) => any) {
    this.responseInterceptors.push(interceptor)
  }
  
  // 重写方法以添加拦截逻辑
  async chatWithLocalService(serviceUrl: string, modelId: string, messages: ChatMessage[], settings?: ChatSettings) {
    // 应用请求拦截器
    let config = { serviceUrl, modelId, messages, settings }
    for (const interceptor of this.requestInterceptors) {
      config = interceptor(config)
    }
    
    // 调用原始方法
    let response = await super.chatWithLocalService(config.serviceUrl, config.modelId, config.messages, config.settings)
    
    // 应用响应拦截器
    for (const interceptor of this.responseInterceptors) {
      response = interceptor(response)
    }
    
    return response
  }
}
```

这些示例涵盖了 `AIChatService` 的各种使用场景，从基础对话到高级功能，帮助开发者更好地理解和使用这个服务。