# AIProviderService 常量提取建议

## 问题
代码中存在大量魔法数字和硬编码值，影响可维护性。

## 解决方案

### 1. 创建常量配置文件
```typescript
// src/constants/aiProviderConstants.ts
export const AI_PROVIDER_CONSTANTS = {
  TIMEOUTS: {
    CONNECTION_TEST: 5000,
    MODEL_FETCH: 15000,
    VERSION_CHECK: 3000
  },
  
  ENDPOINTS: {
    OLLAMA: {
      VERSION: '/api/version',
      TAGS: '/api/tags',
      GENERATE: '/api/generate'
    },
    OPENAI: {
      MODELS: '/v1/models',
      CHAT: '/v1/chat/completions'
    },
    LM_STUDIO: {
      MODELS: '/models',
      V1_MODELS: '/v1/models'
    }
  },
  
  ERROR_MESSAGES: {
    CONNECTION_TIMEOUT: '连接超时，请检查服务是否运行',
    SERVICE_UNAVAILABLE: '服务不可用，请检查地址和端口',
    INVALID_API_KEY: 'API密钥无效或格式错误',
    UNSUPPORTED_PROVIDER: '不支持的提供商类型'
  },
  
  MODEL_CATEGORIES: {
    RECOMMENDED: ['llama2', 'llama3', 'qwen', 'mistral', 'codellama'],
    POPULAR: ['llama2', 'mistral', 'qwen', 'phi', 'gemma'],
    CODE_MODELS: ['codellama', 'deepseek-coder', 'starcoder', 'wizardcoder']
  }
} as const
```

### 2. 使用示例
```typescript
import { AI_PROVIDER_CONSTANTS } from '../constants/aiProviderConstants'

class OllamaProvider {
  async testConnection(baseUrl: string) {
    const response = await fetch(
      `${baseUrl}${AI_PROVIDER_CONSTANTS.ENDPOINTS.OLLAMA.VERSION}`,
      {
        method: 'GET',
        signal: AbortSignal.timeout(AI_PROVIDER_CONSTANTS.TIMEOUTS.CONNECTION_TEST)
      }
    )
    // ...
  }
  
  private isRecommendedModel(modelName: string): boolean {
    return AI_PROVIDER_CONSTANTS.MODEL_CATEGORIES.RECOMMENDED.some(
      recommended => modelName.toLowerCase().includes(recommended)
    )
  }
}
```

## 优势
1. 集中管理配置值
2. 便于修改和维护
3. 提高代码可读性
4. 支持环境特定配置