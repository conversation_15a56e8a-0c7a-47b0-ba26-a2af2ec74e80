# AI提供商服务 - OpenRouter API文档

## 概述

本文档详细描述了 `aiProviderService.ts` 中 OpenRouter 相关的 API 方法和功能。OpenRouter 是一个多模型聚合 API 服务，提供对多种 AI 模型的统一访问接口。

## 文件位置

- **源文件**: `src/services/aiProviderService.ts`
- **测试文件**: `tests/aiProviderService.test.ts`

## OpenRouter 相关方法

### 1. API 密钥验证

#### `validateOpenRouterApiKey(apiKey: string)`

**功能**: 验证 OpenRouter API 密钥的格式和有效性

**参数**:
- `apiKey` (string): 要验证的 API 密钥

**返回值**:
```typescript
{
  isValid: boolean,
  error?: string
}
```

**验证规则**:
- 密钥不能为空
- 必须以 `sk-or-` 或 `sk-` 开头
- 长度至少 20 个字符

**使用示例**:
```typescript
const validation = aiProviderService.validateOpenRouterApiKey('sk-or-1234567890abcdef')
if (!validation.isValid) {
  console.error('API密钥验证失败:', validation.error)
}
```

### 2. 连接测试

#### `testOpenRouterConnection(apiKey: string)`

**功能**: 测试 OpenRouter 服务的连接状态和 API 密钥有效性

**参数**:
- `apiKey` (string): OpenRouter API 密钥

**返回值**:
```typescript
Promise<{
  success: boolean,
  modelCount?: number,
  error?: string
}>
```

**功能特性**:
- 自动验证 API 密钥格式
- 测试与 OpenRouter API 的连接
- 获取可用模型数量
- 详细的错误处理和用户友好的错误信息

**HTTP 请求配置**:
- **URL**: `https://openrouter.ai/api/v1/models`
- **方法**: GET
- **请求头**:
  - `Authorization`: Bearer {apiKey}
  - `Content-Type`: application/json
  - `HTTP-Referer`: https://localhost:3000
  - `X-Title`: Bookmark Manager Extension
- **超时**: 15秒

**错误处理**:
- `401`: API密钥无效或已过期
- `403`: API密钥权限不足
- `429`: API请求频率超限
- `5xx`: OpenRouter服务暂时不可用
- `AbortError`: 连接超时

**使用示例**:
```typescript
const result = await aiProviderService.testOpenRouterConnection('sk-or-your-api-key')
if (result.success) {
  console.log(`连接成功，发现 ${result.modelCount} 个模型`)
} else {
  console.error('连接失败:', result.error)
}
```

### 3. 模型列表获取

#### `getOpenRouterModels(apiKey: string)`

**功能**: 获取 OpenRouter 平台上所有可用的 AI 模型列表

**参数**:
- `apiKey` (string): OpenRouter API 密钥

**返回值**:
```typescript
Promise<AIModel[]>
```

**AIModel 接口扩展字段**:
```typescript
interface AIModel {
  id: string                    // 模型唯一标识
  name: string                  // 模型名称
  displayName: string           // 格式化的显示名称
  description: string           // 模型描述（包含定价信息）
  size?: string                 // 模型大小（如 "1.76T"）
  parameters: string            // 上下文长度（如 "4096 tokens"）
  tags: string[]               // 模型标签
  capabilities: string[]        // 模型能力
  providerId: string           // 提供商ID（"openrouter"）
  isRecommended: boolean       // 是否推荐
  isPopular: boolean          // 是否热门
  maxTokens: number           // 最大token数
  contextLength: number       // 上下文长度
  supportedFormats: string[]  // 支持的格式
}
```

**功能特性**:
- 自动格式化模型名称为用户友好的显示名称
- 生成详细的模型描述，包含定价信息
- 智能提取模型能力和标签
- 按推荐度、热门度和名称排序
- 完整的错误处理和日志记录

**使用示例**:
```typescript
const models = await aiProviderService.getOpenRouterModels('sk-or-your-api-key')
console.log(`获取到 ${models.length} 个模型`)

models.forEach(model => {
  console.log(`${model.displayName}: ${model.description}`)
  console.log(`能力: ${model.capabilities.join(', ')}`)
  console.log(`标签: ${model.tags.join(', ')}`)
})
```

### 4. 辅助方法

#### `formatOpenRouterModelName(model: OpenRouterModel)`

**功能**: 格式化 OpenRouter 模型名称为用户友好的显示名称

**参数**:
- `model` (OpenRouterModel): OpenRouter 模型信息

**返回值**: `string`

**格式化规则**:
- 优先使用模型的 `name` 字段
- 对常见模型进行名称映射（如 `openai/gpt-4` → `GPT-4`）
- 自动格式化模型ID为标题格式

#### `generateOpenRouterModelDescription(model: OpenRouterModel)`

**功能**: 生成 OpenRouter 模型的详细描述

**参数**:
- `model` (OpenRouterModel): OpenRouter 模型信息

**返回值**: `string`

**描述内容**:
- 模型的基本功能描述
- 上下文长度信息
- 模态类型（文本/多模态）
- 定价信息（通过 `formatOpenRouterPricing` 添加）

#### `determineOpenRouterCapabilities(model: OpenRouterModel)`

**功能**: 根据模型特性确定其能力列表

**参数**:
- `model` (OpenRouterModel): OpenRouter 模型信息

**返回值**: `string[]`

**能力类型**:
- `chat`: 对话能力
- `completion`: 文本补全
- `vision`: 视觉理解（多模态模型）
- `image-analysis`: 图像分析
- `coding`: 代码生成
- `instruction-following`: 指令遵循
- `long-context`: 长文本处理（≥32K tokens）

#### `extractOpenRouterModelTags(model: OpenRouterModel)`

**功能**: 提取模型的标签信息

**参数**:
- `model` (OpenRouterModel): OpenRouter 模型信息

**返回值**: `string[]`

**标签类型**:
- 基础标签: `OpenRouter`
- 提供商标签: `openai`, `anthropic`, `meta-llama` 等
- 技术标签: `text`, `multimodal`, `cl100k_base` 等
- 特性标签: `超长上下文`, `快速`, `指令优化` 等

#### `formatOpenRouterPricing(model: OpenRouterModel)`

**功能**: 格式化模型的定价信息

**参数**:
- `model` (OpenRouterModel): OpenRouter 模型信息

**返回值**: `string | null`

**定价格式**: `输入: $XX.XX/1M tokens, 输出: $XX.XX/1M tokens`

#### `extractModelSize(modelId: string)`

**功能**: 从模型ID中提取参数大小

**参数**:
- `modelId` (string): 模型标识符

**返回值**: `string | undefined`

**提取规则**:
- 正则匹配数字+B格式（如 `7b` → `7B`）
- 已知模型的预设大小
- 无法确定时返回 `undefined`

#### `isOpenRouterModelRecommended(model: OpenRouterModel)`

**功能**: 判断模型是否为推荐模型

**推荐模型**: `gpt-4`, `claude-3`, `llama-3`, `gemini-pro`, `mistral-large`

#### `isOpenRouterModelPopular(model: OpenRouterModel)`

**功能**: 判断模型是否为热门模型

**热门模型**: `gpt-3.5-turbo`, `gpt-4`, `claude-2`, `llama-2`, `mistral-7b`

## 数据类型定义

### OpenRouterModel 接口

```typescript
interface OpenRouterModel {
  id: string                    // 模型ID（如 "openai/gpt-4"）
  name: string                  // 显示名称
  description?: string          // 模型描述
  context_length: number        // 上下文长度
  architecture: {
    modality: string           // 模态类型（text/multimodal）
    tokenizer: string          // 分词器类型
    instruct_type?: string     // 指令类型
  }
  pricing: {
    prompt: string             // 输入定价（每token）
    completion: string         // 输出定价（每token）
    image?: string            // 图像处理定价
    request?: string          // 请求定价
  }
  top_provider: {
    context_length: number     // 顶级提供商上下文长度
    max_completion_tokens?: number  // 最大补全token数
  }
}
```

## 错误处理

### 常见错误类型

1. **API密钥错误**:
   - 格式无效
   - 密钥过期
   - 权限不足

2. **网络错误**:
   - 连接超时
   - 服务不可用
   - 请求频率限制

3. **数据解析错误**:
   - 响应格式异常
   - 必需字段缺失

### 错误处理策略

```typescript
try {
  const models = await aiProviderService.getOpenRouterModels(apiKey)
  // 处理成功结果
} catch (error) {
  if (error.message.includes('401')) {
    // 处理认证错误
  } else if (error.message.includes('429')) {
    // 处理频率限制
  } else {
    // 处理其他错误
  }
}
```

## 使用最佳实践

### 1. API密钥管理

```typescript
// 验证密钥格式
const validation = aiProviderService.validateOpenRouterApiKey(apiKey)
if (!validation.isValid) {
  throw new Error(validation.error)
}

// 安全存储（不要在代码中硬编码）
const apiKey = await ChromeStorageService.getSyncSetting('openrouter_api_key')
```

### 2. 错误处理

```typescript
const result = await aiProviderService.testOpenRouterConnection(apiKey)
if (!result.success) {
  // 显示用户友好的错误信息
  showErrorMessage(result.error)
  return
}
```

### 3. 模型筛选

```typescript
const models = await aiProviderService.getOpenRouterModels(apiKey)

// 筛选推荐模型
const recommendedModels = models.filter(m => m.isRecommended)

// 筛选支持长上下文的模型
const longContextModels = models.filter(m => 
  m.capabilities.includes('long-context')
)

// 按定价排序
const sortedByPrice = models.sort((a, b) => {
  const aPricing = a.description?.includes('定价:') ? 
    parseFloat(a.description.match(/输入: \$(\d+\.?\d*)/)?.[1] || '0') : 0
  const bPricing = b.description?.includes('定价:') ? 
    parseFloat(b.description.match(/输入: \$(\d+\.?\d*)/)?.[1] || '0') : 0
  return aPricing - bPricing
})
```

## 性能优化

### 1. 缓存策略

```typescript
// 模型列表缓存（通过 aiModelService）
const cachedModels = await aiModelService.getCachedModels('openrouter')
if (cachedModels) {
  return cachedModels
}

const freshModels = await aiProviderService.getOpenRouterModels(apiKey)
await aiModelService.cacheModels('openrouter', freshModels)
```

### 2. 请求优化

- 设置合理的超时时间（15-20秒）
- 使用 AbortSignal 支持请求取消
- 添加必要的请求头以符合 OpenRouter 要求

## 测试覆盖

相关测试文件: `tests/aiProviderService.test.ts`

### 测试用例

1. **API密钥验证测试**
2. **连接测试（成功/失败场景）**
3. **模型列表获取测试**
4. **错误处理测试**
5. **数据格式化测试**

### 运行测试

```bash
npm test tests/aiProviderService.test.ts
```

## 相关文档

- [AI提供商服务API文档](./aiProviderService-api.md)
- [AI集成服务文档](./aiIntegrationService-api.md)
- [AI模型服务文档](./aiModelService-api.md)
- [OpenRouter官方文档](https://openrouter.ai/docs)

## 更新日志

### 最新更新 (2024-12-17)

- ✅ 新增 OpenRouter API 密钥验证功能
- ✅ 增强连接测试的错误处理
- ✅ 优化模型信息解析和格式化
- ✅ 添加定价信息显示
- ✅ 完善模型能力和标签提取
- ✅ 实现智能模型排序
- ✅ 添加详细的日志记录

### 功能特性

- 🔐 **安全**: API密钥格式验证和安全存储
- 🚀 **性能**: 智能缓存和请求优化
- 🎯 **准确**: 详细的模型信息解析
- 🛡️ **稳定**: 完善的错误处理机制
- 📊 **丰富**: 包含定价、能力、标签等详细信息
- 🔄 **实时**: 支持实时模型列表更新