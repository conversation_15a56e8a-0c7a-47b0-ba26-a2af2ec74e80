# shadcn/ui 团队培训文档

## 🎯 培训目标

通过本培训，团队成员将能够：
- 理解shadcn/ui设计系统的核心概念
- 掌握shadcn迁移的标准流程
- 独立完成组件的shadcn重构
- 编写高质量的shadcn测试用例
- 遵循项目的shadcn使用规范

## 📚 培训内容

### 第一部分：shadcn/ui 基础概念 (30分钟)

#### 1.1 什么是shadcn/ui？
shadcn/ui是一个基于Radix UI和Tailwind CSS的组件库，提供：
- 🎨 一致的设计系统
- 🌓 自动主题切换支持
- ♿ 内置无障碍性支持
- 🔧 高度可定制性

#### 1.2 为什么要迁移到shadcn？
- **设计一致性**：统一的颜色系统和设计令牌
- **主题支持**：自动处理深色/浅色模式切换
- **维护性**：减少自定义CSS，提高代码可维护性
- **无障碍性**：内置ARIA支持和键盘导航

#### 1.3 shadcn颜色系统核心概念
```css
/* 语义化颜色变量 */
--background: 主背景色
--foreground: 主文本色
--primary: 主要强调色
--secondary: 次要背景色
--muted: 静音色调
--accent: 强调色
--destructive: 危险/错误色
--border: 边框色
--ring: 焦点环色
```

### 第二部分：实战演练 - ThemeToggle案例分析 (45分钟)

#### 2.1 迁移前的代码分析
```typescript
// 迁移前：使用硬编码颜色
className="bg-gray-100 hover:bg-gray-200 dark:bg-gray-800 dark:hover:bg-gray-700"
```

**问题识别：**
- ❌ 硬编码gray颜色
- ❌ 手动处理深色模式
- ❌ 不符合设计系统

#### 2.2 迁移后的代码
```typescript
// 迁移后：使用shadcn颜色系统
className="bg-secondary hover:bg-secondary/80"
```

**改进效果：**
- ✅ 使用语义化颜色变量
- ✅ 自动主题切换
- ✅ 符合设计系统规范

#### 2.3 完整迁移对比

| 迁移项目 | 迁移前 | 迁移后 | 效果 |
|---------|--------|--------|------|
| 背景色 | `bg-gray-100` | `bg-secondary` | 语义化 |
| 悬停效果 | `hover:bg-gray-200` | `hover:bg-secondary/80` | 透明度控制 |
| 深色模式 | `dark:bg-gray-800` | 自动处理 | 简化代码 |
| 文本色 | `text-gray-700` | `text-secondary-foreground` | 自动适配 |
| 边框 | `border-gray-300` | `border-border` | 统一边框 |
| 焦点环 | `focus:ring-primary-500` | `focus:ring-ring` | 统一焦点 |

### 第三部分：迁移标准流程 (30分钟)

#### 3.1 迁移前准备
```bash
# 1. 创建功能分支
git checkout -b feature/shadcn-migrate-component-name

# 2. 备份原始文件
cp src/components/Component.tsx src/components/Component.backup.tsx

# 3. 运行迁移检查工具
node scripts/shadcn-migration-checker.js
```

#### 3.2 执行迁移步骤

**步骤1：颜色类替换**
```typescript
// 使用查找替换功能
bg-gray-100     → bg-secondary
bg-gray-200     → bg-secondary/80
text-gray-700   → text-secondary-foreground
border-gray-300 → border-border
```

**步骤2：移除深色模式类**
```typescript
// 删除这些类，shadcn会自动处理
dark:bg-gray-800
dark:text-gray-300
dark:border-gray-600
```

**步骤3：添加性能优化**
```typescript
// 添加必要的性能优化
const memoizedValue = useMemo(() => {
  return expensiveCalculation()
}, [dependencies])
```

**步骤4：更新导入**
```typescript
// 确保导入了必要的hooks
import React, { useMemo, useCallback } from 'react'
```

#### 3.3 迁移后验证
```bash
# 1. 运行构建测试
npm run build

# 2. 运行单元测试
npm test

# 3. 运行迁移检查
node scripts/shadcn-migration-checker.js

# 4. 视觉验证
npm run dev
```

### 第四部分：测试编写指南 (30分钟)

#### 4.1 测试文件命名规范
```
ComponentName.shadcn.test.tsx
```

#### 4.2 测试结构模板
```typescript
import React from 'react'
import { render, screen, fireEvent } from '@testing-library/react'
import { describe, it, expect, vi, beforeEach } from 'vitest'
import ComponentName from '../path/to/ComponentName'

describe('ComponentName - shadcn重构测试', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('shadcn颜色系统使用验证', () => {
    it('应该使用shadcn的颜色系统', () => {
      render(<ComponentName />)
      const element = screen.getByRole('button')
      
      // 验证使用了shadcn颜色类
      expect(element).toHaveClass('bg-secondary')
      expect(element).toHaveClass('text-secondary-foreground')
    })

    it('应该不再使用旧的颜色类', () => {
      render(<ComponentName />)
      const element = screen.getByRole('button')
      
      // 验证不再使用旧颜色类
      expect(element).not.toHaveClass('bg-gray-100')
      expect(element).not.toHaveClass('text-gray-700')
    })
  })

  describe('功能验证', () => {
    it('应该保持原有功能', () => {
      // 功能测试...
    })
  })

  describe('无障碍性验证', () => {
    it('应该支持键盘导航', () => {
      // 无障碍性测试...
    })
  })
})
```

#### 4.3 必须测试的内容
- ✅ shadcn颜色类的使用
- ✅ 旧颜色类的移除
- ✅ 组件功能完整性
- ✅ 无障碍性支持
- ✅ 性能优化效果

### 第五部分：常见问题与解决方案 (20分钟)

#### 5.1 颜色不匹配问题

**问题：** 迁移后颜色与设计稿不符
```typescript
// ❌ 错误做法：使用任意颜色值
className="bg-[#f1f5f9]"

// ✅ 正确做法：检查CSS变量定义
:root {
  --secondary: 210 40% 98%;
}

// 或者使用最接近的shadcn颜色
className="bg-secondary"
```

#### 5.2 主题切换异常

**问题：** 深色模式下显示异常
```typescript
// ❌ 错误做法：手动处理深色模式
className="bg-white dark:bg-gray-900"

// ✅ 正确做法：使用shadcn颜色变量
className="bg-background"
```

#### 5.3 性能问题

**问题：** 组件重渲染频繁
```typescript
// ❌ 错误做法：每次都重新计算
const result = expensiveCalculation()

// ✅ 正确做法：使用useMemo缓存
const result = useMemo(() => expensiveCalculation(), [deps])
```

#### 5.4 测试失败

**问题：** 测试用例无法通过
```typescript
// ❌ 错误做法：测试具体颜色值
expect(element).toHaveStyle('background-color: rgb(241, 245, 249)')

// ✅ 正确做法：测试CSS类名
expect(element).toHaveClass('bg-secondary')
```

### 第六部分：质量检查清单 (15分钟)

#### 6.1 代码质量检查
- [ ] 所有gray颜色类已替换为shadcn颜色变量
- [ ] 移除了所有dark:前缀类
- [ ] 添加了必要的性能优化
- [ ] 保持了原有功能完整性
- [ ] 代码注释使用中文

#### 6.2 测试质量检查
- [ ] 创建了对应的.shadcn.test.tsx文件
- [ ] 测试覆盖率达到100%
- [ ] 包含shadcn颜色系统验证测试
- [ ] 包含功能完整性测试
- [ ] 包含无障碍性测试

#### 6.3 构建质量检查
- [ ] npm run build 成功
- [ ] npm test 全部通过
- [ ] 迁移检查工具无错误
- [ ] 视觉效果符合预期

## 🛠️ 实践练习

### 练习1：基础颜色迁移 (15分钟)

给定以下代码，请完成shadcn迁移：

```typescript
// 练习代码
const Button = ({ children, onClick }) => {
  return (
    <button
      onClick={onClick}
      className="
        bg-gray-100 hover:bg-gray-200 
        dark:bg-gray-800 dark:hover:bg-gray-700
        text-gray-700 dark:text-gray-300
        border border-gray-300 dark:border-gray-600
        focus:ring-2 focus:ring-blue-500
        px-4 py-2 rounded-md
      "
    >
      {children}
    </button>
  )
}
```

**参考答案：**
```typescript
const Button = ({ children, onClick }) => {
  return (
    <button
      onClick={onClick}
      className="
        bg-secondary hover:bg-secondary/80
        text-secondary-foreground
        border border-border
        focus:ring-2 focus:ring-ring
        px-4 py-2 rounded-md
      "
    >
      {children}
    </button>
  )
}
```

### 练习2：性能优化 (15分钟)

为以下组件添加性能优化：

```typescript
// 练习代码
const SearchResults = ({ items, searchTerm }) => {
  const filteredItems = items.filter(item => 
    item.name.toLowerCase().includes(searchTerm.toLowerCase())
  )
  
  const handleItemClick = (item) => {
    console.log('Clicked:', item)
  }

  return (
    <div>
      {filteredItems.map(item => (
        <div key={item.id} onClick={() => handleItemClick(item)}>
          {item.name}
        </div>
      ))}
    </div>
  )
}
```

**参考答案：**
```typescript
const SearchResults = ({ items, searchTerm }) => {
  const filteredItems = useMemo(() => 
    items.filter(item => 
      item.name.toLowerCase().includes(searchTerm.toLowerCase())
    ), [items, searchTerm]
  )
  
  const handleItemClick = useCallback((item) => {
    console.log('Clicked:', item)
  }, [])

  return (
    <div>
      {filteredItems.map(item => (
        <div key={item.id} onClick={() => handleItemClick(item)}>
          {item.name}
        </div>
      ))}
    </div>
  )
}
```

### 练习3：测试编写 (20分钟)

为练习1的Button组件编写完整的shadcn测试：

**参考答案：**
```typescript
describe('Button - shadcn重构测试', () => {
  it('应该使用shadcn颜色系统', () => {
    render(<Button>测试按钮</Button>)
    const button = screen.getByRole('button')
    
    expect(button).toHaveClass('bg-secondary')
    expect(button).toHaveClass('hover:bg-secondary/80')
    expect(button).toHaveClass('text-secondary-foreground')
    expect(button).toHaveClass('border-border')
    expect(button).toHaveClass('focus:ring-ring')
  })

  it('应该不再使用旧的颜色类', () => {
    render(<Button>测试按钮</Button>)
    const button = screen.getByRole('button')
    
    expect(button).not.toHaveClass('bg-gray-100')
    expect(button).not.toHaveClass('text-gray-700')
    expect(button).not.toHaveClass('border-gray-300')
  })

  it('应该正确处理点击事件', () => {
    const handleClick = vi.fn()
    render(<Button onClick={handleClick}>测试按钮</Button>)
    
    const button = screen.getByRole('button')
    fireEvent.click(button)
    
    expect(handleClick).toHaveBeenCalledTimes(1)
  })
})
```

## 📋 培训评估

### 理论知识测试 (10分钟)

1. **shadcn/ui的主要优势是什么？**
   - A. 更小的包体积
   - B. 统一的设计系统和自动主题切换
   - C. 更快的渲染速度
   - D. 更少的依赖

2. **以下哪个是正确的shadcn颜色迁移？**
   - A. `bg-gray-100` → `bg-gray-secondary`
   - B. `bg-gray-100` → `bg-secondary`
   - C. `bg-gray-100` → `bg-muted`
   - D. `bg-gray-100` → `bg-background`

3. **深色模式应该如何处理？**
   - A. 使用 `dark:` 前缀类
   - B. 使用shadcn颜色变量，自动处理
   - C. 手动检测主题状态
   - D. 使用CSS媒体查询

**答案：1-B, 2-B, 3-B**

### 实践能力评估 (20分钟)

请完成一个完整的组件迁移，包括：
1. 颜色系统迁移
2. 性能优化
3. 测试编写
4. 质量检查

## 🎓 培训总结

### 关键要点回顾
1. **shadcn颜色系统**：使用语义化颜色变量替代硬编码颜色
2. **自动主题切换**：移除dark:前缀，让shadcn自动处理
3. **性能优化**：合理使用useMemo和useCallback
4. **测试覆盖**：确保100%测试覆盖率
5. **质量保证**：使用检查工具验证迁移质量

### 下一步行动
1. 开始实际组件迁移
2. 定期使用检查工具验证质量
3. 遇到问题及时沟通
4. 分享迁移经验和最佳实践

### 资源链接
- [shadcn使用规范文档](./shadcn-ui-migration-guidelines.md)
- [快速参考指南](./shadcn-quick-reference.md)
- [迁移检查工具](../scripts/shadcn-migration-checker.js)
- [ThemeToggle案例分析](./ThemeToggle-shadcn-code-quality-analysis.md)

---

**培训版本：** v1.0.0  
**培训时长：** 约3小时  
**适用对象：** 前端开发团队成员  
**更新日期：** 2025年1月14日