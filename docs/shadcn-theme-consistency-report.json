{"timestamp": "2025-08-15T14:49:52.329Z", "cssVariables": true, "tailwindConfig": true, "hardcodedColors": [{"type": "hardcoded-color", "pattern": "bg-gray-\\d+", "matches": ["bg-gray-50"], "file": "src\\components\\AIConfigPanel.tsx"}, {"type": "hardcoded-color", "pattern": "text-gray-\\d+", "matches": ["text-gray-600", "text-gray-500", "text-gray-900", "text-gray-700", "text-gray-800"], "file": "src\\components\\AIConfigPanel.tsx"}, {"type": "hardcoded-color", "pattern": "border-gray-\\d+", "matches": ["border-gray-200", "border-gray-300", "border-gray-100"], "file": "src\\components\\AIConfigPanel.tsx"}, {"type": "hardcoded-color", "pattern": "bg-blue-\\d+", "matches": ["bg-blue-50", "bg-blue-600", "bg-blue-700"], "file": "src\\components\\AIConfigPanel.tsx"}, {"type": "hardcoded-color", "pattern": "text-blue-\\d+", "matches": ["text-blue-500", "text-blue-600", "text-blue-800"], "file": "src\\components\\AIConfigPanel.tsx"}, {"type": "hardcoded-color", "pattern": "border-blue-\\d+", "matches": ["border-blue-600", "border-blue-500"], "file": "src\\components\\AIConfigPanel.tsx"}, {"type": "hardcoded-color", "pattern": "bg-red-\\d+", "matches": ["bg-red-50", "bg-red-500"], "file": "src\\components\\AIConfigPanel.tsx"}, {"type": "hardcoded-color", "pattern": "text-red-\\d+", "matches": ["text-red-400", "text-red-800", "text-red-700", "text-red-600"], "file": "src\\components\\AIConfigPanel.tsx"}, {"type": "hardcoded-color", "pattern": "border-red-\\d+", "matches": ["border-red-200"], "file": "src\\components\\AIConfigPanel.tsx"}, {"type": "hardcoded-color", "pattern": "bg-green-\\d+", "matches": ["bg-green-500"], "file": "src\\components\\AIConfigPanel.tsx"}, {"type": "hardcoded-color", "pattern": "text-green-\\d+", "matches": ["text-green-600"], "file": "src\\components\\AIConfigPanel.tsx"}, {"type": "hardcoded-color", "pattern": "bg-yellow-\\d+", "matches": ["bg-yellow-100"], "file": "src\\components\\AIConfigPanel.tsx"}, {"type": "hardcoded-color", "pattern": "text-yellow-\\d+", "matches": ["text-yellow-800"], "file": "src\\components\\AIConfigPanel.tsx"}, {"type": "hardcoded-color", "pattern": "bg-blue-\\d+", "matches": ["bg-blue-400"], "file": "src\\components\\BookmarkCompact.tsx"}, {"type": "hardcoded-color", "pattern": "text-blue-\\d+", "matches": ["text-blue-600"], "file": "src\\components\\BookmarkCompact.tsx"}, {"type": "hardcoded-color", "pattern": "bg-green-\\d+", "matches": ["bg-green-400"], "file": "src\\components\\BookmarkCompact.tsx"}, {"type": "hardcoded-color", "pattern": "text-blue-\\d+", "matches": ["text-blue-600"], "file": "src\\components\\BookmarkRow.tsx"}, {"type": "hardcoded-color", "pattern": "bg-gray-\\d+", "matches": ["bg-gray-100"], "file": "src\\components\\CategoryCard.tsx"}, {"type": "hardcoded-color", "pattern": "text-gray-\\d+", "matches": ["text-gray-600", "text-gray-900", "text-gray-400", "text-gray-500"], "file": "src\\components\\CategoryCard.tsx"}, {"type": "hardcoded-color", "pattern": "border-gray-\\d+", "matches": ["border-gray-300", "border-gray-200"], "file": "src\\components\\CategoryCard.tsx"}, {"type": "hardcoded-color", "pattern": "bg-blue-\\d+", "matches": ["bg-blue-50"], "file": "src\\components\\CategoryCard.tsx"}, {"type": "hardcoded-color", "pattern": "text-blue-\\d+", "matches": ["text-blue-600"], "file": "src\\components\\CategoryCard.tsx"}, {"type": "hardcoded-color", "pattern": "bg-red-\\d+", "matches": ["bg-red-50"], "file": "src\\components\\CategoryCard.tsx"}, {"type": "hardcoded-color", "pattern": "text-red-\\d+", "matches": ["text-red-600"], "file": "src\\components\\CategoryCard.tsx"}, {"type": "hardcoded-color", "pattern": "bg-green-\\d+", "matches": ["bg-green-100"], "file": "src\\components\\CategoryCard.tsx"}, {"type": "hardcoded-color", "pattern": "text-green-\\d+", "matches": ["text-green-700"], "file": "src\\components\\CategoryCard.tsx"}, {"type": "hardcoded-color", "pattern": "bg-white\\b", "matches": ["bg-white"], "file": "src\\components\\CategoryCard.tsx"}, {"type": "hardcoded-color", "pattern": "bg-gray-\\d+", "matches": ["bg-gray-500", "bg-gray-50"], "file": "src\\components\\CategoryForm.tsx"}, {"type": "hardcoded-color", "pattern": "text-gray-\\d+", "matches": ["text-gray-700", "text-gray-500", "text-gray-600"], "file": "src\\components\\CategoryForm.tsx"}, {"type": "hardcoded-color", "pattern": "border-gray-\\d+", "matches": ["border-gray-300", "border-gray-900", "border-gray-400", "border-gray-200"], "file": "src\\components\\CategoryForm.tsx"}, {"type": "hardcoded-color", "pattern": "bg-blue-\\d+", "matches": ["bg-blue-500"], "file": "src\\components\\CategoryForm.tsx"}, {"type": "hardcoded-color", "pattern": "bg-red-\\d+", "matches": ["bg-red-500"], "file": "src\\components\\CategoryForm.tsx"}, {"type": "hardcoded-color", "pattern": "text-red-\\d+", "matches": ["text-red-600"], "file": "src\\components\\CategoryForm.tsx"}, {"type": "hardcoded-color", "pattern": "border-red-\\d+", "matches": ["border-red-300"], "file": "src\\components\\CategoryForm.tsx"}, {"type": "hardcoded-color", "pattern": "bg-green-\\d+", "matches": ["bg-green-500"], "file": "src\\components\\CategoryForm.tsx"}, {"type": "hardcoded-color", "pattern": "bg-yellow-\\d+", "matches": ["bg-yellow-500"], "file": "src\\components\\CategoryForm.tsx"}, {"type": "hardcoded-color", "pattern": "bg-white\\b", "matches": ["bg-white"], "file": "src\\components\\CategoryForm.tsx"}, {"type": "hardcoded-color", "pattern": "bg-gray-\\d+", "matches": ["bg-gray-100", "bg-gray-50"], "file": "src\\components\\CategoryList.tsx"}, {"type": "hardcoded-color", "pattern": "text-gray-\\d+", "matches": ["text-gray-500", "text-gray-400", "text-gray-900", "text-gray-600"], "file": "src\\components\\CategoryList.tsx"}, {"type": "hardcoded-color", "pattern": "text-green-\\d+", "matches": ["text-green-600"], "file": "src\\components\\CategoryList.tsx"}, {"type": "hardcoded-color", "pattern": "bg-red-\\d+", "matches": ["bg-red-100"], "file": "src\\components\\CategoryManagementTab.tsx"}, {"type": "hardcoded-color", "pattern": "text-red-\\d+", "matches": ["text-red-600"], "file": "src\\components\\CategoryManagementTab.tsx"}, {"type": "hardcoded-color", "pattern": "bg-gray-\\d+", "matches": ["bg-gray-50", "bg-gray-500"], "file": "src\\components\\CategoryModal.tsx"}, {"type": "hardcoded-color", "pattern": "text-gray-\\d+", "matches": ["text-gray-900", "text-gray-500", "text-gray-600", "text-gray-700", "text-gray-400"], "file": "src\\components\\CategoryModal.tsx"}, {"type": "hardcoded-color", "pattern": "border-gray-\\d+", "matches": ["border-gray-200", "border-gray-300"], "file": "src\\components\\CategoryModal.tsx"}, {"type": "hardcoded-color", "pattern": "bg-red-\\d+", "matches": ["bg-red-100", "bg-red-600", "bg-red-700"], "file": "src\\components\\CategoryModal.tsx"}, {"type": "hardcoded-color", "pattern": "text-red-\\d+", "matches": ["text-red-600"], "file": "src\\components\\CategoryModal.tsx"}, {"type": "hardcoded-color", "pattern": "bg-white\\b", "matches": ["bg-white"], "file": "src\\components\\CategoryModal.tsx"}, {"type": "hardcoded-color", "pattern": "bg-gray-\\d+", "matches": ["bg-gray-600", "bg-gray-700"], "file": "src\\components\\ConflictResolution\\BatchActionsPanel.tsx"}, {"type": "hardcoded-color", "pattern": "bg-blue-\\d+", "matches": ["bg-blue-50", "bg-blue-600", "bg-blue-700"], "file": "src\\components\\ConflictResolution\\BatchActionsPanel.tsx"}, {"type": "hardcoded-color", "pattern": "text-blue-\\d+", "matches": ["text-blue-900"], "file": "src\\components\\ConflictResolution\\BatchActionsPanel.tsx"}, {"type": "hardcoded-color", "pattern": "border-blue-\\d+", "matches": ["border-blue-200"], "file": "src\\components\\ConflictResolution\\BatchActionsPanel.tsx"}, {"type": "hardcoded-color", "pattern": "bg-gray-\\d+", "matches": ["bg-gray-50"], "file": "src\\components\\ConflictResolution\\ConflictListPanel.tsx"}, {"type": "hardcoded-color", "pattern": "text-gray-\\d+", "matches": ["text-gray-900", "text-gray-600", "text-gray-500"], "file": "src\\components\\ConflictResolution\\ConflictListPanel.tsx"}, {"type": "hardcoded-color", "pattern": "border-gray-\\d+", "matches": ["border-gray-200", "border-gray-100"], "file": "src\\components\\ConflictResolution\\ConflictListPanel.tsx"}, {"type": "hardcoded-color", "pattern": "bg-blue-\\d+", "matches": ["bg-blue-50"], "file": "src\\components\\ConflictResolution\\ConflictListPanel.tsx"}, {"type": "hardcoded-color", "pattern": "border-blue-\\d+", "matches": ["border-blue-200"], "file": "src\\components\\ConflictResolution\\ConflictListPanel.tsx"}, {"type": "hardcoded-color", "pattern": "text-red-\\d+", "matches": ["text-red-400"], "file": "src\\components\\ConflictResolution\\ConflictListPanel.tsx"}, {"type": "hardcoded-color", "pattern": "text-green-\\d+", "matches": ["text-green-600"], "file": "src\\components\\ConflictResolution\\ConflictListPanel.tsx"}, {"type": "hardcoded-color", "pattern": "bg-gray-\\d+", "matches": ["bg-gray-50"], "file": "src\\components\\ConflictResolution\\ManualEditForm.tsx"}, {"type": "hardcoded-color", "pattern": "text-gray-\\d+", "matches": ["text-gray-900", "text-gray-700"], "file": "src\\components\\ConflictResolution\\ManualEditForm.tsx"}, {"type": "hardcoded-color", "pattern": "border-gray-\\d+", "matches": ["border-gray-300"], "file": "src\\components\\ConflictResolution\\ManualEditForm.tsx"}, {"type": "hardcoded-color", "pattern": "bg-green-\\d+", "matches": ["bg-green-600", "bg-green-700"], "file": "src\\components\\ConflictResolution\\ManualEditForm.tsx"}, {"type": "hardcoded-color", "pattern": "bg-gray-\\d+", "matches": ["bg-gray-500"], "file": "src\\components\\ConflictResolution\\utils.ts"}, {"type": "hardcoded-color", "pattern": "bg-blue-\\d+", "matches": ["bg-blue-500"], "file": "src\\components\\ConflictResolution\\utils.ts"}, {"type": "hardcoded-color", "pattern": "bg-green-\\d+", "matches": ["bg-green-500"], "file": "src\\components\\ConflictResolution\\utils.ts"}, {"type": "hardcoded-color", "pattern": "bg-gray-\\d+", "matches": ["bg-gray-50", "bg-gray-500"], "file": "src\\components\\ConflictResolutionDialog.tsx"}, {"type": "hardcoded-color", "pattern": "text-gray-\\d+", "matches": ["text-gray-900", "text-gray-600", "text-gray-400", "text-gray-700"], "file": "src\\components\\ConflictResolutionDialog.tsx"}, {"type": "hardcoded-color", "pattern": "border-gray-\\d+", "matches": ["border-gray-200", "border-gray-300", "border-gray-500"], "file": "src\\components\\ConflictResolutionDialog.tsx"}, {"type": "hardcoded-color", "pattern": "bg-blue-\\d+", "matches": ["bg-blue-500", "bg-blue-50"], "file": "src\\components\\ConflictResolutionDialog.tsx"}, {"type": "hardcoded-color", "pattern": "border-blue-\\d+", "matches": ["border-blue-500"], "file": "src\\components\\ConflictResolutionDialog.tsx"}, {"type": "hardcoded-color", "pattern": "bg-red-\\d+", "matches": ["bg-red-50"], "file": "src\\components\\ConflictResolutionDialog.tsx"}, {"type": "hardcoded-color", "pattern": "bg-green-\\d+", "matches": ["bg-green-50"], "file": "src\\components\\ConflictResolutionDialog.tsx"}, {"type": "hardcoded-color", "pattern": "text-green-\\d+", "matches": ["text-green-600"], "file": "src\\components\\ConflictResolutionDialog.tsx"}, {"type": "hardcoded-color", "pattern": "border-green-\\d+", "matches": ["border-green-500"], "file": "src\\components\\ConflictResolutionDialog.tsx"}, {"type": "hardcoded-color", "pattern": "bg-white\\b", "matches": ["bg-white"], "file": "src\\components\\ConflictResolutionDialog.tsx"}, {"type": "hardcoded-color", "pattern": "text-blue-\\d+", "matches": ["text-blue-600"], "file": "src\\components\\examples\\AddBookmarkModalDemo.tsx"}, {"type": "hardcoded-color", "pattern": "bg-gray-\\d+", "matches": ["bg-gray-50"], "file": "src\\components\\examples\\BookmarkEditModalDemo.tsx"}, {"type": "hardcoded-color", "pattern": "text-gray-\\d+", "matches": ["text-gray-600"], "file": "src\\components\\examples\\BookmarkEditModalDemo.tsx"}, {"type": "hardcoded-color", "pattern": "bg-green-\\d+", "matches": ["bg-green-500"], "file": "src\\components\\examples\\DeleteConfirmModalDemo.tsx"}, {"type": "hardcoded-color", "pattern": "bg-green-\\d+", "matches": ["bg-green-50"], "file": "src\\components\\examples\\PopupAppDemo.tsx"}, {"type": "hardcoded-color", "pattern": "text-green-\\d+", "matches": ["text-green-800", "text-green-600"], "file": "src\\components\\examples\\PopupAppDemo.tsx"}, {"type": "hardcoded-color", "pattern": "border-green-\\d+", "matches": ["border-green-200"], "file": "src\\components\\examples\\PopupAppDemo.tsx"}, {"type": "hardcoded-color", "pattern": "text-gray-\\d+", "matches": ["text-gray-500", "text-gray-700", "text-gray-900", "text-gray-400", "text-gray-600"], "file": "src\\components\\HelpTooltip.tsx"}, {"type": "hardcoded-color", "pattern": "bg-blue-\\d+", "matches": ["bg-blue-500", "bg-blue-600", "bg-blue-50"], "file": "src\\components\\HelpTooltip.tsx"}, {"type": "hardcoded-color", "pattern": "text-blue-\\d+", "matches": ["text-blue-600", "text-blue-800"], "file": "src\\components\\HelpTooltip.tsx"}, {"type": "hardcoded-color", "pattern": "border-blue-\\d+", "matches": ["border-blue-300"], "file": "src\\components\\HelpTooltip.tsx"}, {"type": "hardcoded-color", "pattern": "text-yellow-\\d+", "matches": ["text-yellow-300"], "file": "src\\components\\HelpTooltip.tsx"}, {"type": "hardcoded-color", "pattern": "bg-white\\b", "matches": ["bg-white"], "file": "src\\components\\HelpTooltip.tsx"}, {"type": "hardcoded-color", "pattern": "text-gray-\\d+", "matches": ["text-gray-600"], "file": "src\\components\\ImportExportTab.tsx"}, {"type": "hardcoded-color", "pattern": "bg-gray-\\d+", "matches": ["bg-gray-200"], "file": "src\\components\\LazyLoadWrapper.tsx"}, {"type": "hardcoded-color", "pattern": "text-gray-\\d+", "matches": ["text-gray-500", "text-gray-600", "text-gray-400"], "file": "src\\components\\LazyLoadWrapper.tsx"}, {"type": "hardcoded-color", "pattern": "text-red-\\d+", "matches": ["text-red-600"], "file": "src\\components\\LazyLoadWrapper.tsx"}, {"type": "hardcoded-color", "pattern": "bg-gray-\\d+", "matches": ["bg-gray-200"], "file": "src\\components\\LoadingIndicator.tsx"}, {"type": "hardcoded-color", "pattern": "text-gray-\\d+", "matches": ["text-gray-600"], "file": "src\\components\\LoadingIndicator.tsx"}, {"type": "hardcoded-color", "pattern": "bg-white\\b", "matches": ["bg-white"], "file": "src\\components\\LoadingIndicator.tsx"}, {"type": "hardcoded-color", "pattern": "bg-gray-\\d+", "matches": ["bg-gray-50", "bg-gray-600", "bg-gray-700", "bg-gray-100"], "file": "src\\components\\OptionsPageErrorBoundary.tsx"}, {"type": "hardcoded-color", "pattern": "text-gray-\\d+", "matches": ["text-gray-900", "text-gray-600", "text-gray-800", "text-gray-700"], "file": "src\\components\\OptionsPageErrorBoundary.tsx"}, {"type": "hardcoded-color", "pattern": "border-gray-\\d+", "matches": ["border-gray-200", "border-gray-300"], "file": "src\\components\\OptionsPageErrorBoundary.tsx"}, {"type": "hardcoded-color", "pattern": "bg-blue-\\d+", "matches": ["bg-blue-500", "bg-blue-600", "bg-blue-700"], "file": "src\\components\\OptionsPageErrorBoundary.tsx"}, {"type": "hardcoded-color", "pattern": "bg-red-\\d+", "matches": ["bg-red-50", "bg-red-100"], "file": "src\\components\\OptionsPageErrorBoundary.tsx"}, {"type": "hardcoded-color", "pattern": "text-red-\\d+", "matches": ["text-red-600"], "file": "src\\components\\OptionsPageErrorBoundary.tsx"}, {"type": "hardcoded-color", "pattern": "bg-green-\\d+", "matches": ["bg-green-600", "bg-green-700"], "file": "src\\components\\OptionsPageErrorBoundary.tsx"}, {"type": "hardcoded-color", "pattern": "bg-yellow-\\d+", "matches": ["bg-yellow-600", "bg-yellow-700"], "file": "src\\components\\OptionsPageErrorBoundary.tsx"}, {"type": "hardcoded-color", "pattern": "bg-white\\b", "matches": ["bg-white"], "file": "src\\components\\OptionsPageErrorBoundary.tsx"}, {"type": "hardcoded-color", "pattern": "bg-gray-\\d+", "matches": ["bg-gray-200", "bg-gray-300", "bg-gray-100", "bg-gray-50"], "file": "src\\components\\TagBatchActions.tsx"}, {"type": "hardcoded-color", "pattern": "text-gray-\\d+", "matches": ["text-gray-700", "text-gray-500", "text-gray-600"], "file": "src\\components\\TagBatchActions.tsx"}, {"type": "hardcoded-color", "pattern": "border-gray-\\d+", "matches": ["border-gray-200"], "file": "src\\components\\TagBatchActions.tsx"}, {"type": "hardcoded-color", "pattern": "bg-blue-\\d+", "matches": ["bg-blue-50", "bg-blue-100", "bg-blue-600", "bg-blue-700"], "file": "src\\components\\TagBatchActions.tsx"}, {"type": "hardcoded-color", "pattern": "text-blue-\\d+", "matches": ["text-blue-600", "text-blue-800", "text-blue-700"], "file": "src\\components\\TagBatchActions.tsx"}, {"type": "hardcoded-color", "pattern": "border-blue-\\d+", "matches": ["border-blue-200", "border-blue-300"], "file": "src\\components\\TagBatchActions.tsx"}, {"type": "hardcoded-color", "pattern": "bg-red-\\d+", "matches": ["bg-red-50", "bg-red-100", "bg-red-600", "bg-red-700"], "file": "src\\components\\TagBatchActions.tsx"}, {"type": "hardcoded-color", "pattern": "text-red-\\d+", "matches": ["text-red-600", "text-red-800", "text-red-700"], "file": "src\\components\\TagBatchActions.tsx"}, {"type": "hardcoded-color", "pattern": "border-red-\\d+", "matches": ["border-red-200"], "file": "src\\components\\TagBatchActions.tsx"}, {"type": "hardcoded-color", "pattern": "bg-white\\b", "matches": ["bg-white"], "file": "src\\components\\TagBatchActions.tsx"}, {"type": "hardcoded-color", "pattern": "bg-gray-\\d+", "matches": ["bg-gray-100", "bg-gray-400"], "file": "src\\components\\TagCard.tsx"}, {"type": "hardcoded-color", "pattern": "text-gray-\\d+", "matches": ["text-gray-600", "text-gray-900", "text-gray-400", "text-gray-500"], "file": "src\\components\\TagCard.tsx"}, {"type": "hardcoded-color", "pattern": "border-gray-\\d+", "matches": ["border-gray-300", "border-gray-200"], "file": "src\\components\\TagCard.tsx"}, {"type": "hardcoded-color", "pattern": "bg-blue-\\d+", "matches": ["bg-blue-50"], "file": "src\\components\\TagCard.tsx"}, {"type": "hardcoded-color", "pattern": "text-blue-\\d+", "matches": ["text-blue-600"], "file": "src\\components\\TagCard.tsx"}, {"type": "hardcoded-color", "pattern": "bg-red-\\d+", "matches": ["bg-red-100", "bg-red-500", "bg-red-50"], "file": "src\\components\\TagCard.tsx"}, {"type": "hardcoded-color", "pattern": "text-red-\\d+", "matches": ["text-red-700", "text-red-600"], "file": "src\\components\\TagCard.tsx"}, {"type": "hardcoded-color", "pattern": "bg-green-\\d+", "matches": ["bg-green-100", "bg-green-500"], "file": "src\\components\\TagCard.tsx"}, {"type": "hardcoded-color", "pattern": "text-green-\\d+", "matches": ["text-green-700"], "file": "src\\components\\TagCard.tsx"}, {"type": "hardcoded-color", "pattern": "bg-yellow-\\d+", "matches": ["bg-yellow-100", "bg-yellow-500"], "file": "src\\components\\TagCard.tsx"}, {"type": "hardcoded-color", "pattern": "text-yellow-\\d+", "matches": ["text-yellow-700"], "file": "src\\components\\TagCard.tsx"}, {"type": "hardcoded-color", "pattern": "bg-white\\b", "matches": ["bg-white"], "file": "src\\components\\TagCard.tsx"}, {"type": "hardcoded-color", "pattern": "bg-gray-\\d+", "matches": ["bg-gray-100", "bg-gray-200"], "file": "src\\components\\TagCloud.tsx"}, {"type": "hardcoded-color", "pattern": "text-gray-\\d+", "matches": ["text-gray-700", "text-gray-400", "text-gray-900", "text-gray-500", "text-gray-600"], "file": "src\\components\\TagCloud.tsx"}, {"type": "hardcoded-color", "pattern": "bg-red-\\d+", "matches": ["bg-red-100", "bg-red-200"], "file": "src\\components\\TagCloud.tsx"}, {"type": "hardcoded-color", "pattern": "text-red-\\d+", "matches": ["text-red-700"], "file": "src\\components\\TagCloud.tsx"}, {"type": "hardcoded-color", "pattern": "bg-green-\\d+", "matches": ["bg-green-100", "bg-green-200"], "file": "src\\components\\TagCloud.tsx"}, {"type": "hardcoded-color", "pattern": "text-green-\\d+", "matches": ["text-green-700"], "file": "src\\components\\TagCloud.tsx"}, {"type": "hardcoded-color", "pattern": "bg-yellow-\\d+", "matches": ["bg-yellow-100", "bg-yellow-200"], "file": "src\\components\\TagCloud.tsx"}, {"type": "hardcoded-color", "pattern": "text-yellow-\\d+", "matches": ["text-yellow-700"], "file": "src\\components\\TagCloud.tsx"}, {"type": "hardcoded-color", "pattern": "bg-white\\b", "matches": ["bg-white"], "file": "src\\components\\TagCloud.tsx"}, {"type": "hardcoded-color", "pattern": "bg-gray-\\d+", "matches": ["bg-gray-50", "bg-gray-100", "bg-gray-200"], "file": "src\\components\\TagColorPicker.tsx"}, {"type": "hardcoded-color", "pattern": "text-gray-\\d+", "matches": ["text-gray-700", "text-gray-400", "text-gray-600", "text-gray-500"], "file": "src\\components\\TagColorPicker.tsx"}, {"type": "hardcoded-color", "pattern": "border-gray-\\d+", "matches": ["border-gray-300"], "file": "src\\components\\TagColorPicker.tsx"}, {"type": "hardcoded-color", "pattern": "bg-blue-\\d+", "matches": ["bg-blue-100", "bg-blue-200"], "file": "src\\components\\TagColorPicker.tsx"}, {"type": "hardcoded-color", "pattern": "text-blue-\\d+", "matches": ["text-blue-700"], "file": "src\\components\\TagColorPicker.tsx"}, {"type": "hardcoded-color", "pattern": "bg-red-\\d+", "matches": ["bg-red-100", "bg-red-200", "bg-red-50"], "file": "src\\components\\TagColorPicker.tsx"}, {"type": "hardcoded-color", "pattern": "text-red-\\d+", "matches": ["text-red-700", "text-red-600"], "file": "src\\components\\TagColorPicker.tsx"}, {"type": "hardcoded-color", "pattern": "border-red-\\d+", "matches": ["border-red-300"], "file": "src\\components\\TagColorPicker.tsx"}, {"type": "hardcoded-color", "pattern": "bg-gray-\\d+", "matches": ["bg-gray-50", "bg-gray-400"], "file": "src\\components\\TagForm.tsx"}, {"type": "hardcoded-color", "pattern": "text-gray-\\d+", "matches": ["text-gray-900", "text-gray-600", "text-gray-700", "text-gray-400", "text-gray-500"], "file": "src\\components\\TagForm.tsx"}, {"type": "hardcoded-color", "pattern": "border-gray-\\d+", "matches": ["border-gray-300"], "file": "src\\components\\TagForm.tsx"}, {"type": "hardcoded-color", "pattern": "bg-blue-\\d+", "matches": ["bg-blue-600", "bg-blue-700"], "file": "src\\components\\TagForm.tsx"}, {"type": "hardcoded-color", "pattern": "bg-red-\\d+", "matches": ["bg-red-50"], "file": "src\\components\\TagForm.tsx"}, {"type": "hardcoded-color", "pattern": "text-red-\\d+", "matches": ["text-red-400", "text-red-700", "text-red-500", "text-red-600"], "file": "src\\components\\TagForm.tsx"}, {"type": "hardcoded-color", "pattern": "border-red-\\d+", "matches": ["border-red-200", "border-red-300"], "file": "src\\components\\TagForm.tsx"}, {"type": "hardcoded-color", "pattern": "bg-white\\b", "matches": ["bg-white"], "file": "src\\components\\TagForm.tsx"}, {"type": "hardcoded-color", "pattern": "bg-gray-\\d+", "matches": ["bg-gray-100", "bg-gray-50", "bg-gray-200"], "file": "src\\components\\TagList.tsx"}, {"type": "hardcoded-color", "pattern": "text-gray-\\d+", "matches": ["text-gray-500", "text-gray-400", "text-gray-900", "text-gray-600", "text-gray-700"], "file": "src\\components\\TagList.tsx"}, {"type": "hardcoded-color", "pattern": "border-gray-\\d+", "matches": ["border-gray-300"], "file": "src\\components\\TagList.tsx"}, {"type": "hardcoded-color", "pattern": "text-green-\\d+", "matches": ["text-green-600"], "file": "src\\components\\TagList.tsx"}, {"type": "hardcoded-color", "pattern": "bg-white\\b", "matches": ["bg-white"], "file": "src\\components\\TagList.tsx"}, {"type": "hardcoded-color", "pattern": "bg-gray-\\d+", "matches": ["bg-gray-50"], "file": "src\\components\\TagManagementTab.tsx"}, {"type": "hardcoded-color", "pattern": "text-gray-\\d+", "matches": ["text-gray-900", "text-gray-500", "text-gray-600", "text-gray-700"], "file": "src\\components\\TagManagementTab.tsx"}, {"type": "hardcoded-color", "pattern": "border-gray-\\d+", "matches": ["border-gray-300"], "file": "src\\components\\TagManagementTab.tsx"}, {"type": "hardcoded-color", "pattern": "bg-red-\\d+", "matches": ["bg-red-100"], "file": "src\\components\\TagManagementTab.tsx"}, {"type": "hardcoded-color", "pattern": "text-red-\\d+", "matches": ["text-red-600"], "file": "src\\components\\TagManagementTab.tsx"}, {"type": "hardcoded-color", "pattern": "bg-white\\b", "matches": ["bg-white"], "file": "src\\components\\TagManagementTab.tsx"}, {"type": "hardcoded-color", "pattern": "bg-gray-\\d+", "matches": ["bg-gray-50", "bg-gray-500"], "file": "src\\components\\TagModal.tsx"}, {"type": "hardcoded-color", "pattern": "text-gray-\\d+", "matches": ["text-gray-900", "text-gray-500", "text-gray-600", "text-gray-700", "text-gray-400"], "file": "src\\components\\TagModal.tsx"}, {"type": "hardcoded-color", "pattern": "border-gray-\\d+", "matches": ["border-gray-200", "border-gray-300"], "file": "src\\components\\TagModal.tsx"}, {"type": "hardcoded-color", "pattern": "bg-red-\\d+", "matches": ["bg-red-100", "bg-red-600", "bg-red-700"], "file": "src\\components\\TagModal.tsx"}, {"type": "hardcoded-color", "pattern": "text-red-\\d+", "matches": ["text-red-600"], "file": "src\\components\\TagModal.tsx"}, {"type": "hardcoded-color", "pattern": "bg-white\\b", "matches": ["bg-white"], "file": "src\\components\\TagModal.tsx"}, {"type": "hardcoded-color", "pattern": "text-gray-\\d+", "matches": ["text-gray-600", "text-gray-500"], "file": "src\\components\\TagsTab.tsx"}, {"type": "hardcoded-color", "pattern": "border-gray-\\d+", "matches": ["border-gray-200"], "file": "src\\components\\TagsTab.tsx"}, {"type": "hardcoded-color", "pattern": "border-blue-\\d+", "matches": ["border-blue-600"], "file": "src\\components\\TagsTab.tsx"}, {"type": "hardcoded-color", "pattern": "bg-red-\\d+", "matches": ["bg-red-100"], "file": "src\\components\\TagsTab.tsx"}, {"type": "hardcoded-color", "pattern": "text-red-\\d+", "matches": ["text-red-600"], "file": "src\\components\\TagsTab.tsx"}, {"type": "hardcoded-color", "pattern": "text-gray-\\d+", "matches": ["text-gray-600", "text-gray-400"], "file": "src\\components\\test\\BookmarkEditModalTest.tsx"}, {"type": "hardcoded-color", "pattern": "text-blue-\\d+", "matches": ["text-blue-600"], "file": "src\\components\\test\\BookmarkEditModalTest.tsx"}, {"type": "hardcoded-color", "pattern": "bg-yellow-\\d+", "matches": ["bg-yellow-50"], "file": "src\\components\\test\\BookmarkEditModalTest.tsx"}, {"type": "hardcoded-color", "pattern": "text-yellow-\\d+", "matches": ["text-yellow-800", "text-yellow-700"], "file": "src\\components\\test\\BookmarkEditModalTest.tsx"}, {"type": "hardcoded-color", "pattern": "border-yellow-\\d+", "matches": ["border-yellow-200"], "file": "src\\components\\test\\BookmarkEditModalTest.tsx"}, {"type": "hardcoded-color", "pattern": "text-green-\\d+", "matches": ["text-green-600"], "file": "src\\components\\test\\BookmarksTabTestPage.tsx"}, {"type": "hardcoded-color", "pattern": "text-blue-\\d+", "matches": ["text-blue-500"], "file": "src\\components\\test\\DeleteConfirmModalTestPage.tsx"}, {"type": "hardcoded-color", "pattern": "text-red-\\d+", "matches": ["text-red-500"], "file": "src\\components\\test\\DeleteConfirmModalTestPage.tsx"}, {"type": "hardcoded-color", "pattern": "text-green-\\d+", "matches": ["text-green-500"], "file": "src\\components\\test\\DeleteConfirmModalTestPage.tsx"}, {"type": "hardcoded-color", "pattern": "text-gray-\\d+", "matches": ["text-gray-700"], "file": "src\\components\\test\\ShadcnModalTest.tsx"}, {"type": "hardcoded-color", "pattern": "bg-blue-\\d+", "matches": ["bg-blue-100"], "file": "src\\components\\test\\ShadcnModalTest.tsx"}, {"type": "hardcoded-color", "pattern": "border-blue-\\d+", "matches": ["border-blue-500"], "file": "src\\components\\test\\ShadcnModalTest.tsx"}, {"type": "hardcoded-color", "pattern": "bg-red-\\d+", "matches": ["bg-red-500", "bg-red-600"], "file": "src\\components\\test\\ShadcnModalTest.tsx"}, {"type": "hardcoded-color", "pattern": "text-red-\\d+", "matches": ["text-red-600", "text-red-500"], "file": "src\\components\\test\\ShadcnModalTest.tsx"}, {"type": "hardcoded-color", "pattern": "border-red-\\d+", "matches": ["border-red-500"], "file": "src\\components\\test\\ShadcnModalTest.tsx"}, {"type": "hardcoded-color", "pattern": "bg-green-\\d+", "matches": ["bg-green-500", "bg-green-600"], "file": "src\\components\\test\\ShadcnModalTest.tsx"}, {"type": "hardcoded-color", "pattern": "bg-yellow-\\d+", "matches": ["bg-yellow-100"], "file": "src\\components\\test\\ShadcnModalTest.tsx"}, {"type": "hardcoded-color", "pattern": "text-blue-\\d+", "matches": ["text-blue-500"], "file": "src\\components\\test\\VirtualBookmarkListTestPage.tsx"}, {"type": "hardcoded-color", "pattern": "bg-red-\\d+", "matches": ["bg-red-100"], "file": "src\\components\\test\\VirtualBookmarkListTestPage.tsx"}, {"type": "hardcoded-color", "pattern": "text-red-\\d+", "matches": ["text-red-500", "text-red-800"], "file": "src\\components\\test\\VirtualBookmarkListTestPage.tsx"}, {"type": "hardcoded-color", "pattern": "bg-green-\\d+", "matches": ["bg-green-100"], "file": "src\\components\\test\\VirtualBookmarkListTestPage.tsx"}, {"type": "hardcoded-color", "pattern": "text-green-\\d+", "matches": ["text-green-500", "text-green-800"], "file": "src\\components\\test\\VirtualBookmarkListTestPage.tsx"}, {"type": "hardcoded-color", "pattern": "bg-yellow-\\d+", "matches": ["bg-yellow-100"], "file": "src\\components\\test\\VirtualBookmarkListTestPage.tsx"}, {"type": "hardcoded-color", "pattern": "text-yellow-\\d+", "matches": ["text-yellow-500", "text-yellow-800"], "file": "src\\components\\test\\VirtualBookmarkListTestPage.tsx"}, {"type": "hardcoded-color", "pattern": "bg-gray-\\d+", "matches": ["bg-gray-50"], "file": "src\\components\\Toast.tsx"}, {"type": "hardcoded-color", "pattern": "text-gray-\\d+", "matches": ["text-gray-800", "text-gray-600", "text-gray-400"], "file": "src\\components\\Toast.tsx"}, {"type": "hardcoded-color", "pattern": "border-gray-\\d+", "matches": ["border-gray-200"], "file": "src\\components\\Toast.tsx"}, {"type": "hardcoded-color", "pattern": "bg-blue-\\d+", "matches": ["bg-blue-50"], "file": "src\\components\\Toast.tsx"}, {"type": "hardcoded-color", "pattern": "text-blue-\\d+", "matches": ["text-blue-800", "text-blue-600"], "file": "src\\components\\Toast.tsx"}, {"type": "hardcoded-color", "pattern": "border-blue-\\d+", "matches": ["border-blue-200"], "file": "src\\components\\Toast.tsx"}, {"type": "hardcoded-color", "pattern": "bg-red-\\d+", "matches": ["bg-red-50"], "file": "src\\components\\Toast.tsx"}, {"type": "hardcoded-color", "pattern": "text-red-\\d+", "matches": ["text-red-800", "text-red-600"], "file": "src\\components\\Toast.tsx"}, {"type": "hardcoded-color", "pattern": "border-red-\\d+", "matches": ["border-red-200"], "file": "src\\components\\Toast.tsx"}, {"type": "hardcoded-color", "pattern": "bg-green-\\d+", "matches": ["bg-green-50"], "file": "src\\components\\Toast.tsx"}, {"type": "hardcoded-color", "pattern": "text-green-\\d+", "matches": ["text-green-800", "text-green-600"], "file": "src\\components\\Toast.tsx"}, {"type": "hardcoded-color", "pattern": "border-green-\\d+", "matches": ["border-green-200"], "file": "src\\components\\Toast.tsx"}, {"type": "hardcoded-color", "pattern": "bg-yellow-\\d+", "matches": ["bg-yellow-50"], "file": "src\\components\\Toast.tsx"}, {"type": "hardcoded-color", "pattern": "text-yellow-\\d+", "matches": ["text-yellow-800", "text-yellow-600"], "file": "src\\components\\Toast.tsx"}, {"type": "hardcoded-color", "pattern": "border-yellow-\\d+", "matches": ["border-yellow-200"], "file": "src\\components\\Toast.tsx"}, {"type": "hardcoded-color", "pattern": "text-gray-\\d+", "matches": ["text-gray-700", "text-gray-400", "text-gray-500"], "file": "src\\components\\ViewModeSelector.tsx"}, {"type": "hardcoded-color", "pattern": "border-gray-\\d+", "matches": ["border-gray-300", "border-gray-400"], "file": "src\\components\\ViewModeSelector.tsx"}, {"type": "hardcoded-color", "pattern": "bg-white\\b", "matches": ["bg-white"], "file": "src\\components\\ViewModeSelector.tsx"}, {"type": "hardcoded-color", "pattern": "text-gray-\\d+", "matches": ["text-gray-600", "text-gray-500"], "file": "src\\components\\VirtualScrollList.tsx"}, {"type": "hardcoded-color", "pattern": "bg-gray-\\d+", "matches": ["bg-gray-50"], "file": "src\\examples\\CategoryCardDemo.tsx"}, {"type": "hardcoded-color", "pattern": "text-gray-\\d+", "matches": ["text-gray-900", "text-gray-600"], "file": "src\\examples\\CategoryCardDemo.tsx"}, {"type": "hardcoded-color", "pattern": "border-gray-\\d+", "matches": ["border-gray-200"], "file": "src\\examples\\CategoryCardDemo.tsx"}, {"type": "hardcoded-color", "pattern": "bg-white\\b", "matches": ["bg-white"], "file": "src\\examples\\CategoryCardDemo.tsx"}, {"type": "hardcoded-color", "pattern": "bg-gray-\\d+", "matches": ["bg-gray-50", "bg-gray-100"], "file": "src\\examples\\CategoryFormDemo.tsx"}, {"type": "hardcoded-color", "pattern": "text-gray-\\d+", "matches": ["text-gray-900", "text-gray-600", "text-gray-500", "text-gray-400"], "file": "src\\examples\\CategoryFormDemo.tsx"}, {"type": "hardcoded-color", "pattern": "border-gray-\\d+", "matches": ["border-gray-200"], "file": "src\\examples\\CategoryFormDemo.tsx"}, {"type": "hardcoded-color", "pattern": "bg-white\\b", "matches": ["bg-white"], "file": "src\\examples\\CategoryFormDemo.tsx"}, {"type": "hardcoded-color", "pattern": "bg-gray-\\d+", "matches": ["bg-gray-50"], "file": "src\\examples\\CategoryModalDemo.tsx"}, {"type": "hardcoded-color", "pattern": "text-gray-\\d+", "matches": ["text-gray-900", "text-gray-600", "text-gray-500"], "file": "src\\examples\\CategoryModalDemo.tsx"}, {"type": "hardcoded-color", "pattern": "border-gray-\\d+", "matches": ["border-gray-200"], "file": "src\\examples\\CategoryModalDemo.tsx"}, {"type": "hardcoded-color", "pattern": "bg-blue-\\d+", "matches": ["bg-blue-600", "bg-blue-700", "bg-blue-50"], "file": "src\\examples\\CategoryModalDemo.tsx"}, {"type": "hardcoded-color", "pattern": "text-blue-\\d+", "matches": ["text-blue-800"], "file": "src\\examples\\CategoryModalDemo.tsx"}, {"type": "hardcoded-color", "pattern": "border-blue-\\d+", "matches": ["border-blue-200"], "file": "src\\examples\\CategoryModalDemo.tsx"}, {"type": "hardcoded-color", "pattern": "bg-red-\\d+", "matches": ["bg-red-600", "bg-red-700", "bg-red-50"], "file": "src\\examples\\CategoryModalDemo.tsx"}, {"type": "hardcoded-color", "pattern": "text-red-\\d+", "matches": ["text-red-800"], "file": "src\\examples\\CategoryModalDemo.tsx"}, {"type": "hardcoded-color", "pattern": "border-red-\\d+", "matches": ["border-red-200"], "file": "src\\examples\\CategoryModalDemo.tsx"}, {"type": "hardcoded-color", "pattern": "bg-green-\\d+", "matches": ["bg-green-600", "bg-green-700", "bg-green-50"], "file": "src\\examples\\CategoryModalDemo.tsx"}, {"type": "hardcoded-color", "pattern": "text-green-\\d+", "matches": ["text-green-800"], "file": "src\\examples\\CategoryModalDemo.tsx"}, {"type": "hardcoded-color", "pattern": "border-green-\\d+", "matches": ["border-green-200"], "file": "src\\examples\\CategoryModalDemo.tsx"}, {"type": "hardcoded-color", "pattern": "bg-white\\b", "matches": ["bg-white"], "file": "src\\examples\\CategoryModalDemo.tsx"}, {"type": "hardcoded-color", "pattern": "bg-gray-\\d+", "matches": ["bg-gray-50"], "file": "src\\examples\\TagCardDemo.tsx"}, {"type": "hardcoded-color", "pattern": "text-gray-\\d+", "matches": ["text-gray-900", "text-gray-600"], "file": "src\\examples\\TagCardDemo.tsx"}, {"type": "hardcoded-color", "pattern": "border-gray-\\d+", "matches": ["border-gray-200"], "file": "src\\examples\\TagCardDemo.tsx"}, {"type": "hardcoded-color", "pattern": "bg-blue-\\d+", "matches": ["bg-blue-50"], "file": "src\\examples\\TagCardDemo.tsx"}, {"type": "hardcoded-color", "pattern": "text-blue-\\d+", "matches": ["text-blue-800"], "file": "src\\examples\\TagCardDemo.tsx"}, {"type": "hardcoded-color", "pattern": "border-blue-\\d+", "matches": ["border-blue-200"], "file": "src\\examples\\TagCardDemo.tsx"}, {"type": "hardcoded-color", "pattern": "bg-white\\b", "matches": ["bg-white"], "file": "src\\examples\\TagCardDemo.tsx"}, {"type": "hardcoded-color", "pattern": "bg-gray-\\d+", "matches": ["bg-gray-50"], "file": "src\\examples\\TagColorPickerDemo.tsx"}, {"type": "hardcoded-color", "pattern": "text-gray-\\d+", "matches": ["text-gray-500"], "file": "src\\examples\\TagColorPickerDemo.tsx"}, {"type": "hardcoded-color", "pattern": "text-red-\\d+", "matches": ["text-red-600"], "file": "src\\examples\\TagColorPickerDemo.tsx"}, {"type": "hardcoded-color", "pattern": "text-green-\\d+", "matches": ["text-green-600"], "file": "src\\examples\\TagColorPickerDemo.tsx"}, {"type": "hardcoded-color", "pattern": "bg-white\\b", "matches": ["bg-white"], "file": "src\\examples\\TagColorPickerDemo.tsx"}, {"type": "hardcoded-color", "pattern": "text-gray-\\d+", "matches": ["text-gray-500", "text-gray-600"], "file": "src\\examples\\TagFormDemo.tsx"}, {"type": "hardcoded-color", "pattern": "border-gray-\\d+", "matches": ["border-gray-200"], "file": "src\\examples\\TagFormDemo.tsx"}, {"type": "hardcoded-color", "pattern": "bg-blue-\\d+", "matches": ["bg-blue-600", "bg-blue-700"], "file": "src\\examples\\TagFormDemo.tsx"}, {"type": "hardcoded-color", "pattern": "text-blue-\\d+", "matches": ["text-blue-500"], "file": "src\\examples\\TagFormDemo.tsx"}, {"type": "hardcoded-color", "pattern": "text-red-\\d+", "matches": ["text-red-500", "text-red-600"], "file": "src\\examples\\TagFormDemo.tsx"}, {"type": "hardcoded-color", "pattern": "bg-green-\\d+", "matches": ["bg-green-600", "bg-green-700", "bg-green-50"], "file": "src\\examples\\TagFormDemo.tsx"}, {"type": "hardcoded-color", "pattern": "text-green-\\d+", "matches": ["text-green-400", "text-green-700", "text-green-500", "text-green-600"], "file": "src\\examples\\TagFormDemo.tsx"}, {"type": "hardcoded-color", "pattern": "border-green-\\d+", "matches": ["border-green-200"], "file": "src\\examples\\TagFormDemo.tsx"}, {"type": "hardcoded-color", "pattern": "bg-white\\b", "matches": ["bg-white"], "file": "src\\examples\\TagFormDemo.tsx"}, {"type": "hardcoded-color", "pattern": "bg-gray-\\d+", "matches": ["bg-gray-50", "bg-gray-100"], "file": "src\\examples\\TagListDemo.tsx"}, {"type": "hardcoded-color", "pattern": "text-gray-\\d+", "matches": ["text-gray-900", "text-gray-600"], "file": "src\\examples\\TagListDemo.tsx"}, {"type": "hardcoded-color", "pattern": "bg-blue-\\d+", "matches": ["bg-blue-600", "bg-blue-700"], "file": "src\\examples\\TagListDemo.tsx"}, {"type": "hardcoded-color", "pattern": "bg-red-\\d+", "matches": ["bg-red-600", "bg-red-700"], "file": "src\\examples\\TagListDemo.tsx"}, {"type": "hardcoded-color", "pattern": "bg-green-\\d+", "matches": ["bg-green-600", "bg-green-700"], "file": "src\\examples\\TagListDemo.tsx"}, {"type": "hardcoded-color", "pattern": "bg-white\\b", "matches": ["bg-white"], "file": "src\\examples\\TagListDemo.tsx"}, {"type": "hardcoded-color", "pattern": "bg-gray-\\d+", "matches": ["bg-gray-50"], "file": "src\\examples\\TagManagementTabDemo.tsx"}, {"type": "hardcoded-color", "pattern": "bg-white\\b", "matches": ["bg-white"], "file": "src\\examples\\TagManagementTabDemo.tsx"}, {"type": "hardcoded-color", "pattern": "bg-gray-\\d+", "matches": ["bg-gray-50", "bg-gray-100"], "file": "src\\examples\\TagModalDemo.tsx"}, {"type": "hardcoded-color", "pattern": "text-gray-\\d+", "matches": ["text-gray-900", "text-gray-600", "text-gray-700"], "file": "src\\examples\\TagModalDemo.tsx"}, {"type": "hardcoded-color", "pattern": "bg-blue-\\d+", "matches": ["bg-blue-600", "bg-blue-700"], "file": "src\\examples\\TagModalDemo.tsx"}, {"type": "hardcoded-color", "pattern": "bg-red-\\d+", "matches": ["bg-red-600", "bg-red-700"], "file": "src\\examples\\TagModalDemo.tsx"}, {"type": "hardcoded-color", "pattern": "bg-green-\\d+", "matches": ["bg-green-600", "bg-green-700"], "file": "src\\examples\\TagModalDemo.tsx"}, {"type": "hardcoded-color", "pattern": "bg-white\\b", "matches": ["bg-white"], "file": "src\\examples\\TagModalDemo.tsx"}, {"type": "hardcoded-color", "pattern": "bg-gray-\\d+", "matches": ["bg-gray-50", "bg-gray-200"], "file": "src\\examples\\ViewModeSelectorExample.tsx"}, {"type": "hardcoded-color", "pattern": "text-gray-\\d+", "matches": ["text-gray-500", "text-gray-900", "text-gray-700", "text-gray-600", "text-gray-800", "text-gray-400"], "file": "src\\examples\\ViewModeSelectorExample.tsx"}, {"type": "hardcoded-color", "pattern": "bg-blue-\\d+", "matches": ["bg-blue-500", "bg-blue-50"], "file": "src\\examples\\ViewModeSelectorExample.tsx"}, {"type": "hardcoded-color", "pattern": "text-blue-\\d+", "matches": ["text-blue-900", "text-blue-800"], "file": "src\\examples\\ViewModeSelectorExample.tsx"}, {"type": "hardcoded-color", "pattern": "bg-white\\b", "matches": ["bg-white"], "file": "src\\examples\\ViewModeSelectorExample.tsx"}, {"type": "hardcoded-color", "pattern": "text-gray-\\d+", "matches": ["text-gray-500", "text-gray-400"], "file": "src\\options\\components\\AboutTab.tsx"}, {"type": "hardcoded-color", "pattern": "bg-gray-\\d+", "matches": ["bg-gray-100", "bg-gray-50"], "file": "src\\options\\components\\HelpCenterTab.tsx"}, {"type": "hardcoded-color", "pattern": "text-gray-\\d+", "matches": ["text-gray-600", "text-gray-900", "text-gray-800", "text-gray-500", "text-gray-300", "text-gray-400"], "file": "src\\options\\components\\HelpCenterTab.tsx"}, {"type": "hardcoded-color", "pattern": "border-gray-\\d+", "matches": ["border-gray-200"], "file": "src\\options\\components\\HelpCenterTab.tsx"}, {"type": "hardcoded-color", "pattern": "bg-yellow-\\d+", "matches": ["bg-yellow-200"], "file": "src\\options\\components\\HelpCenterTab.tsx"}, {"type": "hardcoded-color", "pattern": "bg-gray-\\d+", "matches": ["bg-gray-50"], "file": "src\\options\\components\\HelpSearchBox.tsx"}, {"type": "hardcoded-color", "pattern": "text-gray-\\d+", "matches": ["text-gray-400", "text-gray-600", "text-gray-500"], "file": "src\\options\\components\\HelpSearchBox.tsx"}, {"type": "hardcoded-color", "pattern": "border-gray-\\d+", "matches": ["border-gray-300", "border-gray-200", "border-gray-100"], "file": "src\\options\\components\\HelpSearchBox.tsx"}, {"type": "hardcoded-color", "pattern": "bg-white\\b", "matches": ["bg-white"], "file": "src\\options\\components\\HelpSearchBox.tsx"}, {"type": "hardcoded-color", "pattern": "bg-gray-\\d+", "matches": ["bg-gray-50", "bg-gray-600", "bg-gray-700", "bg-gray-100"], "file": "src\\options\\components\\PageErrorBoundary.tsx"}, {"type": "hardcoded-color", "pattern": "text-gray-\\d+", "matches": ["text-gray-900", "text-gray-600", "text-gray-700", "text-gray-500", "text-gray-800"], "file": "src\\options\\components\\PageErrorBoundary.tsx"}, {"type": "hardcoded-color", "pattern": "border-gray-\\d+", "matches": ["border-gray-200", "border-gray-300"], "file": "src\\options\\components\\PageErrorBoundary.tsx"}, {"type": "hardcoded-color", "pattern": "bg-red-\\d+", "matches": ["bg-red-100"], "file": "src\\options\\components\\PageErrorBoundary.tsx"}, {"type": "hardcoded-color", "pattern": "text-red-\\d+", "matches": ["text-red-600"], "file": "src\\options\\components\\PageErrorBoundary.tsx"}, {"type": "hardcoded-color", "pattern": "bg-white\\b", "matches": ["bg-white"], "file": "src\\options\\components\\PageErrorBoundary.tsx"}, {"type": "hardcoded-color", "pattern": "text-yellow-\\d+", "matches": ["text-yellow-400"], "file": "src\\options\\components\\ThemeToggle.tsx"}, {"type": "hardcoded-color", "pattern": "bg-yellow-\\d+", "matches": ["bg-yellow-200"], "file": "src\\options\\utils\\helpSearch.ts"}, {"type": "hardcoded-color", "pattern": "bg-green-\\d+", "matches": ["bg-green-50"], "file": "src\\popup\\PopupApp.tsx"}, {"type": "hardcoded-color", "pattern": "text-green-\\d+", "matches": ["text-green-500", "text-green-600", "text-green-800"], "file": "src\\popup\\PopupApp.tsx"}, {"type": "hardcoded-color", "pattern": "border-green-\\d+", "matches": ["border-green-200"], "file": "src\\popup\\PopupApp.tsx"}, {"type": "hardcoded-color", "pattern": "bg-red-\\d+", "matches": ["bg-red-100"], "file": "src\\styles\\globals.css"}, {"type": "hardcoded-color", "pattern": "text-red-\\d+", "matches": ["text-red-800"], "file": "src\\styles\\globals.css"}, {"type": "hardcoded-color", "pattern": "bg-green-\\d+", "matches": ["bg-green-100"], "file": "src\\styles\\globals.css"}, {"type": "hardcoded-color", "pattern": "text-green-\\d+", "matches": ["text-green-800"], "file": "src\\styles\\globals.css"}, {"type": "hardcoded-color", "pattern": "border-green-\\d+", "matches": ["border-green-500"], "file": "src\\styles\\globals.css"}, {"type": "hardcoded-color", "pattern": "bg-yellow-\\d+", "matches": ["bg-yellow-100"], "file": "src\\styles\\globals.css"}, {"type": "hardcoded-color", "pattern": "text-yellow-\\d+", "matches": ["text-yellow-800"], "file": "src\\styles\\globals.css"}, {"type": "hardcoded-color", "pattern": "bg-red-\\d+", "matches": ["bg-red-50", "bg-red-600", "bg-red-700"], "file": "src\\utils\\errorHandler.tsx"}, {"type": "hardcoded-color", "pattern": "text-red-\\d+", "matches": ["text-red-800", "text-red-600"], "file": "src\\utils\\errorHandler.tsx"}, {"type": "hardcoded-color", "pattern": "border-red-\\d+", "matches": ["border-red-200"], "file": "src\\utils\\errorHandler.tsx"}, {"type": "hardcoded-color", "pattern": "bg-gray-\\d+", "matches": ["bg-gray-100"], "file": "tests\\BookmarkCompact.shadcn.test.tsx"}, {"type": "hardcoded-color", "pattern": "text-gray-\\d+", "matches": ["text-gray-900", "text-gray-500"], "file": "tests\\BookmarkCompact.shadcn.test.tsx"}, {"type": "hardcoded-color", "pattern": "border-gray-\\d+", "matches": ["border-gray-200"], "file": "tests\\BookmarkCompact.shadcn.test.tsx"}, {"type": "hardcoded-color", "pattern": "bg-white\\b", "matches": ["bg-white"], "file": "tests\\BookmarkCompact.shadcn.test.tsx"}, {"type": "hardcoded-color", "pattern": "bg-gray-\\d+", "matches": ["bg-gray-400"], "file": "tests\\BookmarkCompact.test.tsx"}, {"type": "hardcoded-color", "pattern": "bg-blue-\\d+", "matches": ["bg-blue-400"], "file": "tests\\BookmarkCompact.test.tsx"}, {"type": "hardcoded-color", "pattern": "bg-green-\\d+", "matches": ["bg-green-400"], "file": "tests\\BookmarkCompact.test.tsx"}, {"type": "hardcoded-color", "pattern": "text-green-\\d+", "matches": ["text-green-600"], "file": "tests\\BookmarksTabTestPage.test.tsx"}, {"type": "hardcoded-color", "pattern": "bg-white\\b", "matches": ["bg-white"], "file": "tests\\integration\\tag-management-e2e.test.tsx"}, {"type": "hardcoded-color", "pattern": "bg-gray-\\d+", "matches": ["bg-gray-200"], "file": "tests\\options\\components\\ThemeToggle.test.tsx"}, {"type": "hardcoded-color", "pattern": "text-yellow-\\d+", "matches": ["text-yellow-400"], "file": "tests\\options\\components\\ThemeToggle.test.tsx"}, {"type": "hardcoded-color", "pattern": "text-gray-\\d+", "matches": ["text-gray-700"], "file": "tests\\options\\OptionsApp.test.tsx"}, {"type": "hardcoded-color", "pattern": "bg-yellow-\\d+", "matches": ["bg-yellow-200"], "file": "tests\\options\\utils\\helpSearch.test.ts"}, {"type": "hardcoded-color", "pattern": "bg-gray-\\d+", "matches": ["bg-gray-50"], "file": "tests\\OptionsApp.shadcn.test.tsx"}, {"type": "hardcoded-color", "pattern": "border-gray-\\d+", "matches": ["border-gray-200"], "file": "tests\\OptionsApp.shadcn.test.tsx"}, {"type": "hardcoded-color", "pattern": "bg-green-\\d+", "matches": ["bg-green-50"], "file": "tests\\PopupApp.shadcn.test.tsx"}, {"type": "hardcoded-color", "pattern": "text-blue-\\d+", "matches": ["text-blue-500"], "file": "tests\\shadcn-setup.test.tsx"}, {"type": "hardcoded-color", "pattern": "text-red-\\d+", "matches": ["text-red-500"], "file": "tests\\shadcn-setup.test.tsx"}, {"type": "hardcoded-color", "pattern": "border-gray-\\d+", "matches": ["border-gray-300"], "file": "tests\\TagCard.test.tsx"}, {"type": "hardcoded-color", "pattern": "bg-white\\b", "matches": ["bg-white"], "file": "tests\\TagCard.test.tsx"}, {"type": "hardcoded-color", "pattern": "border-gray-\\d+", "matches": ["border-gray-200"], "file": "tests\\TagsTab.shadcn.test.tsx"}, {"type": "hardcoded-color", "pattern": "bg-gray-\\d+", "matches": ["bg-gray-100", "bg-gray-200"], "file": "tests\\ThemeToggle.shadcn.test.tsx"}, {"type": "hardcoded-color", "pattern": "text-gray-\\d+", "matches": ["text-gray-700"], "file": "tests\\ThemeToggle.shadcn.test.tsx"}, {"type": "hardcoded-color", "pattern": "border-gray-\\d+", "matches": ["border-gray-300"], "file": "tests\\ThemeToggle.shadcn.test.tsx"}, {"type": "hardcoded-color", "pattern": "text-yellow-\\d+", "matches": ["text-yellow-400"], "file": "tests\\ThemeToggle.shadcn.test.tsx"}, {"type": "hardcoded-color", "pattern": "text-gray-\\d+", "matches": ["text-gray-700", "text-gray-400"], "file": "tests\\view-mode-selector-alignment.test.tsx"}, {"type": "hardcoded-color", "pattern": "border-gray-\\d+", "matches": ["border-gray-300"], "file": "tests\\view-mode-selector-alignment.test.tsx"}, {"type": "hardcoded-color", "pattern": "bg-white\\b", "matches": ["bg-white"], "file": "tests\\view-mode-selector-alignment.test.tsx"}, {"type": "hardcoded-color", "pattern": "bg-gray-\\d+", "matches": ["bg-gray-50"], "file": "tests\\ViewModeSelector.test.tsx"}, {"type": "hardcoded-color", "pattern": "text-gray-\\d+", "matches": ["text-gray-600", "text-gray-900"], "file": "tests\\ViewModeSelector.test.tsx"}, {"type": "hardcoded-color", "pattern": "bg-white\\b", "matches": ["bg-white"], "file": "tests\\ViewModeSelector.test.tsx"}], "shadcnUsage": [{"file": "src\\components\\BookmarkCompact.tsx", "variables": ["foreground", "primary", "muted", "muted-foreground", "accent", "destructive"]}, {"file": "src\\components\\BookmarkRow.tsx", "variables": ["foreground", "primary", "muted", "muted-foreground", "accent", "destructive"]}, {"file": "src\\components\\BookmarksTab.tsx", "variables": ["primary", "muted", "muted-foreground"]}, {"file": "src\\components\\CategoryCard.tsx", "variables": ["primary"]}, {"file": "src\\components\\CategoryForm.tsx", "variables": ["foreground", "primary"]}, {"file": "src\\components\\CategoryList.tsx", "variables": ["primary"]}, {"file": "src\\components\\ConflictResolutionDialog.tsx", "variables": ["background", "primary"]}, {"file": "src\\components\\DeleteConfirmModal.tsx", "variables": ["background", "foreground", "primary", "secondary", "secondary-foreground", "muted", "muted-foreground", "destructive", "destructive-foreground"]}, {"file": "src\\components\\examples\\AddBookmarkModalDemo.tsx", "variables": ["primary", "secondary", "muted", "muted-foreground"]}, {"file": "src\\components\\examples\\AdvancedComponentsDemo.tsx", "variables": ["muted", "muted-foreground", "destructive"]}, {"file": "src\\components\\examples\\BookmarkCompactDemo.tsx", "variables": ["muted", "muted-foreground", "border"]}, {"file": "src\\components\\examples\\BookmarkRowDemo.tsx", "variables": ["foreground", "primary", "muted", "muted-foreground", "accent"]}, {"file": "src\\components\\examples\\DeleteConfirmModalDemo.tsx", "variables": ["card", "card-foreground", "primary", "secondary", "secondary-foreground", "muted", "muted-foreground"]}, {"file": "src\\components\\examples\\PopupAppDemo.tsx", "variables": ["foreground", "muted", "muted-foreground"]}, {"file": "src\\components\\examples\\VirtualBookmarkListDemo.tsx", "variables": ["foreground", "card", "primary", "muted", "muted-foreground", "border"]}, {"file": "src\\components\\HelpTooltip.tsx", "variables": ["background", "popover", "popover-foreground"]}, {"file": "src\\components\\ImportExportTab.tsx", "variables": ["primary"]}, {"file": "src\\components\\LazyLoadWrapper.tsx", "variables": ["primary"]}, {"file": "src\\components\\LoadingIndicator.tsx", "variables": ["background", "primary"]}, {"file": "src\\components\\TagCard.tsx", "variables": ["primary"]}, {"file": "src\\components\\TagList.tsx", "variables": ["primary"]}, {"file": "src\\components\\TagManagementTab.tsx", "variables": ["primary"]}, {"file": "src\\components\\TagsTab.tsx", "variables": ["primary"]}, {"file": "src\\components\\test\\BookmarkCompactTest.tsx", "variables": ["background"]}, {"file": "src\\components\\test\\BookmarksTabTestPage.tsx", "variables": ["primary", "secondary", "muted", "muted-foreground"]}, {"file": "src\\components\\test\\DeleteConfirmModalTest.tsx", "variables": ["primary", "muted", "muted-foreground"]}, {"file": "src\\components\\test\\DeleteConfirmModalTestPage.tsx", "variables": ["primary", "muted", "muted-foreground", "destructive", "border"]}, {"file": "src\\components\\test\\PopupAppTest.tsx", "variables": ["background", "muted", "muted-foreground"]}, {"file": "src\\components\\test\\ShadcnModalTest.tsx", "variables": ["muted", "muted-foreground"]}, {"file": "src\\components\\test\\ShadcnTest.tsx", "variables": ["card", "card-foreground"]}, {"file": "src\\components\\test\\TestErrorBoundary.tsx", "variables": ["foreground", "muted", "muted-foreground", "destructive"]}, {"file": "src\\components\\test\\VirtualBookmarkListTest.tsx", "variables": ["foreground", "primary", "muted", "muted-foreground", "destructive", "border"]}, {"file": "src\\components\\test\\VirtualBookmarkListTestPage.tsx", "variables": ["background", "foreground", "card", "primary", "muted", "muted-foreground", "border"]}, {"file": "src\\components\\TruncatedTitle.tsx", "variables": ["popover", "popover-foreground"]}, {"file": "src\\components\\ui\\alert-dialog.tsx", "variables": ["background", "muted", "muted-foreground"]}, {"file": "src\\components\\ui\\alert.tsx", "variables": ["background", "foreground", "destructive"]}, {"file": "src\\components\\ui\\badge.tsx", "variables": ["foreground", "primary", "primary-foreground", "secondary", "secondary-foreground", "destructive", "destructive-foreground"]}, {"file": "src\\components\\ui\\button.tsx", "variables": ["background", "primary", "primary-foreground", "secondary", "secondary-foreground", "accent", "accent-foreground", "destructive", "destructive-foreground", "input"]}, {"file": "src\\components\\ui\\card.tsx", "variables": ["card", "card-foreground", "muted", "muted-foreground"]}, {"file": "src\\components\\ui\\checkbox.tsx", "variables": ["primary", "primary-foreground"]}, {"file": "src\\components\\ui\\dialog.tsx", "variables": ["background", "muted", "muted-foreground", "accent"]}, {"file": "src\\components\\ui\\dropdown-menu.tsx", "variables": ["popover", "popover-foreground", "muted", "accent", "accent-foreground"]}, {"file": "src\\components\\ui\\form.tsx", "variables": ["muted", "muted-foreground", "destructive"]}, {"file": "src\\components\\ui\\input.tsx", "variables": ["background", "foreground", "muted", "muted-foreground", "input"]}, {"file": "src\\components\\ui\\progress.tsx", "variables": ["primary", "secondary"]}, {"file": "src\\components\\ui\\select.tsx", "variables": ["background", "popover", "popover-foreground", "muted", "muted-foreground", "accent", "accent-foreground", "input"]}, {"file": "src\\components\\ui\\separator.tsx", "variables": ["border"]}, {"file": "src\\components\\ui\\switch.tsx", "variables": ["background", "primary", "input"]}, {"file": "src\\components\\ui\\textarea.tsx", "variables": ["background", "muted", "muted-foreground", "input"]}, {"file": "src\\components\\ui\\tooltip.tsx", "variables": ["popover", "popover-foreground"]}, {"file": "src\\components\\ViewModeSelector.tsx", "variables": ["primary"]}, {"file": "src\\components\\VirtualBookmarkList.tsx", "variables": ["foreground", "primary", "muted", "muted-foreground", "destructive", "border"]}, {"file": "src\\components\\VirtualScrollList.tsx", "variables": ["primary"]}, {"file": "src\\examples\\CategoryFormDemo.tsx", "variables": ["primary"]}, {"file": "src\\lib\\shadcn-config.ts", "variables": ["background", "foreground", "card", "card-foreground", "primary", "secondary", "muted", "muted-foreground", "accent", "destructive", "border", "input", "ring"]}, {"file": "src\\options\\components\\AboutTab.tsx", "variables": ["primary"]}, {"file": "src\\options\\components\\HelpCenterTab.tsx", "variables": ["primary"]}, {"file": "src\\options\\components\\HelpSearchBox.tsx", "variables": ["primary"]}, {"file": "src\\options\\components\\PageErrorBoundary.tsx", "variables": ["primary"]}, {"file": "src\\options\\components\\ThemeToggle.tsx", "variables": ["background", "foreground", "secondary", "secondary-foreground", "muted", "muted-foreground", "accent", "accent-foreground", "border", "ring"]}, {"file": "src\\options\\OptionsApp.tsx", "variables": ["background", "foreground", "primary", "primary-foreground", "muted", "muted-foreground", "destructive", "border"]}, {"file": "src\\popup\\components\\DetailedBookmarkForm.tsx", "variables": ["background", "primary", "primary-foreground", "muted", "muted-foreground", "accent"]}, {"file": "src\\popup\\components\\Toggle.tsx", "variables": ["background", "primary", "muted"]}, {"file": "src\\popup\\PopupApp.tsx", "variables": ["foreground", "primary", "primary-foreground", "muted", "muted-foreground", "destructive"]}, {"file": "src\\styles\\globals.css", "variables": ["background", "foreground", "card", "primary", "secondary", "secondary-foreground", "muted", "muted-foreground", "accent", "destructive", "border", "input", "ring"]}, {"file": "src\\styles\\responsive.css", "variables": ["background", "foreground", "card", "primary", "secondary", "secondary-foreground", "muted", "muted-foreground", "accent", "border", "input"]}, {"file": "tests\\AddBookmarkModal.shadcn.test.tsx", "variables": ["background", "primary", "primary-foreground", "input"]}, {"file": "tests\\BookmarkCompact.integration.test.tsx", "variables": ["primary", "muted", "accent"]}, {"file": "tests\\BookmarkCompact.shadcn.test.tsx", "variables": ["foreground", "card", "primary", "muted", "muted-foreground", "accent"]}, {"file": "tests\\BookmarkCompact.test.tsx", "variables": ["primary"]}, {"file": "tests\\BookmarkRow.shadcn.test.tsx", "variables": ["foreground", "primary", "secondary", "secondary-foreground", "muted", "muted-foreground", "accent", "destructive"]}, {"file": "tests\\BookmarkRow.test.tsx", "variables": ["primary", "accent"]}, {"file": "tests\\BookmarksTab.shadcn.refactor.test.tsx", "variables": ["muted", "muted-foreground"]}, {"file": "tests\\BookmarksTab.shadcn.test.tsx", "variables": ["primary"]}, {"file": "tests\\DeleteConfirmModal.shadcn.test.tsx", "variables": ["secondary", "secondary-foreground", "muted", "muted-foreground", "destructive", "destructive-foreground"]}, {"file": "tests\\DetailedBookmarkForm.shadcn.test.tsx", "variables": ["background"]}, {"file": "tests\\e2e\\about-help-pages.spec.ts", "variables": ["primary"]}, {"file": "tests\\ImportExportTab.test.tsx", "variables": ["primary"]}, {"file": "tests\\integration\\tag-management-e2e.test.tsx", "variables": ["primary"]}, {"file": "tests\\options\\OptionsApp.test.tsx", "variables": ["primary"]}, {"file": "tests\\OptionsApp.loading.test.tsx", "variables": ["primary"]}, {"file": "tests\\OptionsApp.shadcn.test.tsx", "variables": ["background", "foreground", "card", "card-foreground", "primary", "muted", "muted-foreground", "destructive", "border"]}, {"file": "tests\\optionsPageIntegration.test.tsx", "variables": ["primary"]}, {"file": "tests\\PopupApp.shadcn.test.tsx", "variables": ["foreground", "muted", "muted-foreground"]}, {"file": "tests\\shadcn-setup.test.tsx", "variables": ["primary", "secondary"]}, {"file": "tests\\ThemeToggle.shadcn.test.tsx", "variables": ["background", "foreground", "secondary", "secondary-foreground", "accent", "accent-foreground", "border"]}, {"file": "tests\\view-mode-selector-alignment.test.tsx", "variables": ["primary"]}, {"file": "tests\\ViewModeSelector.test.tsx", "variables": ["primary"]}, {"file": "tests\\VirtualBookmarkList.shadcn.test.tsx", "variables": ["foreground", "primary", "muted", "muted-foreground", "border"]}], "recommendations": ["将硬编码颜色替换为shadcn CSS变量", "增加shadcn组件的使用率"]}