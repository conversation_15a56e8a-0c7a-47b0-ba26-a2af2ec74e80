# AI集成服务测试脚本函数签名

## 概述

本文档提供 `scripts/test-ai-integration.js` 中所有函数的详细签名和类型定义。

## 导出函数

### runAllTests()
```typescript
async function runAllTests(): Promise<void>
```
**描述**: 执行完整的AI集成服务测试流程  
**返回值**: Promise<void>  
**异常**: 测试失败时输出错误信息但不抛出异常

## 测试函数

### testAIIntegrationBasics()
```typescript
async function testAIIntegrationBasics(): Promise<boolean>
```
**描述**: 测试AI集成服务的基本功能  
**返回值**: Promise<boolean> - 测试是否通过  
**测试内容**:
- 获取支持的提供商列表
- 获取已配置的提供商列表  
- 添加新提供商配置
- 测试提供商连接
- 获取模型列表

### testLocalAIServiceDiscovery()
```typescript
async function testLocalAIServiceDiscovery(): Promise<boolean>
```
**描述**: 测试本地AI服务发现功能  
**返回值**: Promise<boolean> - 测试是否通过  
**测试内容**:
- 发现本地AI服务
- 测试本地服务连接
- 获取本地服务模型列表

### testAIChatFunctionality()
```typescript
async function testAIChatFunctionality(): Promise<boolean>
```
**描述**: 测试AI对话功能  
**返回值**: Promise<boolean> - 测试是否通过  
**测试内容**:
- 本地服务对话测试（模拟）
- 云端服务对话测试（模拟）
- 模型连接测试

### testConfigurationManagement()
```typescript
async function testConfigurationManagement(): Promise<boolean>
```
**描述**: 测试配置管理功能  
**返回值**: Promise<boolean> - 测试是否通过  
**测试内容**:
- 导出配置功能
- 获取统计信息
- 配置验证功能

## 类型定义

### MockProviderConfig
```typescript
interface MockProviderConfig {
  id: string
  type: string
  name: string
  baseUrl: string
  apiKey?: string
  enabled: boolean
  createdAt: string
  updatedAt: string
}
```

### ChromeStorageMock
```typescript
interface ChromeStorageMock {
  storage: {
    sync: {
      get: (keys: string | string[] | object, callback: (result: any) => void) => void
      set: (data: object, callback?: () => void) => void
    }
  }
}
```

### TestResult
```typescript
interface TestResult {
  testName: string
  passed: boolean
  error?: string
  details?: any
}
```

## 模拟数据结构

### 默认模拟提供商配置
```typescript
const mockProviders: MockProviderConfig[] = [
  {
    id: 'openai_demo',
    type: 'openai',
    name: '演示 OpenAI',
    baseUrl: 'https://api.openai.com/v1',
    apiKey: 'sk-demo-key',
    enabled: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: 'ollama_demo',
    type: 'ollama', 
    name: '演示 Ollama',
    baseUrl: 'http://localhost:11434',
    enabled: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  }
]
```

### Chrome存储模拟数据
```typescript
const mockStorageData = {
  ai_providers: MockProviderConfig[]
}
```

## 依赖服务接口

### AIIntegrationService
```typescript
interface AIIntegrationService {
  getSupportedProviders(): AIProviderInfo[]
  getConfiguredProviders(): Promise<AIProviderConfig[]>
  configureProvider(config: Omit<AIProviderConfig, 'id' | 'createdAt' | 'updatedAt'>): Promise<void>
  testConnection(providerId: string): Promise<AIConnectionResult>
  getAvailableModels(providerId: string): Promise<AIModel[]>
  exportConfiguration(): Promise<string>
  getProviderStats(): Promise<{total: number, enabled: number, connected: number}>
}
```

### LocalAIServiceAdapter
```typescript
interface LocalAIServiceAdapter {
  discoverLocalServices(customPorts?: number[]): Promise<{
    services: LocalServiceConfig[]
    errors: string[]
  }>
  testLocalServiceConnection(config: LocalServiceConfig): Promise<AIConnectionResult>
  getLocalServiceModels(config: LocalServiceConfig): Promise<AIModel[]>
}
```

### AIChatService
```typescript
interface AIChatService {
  chatWithLocalService(
    serviceUrl: string,
    modelName: string,
    messages: ChatMessage[],
    settings?: ChatSettings
  ): Promise<ChatResponse>
  
  chatWithCloudService(
    providerConfig: AIProviderConfig,
    modelName: string,
    messages: ChatMessage[],
    settings?: ChatSettings
  ): Promise<ChatResponse>
}
```

## 内部辅助函数

### Chrome API模拟设置
```typescript
function setupChromeMock(): void
```
**描述**: 设置Chrome扩展API的模拟环境  
**功能**:
- 模拟chrome.storage.sync API
- 提供测试数据
- 处理存储操作

### 模块动态导入
```typescript
function loadAIServices(): {
  aiIntegrationService: AIIntegrationService
  aiChatService: AIChatService
  localAIServiceAdapter: LocalAIServiceAdapter
}
```
**描述**: 动态导入AI服务模块  
**返回值**: 包含所有AI服务实例的对象  
**异常**: 模块加载失败时抛出错误

## 测试执行流程

### 主测试流程
```typescript
async function runAllTests(): Promise<void> {
  // 1. 初始化测试环境
  setupChromeMock()
  
  // 2. 执行各项测试
  const results: boolean[] = []
  results.push(await testAIIntegrationBasics())
  results.push(await testLocalAIServiceDiscovery())
  results.push(await testAIChatFunctionality())
  results.push(await testConfigurationManagement())
  
  // 3. 生成测试报告
  generateTestReport(results)
}
```

### 测试报告生成
```typescript
function generateTestReport(results: boolean[]): void
```
**描述**: 生成并输出测试结果汇总报告  
**参数**:
- `results`: 各项测试的结果数组
**功能**:
- 统计通过/失败的测试数量
- 输出详细的测试结果
- 提供测试建议和说明

## 错误处理模式

### 测试函数错误处理
```typescript
async function testFunction(): Promise<boolean> {
  try {
    // 测试逻辑
    console.log('✅ 测试通过')
    return true
  } catch (error) {
    console.error('❌ 测试失败:', error)
    return false
  }
}
```

### 模块加载错误处理
```typescript
try {
  const services = loadAIServices()
  // 使用服务
} catch (error) {
  console.error('❌ 无法加载服务模块:', error.message)
  console.log('💡 提示: 请确保项目已正确构建')
  return false
}
```

## 常量定义

### 测试配置常量
```typescript
const TEST_CONFIG = {
  CUSTOM_PORTS: [8080, 9999],
  TIMEOUT: 10000,
  MAX_PROVIDERS_TO_TEST: 2,
  MAX_MODELS_TO_DISPLAY: 3
} as const
```

### 测试消息常量
```typescript
const TEST_MESSAGES = {
  SUCCESS: '✅',
  ERROR: '❌', 
  WARNING: '⚠️',
  INFO: 'ℹ️',
  SEPARATOR: '='.repeat(60)
} as const
```

### 测试名称常量
```typescript
const TEST_NAMES = [
  'AI集成服务基本功能',
  '本地AI服务发现功能',
  'AI对话功能', 
  '配置管理功能'
] as const
```

## 使用示例

### 基本使用
```typescript
// 运行所有测试
await runAllTests()

// 运行单个测试
const result = await testAIIntegrationBasics()
console.log(`基本功能测试: ${result ? '通过' : '失败'}`)
```

### 自定义测试
```typescript
// 导入特定测试函数
const { testLocalAIServiceDiscovery } = require('./scripts/test-ai-integration.js')

// 运行特定测试
const result = await testLocalAIServiceDiscovery()
if (result) {
  console.log('本地服务发现功能正常')
}
```

### 批量测试
```typescript
const tests = [
  testAIIntegrationBasics,
  testLocalAIServiceDiscovery,
  testAIChatFunctionality,
  testConfigurationManagement
]

const results = await Promise.all(tests.map(test => test()))
const passedCount = results.filter(r => r).length
console.log(`测试完成: ${passedCount}/${results.length} 项通过`)
```

## 扩展接口

### 自定义测试函数接口
```typescript
interface CustomTestFunction {
  (): Promise<boolean>
}
```

### 测试配置接口
```typescript
interface TestConfiguration {
  mockData?: any
  timeout?: number
  customPorts?: number[]
  skipNetworkTests?: boolean
}
```

### 测试报告接口
```typescript
interface TestReport {
  totalTests: number
  passedTests: number
  failedTests: number
  testResults: TestResult[]
  summary: string
  recommendations: string[]
}
```

## 相关文档

- [AI集成服务测试脚本API文档](./test-ai-integration-script-api.md)
- [AI集成服务测试使用示例](./test-ai-integration-usage-examples.md)
- [AI集成服务API文档](./aiIntegrationService-api.md)
- [本地AI服务适配器API文档](./localAIServiceAdapter-api.md)