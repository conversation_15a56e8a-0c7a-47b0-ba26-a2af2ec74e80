# Vitest 配置文档

## 概述

`vitest.config.ts` 是 Universe Bag Chrome 扩展的 Vitest 测试框架配置文件，负责配置测试环境、覆盖率报告、测试超时、并发设置等功能。该配置专门针对 Chrome 扩展的测试需求进行了优化。

## 配置结构

### 基础配置

```typescript
export default defineConfig({
  test: {
    // 测试配置...
  },
  resolve: {
    // 模块解析配置...
  }
})
```

## 主要功能模块

### 1. 测试环境配置

#### 测试环境设置
```typescript
test: {
  environment: 'jsdom'
}
```

**功能**: 设置测试运行环境为 jsdom  
**用途**: 提供浏览器 DOM API 模拟，支持 React 组件和 Chrome Extension API 测试  
**优势**: 
- 支持 DOM 操作测试
- 模拟浏览器环境
- 兼容 Chrome Extension API

#### 全局变量配置
```typescript
test: {
  globals: true
}
```

**功能**: 启用全局测试函数  
**用途**: 无需导入即可使用 `describe`、`it`、`expect` 等测试函数  
**影响**: 简化测试文件的导入语句

### 2. 测试执行配置

#### 监听模式设置
```typescript
test: {
  watch: false,
  run: true
}
```

**功能**: 禁用文件监听模式，确保测试运行后立即退出  
**用途**: 适用于 CI/CD 环境和一次性测试执行  
**优势**: 
- 避免测试进程挂起
- 适合自动化构建流程
- 提高测试执行效率

#### 超时配置
```typescript
test: {
  testTimeout: 10000
}
```

**功能**: 设置单个测试用例的超时时间为 10 秒  
**用途**: 防止异步测试无限等待  
**适用场景**: Chrome Extension API 调用、网络请求测试

#### 并发设置
```typescript
test: {
  pool: 'threads'
}
```

**功能**: 使用线程池并发运行测试  
**用途**: 提高测试执行速度  
**优势**: 
- 充分利用多核 CPU
- 减少总体测试时间
- 提高开发效率

### 3. 报告和输出配置

#### 报告器设置
```typescript
test: {
  reporters: ['verbose'],
  silent: false
}
```

**功能**: 配置测试报告输出格式  
**参数说明**:
- `verbose`: 详细输出模式，显示每个测试用例的执行结果
- `silent: false`: 启用测试输出信息

**输出特性**:
- 显示测试用例名称和状态
- 显示执行时间
- 显示错误详情和堆栈跟踪

#### 失败处理配置
```typescript
test: {
  bail: 0,
  passWithNoTests: true
}
```

**功能**: 配置测试失败时的行为  
**参数说明**:
- `bail: 0`: 不在失败时立即停止，运行所有测试
- `passWithNoTests: true`: 没有测试文件时不报错

### 4. 覆盖率配置

#### 覆盖率报告设置
```typescript
test: {
  coverage: {
    reporter: ['text', 'json', 'html'],
    exclude: [
      'node_modules/',
      'dist/',
      'tests/',
      '**/*.d.ts',
      '**/*.config.*',
      '**/vite.config.*'
    ]
  }
}
```

**功能**: 配置代码覆盖率报告生成  
**报告格式**:
- `text`: 控制台文本输出
- `json`: JSON 格式报告文件
- `html`: HTML 可视化报告

**排除文件**:
- `node_modules/`: 第三方依赖
- `dist/`: 构建输出文件
- `tests/`: 测试文件本身
- `**/*.d.ts`: TypeScript 类型定义文件
- `**/*.config.*`: 配置文件
- `**/vite.config.*`: Vite 配置文件

### 5. 模块解析配置

#### 路径别名设置
```typescript
resolve: {
  alias: {
    '@': resolve(__dirname, './src'),
  },
}
```

**功能**: 配置模块路径别名  
**用途**: 简化测试文件中的导入路径  
**使用示例**:
```typescript
// 使用别名前
import { bookmarkService } from '../../../src/services/bookmarkService'

// 使用别名后
import { bookmarkService } from '@/services/bookmarkService'
```

## 配置函数分析

### defineConfig()
```typescript
function defineConfig(config: UserConfig): UserConfig
```

**功能**: 定义 Vitest 配置对象  
**参数**: 
- `config`: Vitest 配置对象

**返回值**: 处理后的配置对象  
**用途**: 提供类型检查和配置验证

### resolve()
```typescript
function resolve(...paths: string[]): string
```

**功能**: 解析文件路径  
**参数**: 
- `...paths`: 路径片段数组

**返回值**: 绝对路径字符串  
**用途**: 生成跨平台兼容的绝对路径

## 使用示例

### 基本测试运行
```bash
# 运行所有测试
npm run test

# 运行测试并生成覆盖率报告
npm run test:coverage

# 运行特定测试文件
npm run test -- tests/bookmarkService.test.js
```

### 测试文件示例
```typescript
// tests/example.test.ts
import { describe, it, expect } from 'vitest'
import { bookmarkService } from '@/services/bookmarkService'

describe('书签服务测试', () => {
  it('应该能够创建书签', async () => {
    const bookmark = await bookmarkService.createBookmark({
      title: '测试书签',
      url: 'https://example.com'
    })
    
    expect(bookmark).toBeDefined()
    expect(bookmark.title).toBe('测试书签')
  })
})
```

### 异步测试示例
```typescript
// 测试 Chrome Extension API
describe('Chrome Extension API 测试', () => {
  it('应该能够获取存储数据', async () => {
    // 模拟 Chrome Storage API
    const mockStorage = {
      get: vi.fn().mockResolvedValue({ key: 'value' })
    }
    
    global.chrome = {
      storage: { local: mockStorage }
    }
    
    const result = await chromeStorage.get('key')
    expect(result).toBe('value')
  }, 10000) // 使用配置的超时时间
})
```

## 配置优化特性

### Chrome 扩展适配
- jsdom 环境支持 DOM API 测试
- 路径别名简化模块导入
- 适当的超时设置支持异步操作
- 覆盖率排除配置文件和构建产物

### 开发体验优化
- 详细的测试报告输出
- 多格式覆盖率报告
- 并发测试执行提高速度
- 全局测试函数简化语法

### CI/CD 优化
- 非监听模式适合自动化流程
- 测试完成后自动退出
- 详细的错误报告便于调试
- 灵活的失败处理策略

## 扩展配置

### 添加新的测试环境
```typescript
test: {
  environment: 'jsdom',
  // 添加环境特定配置
  environmentOptions: {
    jsdom: {
      resources: 'usable'
    }
  }
}
```

### 自定义报告器
```typescript
test: {
  reporters: [
    'verbose',
    ['json', { outputFile: 'test-results.json' }],
    ['html', { outputDir: 'coverage/html' }]
  ]
}
```

### 添加设置文件
```typescript
test: {
  setupFiles: [
    './tests/setup.ts',
    './tests/chrome-mock.ts'
  ]
}
```

## 故障排除

### 常见问题

#### 1. 测试超时
**症状**: 测试运行超过 10 秒后失败  
**解决方案**: 
- 检查异步操作是否正确处理
- 增加 `testTimeout` 值
- 使用 `vi.mock()` 模拟耗时操作

#### 2. 模块导入失败
**症状**: 找不到模块或路径错误  
**解决方案**: 
- 检查 `resolve.alias` 配置
- 确保路径别名正确设置
- 验证文件路径是否存在

#### 3. Chrome API 未定义
**症状**: `chrome is not defined` 错误  
**解决方案**: 
- 在测试设置文件中模拟 Chrome API
- 使用 `vi.mock()` 模拟扩展 API
- 检查 `@types/chrome` 类型定义

### 调试技巧

#### 启用调试模式
```bash
# 运行单个测试文件并显示详细输出
npm run test -- --reporter=verbose tests/specific.test.js

# 运行测试并保持进程活跃（用于调试）
npm run test:watch
```

#### 查看覆盖率报告
```bash
# 生成 HTML 覆盖率报告
npm run test:coverage

# 打开覆盖率报告
start coverage/index.html
```

## 性能考虑

### 测试执行性能
- 使用线程池并发执行测试
- 合理的超时设置避免无限等待
- 排除不必要的文件提高覆盖率计算速度

### 内存使用优化
- jsdom 环境相对轻量
- 测试完成后自动退出释放内存
- 并发控制避免内存溢出

## 安全考虑

### 测试隔离
- 每个测试用例独立运行
- 避免测试间的状态污染
- 模拟外部依赖减少安全风险

### 数据安全
- 测试数据不包含敏感信息
- 模拟 API 调用避免真实请求
- 覆盖率报告不暴露敏感代码

## 依赖关系

### 核心依赖
- `vitest`: 测试框架核心
- `jsdom`: DOM 环境模拟
- `@types/chrome`: Chrome Extension 类型定义

### 开发依赖
- `typescript`: TypeScript 编译支持
- `@vitejs/plugin-react`: React 组件测试支持

## 最佳实践

### 测试文件组织
```
tests/
├── unit/           # 单元测试
├── integration/    # 集成测试
├── setup.ts        # 测试设置文件
└── mocks/          # 模拟文件
    ├── chrome.ts   # Chrome API 模拟
    └── storage.ts  # 存储 API 模拟
```

### 测试命名规范
```typescript
describe('服务名称', () => {
  describe('方法名称', () => {
    it('应该在正常情况下返回预期结果', () => {
      // 测试逻辑
    })
    
    it('应该在错误情况下抛出异常', () => {
      // 错误测试
    })
  })
})
```

### 模拟最佳实践
```typescript
// 在测试文件顶部设置模拟
vi.mock('@/utils/chromeStorage', () => ({
  get: vi.fn(),
  set: vi.fn(),
  remove: vi.fn()
}))

// 在测试中使用模拟
it('应该调用存储服务', async () => {
  const mockGet = vi.mocked(chromeStorage.get)
  mockGet.mockResolvedValue('test-value')
  
  const result = await service.getData('key')
  expect(mockGet).toHaveBeenCalledWith('key')
  expect(result).toBe('test-value')
})
```