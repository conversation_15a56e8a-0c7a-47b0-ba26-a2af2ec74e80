# 测试构建 API 文档

## 概述

本文档描述了项目中各种测试脚本的 API 和使用方法，包括集成测试、单元测试和构建验证脚本。

## 测试脚本分类

### 1. 集成测试脚本

#### DetailedBookmarkForm shadcn 集成测试
- **文件**: `tests/integration-test-shadcn.js`
- **用途**: 验证 DetailedBookmarkForm 组件的 shadcn 重构完整性
- **执行**: `node tests/integration-test-shadcn.js`
- **详细文档**: [integration-test-shadcn-api.md](./integration-test-shadcn-api.md)

#### Popup 组件集成测试
- **文件**: `tests/popup-components.test.js`
- **用途**: 测试 Popup 组件的功能和结构
- **执行**: `node tests/popup-components.test.js`

### 2. 单元测试文件

#### DetailedBookmarkForm shadcn 单元测试
- **文件**: `tests/DetailedBookmarkForm.shadcn.test.tsx`
- **用途**: 详细测试 DetailedBookmarkForm 组件的 shadcn 功能
- **执行**: `npm test DetailedBookmarkForm.shadcn.test.tsx`

### 3. 手动测试指南

#### DetailedBookmarkForm 手动测试
- **文件**: `tests/manual-test-detailedbookmarkform.md`
- **用途**: 提供手动测试 DetailedBookmarkForm 组件的详细步骤
- **类型**: 测试指南文档

## 构建和测试流程

### 标准测试流程

```bash
# 1. 构建项目
npm run build

# 2. 运行集成测试
node tests/integration-test-shadcn.js

# 3. 运行单元测试
npm test

# 4. 手动测试（参考手动测试指南）
```

### 快速验证流程

```bash
# 快速验证 shadcn 重构
npm run build && node tests/integration-test-shadcn.js
```

## 测试覆盖范围

### shadcn 组件重构测试
- ✅ 构建产物验证
- ✅ 组件导入检查
- ✅ react-hook-form 使用验证
- ✅ 旧 CSS 类名清理检查
- ✅ shadcn 组件使用验证
- ✅ 测试文件完整性检查
- ✅ 文档完整性验证

### 功能测试覆盖
- ✅ 表单渲染和验证
- ✅ 标签管理功能
- ✅ AI 助手功能
- ✅ 用户交互测试
- ✅ 错误处理测试

## 测试工具和依赖

### Node.js 测试脚本依赖
- `fs` - 文件系统操作
- `path` - 路径处理

### React 测试依赖
- `@testing-library/react` - React 组件测试
- `@testing-library/user-event` - 用户交互模拟
- `vitest` - 测试框架

### Chrome 扩展测试
- `chrome` API 模拟
- 扩展环境模拟

## 错误排查指南

### 常见错误和解决方案

1. **构建产物不存在**
   ```
   错误: dist目录不存在，请先运行 npm run build
   解决: npm run build
   ```

2. **shadcn 导入缺失**
   ```
   错误: 缺少shadcn组件导入
   解决: 检查组件文件中的导入语句
   ```

3. **测试文件不存在**
   ```
   错误: shadcn测试文件不存在
   解决: 创建对应的测试文件
   ```

## 测试最佳实践

### 1. 测试执行顺序
1. 构建验证
2. 静态分析测试
3. 单元测试
4. 集成测试
5. 手动测试

### 2. 持续集成建议
- 每次代码提交前运行完整测试套件
- 重构后必须运行相关的集成测试
- 定期执行手动测试验证用户体验

### 3. 测试维护
- 保持测试用例与代码同步
- 及时更新测试文档
- 定期审查测试覆盖率

## 相关文档

- [integration-test-shadcn-api.md](./integration-test-shadcn-api.md) - shadcn 集成测试详细文档
- [task-13-detailedbookmarkform-shadcn-refactor.md](./task-13-detailedbookmarkform-shadcn-refactor.md) - 重构完成报告
- [manual-test-detailedbookmarkform.md](../tests/manual-test-detailedbookmarkform.md) - 手动测试指南