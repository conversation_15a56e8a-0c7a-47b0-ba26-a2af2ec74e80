# AI提供商服务使用示例

## 概述

本文档提供 `aiProviderService.ts` 的详细使用示例，涵盖各种AI提供商的集成和使用场景。

## 基础使用

### 导入服务

```typescript
import { aiProviderService } from '../services/aiProviderService'
import { AIProviderConfig, AIModel } from '../types/ai'
```

### 基本配置结构

```typescript
const baseConfig: Omit<AIProviderConfig, 'id' | 'createdAt' | 'updatedAt'> = {
  name: '提供商名称',
  type: 'provider-type',
  baseUrl: 'https://api.example.com',
  enabled: true
}
```

## Ollama 使用示例

### 1. 基本连接测试

```typescript
async function testOllamaBasic() {
  const config: AIProviderConfig = {
    id: 'ollama-local',
    name: 'Local Ollama',
    type: 'ollama',
    baseUrl: 'http://localhost:11434',
    enabled: true,
    createdAt: new Date(),
    updatedAt: new Date()
  }

  try {
    // 测试连接
    const result = await aiProviderService.testConnection(config)
    
    if (result.success) {
      console.log(`✅ Ollama连接成功`)
      console.log(`📊 响应时间: ${result.responseTime}ms`)
      console.log(`🤖 模型数量: ${result.modelCount}`)
      
      // 获取模型列表
      const models = await aiProviderService.getModels(config)
      console.log(`📋 获取到 ${models.length} 个模型:`)
      
      models.forEach(model => {
        console.log(`  - ${model.displayName} (${model.size})`)
        console.log(`    能力: ${model.capabilities?.join(', ')}`)
        console.log(`    标签: ${model.tags?.join(', ')}`)
      })
    } else {
      console.error(`❌ Ollama连接失败: ${result.error}`)
    }
  } catch (error) {
    console.error('Ollama测试异常:', error)
  }
}
```

### 2. 服务状态验证

```typescript
async function validateOllamaService() {
  const baseUrl = 'http://localhost:11434'
  
  try {
    const status = await aiProviderService.validateOllamaService(baseUrl)
    
    if (status.isRunning) {
      console.log(`✅ Ollama服务运行中`)
      console.log(`📦 版本: ${status.version}`)
    } else {
      console.error(`❌ Ollama服务未运行: ${status.error}`)
      
      // 提供解决建议
      console.log('💡 解决建议:')
      console.log('  1. 确认Ollama已安装并启动')
      console.log('  2. 检查端口11434是否被占用')
      console.log('  3. 尝试运行: ollama serve')
    }
  } catch (error) {
    console.error('验证Ollama服务失败:', error)
  }
}
```

### 3. 模型筛选和分析

```typescript
async function analyzeOllamaModels() {
  const models = await aiProviderService.getOllamaModels('http://localhost:11434')
  
  // 按类型分组
  const modelsByType = models.reduce((acc, model) => {
    const isCodeModel = model.capabilities?.includes('coding')
    const isChatModel = model.capabilities?.includes('chat')
    
    if (isCodeModel) {
      acc.coding = acc.coding || []
      acc.coding.push(model)
    }
    if (isChatModel) {
      acc.chat = acc.chat || []
      acc.chat.push(model)
    }
    
    return acc
  }, {} as Record<string, AIModel[]>)
  
  console.log('📊 Ollama模型分析:')
  console.log(`  💬 对话模型: ${modelsByType.chat?.length || 0}`)
  console.log(`  💻 代码模型: ${modelsByType.coding?.length || 0}`)
  
  // 推荐模型
  const recommendedModels = models.filter(m => m.isRecommended)
  console.log(`⭐ 推荐模型 (${recommendedModels.length}):`)
  recommendedModels.forEach(model => {
    console.log(`  - ${model.displayName}`)
  })
  
  // 按大小排序
  const sortedBySize = models
    .filter(m => m.size)
    .sort((a, b) => {
      const aSize = parseFloat(a.size?.replace(/[^\d.]/g, '') || '0')
      const bSize = parseFloat(b.size?.replace(/[^\d.]/g, '') || '0')
      return aSize - bSize
    })
  
  console.log(`📏 按大小排序 (前3个):`)
  sortedBySize.slice(0, 3).forEach(model => {
    console.log(`  - ${model.displayName}: ${model.size}`)
  })
}
```

## LM Studio 使用示例

### 1. 连接和模型获取

```typescript
async function testLMStudio() {
  const config: AIProviderConfig = {
    id: 'lm-studio-local',
    name: 'LM Studio Local',
    type: 'lm-studio',
    baseUrl: 'http://localhost:1234/v1',
    enabled: true,
    createdAt: new Date(),
    updatedAt: new Date()
  }

  try {
    // 验证服务状态
    const status = await aiProviderService.validateLMStudioService(config.baseUrl)
    
    if (!status.isRunning) {
      console.error('❌ LM Studio服务未运行')
      console.log('💡 请确保:')
      console.log('  1. LM Studio已启动')
      console.log('  2. 已启用本地服务器功能')
      console.log('  3. 端口1234未被占用')
      return
    }
    
    console.log(`✅ LM Studio服务运行中 (${status.version})`)
    
    // 获取模型列表
    const models = await aiProviderService.getModels(config)
    
    if (models.length === 0) {
      console.log('⚠️  未找到已加载的模型')
      console.log('💡 请在LM Studio中加载至少一个模型')
      return
    }
    
    console.log(`🤖 找到 ${models.length} 个已加载的模型:`)
    models.forEach(model => {
      console.log(`  📦 ${model.displayName}`)
      console.log(`     ID: ${model.id}`)
      console.log(`     能力: ${model.capabilities?.join(', ')}`)
      console.log(`     标签: ${model.tags?.join(', ')}`)
    })
    
  } catch (error) {
    console.error('LM Studio测试失败:', error)
  }
}
```

### 2. 模型类型识别

```typescript
async function identifyLMStudioModelTypes() {
  const models = await aiProviderService.getLMStudioModels('http://localhost:1234/v1')
  
  const modelAnalysis = models.map(model => {
    const modelName = model.name.toLowerCase()
    
    // 识别模型类型
    let modelType = 'general'
    if (modelName.includes('code') || modelName.includes('coder')) {
      modelType = 'coding'
    } else if (modelName.includes('chat') || modelName.includes('instruct')) {
      modelType = 'chat'
    } else if (modelName.includes('embed')) {
      modelType = 'embedding'
    }
    
    // 识别参数大小
    const sizeMatch = modelName.match(/(\d+)b/i)
    const parameterSize = sizeMatch ? `${sizeMatch[1]}B` : 'Unknown'
    
    // 识别量化级别
    let quantization = 'Unknown'
    if (modelName.includes('q4')) quantization = 'Q4'
    else if (modelName.includes('q8')) quantization = 'Q8'
    else if (modelName.includes('f16')) quantization = 'FP16'
    
    return {
      name: model.displayName,
      type: modelType,
      parameters: parameterSize,
      quantization,
      recommended: model.isRecommended
    }
  })
  
  console.log('🔍 LM Studio模型分析:')
  console.table(modelAnalysis)
}
```

## OpenRouter 使用示例

### 1. API密钥验证和连接

```typescript
async function testOpenRouter() {
  const apiKey = 'sk-or-your-api-key-here'
  
  // 首先验证API密钥格式
  const validation = aiProviderService['validateOpenRouterApiKey'](apiKey)
  if (!validation.isValid) {
    console.error(`❌ API密钥格式无效: ${validation.error}`)
    return
  }
  
  console.log('✅ API密钥格式验证通过')
  
  const config: AIProviderConfig = {
    id: 'openrouter-main',
    name: 'OpenRouter',
    type: 'openrouter',
    baseUrl: 'https://openrouter.ai/api/v1',
    apiKey: apiKey,
    enabled: true,
    createdAt: new Date(),
    updatedAt: new Date()
  }

  try {
    // 测试连接
    console.log('🔄 测试OpenRouter连接...')
    const result = await aiProviderService.testConnection(config)
    
    if (result.success) {
      console.log(`✅ OpenRouter连接成功`)
      console.log(`📊 响应时间: ${result.responseTime}ms`)
      console.log(`🤖 可用模型: ${result.modelCount}`)
      
      // 获取模型列表
      console.log('🔄 获取模型列表...')
      const models = await aiProviderService.getModels(config)
      
      console.log(`📋 获取到 ${models.length} 个模型`)
      
      // 显示前5个推荐模型
      const topModels = models
        .filter(m => m.isRecommended)
        .slice(0, 5)
      
      console.log('⭐ 推荐模型 (前5个):')
      topModels.forEach(model => {
        console.log(`  🤖 ${model.displayName}`)
        console.log(`     ID: ${model.id}`)
        console.log(`     上下文: ${model.parameters}`)
        console.log(`     能力: ${model.capabilities?.join(', ')}`)
        
        // 显示定价信息（如果有）
        if (model.description?.includes('定价:')) {
          const pricingMatch = model.description.match(/💰 定价: (.+?)(?:\n|$)/)
          if (pricingMatch) {
            console.log(`     💰 ${pricingMatch[1]}`)
          }
        }
        console.log()
      })
      
    } else {
      console.error(`❌ OpenRouter连接失败: ${result.error}`)
      
      // 提供解决建议
      if (result.error?.includes('401')) {
        console.log('💡 解决建议:')
        console.log('  1. 检查API密钥是否正确')
        console.log('  2. 确认API密钥未过期')
        console.log('  3. 访问 https://openrouter.ai/keys 检查密钥状态')
      }
    }
    
  } catch (error) {
    console.error('OpenRouter测试异常:', error)
  }
}
```

### 2. 模型搜索和筛选

```typescript
async function searchOpenRouterModels() {
  const apiKey = 'sk-or-your-api-key-here'
  const models = await aiProviderService.getOpenRouterModels(apiKey)
  
  console.log('🔍 OpenRouter模型搜索示例:')
  
  // 1. 按提供商筛选
  const openaiModels = models.filter(m => m.id.startsWith('openai/'))
  const anthropicModels = models.filter(m => m.id.startsWith('anthropic/'))
  const metaModels = models.filter(m => m.id.startsWith('meta-llama/'))
  
  console.log(`🤖 OpenAI模型: ${openaiModels.length}`)
  console.log(`🧠 Anthropic模型: ${anthropicModels.length}`)
  console.log(`🦙 Meta模型: ${metaModels.length}`)
  
  // 2. 按能力筛选
  const codingModels = models.filter(m => 
    m.capabilities?.includes('coding') || 
    m.id.toLowerCase().includes('code')
  )
  
  const visionModels = models.filter(m => 
    m.capabilities?.includes('vision') ||
    m.capabilities?.includes('multimodal')
  )
  
  console.log(`💻 编程模型: ${codingModels.length}`)
  console.log(`👁️  视觉模型: ${visionModels.length}`)
  
  // 3. 按上下文长度筛选
  const longContextModels = models.filter(m => {
    const contextLength = parseInt(m.parameters?.replace(/[^\d]/g, '') || '0')
    return contextLength >= 32000
  })
  
  console.log(`📄 长上下文模型 (≥32K): ${longContextModels.length}`)
  
  // 4. 按定价筛选（低成本模型）
  const lowCostModels = models.filter(m => {
    if (!m.description?.includes('定价:')) return false
    
    const inputPriceMatch = m.description.match(/输入: \$(\d+\.?\d*)/);
    if (!inputPriceMatch) return false
    
    const inputPrice = parseFloat(inputPriceMatch[1])
    return inputPrice < 1000 // 低于$1000/1M tokens
  })
  
  console.log(`💰 低成本模型 (<$1000/1M): ${lowCostModels.length}`)
  
  // 5. 显示最受欢迎的模型
  const popularModels = models
    .filter(m => m.isPopular)
    .sort((a, b) => a.displayName.localeCompare(b.displayName))
  
  console.log('\n🔥 热门模型:')
  popularModels.slice(0, 10).forEach(model => {
    console.log(`  - ${model.displayName} (${model.id})`)
  })
}
```

### 3. 定价分析

```typescript
async function analyzeOpenRouterPricing() {
  const apiKey = 'sk-or-your-api-key-here'
  const models = await aiProviderService.getOpenRouterModels(apiKey)
  
  const pricingData = models
    .filter(m => m.description?.includes('定价:'))
    .map(model => {
      const inputMatch = model.description?.match(/输入: \$(\d+\.?\d*)/)
      const outputMatch = model.description?.match(/输出: \$(\d+\.?\d*)/)
      
      return {
        name: model.displayName,
        id: model.id,
        inputPrice: inputMatch ? parseFloat(inputMatch[1]) : 0,
        outputPrice: outputMatch ? parseFloat(outputMatch[1]) : 0,
        contextLength: parseInt(model.parameters?.replace(/[^\d]/g, '') || '0'),
        isRecommended: model.isRecommended
      }
    })
    .filter(m => m.inputPrice > 0)
    .sort((a, b) => a.inputPrice - b.inputPrice)
  
  console.log('💰 OpenRouter模型定价分析:')
  console.log('\n📊 最便宜的10个模型:')
  pricingData.slice(0, 10).forEach(model => {
    console.log(`  ${model.name}`)
    console.log(`    输入: $${model.inputPrice}/1M tokens`)
    console.log(`    输出: $${model.outputPrice}/1M tokens`)
    console.log(`    上下文: ${model.contextLength.toLocaleString()} tokens`)
    console.log(`    推荐: ${model.isRecommended ? '是' : '否'}`)
    console.log()
  })
  
  // 计算平均价格
  const avgInputPrice = pricingData.reduce((sum, m) => sum + m.inputPrice, 0) / pricingData.length
  const avgOutputPrice = pricingData.reduce((sum, m) => sum + m.outputPrice, 0) / pricingData.length
  
  console.log(`📈 平均定价:`)
  console.log(`  输入: $${avgInputPrice.toFixed(2)}/1M tokens`)
  console.log(`  输出: $${avgOutputPrice.toFixed(2)}/1M tokens`)
}
```

## 多提供商集成示例

### 1. 批量测试多个提供商

```typescript
async function testMultipleProviders() {
  const providers: AIProviderConfig[] = [
    {
      id: 'ollama-1',
      name: 'Local Ollama',
      type: 'ollama',
      baseUrl: 'http://localhost:11434',
      enabled: true,
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      id: 'lm-studio-1',
      name: 'LM Studio',
      type: 'lm-studio',
      baseUrl: 'http://localhost:1234/v1',
      enabled: true,
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      id: 'openrouter-1',
      name: 'OpenRouter',
      type: 'openrouter',
      baseUrl: 'https://openrouter.ai/api/v1',
      apiKey: 'sk-or-your-api-key',
      enabled: true,
      createdAt: new Date(),
      updatedAt: new Date()
    }
  ]

  console.log('🔄 批量测试AI提供商...')
  
  const results = await Promise.allSettled(
    providers.map(async (config) => {
      const startTime = Date.now()
      
      try {
        const result = await aiProviderService.testConnection(config)
        const models = result.success ? await aiProviderService.getModels(config) : []
        
        return {
          name: config.name,
          type: config.type,
          success: result.success,
          responseTime: result.responseTime,
          modelCount: models.length,
          error: result.error,
          testDuration: Date.now() - startTime
        }
      } catch (error) {
        return {
          name: config.name,
          type: config.type,
          success: false,
          error: error.message,
          testDuration: Date.now() - startTime
        }
      }
    })
  )

  console.log('\n📊 测试结果汇总:')
  console.log('=' .repeat(60))
  
  results.forEach((result, index) => {
    if (result.status === 'fulfilled') {
      const data = result.value
      const status = data.success ? '✅' : '❌'
      
      console.log(`${status} ${data.name} (${data.type})`)
      if (data.success) {
        console.log(`   响应时间: ${data.responseTime}ms`)
        console.log(`   模型数量: ${data.modelCount}`)
      } else {
        console.log(`   错误: ${data.error}`)
      }
      console.log(`   测试耗时: ${data.testDuration}ms`)
    } else {
      console.log(`❌ ${providers[index].name}: ${result.reason}`)
    }
    console.log()
  })
  
  // 统计信息
  const successful = results.filter(r => 
    r.status === 'fulfilled' && r.value.success
  ).length
  
  console.log(`📈 成功率: ${successful}/${results.length} (${(successful/results.length*100).toFixed(1)}%)`)
}
```

### 2. 模型聚合和去重

```typescript
async function aggregateModelsFromProviders() {
  const providers = [
    { type: 'ollama', baseUrl: 'http://localhost:11434' },
    { type: 'lm-studio', baseUrl: 'http://localhost:1234/v1' },
    { type: 'openrouter', apiKey: 'sk-or-your-api-key' }
  ]

  const allModels: AIModel[] = []
  
  for (const provider of providers) {
    try {
      let models: AIModel[] = []
      
      if (provider.type === 'ollama') {
        models = await aiProviderService.getOllamaModels(provider.baseUrl)
      } else if (provider.type === 'lm-studio') {
        models = await aiProviderService.getLMStudioModels(provider.baseUrl)
      } else if (provider.type === 'openrouter') {
        models = await aiProviderService.getOpenRouterModels(provider.apiKey!)
      }
      
      allModels.push(...models)
      console.log(`📦 从 ${provider.type} 获取 ${models.length} 个模型`)
      
    } catch (error) {
      console.warn(`⚠️  ${provider.type} 获取模型失败: ${error.message}`)
    }
  }
  
  console.log(`\n📊 总计获取 ${allModels.length} 个模型`)
  
  // 按能力分组
  const modelsByCapability = allModels.reduce((acc, model) => {
    model.capabilities?.forEach(capability => {
      acc[capability] = acc[capability] || []
      acc[capability].push(model)
    })
    return acc
  }, {} as Record<string, AIModel[]>)
  
  console.log('\n🎯 按能力分组:')
  Object.entries(modelsByCapability).forEach(([capability, models]) => {
    console.log(`  ${capability}: ${models.length} 个模型`)
  })
  
  // 去重（基于模型名称）
  const uniqueModels = allModels.reduce((acc, model) => {
    const key = model.name.toLowerCase()
    if (!acc.has(key)) {
      acc.set(key, model)
    }
    return acc
  }, new Map<string, AIModel>())
  
  console.log(`\n🔄 去重后: ${uniqueModels.size} 个唯一模型`)
  
  // 推荐模型统计
  const recommendedCount = Array.from(uniqueModels.values())
    .filter(m => m.isRecommended).length
  
  console.log(`⭐ 推荐模型: ${recommendedCount} 个`)
  
  return Array.from(uniqueModels.values())
}
```

## 错误处理和重试机制

### 1. 智能重试

```typescript
async function testWithRetry(config: AIProviderConfig, maxRetries = 3) {
  let lastError: Error | null = null
  
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      console.log(`🔄 尝试连接 ${config.name} (第${attempt}次)...`)
      
      const result = await aiProviderService.testConnection(config)
      
      if (result.success) {
        console.log(`✅ ${config.name} 连接成功 (第${attempt}次尝试)`)
        return result
      } else {
        throw new Error(result.error || '连接失败')
      }
      
    } catch (error) {
      lastError = error as Error
      console.warn(`⚠️  第${attempt}次尝试失败: ${error.message}`)
      
      if (attempt < maxRetries) {
        const delay = Math.pow(2, attempt) * 1000 // 指数退避
        console.log(`⏳ 等待 ${delay}ms 后重试...`)
        await new Promise(resolve => setTimeout(resolve, delay))
      }
    }
  }
  
  console.error(`❌ ${config.name} 连接失败 (已重试${maxRetries}次)`)
  throw lastError
}
```

### 2. 健康检查

```typescript
async function performHealthCheck() {
  const services = [
    { name: 'Ollama', check: () => aiProviderService.validateOllamaService('http://localhost:11434') },
    { name: 'LM Studio', check: () => aiProviderService.validateLMStudioService('http://localhost:1234/v1') },
    { name: 'Xinference', check: () => aiProviderService.validateXinferenceService('http://localhost:9997') }
  ]

  console.log('🏥 执行健康检查...')
  
  const healthResults = await Promise.allSettled(
    services.map(async service => {
      const startTime = Date.now()
      const result = await service.check()
      const duration = Date.now() - startTime
      
      return {
        name: service.name,
        isHealthy: result.isRunning,
        version: result.version,
        error: result.error,
        responseTime: duration
      }
    })
  )

  console.log('\n🏥 健康检查结果:')
  console.log('=' .repeat(50))
  
  healthResults.forEach((result, index) => {
    if (result.status === 'fulfilled') {
      const data = result.value
      const status = data.isHealthy ? '🟢' : '🔴'
      
      console.log(`${status} ${data.name}`)
      if (data.isHealthy) {
        console.log(`   版本: ${data.version}`)
        console.log(`   响应时间: ${data.responseTime}ms`)
      } else {
        console.log(`   错误: ${data.error}`)
      }
    } else {
      console.log(`🔴 ${services[index].name}: 检查失败`)
    }
    console.log()
  })
  
  const healthyCount = healthResults.filter(r => 
    r.status === 'fulfilled' && r.value.isHealthy
  ).length
  
  console.log(`📊 健康状态: ${healthyCount}/${services.length} 服务正常`)
  
  return healthyCount === services.length
}
```

## 性能监控

### 1. 响应时间监控

```typescript
async function monitorPerformance() {
  const config: AIProviderConfig = {
    id: 'perf-test',
    name: 'Performance Test',
    type: 'ollama',
    baseUrl: 'http://localhost:11434',
    enabled: true,
    createdAt: new Date(),
    updatedAt: new Date()
  }

  const measurements: number[] = []
  const testCount = 10
  
  console.log(`📊 开始性能监控 (${testCount}次测试)...`)
  
  for (let i = 1; i <= testCount; i++) {
    try {
      const result = await aiProviderService.testConnection(config)
      
      if (result.success && result.responseTime) {
        measurements.push(result.responseTime)
        console.log(`✅ 测试 ${i}: ${result.responseTime}ms`)
      } else {
        console.log(`❌ 测试 ${i}: 失败`)
      }
      
      // 间隔1秒
      if (i < testCount) {
        await new Promise(resolve => setTimeout(resolve, 1000))
      }
      
    } catch (error) {
      console.log(`❌ 测试 ${i}: 异常 - ${error.message}`)
    }
  }
  
  if (measurements.length > 0) {
    const avg = measurements.reduce((a, b) => a + b, 0) / measurements.length
    const min = Math.min(...measurements)
    const max = Math.max(...measurements)
    const median = measurements.sort((a, b) => a - b)[Math.floor(measurements.length / 2)]
    
    console.log('\n📈 性能统计:')
    console.log(`  平均响应时间: ${avg.toFixed(2)}ms`)
    console.log(`  最快响应时间: ${min}ms`)
    console.log(`  最慢响应时间: ${max}ms`)
    console.log(`  中位数响应时间: ${median}ms`)
    console.log(`  成功率: ${(measurements.length/testCount*100).toFixed(1)}%`)
  } else {
    console.log('❌ 没有成功的测试数据')
  }
}
```

## 配置管理

### 1. 配置验证

```typescript
function validateProviderConfig(config: Partial<AIProviderConfig>): string[] {
  const errors: string[] = []
  
  if (!config.name?.trim()) {
    errors.push('提供商名称不能为空')
  }
  
  if (!config.type) {
    errors.push('必须指定提供商类型')
  }
  
  if (!config.baseUrl?.trim()) {
    errors.push('API基础URL不能为空')
  } else {
    try {
      new URL(config.baseUrl)
    } catch {
      errors.push('API基础URL格式无效')
    }
  }
  
  // 检查是否需要API密钥
  const requiresApiKey = ['openrouter', 'openai', 'claude', 'gemini'].includes(config.type!)
  if (requiresApiKey && !config.apiKey?.trim()) {
    errors.push('该提供商需要API密钥')
  }
  
  return errors
}

// 使用示例
const config = {
  name: 'Test Provider',
  type: 'openrouter' as const,
  baseUrl: 'https://openrouter.ai/api/v1',
  apiKey: 'sk-or-test-key'
}

const validationErrors = validateProviderConfig(config)
if (validationErrors.length > 0) {
  console.error('❌ 配置验证失败:')
  validationErrors.forEach(error => console.error(`  - ${error}`))
} else {
  console.log('✅ 配置验证通过')
}
```

### 2. 配置模板

```typescript
const providerTemplates = {
  ollama: {
    name: 'Local Ollama',
    type: 'ollama' as const,
    baseUrl: 'http://localhost:11434',
    enabled: true
  },
  
  lmStudio: {
    name: 'LM Studio',
    type: 'lm-studio' as const,
    baseUrl: 'http://localhost:1234/v1',
    enabled: true
  },
  
  openrouter: {
    name: 'OpenRouter',
    type: 'openrouter' as const,
    baseUrl: 'https://openrouter.ai/api/v1',
    apiKey: '', // 需要用户填写
    enabled: true
  },
  
  openai: {
    name: 'OpenAI',
    type: 'openai' as const,
    baseUrl: 'https://api.openai.com/v1',
    apiKey: '', // 需要用户填写
    enabled: true
  }
}

// 使用模板创建配置
function createConfigFromTemplate(templateName: keyof typeof providerTemplates, overrides: Partial<AIProviderConfig> = {}) {
  const template = providerTemplates[templateName]
  
  return {
    ...template,
    ...overrides,
    id: `${template.type}_${Date.now()}`,
    createdAt: new Date(),
    updatedAt: new Date()
  } as AIProviderConfig
}

// 示例
const ollamaConfig = createConfigFromTemplate('ollama', {
  name: 'My Ollama Instance',
  baseUrl: 'http://*************:11434'
})
```

## 相关文档

- [AI提供商服务API文档](./aiProviderService-api.md)
- [OpenRouter API文档](./aiProviderService-openrouter-api.md)
- [AI集成服务文档](./aiIntegrationService-api.md)
- [AI模型服务文档](./aiModelService-api.md)