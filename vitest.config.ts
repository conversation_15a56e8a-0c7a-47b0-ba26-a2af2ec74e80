import { defineConfig } from 'vitest/config'
import { resolve } from 'path'

export default defineConfig({
  test: {
    environment: 'jsdom',
    globals: true,
    setupFiles: ['./tests/setup.ts'],
    // 测试完成后自动退出，不进入监听模式
    watch: false,
    // 显示测试覆盖率
    coverage: {
      reporter: ['text', 'json', 'html'],
      exclude: [
        'node_modules/',
        'dist/',
        'tests/',
        '**/*.d.ts',
        '**/*.config.*',
        '**/vite.config.*'
      ]
    },
    // 测试超时设置
    testTimeout: 10000,
    // 并发运行测试
    pool: 'threads',
    // 静默模式，减少输出
    silent: false,
    // 报告器配置 - 修复配置项名称
    reporters: ['verbose'],
    // 失败时立即停止
    bail: 0,
    // 确保测试运行后立即退出
    run: true,
    // 禁用交互模式
    passWithNoTests: true
  },
  resolve: {
    alias: {
      '@': resolve(__dirname, './src'),
    },
  },
})