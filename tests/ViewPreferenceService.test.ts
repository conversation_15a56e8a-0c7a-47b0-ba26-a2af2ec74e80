import { describe, it, expect, beforeEach, vi } from 'vitest'
import { ViewPreferenceService, type LayoutConfig } from '../src/services/ViewPreferenceService'

// Mock localStorage
const mockLocalStorage = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn()
}

Object.defineProperty(window, 'localStorage', {
  value: mockLocalStorage
})

describe('ViewPreferenceService', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('视图模式管理', () => {
    it('应该保存视图模式', async () => {
      await ViewPreferenceService.saveViewMode('row')
      
      expect(mockLocalStorage.setItem).toHaveBeenCalledWith('bookmark-view-mode', 'row')
    })

    it('应该获取保存的视图模式', async () => {
      mockLocalStorage.getItem.mockReturnValue('compact')
      
      const mode = await ViewPreferenceService.getViewMode()
      
      expect(mode).toBe('compact')
      expect(mockLocalStorage.getItem).toHaveBeenCalledWith('bookmark-view-mode')
    })

    it('应该在无保存值时返回默认视图模式', async () => {
      mockLocalStorage.getItem.mockReturnValue(null)
      
      const mode = await ViewPreferenceService.getViewMode()
      
      expect(mode).toBe('card')
    })

    it('应该在无效保存值时返回默认视图模式', async () => {
      mockLocalStorage.getItem.mockReturnValue('invalid-mode')
      
      const mode = await ViewPreferenceService.getViewMode()
      
      expect(mode).toBe('card')
    })

    it('应该处理保存视图模式时的错误', async () => {
      mockLocalStorage.setItem.mockImplementation(() => {
        throw new Error('Storage error')
      })

      await expect(ViewPreferenceService.saveViewMode('row')).rejects.toThrow('无法保存视图模式偏好')
    })

    it('应该处理获取视图模式时的错误', async () => {
      const consoleErrorSpy = vi.spyOn(console, 'error').mockImplementation(() => {})
      mockLocalStorage.getItem.mockImplementation(() => {
        throw new Error('Storage error')
      })

      const mode = await ViewPreferenceService.getViewMode()
      
      expect(mode).toBe('card')
      expect(consoleErrorSpy).toHaveBeenCalled()

      consoleErrorSpy.mockRestore()
    })
  })

  describe('布局配置管理', () => {
    const mockLayoutConfig: LayoutConfig = {
      card: {
        columns: 4,
        showThumbnails: false,
        showDescriptions: true,
        cardHeight: 'fixed',
        minCardWidth: 300,
        gap: 20
      },
      row: {
        showFavicons: false,
        showCategories: true,
        showTags: true,
        density: 'compact',
        height: 40
      },
      compact: {
        itemsPerRow: 6,
        showMetadata: false,
        spacing: 'tight',
        minItemWidth: 180
      }
    }

    it('应该保存布局配置', async () => {
      mockLocalStorage.getItem.mockReturnValue(null) // 无现有配置
      mockLocalStorage.setItem.mockImplementation(() => {}) // 正常保存
      
      await ViewPreferenceService.saveLayoutConfig(mockLayoutConfig)
      
      expect(mockLocalStorage.setItem).toHaveBeenCalledWith(
        'bookmark-layout-config',
        JSON.stringify(mockLayoutConfig)
      )
    })

    it('应该获取保存的布局配置', async () => {
      mockLocalStorage.getItem.mockReturnValue(JSON.stringify(mockLayoutConfig))
      
      const config = await ViewPreferenceService.getLayoutConfig()
      
      expect(config).toEqual(mockLayoutConfig)
    })

    it('应该在无保存值时返回默认布局配置', async () => {
      mockLocalStorage.getItem.mockReturnValue(null)
      
      const config = await ViewPreferenceService.getLayoutConfig()
      
      expect(config).toHaveProperty('card')
      expect(config).toHaveProperty('row')
      expect(config).toHaveProperty('compact')
      expect(config.card.columns).toBe(3) // 默认值
    })

    it('应该合并部分配置更新', async () => {
      const existingConfig = {
        card: { columns: 2, showThumbnails: true, showDescriptions: true, cardHeight: 'auto' as const, minCardWidth: 280, gap: 16 },
        row: { showFavicons: true, showCategories: true, showTags: false, density: 'comfortable' as const, height: 48 },
        compact: { itemsPerRow: 4, showMetadata: true, spacing: 'normal' as const, minItemWidth: 200 }
      }
      
      mockLocalStorage.getItem.mockReturnValue(JSON.stringify(existingConfig))
      mockLocalStorage.setItem.mockImplementation(() => {}) // 正常保存
      
      const partialUpdate = {
        card: { columns: 5 }
      }
      
      await ViewPreferenceService.saveLayoutConfig(partialUpdate)
      
      const savedConfig = JSON.parse(mockLocalStorage.setItem.mock.calls[0][1])
      expect(savedConfig.card.columns).toBe(5)
      expect(savedConfig.card.showThumbnails).toBe(true) // 保持现有值
    })

    it('应该处理保存布局配置时的错误', async () => {
      mockLocalStorage.setItem.mockImplementation(() => {
        throw new Error('Storage error')
      })

      await expect(ViewPreferenceService.saveLayoutConfig({})).rejects.toThrow('无法保存布局配置')
    })

    it('应该处理获取布局配置时的错误', async () => {
      const consoleErrorSpy = vi.spyOn(console, 'error').mockImplementation(() => {})
      mockLocalStorage.getItem.mockImplementation(() => {
        throw new Error('Storage error')
      })

      const config = await ViewPreferenceService.getLayoutConfig()
      
      expect(config).toHaveProperty('card')
      expect(consoleErrorSpy).toHaveBeenCalled()

      consoleErrorSpy.mockRestore()
    })
  })

  describe('特定视图模式配置', () => {
    it('应该获取特定视图模式的配置', async () => {
      const mockConfig = {
        card: { columns: 4, showThumbnails: true, showDescriptions: false, cardHeight: 'auto' as const, minCardWidth: 280, gap: 16 },
        row: { showFavicons: true, showCategories: true, showTags: false, density: 'comfortable' as const, height: 48 },
        compact: { itemsPerRow: 4, showMetadata: true, spacing: 'normal' as const, minItemWidth: 200 }
      }
      
      mockLocalStorage.getItem.mockReturnValue(JSON.stringify(mockConfig))
      
      const cardConfig = await ViewPreferenceService.getViewModeConfig('card')
      
      expect(cardConfig).toEqual(mockConfig.card)
    })

    it('应该更新特定视图模式的配置', async () => {
      const existingConfig = {
        card: { columns: 3, showThumbnails: true, showDescriptions: true, cardHeight: 'auto' as const, minCardWidth: 280, gap: 16 },
        row: { showFavicons: true, showCategories: true, showTags: false, density: 'comfortable' as const, height: 48 },
        compact: { itemsPerRow: 4, showMetadata: true, spacing: 'normal' as const, minItemWidth: 200 }
      }
      
      mockLocalStorage.getItem.mockReturnValue(JSON.stringify(existingConfig))
      mockLocalStorage.setItem.mockImplementation(() => {}) // 正常保存
      
      await ViewPreferenceService.updateViewModeConfig('card', { columns: 5 })
      
      const savedConfig = JSON.parse(mockLocalStorage.setItem.mock.calls[0][1])
      expect(savedConfig.card.columns).toBe(5)
      expect(savedConfig.row).toEqual(existingConfig.row) // 其他配置不变
    })
  })

  describe('配置验证', () => {
    it('应该验证有效的布局配置', () => {
      const validConfig = {
        card: { columns: 3, minCardWidth: 280, gap: 16 },
        row: { height: 48, density: 'comfortable' as const },
        compact: { itemsPerRow: 4, minItemWidth: 200 }
      }
      
      expect(ViewPreferenceService.validateLayoutConfig(validConfig)).toBe(true)
    })

    it('应该拒绝无效的卡片配置', () => {
      const invalidConfigs = [
        { card: { columns: 0 } }, // 列数太少
        { card: { columns: 10 } }, // 列数太多
        { card: { minCardWidth: 100 } }, // 最小宽度太小
        { card: { minCardWidth: 600 } }, // 最小宽度太大
        { card: { gap: -5 } }, // 间距为负
        { card: { gap: 50 } } // 间距太大
      ]
      
      invalidConfigs.forEach((config, index) => {
        const result = ViewPreferenceService.validateLayoutConfig(config)
        expect(result).toBe(false, `配置 ${index} 应该被拒绝: ${JSON.stringify(config)}`)
      })
    })

    it('应该拒绝无效的行配置', () => {
      const invalidConfigs = [
        { row: { height: 20 } }, // 高度太小
        { row: { height: 100 } }, // 高度太大
        { row: { density: 'invalid' as any } } // 无效密度
      ]
      
      invalidConfigs.forEach(config => {
        expect(ViewPreferenceService.validateLayoutConfig(config)).toBe(false)
      })
    })

    it('应该拒绝无效的紧凑配置', () => {
      const invalidConfigs = [
        { compact: { itemsPerRow: 1 } }, // 每行项目太少
        { compact: { itemsPerRow: 10 } }, // 每行项目太多
        { compact: { minItemWidth: 100 } }, // 最小宽度太小
        { compact: { minItemWidth: 400 } } // 最小宽度太大
      ]
      
      invalidConfigs.forEach(config => {
        expect(ViewPreferenceService.validateLayoutConfig(config)).toBe(false)
      })
    })

    it('应该处理验证时的错误', () => {
      const consoleErrorSpy = vi.spyOn(console, 'error').mockImplementation(() => {})
      
      // 传入会导致错误的配置
      const result = ViewPreferenceService.validateLayoutConfig(null as any)
      
      expect(result).toBe(false)
      expect(consoleErrorSpy).toHaveBeenCalled()

      consoleErrorSpy.mockRestore()
    })
  })

  describe('配置重置', () => {
    it('应该重置为默认配置', async () => {
      await ViewPreferenceService.resetToDefaults()
      
      expect(mockLocalStorage.removeItem).toHaveBeenCalledWith('bookmark-view-mode')
      expect(mockLocalStorage.removeItem).toHaveBeenCalledWith('bookmark-layout-config')
    })

    it('应该处理重置时的错误', async () => {
      mockLocalStorage.removeItem.mockImplementation(() => {
        throw new Error('Storage error')
      })

      await expect(ViewPreferenceService.resetToDefaults()).rejects.toThrow('无法重置配置')
    })
  })

  describe('配置导入导出', () => {
    it('应该导出当前配置', async () => {
      mockLocalStorage.getItem
        .mockReturnValueOnce('row') // 视图模式
        .mockReturnValueOnce(JSON.stringify({ card: { columns: 4 } })) // 布局配置

      const exported = await ViewPreferenceService.exportConfig()
      
      expect(exported.viewMode).toBe('row')
      expect(exported.layoutConfig).toHaveProperty('card')
    })

    it('应该导入配置', async () => {
      mockLocalStorage.setItem.mockImplementation(() => {}) // 正常保存
      mockLocalStorage.getItem.mockReturnValue(null) // 无现有配置
      
      const importConfig = {
        viewMode: 'compact' as const,
        layoutConfig: { card: { columns: 5 } }
      }
      
      await ViewPreferenceService.importConfig(importConfig)
      
      expect(mockLocalStorage.setItem).toHaveBeenCalledWith('bookmark-view-mode', 'compact')
    })

    it('应该在导入无效配置时跳过布局配置', async () => {
      mockLocalStorage.setItem.mockImplementation(() => {}) // 正常保存
      
      const importConfig = {
        viewMode: 'row' as const,
        layoutConfig: { card: { columns: 0 } } // 无效配置
      }
      
      await ViewPreferenceService.importConfig(importConfig)
      
      // 应该保存视图模式但跳过无效的布局配置
      expect(mockLocalStorage.setItem).toHaveBeenCalledWith('bookmark-view-mode', 'row')
      expect(mockLocalStorage.setItem).toHaveBeenCalledTimes(1)
    })
  })
})