// Card组件导入测试

import React from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../src/components/ui/card'

/**
 * Card组件导入测试组件
 * 用于验证Card组件及其子组件是否能正确导入和渲染
 */
const CardImportTest: React.FC = () => {
  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      <div className="max-w-2xl mx-auto space-y-6">
        {/* 基础Card测试 */}
        <Card>
          <CardHeader>
            <CardTitle className="text-green-600">✅ Card导入测试成功</CardTitle>
            <CardDescription>
              测试Card组件是否能正确导入和使用
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-gray-700">
              如果您能看到这个内容，说明Card组件导入成功！
            </p>
            <div className="mt-4 p-3 bg-blue-50 rounded-md">
              <p className="text-sm text-blue-800">
                <strong>测试项目：</strong>
              </p>
              <ul className="mt-2 text-sm text-blue-700 space-y-1">
                <li>• Card 组件导入</li>
                <li>• CardHeader 组件导入</li>
                <li>• CardTitle 组件导入</li>
                <li>• CardDescription 组件导入</li>
                <li>• CardContent 组件导入</li>
              </ul>
            </div>
          </CardContent>
        </Card>

        {/* 样式测试Card */}
        <Card className="border-green-200 bg-green-50">
          <CardHeader>
            <CardTitle className="text-green-700">样式测试</CardTitle>
            <CardDescription className="text-green-600">
              测试Card组件的样式定制能力
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-green-800">
              这个Card使用了自定义的绿色主题样式，验证className属性是否正常工作。
            </p>
          </CardContent>
        </Card>

        {/* 功能测试Card */}
        <Card>
          <CardHeader>
            <CardTitle>功能测试</CardTitle>
            <CardDescription>
              测试Card组件在实际使用场景中的表现
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <h4 className="font-medium text-gray-900 mb-2">测试结果：</h4>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div className="flex items-center space-x-2">
                  <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                  <span>组件导入正常</span>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                  <span>样式应用正常</span>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                  <span>布局结构正确</span>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                  <span>TypeScript类型正确</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

export default CardImportTest