// LayoutUtils 工具类单元测试

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { LayoutUtils } from '../src/utils/layoutUtils'

describe('LayoutUtils', () => {
  beforeEach(() => {
    // 模拟DOM环境
    const mockResizeObserver = vi.fn(() => ({
      observe: vi.fn(),
      disconnect: vi.fn()
    }))
    
    global.window = {
      innerWidth: 1024,
      innerHeight: 768,
      ResizeObserver: mockResizeObserver,
      addEventListener: vi.fn(),
      removeEventListener: vi.fn(),
      getComputedStyle: vi.fn(() => ({
        paddingLeft: '0px',
        paddingRight: '0px',
        paddingTop: '0px',
        paddingBottom: '0px',
        borderLeftWidth: '0px',
        borderRightWidth: '0px',
        borderTopWidth: '0px',
        borderBottomWidth: '0px'
      }))
    }
    
    // 确保全局ResizeObserver可用
    global.ResizeObserver = mockResizeObserver

    global.document = {
      createElement: vi.fn()
    }

    // 清理定时器和观察器
    LayoutUtils.clearAllDebounceTimers()
    LayoutUtils.clearAllResizeObservers()
  })

  afterEach(() => {
    vi.clearAllMocks()
    vi.clearAllTimers()
    LayoutUtils.clearAllDebounceTimers()
    LayoutUtils.clearAllResizeObservers()
  })

  describe('calculateOptimalColumns', () => {
    it('应该计算最优列数', () => {
      const result = LayoutUtils.calculateOptimalColumns(800, 200, 16)
      expect(result).toBe(3) // (800 - 16) / (200 + 16) ≈ 3.6，向下取整为3
    })

    it('应该处理容器宽度为0的情况', () => {
      const result = LayoutUtils.calculateOptimalColumns(0, 200, 16)
      expect(result).toBe(1)
    })

    it('应该处理最小项目宽度为0的情况', () => {
      const result = LayoutUtils.calculateOptimalColumns(800, 0, 16)
      expect(result).toBe(1)
    })

    it('应该限制最大列数', () => {
      const result = LayoutUtils.calculateOptimalColumns(2000, 100, 16, 4)
      expect(result).toBeLessThanOrEqual(4)
    })

    it('应该至少返回1列', () => {
      const result = LayoutUtils.calculateOptimalColumns(50, 200, 16)
      expect(result).toBe(1)
    })
  })

  describe('calculateLayout', () => {
    it('应该计算布局参数', () => {
      const result = LayoutUtils.calculateLayout(800, 3, 16, 1.5)
      
      expect(result).toHaveProperty('columns', 3)
      expect(result).toHaveProperty('itemWidth')
      expect(result).toHaveProperty('itemHeight')
      expect(result).toHaveProperty('gap', 16)
      expect(result).toHaveProperty('totalWidth', 800)
      
      // 验证计算结果
      const expectedItemWidth = (800 - 2 * 16) / 3 // (总宽度 - 间距) / 列数
      expect(result.itemWidth).toBeCloseTo(expectedItemWidth, 1)
      expect(result.itemHeight).toBeCloseTo(expectedItemWidth * 1.5, 1)
    })

    it('应该处理单列布局', () => {
      const result = LayoutUtils.calculateLayout(400, 1, 0, 1.2)
      
      expect(result.columns).toBe(1)
      expect(result.itemWidth).toBe(400)
      expect(result.itemHeight).toBe(480) // 400 * 1.2
      expect(result.gap).toBe(0)
    })

    it('应该确保项目尺寸不为负数', () => {
      const result = LayoutUtils.calculateLayout(100, 10, 20, 1.0)
      
      expect(result.itemWidth).toBeGreaterThanOrEqual(0)
      expect(result.itemHeight).toBeGreaterThanOrEqual(0)
    })
  })

  describe('debouncedLayoutUpdate', () => {
    beforeEach(() => {
      vi.useFakeTimers()
    })

    afterEach(() => {
      vi.useRealTimers()
    })

    it('应该防抖执行回调函数', () => {
      const callback = vi.fn()
      const debouncedFn = LayoutUtils.debouncedLayoutUpdate('test', callback, { delay: 100 })
      
      // 快速调用多次
      debouncedFn()
      debouncedFn()
      debouncedFn()
      
      // 回调不应该立即执行
      expect(callback).not.toHaveBeenCalled()
      
      // 等待防抖延迟
      vi.advanceTimersByTime(100)
      
      // 回调应该只执行一次
      expect(callback).toHaveBeenCalledTimes(1)
    })

    it('应该支持立即执行', () => {
      const callback = vi.fn()
      const debouncedFn = LayoutUtils.debouncedLayoutUpdate('test-immediate', callback, { 
        delay: 100, 
        immediate: true 
      })
      
      debouncedFn()
      
      // 应该立即执行一次
      expect(callback).toHaveBeenCalledTimes(1)
      
      // 再次调用
      debouncedFn()
      
      // 等待防抖延迟
      vi.advanceTimersByTime(100)
      
      // 总共应该执行两次
      expect(callback).toHaveBeenCalledTimes(2)
    })

    it('应该为不同的key维护独立的防抖', () => {
      const callback1 = vi.fn()
      const callback2 = vi.fn()
      
      const debouncedFn1 = LayoutUtils.debouncedLayoutUpdate('test1', callback1, { delay: 100 })
      const debouncedFn2 = LayoutUtils.debouncedLayoutUpdate('test2', callback2, { delay: 100 })
      
      debouncedFn1()
      debouncedFn2()
      
      vi.advanceTimersByTime(100)
      
      expect(callback1).toHaveBeenCalledTimes(1)
      expect(callback2).toHaveBeenCalledTimes(1)
    })
  })

  describe('observeContainerResize', () => {
    it('应该创建ResizeObserver监听容器变化', () => {
      const mockElement = {
        getBoundingClientRect: vi.fn(() => ({ width: 800, height: 600 }))
      }
      const callback = vi.fn()
      
      const cleanup = LayoutUtils.observeContainerResize(mockElement, callback)
      
      expect(window.ResizeObserver).toHaveBeenCalled()
      expect(typeof cleanup).toBe('function')
    })

    it('应该在立即模式下立即触发回调', () => {
      const mockElement = {
        getBoundingClientRect: vi.fn(() => ({ width: 800, height: 600 }))
      }
      const callback = vi.fn()
      
      LayoutUtils.observeContainerResize(mockElement, callback, { immediate: true })
      
      expect(callback).toHaveBeenCalledWith({ width: 800, height: 600 })
    })

    it('应该在不支持ResizeObserver时使用降级方案', () => {
      // 临时移除ResizeObserver
      const originalResizeObserver = window.ResizeObserver
      const originalGlobalResizeObserver = global.ResizeObserver
      delete window.ResizeObserver
      delete global.ResizeObserver
      
      const mockElement = {
        getBoundingClientRect: vi.fn(() => ({ width: 800, height: 600 }))
      }
      const callback = vi.fn()
      
      const cleanup = LayoutUtils.observeContainerResize(mockElement, callback)
      
      expect(typeof cleanup).toBe('function')
      expect(window.addEventListener).toHaveBeenCalledWith('resize', expect.any(Function))
      
      // 恢复ResizeObserver
      window.ResizeObserver = originalResizeObserver
      global.ResizeObserver = originalGlobalResizeObserver
    })
  })

  describe('getCurrentBreakpoint', () => {
    it('应该返回正确的断点', () => {
      expect(LayoutUtils.getCurrentBreakpoint(500)).toBe('xs')
      expect(LayoutUtils.getCurrentBreakpoint(700)).toBe('sm')
      expect(LayoutUtils.getCurrentBreakpoint(900)).toBe('md')
      expect(LayoutUtils.getCurrentBreakpoint(1100)).toBe('lg')
      expect(LayoutUtils.getCurrentBreakpoint(1300)).toBe('xl')
    })

    it('应该支持自定义断点', () => {
      const customBreakpoints = {
        sm: 600,
        md: 800,
        lg: 1000
      }
      
      expect(LayoutUtils.getCurrentBreakpoint(700, customBreakpoints)).toBe('sm')
      expect(LayoutUtils.getCurrentBreakpoint(900, customBreakpoints)).toBe('md')
    })
  })

  describe('设备类型检测', () => {
    it('应该正确检测移动设备', () => {
      expect(LayoutUtils.isMobile(600)).toBe(true)
      expect(LayoutUtils.isMobile(800)).toBe(false)
    })

    it('应该正确检测平板设备', () => {
      expect(LayoutUtils.isTablet(800)).toBe(true)
      expect(LayoutUtils.isTablet(600)).toBe(false)
      expect(LayoutUtils.isTablet(1100)).toBe(false)
    })

    it('应该正确检测桌面设备', () => {
      expect(LayoutUtils.isDesktop(1100)).toBe(true)
      expect(LayoutUtils.isDesktop(800)).toBe(false)
    })
  })

  describe('calculateVirtualScrollRange', () => {
    it('应该计算虚拟滚动范围', () => {
      const result = LayoutUtils.calculateVirtualScrollRange(400, 50, 100, 2)
      
      expect(result).toHaveProperty('start')
      expect(result).toHaveProperty('end')
      expect(result).toHaveProperty('visibleStart')
      expect(result).toHaveProperty('visibleEnd')
      
      expect(result.start).toBeGreaterThanOrEqual(0)
      expect(result.end).toBeGreaterThan(result.start)
      expect(result.visibleStart).toBeGreaterThanOrEqual(result.start)
      expect(result.visibleEnd).toBeLessThanOrEqual(result.end)
    })

    it('应该处理滚动到顶部的情况', () => {
      const result = LayoutUtils.calculateVirtualScrollRange(400, 50, 0, 2)
      
      expect(result.start).toBe(0)
      expect(result.visibleStart).toBe(0)
    })

    it('应该包含预渲染项目', () => {
      const overscan = 3
      const result = LayoutUtils.calculateVirtualScrollRange(400, 50, 100, overscan)
      
      // 开始位置应该考虑预渲染
      expect(result.start).toBeLessThanOrEqual(result.visibleStart)
      expect(result.end).toBeGreaterThanOrEqual(result.visibleEnd)
    })
  })

  describe('getElementSize', () => {
    it('应该获取元素的精确尺寸', () => {
      const mockElement = {
        getBoundingClientRect: vi.fn(() => ({ width: 200, height: 100 }))
      }
      
      const result = LayoutUtils.getElementSize(mockElement)
      
      expect(result).toHaveProperty('width', 200)
      expect(result).toHaveProperty('height', 100)
      expect(result).toHaveProperty('contentWidth')
      expect(result).toHaveProperty('contentHeight')
      expect(result).toHaveProperty('paddingWidth')
      expect(result).toHaveProperty('paddingHeight')
      expect(result).toHaveProperty('borderWidth')
      expect(result).toHaveProperty('borderHeight')
    })

    it('应该正确计算内容尺寸', () => {
      const mockElement = {
        getBoundingClientRect: vi.fn(() => ({ width: 200, height: 100 }))
      }
      
      // 模拟有内边距和边框的情况
      window.getComputedStyle = vi.fn(() => ({
        paddingLeft: '10px',
        paddingRight: '10px',
        paddingTop: '5px',
        paddingBottom: '5px',
        borderLeftWidth: '2px',
        borderRightWidth: '2px',
        borderTopWidth: '1px',
        borderBottomWidth: '1px'
      }))
      
      const result = LayoutUtils.getElementSize(mockElement)
      
      expect(result.paddingWidth).toBe(20) // 10 + 10
      expect(result.paddingHeight).toBe(10) // 5 + 5
      expect(result.borderWidth).toBe(4) // 2 + 2
      expect(result.borderHeight).toBe(2) // 1 + 1
      expect(result.contentWidth).toBe(176) // 200 - 20 - 4
      expect(result.contentHeight).toBe(88) // 100 - 10 - 2
    })
  })

  describe('清理方法', () => {
    it('应该清理所有防抖定时器', () => {
      const callback = vi.fn()
      LayoutUtils.debouncedLayoutUpdate('test1', callback)
      LayoutUtils.debouncedLayoutUpdate('test2', callback)
      
      LayoutUtils.clearAllDebounceTimers()
      
      // 这里主要测试方法不会报错
      expect(() => LayoutUtils.clearAllDebounceTimers()).not.toThrow()
    })

    it('应该清理所有ResizeObserver', () => {
      const mockElement = {
        getBoundingClientRect: vi.fn(() => ({ width: 800, height: 600 }))
      }
      const callback = vi.fn()
      
      LayoutUtils.observeContainerResize(mockElement, callback)
      LayoutUtils.clearAllResizeObservers()
      
      // 这里主要测试方法不会报错
      expect(() => LayoutUtils.clearAllResizeObservers()).not.toThrow()
    })
  })
})