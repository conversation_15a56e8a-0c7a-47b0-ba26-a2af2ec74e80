// AI集成服务测试

import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest'
import { aiIntegrationService } from '../src/services/aiIntegrationService'
import { ChromeStorageService } from '../src/utils/chromeStorage'
import { aiProviderService } from '../src/services/aiProviderService'
import { aiModelService } from '../src/services/aiModelService'

// Mock依赖
vi.mock('../src/utils/chromeStorage')
vi.mock('../src/services/aiProviderService')
vi.mock('../src/services/aiModelService')

describe('AIIntegrationService', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('getSupportedProviders', () => {
    it('应该返回支持的提供商列表', () => {
      const providers = aiIntegrationService.getSupportedProviders()
      
      expect(providers).toBeInstanceOf(Array)
      expect(providers.length).toBeGreaterThan(0)
      
      // 检查必需的提供商
      const providerIds = providers.map(p => p.id)
      expect(providerIds).toContain('ollama')
      expect(providerIds).toContain('openai')
      expect(providerIds).toContain('claude')
      expect(providerIds).toContain('openrouter')
    })

    it('每个提供商应该包含必需的字段', () => {
      const providers = aiIntegrationService.getSupportedProviders()
      
      providers.forEach(provider => {
        expect(provider).toHaveProperty('id')
        expect(provider).toHaveProperty('name')
        expect(provider).toHaveProperty('description')
        expect(provider).toHaveProperty('type')
        expect(provider).toHaveProperty('defaultBaseUrl')
        expect(provider).toHaveProperty('requiresApiKey')
        expect(provider).toHaveProperty('supportedFeatures')
        
        expect(typeof provider.id).toBe('string')
        expect(typeof provider.name).toBe('string')
        expect(typeof provider.description).toBe('string')
        expect(typeof provider.requiresApiKey).toBe('boolean')
        expect(Array.isArray(provider.supportedFeatures)).toBe(true)
      })
    })
  })

  describe('getProviderInfo', () => {
    it('应该返回指定提供商的信息', () => {
      const ollamaInfo = aiIntegrationService.getProviderInfo('ollama')
      
      expect(ollamaInfo).not.toBeNull()
      expect(ollamaInfo?.id).toBe('ollama')
      expect(ollamaInfo?.name).toBe('Ollama')
      expect(ollamaInfo?.requiresApiKey).toBe(false)
    })

    it('不存在的提供商应该返回null', () => {
      const info = aiIntegrationService.getProviderInfo('nonexistent')
      expect(info).toBeNull()
    })
  })

  describe('getConfiguredProviders', () => {
    it('应该返回已配置的提供商列表', async () => {
      const mockProviders = [
        {
          id: 'test_1',
          name: 'Test Provider',
          type: 'ollama',
          baseUrl: 'http://localhost:11434',
          enabled: true,
          createdAt: '2024-01-01T00:00:00.000Z',
          updatedAt: '2024-01-01T00:00:00.000Z'
        }
      ]

      vi.mocked(ChromeStorageService.getSyncSetting).mockResolvedValue(mockProviders)

      const providers = await aiIntegrationService.getConfiguredProviders()
      
      expect(providers).toHaveLength(1)
      expect(providers[0].id).toBe('test_1')
      expect(providers[0].createdAt).toBeInstanceOf(Date)
      expect(providers[0].updatedAt).toBeInstanceOf(Date)
    })

    it('存储错误时应该返回空数组', async () => {
      vi.mocked(ChromeStorageService.getSyncSetting).mockRejectedValue(new Error('Storage error'))

      const providers = await aiIntegrationService.getConfiguredProviders()
      expect(providers).toEqual([])
    })
  })

  describe('configureProvider', () => {
    it('应该成功配置新的提供商', async () => {
      vi.mocked(ChromeStorageService.getSyncSetting).mockResolvedValue([])
      vi.mocked(ChromeStorageService.saveSyncSetting).mockResolvedValue(undefined)

      const config = {
        name: 'Test Ollama',
        type: 'ollama' as const,
        baseUrl: 'http://localhost:11434',
        enabled: true
      }

      await expect(aiIntegrationService.configureProvider(config)).resolves.toBeUndefined()
      
      expect(ChromeStorageService.saveSyncSetting).toHaveBeenCalledWith(
        'ai_providers',
        expect.arrayContaining([
          expect.objectContaining({
            name: 'Test Ollama',
            type: 'ollama',
            baseUrl: 'http://localhost:11434'
          })
        ])
      )
    })

    it('不支持的提供商类型应该抛出错误', async () => {
      const config = {
        name: 'Invalid Provider',
        type: 'invalid' as any,
        baseUrl: 'http://example.com',
        enabled: true
      }

      await expect(aiIntegrationService.configureProvider(config))
        .rejects.toThrow('不支持的提供商类型: invalid')
    })

    it('空名称应该抛出错误', async () => {
      const config = {
        name: '',
        type: 'ollama' as const,
        baseUrl: 'http://localhost:11434',
        enabled: true
      }

      await expect(aiIntegrationService.configureProvider(config))
        .rejects.toThrow('提供商名称不能为空')
    })

    it('无效URL应该抛出错误', async () => {
      const config = {
        name: 'Test Provider',
        type: 'ollama' as const,
        baseUrl: 'invalid-url',
        enabled: true
      }

      await expect(aiIntegrationService.configureProvider(config))
        .rejects.toThrow('API基础URL格式无效')
    })

    it('需要API密钥的提供商缺少密钥应该抛出错误', async () => {
      const config = {
        name: 'Test OpenAI',
        type: 'openai' as const,
        baseUrl: 'https://api.openai.com/v1',
        enabled: true
      }

      await expect(aiIntegrationService.configureProvider(config))
        .rejects.toThrow('该提供商需要API密钥')
    })

    it('重复名称应该抛出错误', async () => {
      const existingProviders = [
        {
          id: 'existing_1',
          name: 'Existing Provider',
          type: 'ollama',
          baseUrl: 'http://localhost:11434',
          enabled: true,
          createdAt: new Date(),
          updatedAt: new Date()
        }
      ]

      vi.mocked(ChromeStorageService.getSyncSetting).mockResolvedValue(existingProviders)

      const config = {
        name: 'Existing Provider',
        type: 'ollama' as const,
        baseUrl: 'http://localhost:11435',
        enabled: true
      }

      await expect(aiIntegrationService.configureProvider(config))
        .rejects.toThrow('已存在同名的提供商配置')
    })
  })

  describe('updateProvider', () => {
    it('应该成功更新提供商配置', async () => {
      const existingProviders = [
        {
          id: 'test_1',
          name: 'Test Provider',
          type: 'ollama',
          baseUrl: 'http://localhost:11434',
          enabled: true,
          createdAt: new Date(),
          updatedAt: new Date()
        }
      ]

      vi.mocked(ChromeStorageService.getSyncSetting).mockResolvedValue(existingProviders)
      vi.mocked(ChromeStorageService.saveSyncSetting).mockResolvedValue(undefined)

      const updates = {
        name: 'Updated Provider',
        baseUrl: 'http://localhost:11435'
      }

      await expect(aiIntegrationService.updateProvider('test_1', updates)).resolves.toBeUndefined()
      
      expect(ChromeStorageService.saveSyncSetting).toHaveBeenCalledWith(
        'ai_providers',
        expect.arrayContaining([
          expect.objectContaining({
            id: 'test_1',
            name: 'Updated Provider',
            baseUrl: 'http://localhost:11435'
          })
        ])
      )
    })

    it('不存在的提供商ID应该抛出错误', async () => {
      vi.mocked(ChromeStorageService.getSyncSetting).mockResolvedValue([])

      await expect(aiIntegrationService.updateProvider('nonexistent', {}))
        .rejects.toThrow('未找到指定的提供商配置')
    })
  })

  describe('removeProvider', () => {
    it('应该成功删除提供商配置', async () => {
      const existingProviders = [
        {
          id: 'test_1',
          name: 'Test Provider',
          type: 'ollama',
          baseUrl: 'http://localhost:11434',
          enabled: true,
          createdAt: new Date(),
          updatedAt: new Date()
        }
      ]

      vi.mocked(ChromeStorageService.getSyncSetting).mockResolvedValue(existingProviders)
      vi.mocked(ChromeStorageService.saveSyncSetting).mockResolvedValue(undefined)
      vi.mocked(aiModelService.clearModelCache).mockResolvedValue(undefined)

      await expect(aiIntegrationService.removeProvider('test_1')).resolves.toBeUndefined()
      
      expect(ChromeStorageService.saveSyncSetting).toHaveBeenCalledWith('ai_providers', [])
    })

    it('不存在的提供商ID应该抛出错误', async () => {
      vi.mocked(ChromeStorageService.getSyncSetting).mockResolvedValue([])

      await expect(aiIntegrationService.removeProvider('nonexistent'))
        .rejects.toThrow('未找到指定的提供商配置')
    })
  })

  describe('testConnection', () => {
    it('应该成功测试提供商连接', async () => {
      const existingProviders = [
        {
          id: 'test_1',
          name: 'Test Provider',
          type: 'ollama',
          baseUrl: 'http://localhost:11434',
          enabled: true,
          createdAt: new Date(),
          updatedAt: new Date()
        }
      ]

      const mockResult = {
        providerId: 'test_1',
        success: true,
        responseTime: 100,
        modelCount: 5,
        testedAt: new Date()
      }

      vi.mocked(ChromeStorageService.getSyncSetting).mockResolvedValue(existingProviders)
      vi.mocked(aiProviderService.testConnection).mockResolvedValue(mockResult)

      const result = await aiIntegrationService.testConnection('test_1')
      
      expect(result.success).toBe(true)
      expect(result.providerId).toBe('test_1')
      expect(aiProviderService.testConnection).toHaveBeenCalledWith(existingProviders[0])
    })

    it('不存在的提供商ID应该返回失败结果', async () => {
      vi.mocked(ChromeStorageService.getSyncSetting).mockResolvedValue([])

      const result = await aiIntegrationService.testConnection('nonexistent')
      
      expect(result.success).toBe(false)
      expect(result.error).toContain('未找到指定的提供商配置')
    })
  })

  describe('exportConfiguration', () => {
    it('应该导出配置并隐藏敏感信息', async () => {
      const mockProviders = [
        {
          id: 'test_1',
          name: 'Test Provider',
          type: 'openai',
          baseUrl: 'https://api.openai.com/v1',
          apiKey: 'secret-key',
          enabled: true,
          createdAt: new Date(),
          updatedAt: new Date()
        }
      ]

      vi.mocked(ChromeStorageService.getSyncSetting).mockResolvedValue(mockProviders)

      const exportedConfig = await aiIntegrationService.exportConfiguration()
      const parsedConfig = JSON.parse(exportedConfig)
      
      expect(parsedConfig.version).toBe('1.0')
      expect(parsedConfig.providers).toHaveLength(1)
      expect(parsedConfig.providers[0].apiKey).toBe('***已隐藏***')
      expect(parsedConfig.providers[0].name).toBe('Test Provider')
    })
  })

  describe('importConfiguration', () => {
    it('应该成功导入配置', async () => {
      const importConfig = {
        version: '1.0',
        providers: [
          {
            name: 'Imported Provider',
            type: 'ollama',
            baseUrl: 'http://localhost:11434',
            enabled: true
          }
        ]
      }

      vi.mocked(ChromeStorageService.getSyncSetting).mockResolvedValue([])
      vi.mocked(ChromeStorageService.saveSyncSetting).mockResolvedValue(undefined)

      await expect(aiIntegrationService.importConfiguration(JSON.stringify(importConfig)))
        .resolves.toBeUndefined()
    })

    it('无效的JSON应该抛出错误', async () => {
      await expect(aiIntegrationService.importConfiguration('invalid json'))
        .rejects.toThrow()
    })

    it('无效的配置格式应该抛出错误', async () => {
      const invalidConfig = {
        version: '1.0',
        // 缺少providers字段
      }

      await expect(aiIntegrationService.importConfiguration(JSON.stringify(invalidConfig)))
        .rejects.toThrow('无效的配置格式')
    })
  })
})