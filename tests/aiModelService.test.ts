// AI模型服务测试

import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest'
import { aiModelService } from '../src/services/aiModelService'
import { aiProviderService } from '../src/services/aiProviderService'
import { ChromeStorageService } from '../src/utils/chromeStorage'
import { AIModel, AIProviderConfig } from '../src/types/ai'

// Mock依赖
vi.mock('../src/services/aiProviderService')
vi.mock('../src/utils/chromeStorage')

describe('AIModelService', () => {
  const mockProvider: AIProviderConfig = {
    id: 'test_provider',
    name: 'Test Provider',
    type: 'ollama',
    baseUrl: 'http://localhost:11434',
    enabled: true,
    createdAt: new Date(),
    updatedAt: new Date()
  }

  const mockModels: AIModel[] = [
    {
      id: 'llama2:7b',
      name: 'llama2:7b',
      displayName: 'Llama 2 7B',
      description: 'Meta Llama 2 7B model',
      size: '3.8 GB',
      parameters: '7B',
      tags: ['llama', 'meta'],
      capabilities: ['chat', 'completion'],
      providerId: 'test_provider',
      isRecommended: true,
      isPopular: true
    },
    {
      id: 'qwen:14b',
      name: 'qwen:14b',
      displayName: 'Qwen 14B',
      description: 'Alibaba Qwen 14B model',
      size: '8.2 GB',
      parameters: '14B',
      tags: ['qwen', 'alibaba'],
      capabilities: ['chat', 'completion'],
      providerId: 'test_provider',
      isRecommended: true,
      isPopular: false
    },
    {
      id: 'mistral:7b',
      name: 'mistral:7b',
      displayName: 'Mistral 7B',
      description: 'Mistral AI 7B model',
      size: '4.1 GB',
      parameters: '7B',
      tags: ['mistral'],
      capabilities: ['chat'],
      providerId: 'test_provider',
      isRecommended: false,
      isPopular: true
    }
  ]

  beforeEach(() => {
    vi.clearAllMocks()
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('getModels', () => {
    it('应该从缓存返回模型列表', async () => {
      const cachedModels = mockModels.slice(0, 2)
      
      vi.mocked(ChromeStorageService.getLocalSetting).mockResolvedValue([
        {
          providerId: 'test_provider',
          models: cachedModels,
          cachedAt: new Date().toISOString(),
          expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString()
        }
      ])

      const models = await aiModelService.getModels(mockProvider, false)

      expect(models).toEqual(cachedModels)
      expect(aiProviderService.getModels).not.toHaveBeenCalled()
    })

    it('应该从API获取模型列表并缓存', async () => {
      vi.mocked(ChromeStorageService.getLocalSetting).mockResolvedValue([])
      vi.mocked(aiProviderService.getModels).mockResolvedValue(mockModels)
      vi.mocked(ChromeStorageService.saveLocalSetting).mockResolvedValue(undefined)

      const models = await aiModelService.getModels(mockProvider, false)

      expect(models).toEqual(mockModels)
      expect(aiProviderService.getModels).toHaveBeenCalledWith(mockProvider)
      expect(ChromeStorageService.saveLocalSetting).toHaveBeenCalledWith(
        'ai_models_cache',
        expect.arrayContaining([
          expect.objectContaining({
            providerId: 'test_provider',
            models: mockModels
          })
        ])
      )
    })

    it('强制刷新应该跳过缓存', async () => {
      vi.mocked(ChromeStorageService.getLocalSetting).mockResolvedValue([
        {
          providerId: 'test_provider',
          models: mockModels.slice(0, 1),
          cachedAt: new Date().toISOString(),
          expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString()
        }
      ])
      vi.mocked(aiProviderService.getModels).mockResolvedValue(mockModels)
      vi.mocked(ChromeStorageService.saveLocalSetting).mockResolvedValue(undefined)

      const models = await aiModelService.getModels(mockProvider, true)

      expect(models).toEqual(mockModels)
      expect(aiProviderService.getModels).toHaveBeenCalledWith(mockProvider)
    })

    it('API错误时应该返回缓存的模型', async () => {
      const cachedModels = mockModels.slice(0, 1)
      
      // 第一次调用返回有效缓存，第二次调用（在getCachedModels中）返回相同缓存
      vi.mocked(ChromeStorageService.getLocalSetting)
        .mockResolvedValueOnce([]) // 第一次调用（检查缓存）
        .mockResolvedValueOnce([   // 第二次调用（API错误后尝试获取缓存）
          {
            providerId: 'test_provider',
            models: cachedModels,
            cachedAt: new Date().toISOString(),
            expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString() // 未过期
          }
        ])
      vi.mocked(aiProviderService.getModels).mockRejectedValue(new Error('API Error'))

      const models = await aiModelService.getModels(mockProvider, false)

      expect(models).toEqual(cachedModels)
    })

    it('没有缓存且API错误时应该返回空数组', async () => {
      vi.mocked(ChromeStorageService.getLocalSetting).mockResolvedValue([])
      vi.mocked(aiProviderService.getModels).mockRejectedValue(new Error('API Error'))

      const models = await aiModelService.getModels(mockProvider, false)

      expect(models).toEqual([])
    })
  })

  describe('searchModels', () => {
    it('应该按名称搜索模型', () => {
      const results = aiModelService.searchModels(mockModels, 'llama')

      expect(results).toHaveLength(1)
      expect(results[0].id).toBe('llama2:7b')
    })

    it('应该按显示名称搜索模型', () => {
      const results = aiModelService.searchModels(mockModels, 'Qwen')

      expect(results).toHaveLength(1)
      expect(results[0].id).toBe('qwen:14b')
    })

    it('应该按描述搜索模型', () => {
      const results = aiModelService.searchModels(mockModels, 'Meta')

      expect(results).toHaveLength(1)
      expect(results[0].id).toBe('llama2:7b')
    })

    it('应该按标签搜索模型', () => {
      const results = aiModelService.searchModels(mockModels, 'alibaba')

      expect(results).toHaveLength(1)
      expect(results[0].id).toBe('qwen:14b')
    })

    it('应该按能力搜索模型', () => {
      const results = aiModelService.searchModels(mockModels, 'completion')

      expect(results).toHaveLength(2)
      expect(results.map(r => r.id)).toContain('llama2:7b')
      expect(results.map(r => r.id)).toContain('qwen:14b')
    })

    it('空搜索词应该返回所有模型', () => {
      const results = aiModelService.searchModels(mockModels, '')

      expect(results).toEqual(mockModels)
    })

    it('无匹配结果应该返回空数组', () => {
      const results = aiModelService.searchModels(mockModels, 'nonexistent')

      expect(results).toEqual([])
    })

    it('应该按相关性排序搜索结果', () => {
      const results = aiModelService.searchModels(mockModels, '7b')

      // llama2:7b应该排在前面，因为名称完全匹配
      expect(results[0].id).toBe('llama2:7b')
      expect(results[1].id).toBe('mistral:7b')
    })
  })

  describe('filterModels', () => {
    it('应该按大小筛选模型', () => {
      const results = aiModelService.filterModels(mockModels, {
        size: ['3.8 GB']
      })

      expect(results).toHaveLength(1)
      expect(results[0].id).toBe('llama2:7b')
    })

    it('应该按类型筛选模型', () => {
      const results = aiModelService.filterModels(mockModels, {
        type: ['completion']
      })

      expect(results).toHaveLength(2)
      expect(results.map(r => r.id)).toContain('llama2:7b')
      expect(results.map(r => r.id)).toContain('qwen:14b')
    })

    it('应该按能力筛选模型', () => {
      const results = aiModelService.filterModels(mockModels, {
        capabilities: ['chat', 'completion']
      })

      expect(results).toHaveLength(2)
      expect(results.map(r => r.id)).toContain('llama2:7b')
      expect(results.map(r => r.id)).toContain('qwen:14b')
    })

    it('应该按标签筛选模型', () => {
      const results = aiModelService.filterModels(mockModels, {
        tags: ['llama']
      })

      expect(results).toHaveLength(1)
      expect(results[0].id).toBe('llama2:7b')
    })

    it('应该筛选推荐模型', () => {
      const results = aiModelService.filterModels(mockModels, {
        isRecommended: true
      })

      expect(results).toHaveLength(2)
      expect(results.map(r => r.id)).toContain('llama2:7b')
      expect(results.map(r => r.id)).toContain('qwen:14b')
    })

    it('应该筛选热门模型', () => {
      const results = aiModelService.filterModels(mockModels, {
        isPopular: true
      })

      expect(results).toHaveLength(2)
      expect(results.map(r => r.id)).toContain('llama2:7b')
      expect(results.map(r => r.id)).toContain('mistral:7b')
    })

    it('应该支持多条件筛选', () => {
      const results = aiModelService.filterModels(mockModels, {
        isRecommended: true,
        isPopular: true
      })

      expect(results).toHaveLength(1)
      expect(results[0].id).toBe('llama2:7b')
    })

    it('空筛选条件应该返回所有模型', () => {
      const results = aiModelService.filterModels(mockModels, {})

      expect(results).toEqual(mockModels)
    })
  })

  describe('cacheModels', () => {
    it('应该成功缓存模型列表', async () => {
      vi.mocked(ChromeStorageService.getLocalSetting).mockResolvedValue([])
      vi.mocked(ChromeStorageService.saveLocalSetting).mockResolvedValue(undefined)

      await aiModelService.cacheModels('test_provider', mockModels)

      expect(ChromeStorageService.saveLocalSetting).toHaveBeenCalledWith(
        'ai_models_cache',
        expect.arrayContaining([
          expect.objectContaining({
            providerId: 'test_provider',
            models: mockModels
          })
        ])
      )
    })

    it('应该更新已存在的缓存项', async () => {
      const existingCache = [
        {
          providerId: 'test_provider',
          models: mockModels.slice(0, 1),
          cachedAt: new Date(),
          expiresAt: new Date()
        }
      ]

      vi.mocked(ChromeStorageService.getLocalSetting).mockResolvedValue(existingCache)
      vi.mocked(ChromeStorageService.saveLocalSetting).mockResolvedValue(undefined)

      await aiModelService.cacheModels('test_provider', mockModels)

      expect(ChromeStorageService.saveLocalSetting).toHaveBeenCalledWith(
        'ai_models_cache',
        expect.arrayContaining([
          expect.objectContaining({
            providerId: 'test_provider',
            models: mockModels
          })
        ])
      )
    })
  })

  describe('getCachedModels', () => {
    it('应该返回有效的缓存模型', async () => {
      const cacheItem = {
        providerId: 'test_provider',
        models: mockModels,
        cachedAt: new Date().toISOString(),
        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString()
      }

      vi.mocked(ChromeStorageService.getLocalSetting).mockResolvedValue([cacheItem])

      const models = await aiModelService.getCachedModels('test_provider')

      expect(models).toEqual(mockModels)
    })

    it('过期缓存应该返回null并清理', async () => {
      const cacheItem = {
        providerId: 'test_provider',
        models: mockModels,
        cachedAt: new Date().toISOString(),
        expiresAt: new Date(Date.now() - 1000).toISOString() // 已过期
      }

      vi.mocked(ChromeStorageService.getLocalSetting).mockResolvedValue([cacheItem])
      vi.mocked(ChromeStorageService.saveLocalSetting).mockResolvedValue(undefined)

      const models = await aiModelService.getCachedModels('test_provider')

      expect(models).toBeNull()
      expect(ChromeStorageService.saveLocalSetting).toHaveBeenCalledWith('ai_models_cache', [])
    })

    it('不存在的提供商应该返回null', async () => {
      vi.mocked(ChromeStorageService.getLocalSetting).mockResolvedValue([])

      const models = await aiModelService.getCachedModels('nonexistent')

      expect(models).toBeNull()
    })
  })

  describe('clearModelCache', () => {
    it('应该清理指定提供商的缓存', async () => {
      const cache = [
        {
          providerId: 'provider1',
          models: mockModels.slice(0, 1),
          cachedAt: new Date(),
          expiresAt: new Date()
        },
        {
          providerId: 'provider2',
          models: mockModels.slice(1, 2),
          cachedAt: new Date(),
          expiresAt: new Date()
        }
      ]

      vi.mocked(ChromeStorageService.getLocalSetting).mockResolvedValue(cache)
      vi.mocked(ChromeStorageService.saveLocalSetting).mockResolvedValue(undefined)

      await aiModelService.clearModelCache('provider1')

      expect(ChromeStorageService.saveLocalSetting).toHaveBeenCalledWith(
        'ai_models_cache',
        expect.arrayContaining([
          expect.objectContaining({ providerId: 'provider2' })
        ])
      )
    })

    it('应该清理所有缓存', async () => {
      vi.mocked(ChromeStorageService.saveLocalSetting).mockResolvedValue(undefined)

      await aiModelService.clearModelCache()

      expect(ChromeStorageService.saveLocalSetting).toHaveBeenCalledWith('ai_models_cache', [])
    })
  })

  describe('getRecommendedModels', () => {
    it('应该返回聊天场景的推荐模型', () => {
      const models = aiModelService.getRecommendedModels('chat')

      expect(models).toHaveLength(3)
      expect(models.map(m => m.id)).toContain('gpt-4')
      expect(models.map(m => m.id)).toContain('claude-3-opus')
      expect(models.map(m => m.id)).toContain('claude-3-sonnet')
      expect(models.every(m => m.isRecommended)).toBe(true)
    })

    it('应该返回编程场景的推荐模型', () => {
      const models = aiModelService.getRecommendedModels('coding')

      expect(models).toHaveLength(3)
      expect(models.map(m => m.id)).toContain('gpt-4')
      expect(models.map(m => m.id)).toContain('deepseek-coder')
      expect(models.map(m => m.id)).toContain('claude-3-opus')
    })

    it('未知场景应该返回通用推荐模型', () => {
      const models = aiModelService.getRecommendedModels('unknown')

      expect(models).toHaveLength(3)
      expect(models.map(m => m.id)).toContain('gpt-3.5-turbo')
      expect(models.map(m => m.id)).toContain('claude-3-haiku')
      expect(models.map(m => m.id)).toContain('llama2')
    })
  })

  describe('getPopularModels', () => {
    it('应该返回热门模型列表', () => {
      const models = aiModelService.getPopularModels()

      expect(models.length).toBeGreaterThan(0)
      expect(models.every(m => m.isPopular)).toBe(true)
      expect(models.map(m => m.id)).toContain('gpt-3.5-turbo')
      expect(models.map(m => m.id)).toContain('gpt-4')
    })
  })

  describe('cleanExpiredCache', () => {
    it('应该清理过期的缓存项', async () => {
      const cache = [
        {
          providerId: 'provider1',
          models: mockModels.slice(0, 1),
          cachedAt: new Date().toISOString(),
          expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString() // 未过期
        },
        {
          providerId: 'provider2',
          models: mockModels.slice(1, 2),
          cachedAt: new Date().toISOString(),
          expiresAt: new Date(Date.now() - 1000).toISOString() // 已过期
        }
      ]

      vi.mocked(ChromeStorageService.getLocalSetting).mockResolvedValue(cache)
      vi.mocked(ChromeStorageService.saveLocalSetting).mockResolvedValue(undefined)

      const expiredCount = await aiModelService.cleanExpiredCache()

      expect(expiredCount).toBe(1)
      expect(ChromeStorageService.saveLocalSetting).toHaveBeenCalledWith(
        'ai_models_cache',
        expect.arrayContaining([
          expect.objectContaining({ providerId: 'provider1' })
        ])
      )
    })

    it('没有过期缓存时应该返回0', async () => {
      const cache = [
        {
          providerId: 'provider1',
          models: mockModels,
          cachedAt: new Date().toISOString(),
          expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString()
        }
      ]

      vi.mocked(ChromeStorageService.getLocalSetting).mockResolvedValue(cache)

      const expiredCount = await aiModelService.cleanExpiredCache()

      expect(expiredCount).toBe(0)
      expect(ChromeStorageService.saveLocalSetting).not.toHaveBeenCalled()
    })
  })

  describe('聚合服务模型搜索', () => {
    const aggregateModels: AIModel[] = [
      {
        id: 'openai/gpt-4',
        name: 'openai/gpt-4',
        displayName: 'GPT-4',
        description: 'OpenAI最先进的大型语言模型，具有出色的推理和创作能力 (文本模型, 支持8,192个token的上下文)\n\n💰 定价: 输入: $30000.00/1M tokens, 输出: $60000.00/1M tokens',
        size: '1.76T',
        parameters: '8192 tokens',
        tags: ['OpenRouter', 'openai', 'text', 'cl100k_base'],
        capabilities: ['chat', 'completion'],
        providerId: 'openrouter',
        isRecommended: true,
        isPopular: true,
        maxTokens: 8192,
        contextLength: 8192
      },
      {
        id: 'anthropic/claude-3-opus',
        name: 'anthropic/claude-3-opus',
        displayName: 'Claude 3 Opus',
        description: 'Anthropic开发的安全、有用的AI助手 (多模态模型, 支持200,000个token的上下文)\n\n💰 定价: 输入: $15000.00/1M tokens, 输出: $75000.00/1M tokens',
        parameters: '200000 tokens',
        tags: ['OpenRouter', 'anthropic', 'multimodal', 'claude'],
        capabilities: ['chat', 'completion', 'vision', 'image-analysis', 'long-context'],
        providerId: 'openrouter',
        isRecommended: true,
        isPopular: false,
        maxTokens: 200000,
        contextLength: 200000
      },
      {
        id: 'meta-llama/Llama-2-70b-chat-hf',
        name: 'meta-llama/Llama-2-70b-chat-hf',
        displayName: 'Llama 2 70B Chat',
        description: 'Meta开发的Llama 2大型语言模型，开源且性能优秀 (开源许可: custom, 支持4,096个token的上下文)\n\n💰 定价: 输入: $200.000/1M tokens, 输出: $200.000/1M tokens',
        size: '70B',
        parameters: '4096 tokens',
        tags: ['Together AI', '开源', 'meta-llama', 'custom', '对话优化'],
        capabilities: ['chat', 'completion', 'instruction-following'],
        providerId: 'together',
        isRecommended: true,
        isPopular: false,
        maxTokens: 4096,
        contextLength: 4096
      },
      {
        id: 'codellama/CodeLlama-34b-Python-hf',
        name: 'codellama/CodeLlama-34b-Python-hf',
        displayName: 'Code Llama 34B Python',
        description: '专门优化代码生成和编程任务的语言模型 (开源许可: custom, 支持16,384个token的上下文)\n\n💰 定价: 输入: $200.000/1M tokens, 输出: $200.000/1M tokens',
        size: '34B',
        parameters: '16384 tokens',
        tags: ['Together AI', '开源', 'codellama', 'custom', '长上下文', '代码生成'],
        capabilities: ['chat', 'completion', 'coding'],
        providerId: 'together',
        isRecommended: true,
        isPopular: false,
        maxTokens: 16384,
        contextLength: 16384
      }
    ]

    describe('advancedSearchModels', () => {
      it('应该支持基础关键词搜索', () => {
        const results = aiModelService.advancedSearchModels(aggregateModels, {
          query: 'gpt'
        })

        expect(results).toHaveLength(1)
        expect(results[0].id).toBe('openai/gpt-4')
      })

      it('应该支持按提供商筛选', () => {
        const results = aiModelService.advancedSearchModels(aggregateModels, {
          provider: ['together']
        })

        expect(results).toHaveLength(2)
        expect(results.every(m => m.providerId === 'together')).toBe(true)
      })

      it('应该支持按能力筛选', () => {
        const results = aiModelService.advancedSearchModels(aggregateModels, {
          capabilities: ['coding']
        })

        expect(results).toHaveLength(1)
        expect(results[0].id).toBe('codellama/CodeLlama-34b-Python-hf')
      })

      it('应该支持按上下文长度筛选', () => {
        const results = aiModelService.advancedSearchModels(aggregateModels, {
          minContextLength: 100000
        })

        expect(results).toHaveLength(1)
        expect(results[0].id).toBe('anthropic/claude-3-opus')
      })

      it('应该支持多条件组合搜索', () => {
        const results = aiModelService.advancedSearchModels(aggregateModels, {
          query: 'llama',
          provider: ['together'],
          capabilities: ['chat'],
          includeRecommended: true
        })

        expect(results).toHaveLength(2)
        expect(results.every(m => m.providerId === 'together')).toBe(true)
        expect(results.every(m => m.isRecommended)).toBe(true)
        expect(results.every(m => m.capabilities?.includes('chat'))).toBe(true)
      })

      it('应该支持按相关性排序', () => {
        const results = aiModelService.advancedSearchModels(aggregateModels, {
          sortBy: 'relevance',
          sortOrder: 'desc'
        })

        expect(results).toHaveLength(4)
        // 推荐模型应该排在前面
        expect(results.filter(m => m.isRecommended).length).toBeGreaterThan(0)
      })

      it('应该支持按上下文长度排序', () => {
        const results = aiModelService.advancedSearchModels(aggregateModels, {
          sortBy: 'contextLength',
          sortOrder: 'desc'
        })

        expect(results).toHaveLength(4)
        expect(results[0].contextLength).toBeGreaterThanOrEqual(results[1].contextLength || 0)
      })
    })

    describe('searchModelsByCategory', () => {
      it('应该按编程分类搜索', () => {
        const results = aiModelService.searchModelsByCategory(aggregateModels, 'coding')

        expect(results).toHaveLength(1)
        expect(results[0].id).toBe('codellama/CodeLlama-34b-Python-hf')
      })

      it('应该按多模态分类搜索', () => {
        const results = aiModelService.searchModelsByCategory(aggregateModels, 'multimodal')

        expect(results).toHaveLength(1)
        expect(results[0].id).toBe('anthropic/claude-3-opus')
      })

      it('应该按长上下文分类搜索', () => {
        const results = aiModelService.searchModelsByCategory(aggregateModels, 'long-context')

        expect(results).toHaveLength(1)
        expect(results.every(m => m.contextLength && m.contextLength >= 16000)).toBe(true)
      })

      it('应该按开源分类搜索', () => {
        // 测试英文关键词
        const results1 = aiModelService.searchModelsByCategory(aggregateModels, 'open-source')
        expect(results1.length).toBeGreaterThan(0)
        
        // 测试中文关键词（直接搜索）
        const results2 = aiModelService.searchModels(aggregateModels, '开源')
        expect(results2.length).toBeGreaterThan(0)
        expect(results2.some(m => m.providerId === 'together')).toBe(true)
      })
    })

    describe('getSearchSuggestions', () => {
      it('应该提供搜索建议', () => {
        const suggestions = aiModelService.getSearchSuggestions(aggregateModels, 'gp')

        expect(suggestions).toContain('openai/gpt-4')
        expect(suggestions).toContain('GPT-4')
      })

      it('应该基于标签提供建议', () => {
        const suggestions = aiModelService.getSearchSuggestions(aggregateModels, 'open')

        expect(suggestions.some(s => s.includes('开源') || s.includes('OpenRouter'))).toBe(true)
      })

      it('应该限制建议数量', () => {
        const suggestions = aiModelService.getSearchSuggestions(aggregateModels, 'a')

        expect(suggestions.length).toBeLessThanOrEqual(10)
      })

      it('空查询应该返回空建议', () => {
        const suggestions = aiModelService.getSearchSuggestions(aggregateModels, '')

        expect(suggestions).toHaveLength(0)
      })
    })

    describe('getModelCategoryStats', () => {
      it('应该统计模型分类信息', () => {
        const stats = aiModelService.getModelCategoryStats(aggregateModels)

        expect(stats['provider_openrouter']).toBe(2)
        expect(stats['provider_together']).toBe(2)
        expect(stats['capability_chat']).toBe(4)
        expect(stats['capability_coding']).toBe(1)
        expect(stats['recommended']).toBe(4)
        expect(stats['popular']).toBe(1)
      })
    })

    describe('getSearchPerformanceMetrics', () => {
      it('应该返回搜索性能指标', () => {
        const metrics = aiModelService.getSearchPerformanceMetrics(aggregateModels, 'gpt')

        expect(metrics.searchTime).toBeGreaterThan(0)
        expect(metrics.resultCount).toBe(1)
        expect(metrics.totalModels).toBe(4)
      })
    })

    describe('聚合服务特殊搜索功能', () => {
      it('应该搜索定价信息', () => {
        const results = aiModelService.searchModels(aggregateModels, '定价')

        expect(results.length).toBeGreaterThan(0)
        expect(results.every(m => m.description?.includes('定价'))).toBe(true)
      })

      it('应该搜索上下文长度', () => {
        const results = aiModelService.searchModels(aggregateModels, '200000')

        expect(results).toHaveLength(1)
        expect(results[0].id).toBe('anthropic/claude-3-opus')
      })

      it('应该支持模糊匹配', () => {
        const results = aiModelService.searchModels(aggregateModels, 'cld')

        expect(results.some(m => m.id.includes('claude'))).toBe(true)
      })

      it('应该按聚合服务提供商加分', () => {
        const results = aiModelService.searchModels(aggregateModels, 'model')

        // 聚合服务的模型应该有额外加分
        expect(results.length).toBeGreaterThan(0)
      })
    })
  })
})