/**
 * aboutInfo.ts 测试文件
 * 测试关于页面数据配置的接口和默认数据
 */

import { describe, it, expect, beforeEach } from 'vitest'
import { 
  ExtensionInfo, 
  AboutPageData, 
  defaultAboutData 
} from '../../../src/options/data/aboutInfo'

describe('aboutInfo 数据配置测试', () => {
  describe('ExtensionInfo 接口测试', () => {
    it('应该包含必需的属性', () => {
      const extensionInfo: ExtensionInfo = {
        name: 'Test Extension',
        version: '1.0.0',
        description: 'Test description',
        developer: 'Test Developer'
      }

      expect(extensionInfo.name).toBe('Test Extension')
      expect(extensionInfo.version).toBe('1.0.0')
      expect(extensionInfo.description).toBe('Test description')
      expect(extensionInfo.developer).toBe('Test Developer')
    })

    it('应该支持可选属性', () => {
      const extensionInfo: ExtensionInfo = {
        name: 'Test Extension',
        version: '1.0.0',
        description: 'Test description',
        developer: 'Test Developer',
        website: 'https://example.com',
        email: '<EMAIL>',
        license: 'MIT',
        buildDate: '2025-01-01'
      }

      expect(extensionInfo.website).toBe('https://example.com')
      expect(extensionInfo.email).toBe('<EMAIL>')
      expect(extensionInfo.license).toBe('MIT')
      expect(extensionInfo.buildDate).toBe('2025-01-01')
    })
  })

  describe('AboutPageData 接口测试', () => {
    it('应该包含所有必需的嵌套对象', () => {
      const aboutData: AboutPageData = {
        extensionInfo: {
          name: 'Test Extension',
          version: '1.0.0',
          description: 'Test description',
          developer: 'Test Developer'
        },
        buildInfo: {
          buildDate: '2025-01-01',
          buildVersion: '1.0.0'
        },
        developerInfo: {
          name: 'Test Developer'
        },
        licenseInfo: {
          type: 'MIT License'
        }
      }

      expect(aboutData.extensionInfo).toBeDefined()
      expect(aboutData.buildInfo).toBeDefined()
      expect(aboutData.developerInfo).toBeDefined()
      expect(aboutData.licenseInfo).toBeDefined()
    })

    it('应该支持开发者信息的可选属性', () => {
      const aboutData: AboutPageData = {
        extensionInfo: {
          name: 'Test Extension',
          version: '1.0.0',
          description: 'Test description',
          developer: 'Test Developer'
        },
        buildInfo: {
          buildDate: '2025-01-01',
          buildVersion: '1.0.0'
        },
        developerInfo: {
          name: 'Test Developer',
          website: 'https://developer.com',
          email: '<EMAIL>'
        },
        licenseInfo: {
          type: 'MIT License',
          text: 'License text',
          url: 'https://license.url'
        }
      }

      expect(aboutData.developerInfo.website).toBe('https://developer.com')
      expect(aboutData.developerInfo.email).toBe('<EMAIL>')
      expect(aboutData.licenseInfo.text).toBe('License text')
      expect(aboutData.licenseInfo.url).toBe('https://license.url')
    })
  })

  describe('defaultAboutData 默认数据测试', () => {
    it('应该包含正确的扩展信息', () => {
      expect(defaultAboutData.extensionInfo.name).toBe('Universe Bag（乾坤袋）')
      expect(defaultAboutData.extensionInfo.version).toBe('1.0.0')
      expect(defaultAboutData.extensionInfo.description).toBe('智能收藏管理工具，支持AI自动分类和云端同步')
      expect(defaultAboutData.extensionInfo.developer).toBe('coffeebean')
    })

    it('应该包含正确的构建信息', () => {
      expect(defaultAboutData.buildInfo.buildVersion).toBe('1.0.0')
      expect(defaultAboutData.buildInfo.buildDate).toMatch(/^\d{4}-\d{2}-\d{2}$/)
      
      // 验证构建日期是今天的日期
      const today = new Date().toISOString().split('T')[0]
      expect(defaultAboutData.buildInfo.buildDate).toBe(today)
    })

    it('应该包含正确的开发者信息', () => {
      expect(defaultAboutData.developerInfo.name).toBe('coffeebean')
      expect(defaultAboutData.developerInfo.website).toBe('https://www.obsidian.vip')
      expect(defaultAboutData.developerInfo.communityUrl).toBe('https://obsidian.vip/zh/documentation/community.html')
    })

    it('应该包含正确的许可证信息', () => {
      expect(defaultAboutData.licenseInfo.type).toBe('')
      expect(defaultAboutData.licenseInfo.text).toBe('')
      expect(defaultAboutData.licenseInfo.url).toBe('')
    })

    it('应该是一个有效的 AboutPageData 对象', () => {
      // 类型检查 - 确保默认数据符合接口定义
      const data: AboutPageData = defaultAboutData
      expect(data).toBeDefined()
      
      // 验证所有必需属性都存在
      expect(data.extensionInfo).toBeDefined()
      expect(data.buildInfo).toBeDefined()
      expect(data.developerInfo).toBeDefined()
      expect(data.licenseInfo).toBeDefined()
    })
  })

  describe('数据完整性测试', () => {
    it('默认数据应该包含所有必需字段', () => {
      const requiredFields = [
        'extensionInfo.name',
        'extensionInfo.version', 
        'extensionInfo.description',
        'extensionInfo.developer',
        'buildInfo.buildDate',
        'buildInfo.buildVersion',
        'developerInfo.name'
      ]

      requiredFields.forEach(field => {
        const fieldPath = field.split('.')
        let value = defaultAboutData as any
        
        fieldPath.forEach(key => {
          value = value[key]
        })
        
        expect(value).toBeDefined()
        expect(value).not.toBe('')
      })
    })

    it('版本号应该符合语义化版本格式', () => {
      const versionRegex = /^\d+\.\d+\.\d+$/
      expect(defaultAboutData.extensionInfo.version).toMatch(versionRegex)
      expect(defaultAboutData.buildInfo.buildVersion).toMatch(versionRegex)
    })

    it('URL 字段应该是有效的 URL 格式', () => {
      const urlRegex = /^https?:\/\/.+/
      
      if (defaultAboutData.developerInfo.website) {
        expect(defaultAboutData.developerInfo.website).toMatch(urlRegex)
      }
      
      if (defaultAboutData.licenseInfo.url) {
        expect(defaultAboutData.licenseInfo.url).toMatch(urlRegex)
      }
    })

    it('邮箱字段应该是有效的邮箱格式', () => {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      
      if (defaultAboutData.developerInfo.email) {
        expect(defaultAboutData.developerInfo.email).toMatch(emailRegex)
      }
    })
  })

  describe('数据不变性测试', () => {
    it('默认数据对象应该是可序列化的', () => {
      expect(() => JSON.stringify(defaultAboutData)).not.toThrow()
      
      const serialized = JSON.stringify(defaultAboutData)
      const deserialized = JSON.parse(serialized)
      
      // 由于 buildDate 是动态生成的，我们需要单独比较
      expect(deserialized.extensionInfo).toEqual(defaultAboutData.extensionInfo)
      expect(deserialized.developerInfo).toEqual(defaultAboutData.developerInfo)
      expect(deserialized.licenseInfo).toEqual(defaultAboutData.licenseInfo)
      expect(deserialized.buildInfo.buildVersion).toBe(defaultAboutData.buildInfo.buildVersion)
    })

    it('修改默认数据副本不应该影响原始对象', () => {
      const originalName = defaultAboutData.extensionInfo.name
      
      // 创建深拷贝并修改
      const modifiedData = JSON.parse(JSON.stringify(defaultAboutData))
      modifiedData.extensionInfo.name = 'Modified Name'
      
      // 原始数据应该保持不变
      expect(defaultAboutData.extensionInfo.name).toBe(originalName)
      expect(defaultAboutData.extensionInfo.name).not.toBe('Modified Name')
      expect(defaultAboutData.extensionInfo.name).toBe('Universe Bag（乾坤袋）')
    })

    it('浅拷贝修改嵌套对象会影响原始对象（预期行为）', () => {
      // 这个测试说明了为什么需要深拷贝
      const shallowCopy = { ...defaultAboutData }
      const originalName = defaultAboutData.extensionInfo.name
      
      // 修改浅拷贝的嵌套对象
      shallowCopy.extensionInfo.name = 'Modified Name'
      
      // 由于是浅拷贝，原始对象的嵌套对象也会被修改
      expect(defaultAboutData.extensionInfo.name).toBe('Modified Name')
      
      // 恢复原始值以不影响其他测试
      defaultAboutData.extensionInfo.name = originalName
    })
  })
})