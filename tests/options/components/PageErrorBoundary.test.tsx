/**
 * PageErrorBoundary 组件单元测试
 */

import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import '@testing-library/jest-dom'
import PageErrorBoundary from '../../../src/options/components/PageErrorBoundary'

// 模拟 navigator.clipboard
Object.assign(navigator, {
  clipboard: {
    writeText: jest.fn().mockResolvedValue(undefined)
  }
})

// 模拟 console 方法
const originalConsoleError = console.error
const originalConsoleLog = console.log

// 抛出错误的测试组件
const ThrowError: React.FC<{ shouldThrow?: boolean; errorMessage?: string }> = ({ 
  shouldThrow = true, 
  errorMessage = 'Test error' 
}) => {
  if (shouldThrow) {
    throw new Error(errorMessage)
  }
  return <div data-testid="success-component">正常组件</div>
}

// 正常的测试组件
const NormalComponent: React.FC = () => {
  return <div data-testid="normal-component">正常渲染的组件</div>
}

describe('PageErrorBoundary', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    // 抑制错误日志输出，避免测试输出混乱
    console.error = jest.fn()
    console.log = jest.fn()
  })

  afterEach(() => {
    console.error = originalConsoleError
    console.log = originalConsoleLog
  })

  it('应该正常渲染子组件当没有错误时', () => {
    render(
      <PageErrorBoundary>
        <NormalComponent />
      </PageErrorBoundary>
    )
    
    expect(screen.getByTestId('normal-component')).toBeInTheDocument()
  })

  it('应该捕获并显示错误UI当子组件抛出错误时', () => {
    render(
      <PageErrorBoundary>
        <ThrowError />
      </PageErrorBoundary>
    )
    
    expect(screen.getByText('页面出现错误')).toBeInTheDocument()
    expect(screen.getByText('Test error')).toBeInTheDocument()
    expect(screen.getByText('重试')).toBeInTheDocument()
  })

  it('应该显示自定义错误消息', () => {
    render(
      <PageErrorBoundary>
        <ThrowError errorMessage="自定义错误消息" />
      </PageErrorBoundary>
    )
    
    expect(screen.getByText('自定义错误消息')).toBeInTheDocument()
  })

  it('应该调用onError回调当发生错误时', () => {
    const onError = jest.fn()
    
    render(
      <PageErrorBoundary onError={onError}>
        <ThrowError />
      </PageErrorBoundary>
    )
    
    expect(onError).toHaveBeenCalledWith(
      expect.any(Error),
      expect.objectContaining({
        componentStack: expect.any(String)
      })
    )
  })

  it('应该支持重试功能', async () => {
    const user = userEvent.setup()
    let shouldThrow = true
    
    const TestComponent = () => <ThrowError shouldThrow={shouldThrow} />
    
    const { rerender } = render(
      <PageErrorBoundary>
        <TestComponent />
      </PageErrorBoundary>
    )
    
    // 验证错误UI显示
    expect(screen.getByText('页面出现错误')).toBeInTheDocument()
    
    // 修复错误条件
    shouldThrow = false
    
    // 点击重试按钮
    const retryButton = screen.getByText('重试')
    await user.click(retryButton)
    
    // 重新渲染组件
    rerender(
      <PageErrorBoundary>
        <ThrowError shouldThrow={shouldThrow} />
      </PageErrorBoundary>
    )
    
    // 验证组件正常渲染
    await waitFor(() => {
      expect(screen.getByTestId('success-component')).toBeInTheDocument()
    })
  })

  it('应该限制重试次数', async () => {
    const user = userEvent.setup()
    
    render(
      <PageErrorBoundary maxRetries={2}>
        <ThrowError />
      </PageErrorBoundary>
    )
    
    // 第一次重试
    await user.click(screen.getByText('重试'))
    expect(screen.getByText('已重试 1 次，剩余 1 次机会')).toBeInTheDocument()
    
    // 第二次重试
    await user.click(screen.getByText('重试'))
    expect(screen.getByText('已重试 2 次，剩余 0 次机会')).toBeInTheDocument()
    
    // 重试按钮应该消失
    expect(screen.queryByText('重试')).not.toBeInTheDocument()
  })

  it('应该支持刷新页面功能', async () => {
    const user = userEvent.setup()
    const mockReload = jest.fn()
    
    Object.defineProperty(window, 'location', {
      value: { reload: mockReload },
      writable: true
    })
    
    render(
      <PageErrorBoundary>
        <ThrowError />
      </PageErrorBoundary>
    )
    
    const refreshButton = screen.getByText('刷新页面')
    await user.click(refreshButton)
    
    expect(mockReload).toHaveBeenCalled()
  })

  it('应该支持返回首页功能', async () => {
    const user = userEvent.setup()
    
    Object.defineProperty(window, 'location', {
      value: { hash: '#test' },
      writable: true
    })
    
    render(
      <PageErrorBoundary>
        <ThrowError />
      </PageErrorBoundary>
    )
    
    const homeButton = screen.getByText('返回首页')
    await user.click(homeButton)
    
    expect(window.location.hash).toBe('#bookmarks')
  })

  it('应该支持复制错误信息功能', async () => {
    const user = userEvent.setup()
    
    render(
      <PageErrorBoundary>
        <ThrowError errorMessage="复制测试错误" />
      </PageErrorBoundary>
    )
    
    // 展开高级选项
    const details = screen.getByText('高级选项和错误详情')
    await user.click(details)
    
    // 点击复制按钮
    const copyButton = screen.getByText('复制错误信息')
    await user.click(copyButton)
    
    expect(navigator.clipboard.writeText).toHaveBeenCalledWith(
      expect.stringContaining('复制测试错误')
    )
  })

  it('应该处理复制失败的情况', async () => {
    const user = userEvent.setup()
    
    // 模拟复制失败
    ;(navigator.clipboard.writeText as jest.Mock).mockRejectedValue(new Error('复制失败'))
    
    // 模拟 document.execCommand
    document.execCommand = jest.fn().mockReturnValue(true)
    
    // 模拟 alert
    window.alert = jest.fn()
    
    render(
      <PageErrorBoundary>
        <ThrowError />
      </PageErrorBoundary>
    )
    
    // 展开高级选项
    const details = screen.getByText('高级选项和错误详情')
    await user.click(details)
    
    // 点击复制按钮
    const copyButton = screen.getByText('复制错误信息')
    await user.click(copyButton)
    
    // 应该降级到 execCommand
    expect(document.execCommand).toHaveBeenCalledWith('copy')
    expect(window.alert).toHaveBeenCalledWith('错误信息已复制到剪贴板')
  })

  it('应该显示错误堆栈信息', async () => {
    const user = userEvent.setup()
    
    render(
      <PageErrorBoundary>
        <ThrowError />
      </PageErrorBoundary>
    )
    
    // 展开高级选项
    const details = screen.getByText('高级选项和错误详情')
    await user.click(details)
    
    // 应该显示错误堆栈
    expect(screen.getByText('错误堆栈：')).toBeInTheDocument()
    expect(screen.getByText('组件堆栈：')).toBeInTheDocument()
  })

  it('应该使用自定义fallback组件', () => {
    const customFallback = <div data-testid="custom-fallback">自定义错误页面</div>
    
    render(
      <PageErrorBoundary fallback={customFallback}>
        <ThrowError />
      </PageErrorBoundary>
    )
    
    expect(screen.getByTestId('custom-fallback')).toBeInTheDocument()
    expect(screen.queryByText('页面出现错误')).not.toBeInTheDocument()
  })

  it('应该记录错误到控制台', () => {
    render(
      <PageErrorBoundary>
        <ThrowError errorMessage="控制台测试错误" />
      </PageErrorBoundary>
    )
    
    expect(console.error).toHaveBeenCalledWith(
      '页面错误边界捕获到错误:',
      expect.any(Error),
      expect.objectContaining({
        componentStack: expect.any(String)
      })
    )
  })

  it('应该在组件卸载时清理定时器', () => {
    const { unmount } = render(
      <PageErrorBoundary>
        <NormalComponent />
      </PageErrorBoundary>
    )
    
    // 应该不会抛出错误
    expect(() => unmount()).not.toThrow()
  })

  it('应该处理未知错误', () => {
    // 创建一个抛出非Error对象的组件
    const ThrowNonError = () => {
      throw 'string error' // eslint-disable-line no-throw-literal
    }
    
    render(
      <PageErrorBoundary>
        <ThrowNonError />
      </PageErrorBoundary>
    )
    
    expect(screen.getByText('页面出现错误')).toBeInTheDocument()
    expect(screen.getByText('发生了未知错误')).toBeInTheDocument()
  })
})