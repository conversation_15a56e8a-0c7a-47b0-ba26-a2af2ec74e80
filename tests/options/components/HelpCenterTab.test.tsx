/**
 * HelpCenterTab 组件单元测试
 */

import React from 'react'
import { render, screen, waitFor, fireEvent } from '@testing-library/react'
import '@testing-library/jest-dom'
import HelpCenterTab from '../../../src/options/components/HelpCenterTab'
import type { HelpContent } from '../../../src/options/data/helpContent'

// 测试数据
const mockHelpContent: HelpContent = {
  sections: [
    {
      id: 'getting-started',
      title: '快速开始',
      content: '这是快速开始的内容\n\n# 安装步骤\n1. 下载扩展\n2. 安装到浏览器\n3. 开始使用',
      category: 'guide',
      keywords: ['开始', '安装', '使用']
    },
    {
      id: 'faq-login',
      title: '登录问题',
      content: '如果您遇到登录问题，请检查以下几点：\n\n- 确认用户名和密码正确\n- 检查网络连接\n- 清除浏览器缓存',
      category: 'faq',
      keywords: ['登录', '密码', '网络']
    },
    {
      id: 'troubleshooting-sync',
      title: '同步故障',
      content: '同步功能出现问题时的解决方案：\n\n## 检查网络\n确保网络连接正常\n\n## 重新登录\n退出后重新登录账户',
      category: 'troubleshooting',
      keywords: ['同步', '网络', '登录']
    }
  ],
  categories: [
    {
      id: 'guide',
      name: '使用指南',
      description: '基本使用方法和技巧',
      icon: 'book',
      order: 1
    },
    {
      id: 'faq',
      name: '常见问题',
      description: '用户常见问题解答',
      icon: 'help-circle',
      order: 2
    },
    {
      id: 'troubleshooting',
      name: '故障排除',
      description: '问题诊断和解决方案',
      icon: 'tool',
      order: 3
    }
  ]
}

describe('HelpCenterTab', () => {
  it('应该正确渲染传入的帮助内容', () => {
    render(<HelpCenterTab helpContent={mockHelpContent} />)
    
    // 检查页面标题
    expect(screen.getByText('帮助中心')).toBeInTheDocument()
    
    // 检查分类按钮
    expect(screen.getByText('全部 (3)')).toBeInTheDocument()
    expect(screen.getByText('使用指南 (1)')).toBeInTheDocument()
    expect(screen.getByText('常见问题 (1)')).toBeInTheDocument()
    expect(screen.getByText('故障排除 (1)')).toBeInTheDocument()
    
    // 检查帮助内容标题（使用 getAllByText 因为标题会出现在多个地方）
    expect(screen.getAllByText('快速开始')).toHaveLength(2) // 快速导航 + 内容标题
    expect(screen.getAllByText('登录问题')).toHaveLength(2) // 快速导航 + 内容标题
    expect(screen.getAllByText('同步故障')).toHaveLength(2) // 快速导航 + 内容标题
  })

  it('应该显示加载状态当没有传入数据时', async () => {
    render(<HelpCenterTab />)
    
    // 检查加载状态
    expect(screen.getByText('加载帮助内容中...')).toBeInTheDocument()
    
    // 等待加载完成
    await waitFor(() => {
      expect(screen.getByText('帮助中心')).toBeInTheDocument()
    }, { timeout: 2000 })
  })

  it('应该能够展开和折叠帮助内容', async () => {
    render(<HelpCenterTab helpContent={mockHelpContent} />)
    
    // 初始状态下内容应该是折叠的
    expect(screen.queryByText('这是快速开始的内容')).not.toBeInTheDocument()
    
    // 点击展开（选择内容区域的按钮，而不是快速导航的按钮）
    const quickStartButtons = screen.getAllByText('快速开始')
    const quickStartButton = quickStartButtons.find(button => 
      button.closest('button')?.className.includes('w-full')
    ) || quickStartButtons[1] // 选择内容区域的按钮
    fireEvent.click(quickStartButton)
    
    // 内容应该展开
    await waitFor(() => {
      expect(screen.getByText('这是快速开始的内容')).toBeInTheDocument()
    })
    
    // 再次点击应该折叠
    fireEvent.click(quickStartButton)
    
    await waitFor(() => {
      expect(screen.queryByText('这是快速开始的内容')).not.toBeInTheDocument()
    })
  })

  it('应该能够按分类筛选内容', () => {
    render(<HelpCenterTab helpContent={mockHelpContent} />)
    
    // 点击"使用指南"分类
    const guideButton = screen.getByText('使用指南 (1)')
    fireEvent.click(guideButton)
    
    // 应该只显示使用指南类别的内容
    expect(screen.getAllByText('快速开始')).toHaveLength(2) // 快速导航 + 内容标题
    expect(screen.queryByText('登录问题')).not.toBeInTheDocument()
    expect(screen.queryByText('同步故障')).not.toBeInTheDocument()
  })

  it('应该能够展开和折叠所有内容', () => {
    render(<HelpCenterTab helpContent={mockHelpContent} />)
    
    // 点击展开全部
    const expandAllButton = screen.getByText('展开全部')
    fireEvent.click(expandAllButton)
    
    // 所有内容都应该展开
    expect(screen.getByText('这是快速开始的内容')).toBeInTheDocument()
    expect(screen.getByText('如果您遇到登录问题，请检查以下几点：')).toBeInTheDocument()
    expect(screen.getByText('同步功能出现问题时的解决方案：')).toBeInTheDocument()
    
    // 点击折叠全部
    const collapseAllButton = screen.getByText('折叠全部')
    fireEvent.click(collapseAllButton)
    
    // 所有内容都应该折叠
    expect(screen.queryByText('这是快速开始的内容')).not.toBeInTheDocument()
    expect(screen.queryByText('如果您遇到登录问题，请检查以下几点：')).not.toBeInTheDocument()
    expect(screen.queryByText('同步功能出现问题时的解决方案：')).not.toBeInTheDocument()
  })

  it('应该正确渲染格式化的内容', () => {
    render(<HelpCenterTab helpContent={mockHelpContent} />)
    
    // 展开快速开始内容
    const quickStartButtons = screen.getAllByText('快速开始')
    const quickStartButton = quickStartButtons.find(button => 
      button.closest('button')?.className.includes('w-full')
    ) || quickStartButtons[1] // 选择内容区域的按钮
    fireEvent.click(quickStartButton)
    
    // 检查格式化的内容
    expect(screen.getByText('安装步骤')).toBeInTheDocument()
    expect(screen.getByText('下载扩展')).toBeInTheDocument()
    expect(screen.getByText('安装到浏览器')).toBeInTheDocument()
    expect(screen.getByText('开始使用')).toBeInTheDocument()
  })

  it('应该显示快速导航', () => {
    render(<HelpCenterTab helpContent={mockHelpContent} />)
    
    // 检查快速导航标题
    expect(screen.getByText('快速导航')).toBeInTheDocument()
    
    // 检查导航按钮（应该显示前6个）
    expect(screen.getByRole('button', { name: '快速开始' })).toBeInTheDocument()
    expect(screen.getByRole('button', { name: '登录问题' })).toBeInTheDocument()
    expect(screen.getByRole('button', { name: '同步故障' })).toBeInTheDocument()
  })

  it('应该显示联系信息', () => {
    render(<HelpCenterTab helpContent={mockHelpContent} />)
    
    // 检查联系信息
    expect(screen.getByText('需要更多帮助？')).toBeInTheDocument()
    expect(screen.getByText('发送邮件')).toBeInTheDocument()
    expect(screen.getByText('访问官网')).toBeInTheDocument()
    
    // 检查链接
    const emailLink = screen.getByRole('link', { name: '发送邮件' })
    expect(emailLink).toHaveAttribute('href', 'mailto:<EMAIL>')
    
    const websiteLink = screen.getByRole('link', { name: '访问官网' })
    expect(websiteLink).toHaveAttribute('href', 'https://universebag.com')
    expect(websiteLink).toHaveAttribute('target', '_blank')
  })

  it('应该处理空内容的情况', () => {
    const emptyContent: HelpContent = {
      sections: [],
      categories: []
    }
    
    render(<HelpCenterTab helpContent={emptyContent} />)
    
    // 应该显示空状态
    expect(screen.getByText('没有找到相关内容')).toBeInTheDocument()
    expect(screen.getByText('尝试调整搜索关键词或选择不同的分类')).toBeInTheDocument()
  })

  it('应该正确处理URL锚点', () => {
    // 模拟URL锚点
    Object.defineProperty(window, 'location', {
      value: {
        hash: '#getting-started'
      },
      writable: true
    })
    
    render(<HelpCenterTab helpContent={mockHelpContent} />)
    
    // 对应的内容应该自动展开
    expect(screen.getByText('这是快速开始的内容')).toBeInTheDocument()
  })
})