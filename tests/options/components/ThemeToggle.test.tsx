/**
 * ThemeToggle 组件单元测试
 */

import { describe, it, expect, beforeEach, vi } from 'vitest'
import React from 'react'
import { render, screen, fireEvent } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import '@testing-library/jest-dom'
import ThemeToggle from '../../../src/options/components/ThemeToggle'
import * as useThemeModule from '../../../src/options/hooks/useTheme'

// 模拟 useTheme Hook
const mockUseTheme = vi.fn()
vi.mock('../../../src/options/hooks/useTheme', () => ({
    useTheme: () => mockUseTheme(),
    type: {} // 模拟 type 导出
}))

describe('ThemeToggle', () => {
    const mockSetTheme = vi.fn()

    beforeEach(() => {
        vi.clearAllMocks()

        // 默认模拟返回值
        mockUseTheme.mockReturnValue({
            theme: 'system',
            actualTheme: 'light',
            setTheme: mockSetTheme
        })
    })

    describe('按钮模式 (默认)', () => {
        it('应该渲染主题切换按钮', () => {
            render(<ThemeToggle />)

            const button = screen.getByRole('button')
            expect(button).toBeInTheDocument()
            expect(button).toHaveAttribute('aria-label', '切换主题，当前: 系统')
        })

        it('应该显示当前主题对应的图标', () => {
            render(<ThemeToggle />)

            const button = screen.getByRole('button')
            // 系统主题应该显示 Monitor 图标
            expect(button.querySelector('svg')).toBeInTheDocument()
        })

        it('应该在点击时循环切换主题', async () => {
            const user = userEvent.setup()
            const { rerender } = render(<ThemeToggle />)

            const button = screen.getByRole('button')

            // 第一次点击：system -> light
            await user.click(button)
            expect(mockSetTheme).toHaveBeenCalledWith('light')

            // 模拟主题变为 light
            mockUseTheme.mockReturnValue({
                theme: 'light',
                actualTheme: 'light',
                setTheme: mockSetTheme
            })

            // 重新渲染
            rerender(<ThemeToggle />)
            const newButton = screen.getByRole('button')

            // 第二次点击：light -> dark
            await user.click(newButton)
            expect(mockSetTheme).toHaveBeenCalledWith('dark')
        })

        it('应该支持不同尺寸', () => {
            const { rerender } = render(<ThemeToggle size="sm" />)
            let button = screen.getByRole('button')
            expect(button).toHaveClass('w-8', 'h-8')

            rerender(<ThemeToggle size="md" />)
            button = screen.getByRole('button')
            expect(button).toHaveClass('w-10', 'h-10')

            rerender(<ThemeToggle size="lg" />)
            button = screen.getByRole('button')
            expect(button).toHaveClass('w-12', 'h-12')
        })

        it('应该显示正确的 tooltip', () => {
            mockUseTheme.mockReturnValue({
                theme: 'dark',
                actualTheme: 'dark',
                setTheme: mockSetTheme
            })

            render(<ThemeToggle />)

            const button = screen.getByRole('button')
            expect(button).toHaveAttribute('title', '当前: 深色 (深色)')
        })

        it('应该应用自定义 className', () => {
            render(<ThemeToggle className="custom-class" />)

            const button = screen.getByRole('button')
            expect(button).toHaveClass('custom-class')
        })
    })

    describe('标签模式', () => {
        it('应该渲染带标签的选择器', () => {
            render(<ThemeToggle showLabel={true} />)

            expect(screen.getByText('主题:')).toBeInTheDocument()
            expect(screen.getByRole('combobox')).toBeInTheDocument()
        })

        it('应该显示所有主题选项', () => {
            render(<ThemeToggle showLabel={true} />)

            const select = screen.getByRole('combobox')
            expect(select).toBeInTheDocument()

            // 检查选项
            const options = screen.getAllByRole('option')
            expect(options).toHaveLength(3)
            expect(screen.getByRole('option', { name: '浅色' })).toBeInTheDocument()
            expect(screen.getByRole('option', { name: '深色' })).toBeInTheDocument()
            expect(screen.getByRole('option', { name: '系统' })).toBeInTheDocument()
        })

        it('应该显示当前选中的主题', () => {
            mockUseTheme.mockReturnValue({
                theme: 'dark',
                actualTheme: 'dark',
                setTheme: mockSetTheme
            })

            render(<ThemeToggle showLabel={true} />)

            const select = screen.getByRole('combobox') as HTMLSelectElement
            expect(select.value).toBe('dark')
        })

        it('应该在选择时切换主题', async () => {
            const user = userEvent.setup()
            render(<ThemeToggle showLabel={true} />)

            const select = screen.getByRole('combobox')

            await user.selectOptions(select, 'light')
            expect(mockSetTheme).toHaveBeenCalledWith('light')
        })

        it('应该支持不同尺寸的文字', () => {
            const { rerender } = render(<ThemeToggle showLabel={true} size="sm" />)
            let label = screen.getByText('主题:')
            expect(label).toHaveClass('text-xs')

            rerender(<ThemeToggle showLabel={true} size="md" />)
            label = screen.getByText('主题:')
            expect(label).toHaveClass('text-sm')

            rerender(<ThemeToggle showLabel={true} size="lg" />)
            label = screen.getByText('主题:')
            expect(label).toHaveClass('text-base')
        })
    })

    describe('主题状态显示', () => {
        it('应该根据实际主题显示不同的图标颜色', () => {
            // 深色主题
            mockUseTheme.mockReturnValue({
                theme: 'system',
                actualTheme: 'dark',
                setTheme: mockSetTheme
            })

            const { rerender } = render(<ThemeToggle />)
            let button = screen.getByRole('button')
            let icon = button.querySelector('svg')
            expect(icon).toHaveClass('text-yellow-400')

            // 浅色主题
            mockUseTheme.mockReturnValue({
                theme: 'system',
                actualTheme: 'light',
                setTheme: mockSetTheme
            })

            rerender(<ThemeToggle />)
            button = screen.getByRole('button')
            icon = button.querySelector('svg')
            expect(icon).toHaveClass('text-orange-500')
        })

        it('应该为不同主题显示正确的图标', () => {
            // 测试浅色主题图标
            mockUseTheme.mockReturnValue({
                theme: 'light',
                actualTheme: 'light',
                setTheme: mockSetTheme
            })

            const { rerender } = render(<ThemeToggle />)
            expect(screen.getByRole('button')).toBeInTheDocument()

            // 测试深色主题图标
            mockUseTheme.mockReturnValue({
                theme: 'dark',
                actualTheme: 'dark',
                setTheme: mockSetTheme
            })

            rerender(<ThemeToggle />)
            expect(screen.getByRole('button')).toBeInTheDocument()

            // 测试系统主题图标
            mockUseTheme.mockReturnValue({
                theme: 'system',
                actualTheme: 'light',
                setTheme: mockSetTheme
            })

            rerender(<ThemeToggle />)
            expect(screen.getByRole('button')).toBeInTheDocument()
        })
    })

    describe('无障碍访问', () => {
        it('应该有正确的 ARIA 标签', () => {
            render(<ThemeToggle />)

            const button = screen.getByRole('button')
            expect(button).toHaveAttribute('aria-label', '切换主题，当前: 系统')
        })

        it('应该支持键盘导航', async () => {
            const user = userEvent.setup()
            render(<ThemeToggle />)

            const button = screen.getByRole('button')

            // Tab 键聚焦
            await user.tab()
            expect(button).toHaveFocus()

            // Enter 键激活
            await user.keyboard('{Enter}')
            expect(mockSetTheme).toHaveBeenCalled()

            // Space 键激活
            await user.keyboard(' ')
            expect(mockSetTheme).toHaveBeenCalledTimes(2)
        })

        it('标签模式应该支持键盘导航', async () => {
            const user = userEvent.setup()
            render(<ThemeToggle showLabel={true} />)

            const select = screen.getByRole('combobox')

            // Tab 键聚焦
            await user.tab()
            expect(select).toHaveFocus()

            // 方向键选择
            await user.keyboard('{ArrowDown}')
            // 注意：这里的行为取决于浏览器实现
        })
    })

    describe('样式和交互', () => {
        it('应该有 hover 效果', () => {
            render(<ThemeToggle />)

            const button = screen.getByRole('button')
            expect(button).toHaveClass('hover:bg-gray-200')
        })

        it('应该有 focus 样式', () => {
            render(<ThemeToggle />)

            const button = screen.getByRole('button')
            expect(button).toHaveClass('focus:ring-2', 'focus:ring-primary-500')
        })

        it('应该有过渡动画', () => {
            render(<ThemeToggle />)

            const button = screen.getByRole('button')
            expect(button).toHaveClass('transition-all', 'duration-200')

            const icon = button.querySelector('svg')
            expect(icon).toHaveClass('transition-transform', 'duration-200')
        })
    })

    describe('错误处理', () => {
        it('应该处理 useTheme 返回 undefined 的情况', () => {
            mockUseTheme.mockReturnValue({
                theme: 'invalid' as any,
                actualTheme: 'light',
                setTheme: mockSetTheme
            })

            // 应该不会抛出错误
            expect(() => render(<ThemeToggle />)).not.toThrow()
        })

        it('应该处理缺少主题选项的情况', () => {
            mockUseTheme.mockReturnValue({
                theme: 'unknown' as any,
                actualTheme: 'light',
                setTheme: mockSetTheme
            })

            render(<ThemeToggle />)

            // 应该显示默认图标（Sun）
            const button = screen.getByRole('button')
            expect(button.querySelector('svg')).toBeInTheDocument()
        })
    })
})