/**
 * OptionsApp 组件单元测试
 */

import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import '@testing-library/jest-dom'
import OptionsApp from '../../src/options/OptionsApp'

// 模拟 Chrome 扩展 API
const mockChrome = {
  runtime: {
    sendMessage: vi.fn(),
    getManifest: vi.fn(() => ({
      name: 'Test Extension',
      version: '1.0.0',
      description: 'Test description',
      manifest_version: 3
    }))
  }
}

// 模拟组件
vi.mock('../../src/components/ImportExportTab', () => {
  return {
    default: function MockImportExportTab() {
      return <div data-testid="import-export-tab">导入导出页面</div>
    }
  }
})

vi.mock('../../src/components/CategoryManagementTab', () => {
  return {
    default: function MockCategoryManagementTab() {
      return <div data-testid="category-management-tab">分类管理页面</div>
    }
  }
})

vi.mock('../../src/components/TagsTab', () => {
  return {
    default: function MockTagsTab() {
      return <div data-testid="tags-tab">标签管理页面</div>
    }
  }
})

vi.mock('../../src/options/components/AboutTab', () => {
  return {
    default: function MockAboutTab() {
      return <div data-testid="about-tab">关于我们页面</div>
    }
  }
})

vi.mock('../../src/options/components/HelpCenterTab', () => {
  return {
    default: function MockHelpCenterTab() {
      return <div data-testid="help-center-tab">帮助中心页面</div>
    }
  }
})

// 模拟其他依赖
vi.mock('../../src/hooks/useViewMode', () => ({
  useViewMode: () => ({
    viewMode: 'card',
    setViewMode: vi.fn(),
    isLoading: false
  })
}))

vi.mock('../../src/utils/layoutStability', () => ({
  useViewSwitchStability: () => ({
    containerRef: { current: null },
    displayView: 'card',
    isTransitioning: false
  }),
  useScrollPositionLock: () => ({
    lockScrollPosition: vi.fn()
  })
}))

vi.mock('../../src/hooks/useAdvancedSearch', () => ({
  useAdvancedSearch: () => ({
    query: '',
    setQuery: vi.fn(),
    results: [],
    suggestions: [],
    isSearching: false,
    hasResults: false,
    totalResults: 0,
    searchTime: 0,
    addFilter: vi.fn(),
    clearFilters: vi.fn()
  })
}))

describe('OptionsApp', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    ;(global as any).chrome = mockChrome
    
    // 模拟 window.matchMedia
    Object.defineProperty(window, 'matchMedia', {
      writable: true,
      value: vi.fn().mockImplementation(query => ({
        matches: false,
        media: query,
        onchange: null,
        addListener: vi.fn(), // deprecated
        removeListener: vi.fn(), // deprecated
        addEventListener: vi.fn(),
        removeEventListener: vi.fn(),
        dispatchEvent: vi.fn(),
      })),
    })
    
    // 模拟成功的消息响应
    vi.mocked(mockChrome.runtime.sendMessage).mockResolvedValue({
      success: true,
      data: []
    })
  })

  afterEach(() => {
    delete (global as any).chrome
  })

  it('应该正确渲染应用标题和导航', async () => {
    render(<OptionsApp />)
    
    // 等待初始化完成
    await waitFor(() => {
      expect(screen.getByText('Universe Bag')).toBeInTheDocument()
    })
    
    // 检查导航选项
    expect(screen.getByText('收藏管理')).toBeInTheDocument()
    expect(screen.getByText('分类管理')).toBeInTheDocument()
    expect(screen.getByText('标签管理')).toBeInTheDocument()
    expect(screen.getByText('导入导出')).toBeInTheDocument()
    expect(screen.getByText('设置')).toBeInTheDocument()
    expect(screen.getByText('关于我们')).toBeInTheDocument()
    expect(screen.getByText('帮助中心')).toBeInTheDocument()
  })

  it('应该默认显示收藏管理页面', async () => {
    render(<OptionsApp />)
    
    await waitFor(() => {
      expect(screen.getByText('收藏管理')).toHaveClass('bg-primary-50')
    })
  })

  it('应该能够切换到关于我们页面', async () => {
    const user = userEvent.setup()
    render(<OptionsApp />)
    
    await waitFor(() => {
      expect(screen.getByText('Universe Bag')).toBeInTheDocument()
    })
    
    // 点击关于我们导航
    const aboutButton = screen.getByText('关于我们')
    await user.click(aboutButton)
    
    // 验证页面切换
    expect(screen.getByTestId('about-tab')).toBeInTheDocument()
    expect(aboutButton).toHaveClass('bg-primary-50')
  })

  it('应该能够切换到帮助中心页面', async () => {
    const user = userEvent.setup()
    render(<OptionsApp />)
    
    await waitFor(() => {
      expect(screen.getByText('Universe Bag')).toBeInTheDocument()
    })
    
    // 点击帮助中心导航
    const helpButton = screen.getByText('帮助中心')
    await user.click(helpButton)
    
    // 验证页面切换
    expect(screen.getByTestId('help-center-tab')).toBeInTheDocument()
    expect(helpButton).toHaveClass('bg-primary-50')
  })

  it('应该能够在不同标签页之间切换', async () => {
    const user = userEvent.setup()
    render(<OptionsApp />)
    
    await waitFor(() => {
      expect(screen.getByText('Universe Bag')).toBeInTheDocument()
    })
    
    // 切换到分类管理
    await user.click(screen.getByText('分类管理'))
    expect(screen.getByTestId('category-management-tab')).toBeInTheDocument()
    
    // 切换到标签管理
    await user.click(screen.getByText('标签管理'))
    expect(screen.getByTestId('tags-tab')).toBeInTheDocument()
    
    // 切换到导入导出
    await user.click(screen.getByText('导入导出'))
    expect(screen.getByTestId('import-export-tab')).toBeInTheDocument()
    
    // 切换到关于我们
    await user.click(screen.getByText('关于我们'))
    expect(screen.getByTestId('about-tab')).toBeInTheDocument()
    
    // 切换到帮助中心
    await user.click(screen.getByText('帮助中心'))
    expect(screen.getByTestId('help-center-tab')).toBeInTheDocument()
  })

  it('应该根据URL hash设置默认标签页', async () => {
    // 模拟URL hash
    Object.defineProperty(window, 'location', {
      value: {
        hash: '#about'
      },
      writable: true
    })
    
    render(<OptionsApp />)
    
    await waitFor(() => {
      expect(screen.getByTestId('about-tab')).toBeInTheDocument()
    })
    
    expect(screen.getByText('关于我们')).toHaveClass('bg-primary-50')
  })

  it('应该支持帮助中心的URL hash', async () => {
    // 模拟URL hash
    Object.defineProperty(window, 'location', {
      value: {
        hash: '#help'
      },
      writable: true
    })
    
    render(<OptionsApp />)
    
    await waitFor(() => {
      expect(screen.getByTestId('help-center-tab')).toBeInTheDocument()
    })
    
    expect(screen.getByText('帮助中心')).toHaveClass('bg-primary-50')
  })

  it('应该显示加载状态', () => {
    // 模拟Chrome API不可用
    delete (global as any).chrome
    
    render(<OptionsApp />)
    
    expect(screen.getByText('正在初始化收藏管理页面...')).toBeInTheDocument()
  })

  it('应该处理初始化错误', async () => {
    // 模拟Chrome API不可用
    delete (global as any).chrome
    
    render(<OptionsApp />)
    
    await waitFor(() => {
      expect(screen.getByText('初始化失败')).toBeInTheDocument()
    })
    
    expect(screen.getByText('Chrome扩展API不可用，请确保在扩展环境中运行')).toBeInTheDocument()
  })

  it('应该支持重试功能', async () => {
    const user = userEvent.setup()
    
    // 模拟Chrome API不可用
    delete (global as any).chrome
    
    render(<OptionsApp />)
    
    await waitFor(() => {
      expect(screen.getByText('初始化失败')).toBeInTheDocument()
    })
    
    // 恢复Chrome API
    ;(global as any).chrome = mockChrome
    
    // 点击重试按钮
    const retryButton = screen.getByText(/重试/)
    await user.click(retryButton)
    
    // 验证重试成功
    await waitFor(() => {
      expect(screen.getByText('Universe Bag')).toBeInTheDocument()
    })
  })

  it('应该正确显示导航图标', async () => {
    render(<OptionsApp />)
    
    await waitFor(() => {
      expect(screen.getByText('Universe Bag')).toBeInTheDocument()
    })
    
    // 检查所有导航按钮都有对应的图标
    const navButtons = screen.getAllByRole('button').filter(button => 
      ['收藏管理', '分类管理', '标签管理', '导入导出', '设置', '关于我们', '帮助中心'].includes(button.textContent || '')
    )
    
    expect(navButtons).toHaveLength(7)
    
    // 每个导航按钮都应该包含图标和文本
    navButtons.forEach(button => {
      expect(button.querySelector('svg')).toBeInTheDocument()
    })
  })

  it('应该保持导航状态高亮', async () => {
    const user = userEvent.setup()
    render(<OptionsApp />)
    
    await waitFor(() => {
      expect(screen.getByText('Universe Bag')).toBeInTheDocument()
    })
    
    // 点击关于我们
    const aboutButton = screen.getByText('关于我们')
    await user.click(aboutButton)
    
    // 验证高亮状态
    expect(aboutButton).toHaveClass('bg-primary-50', 'text-primary-700')
    
    // 验证其他按钮没有高亮
    const bookmarksButton = screen.getByText('收藏管理')
    expect(bookmarksButton).not.toHaveClass('bg-primary-50')
    expect(bookmarksButton).toHaveClass('text-gray-700')
  })
})