/**
 * helpSearch.ts 单元测试
 */

import {
  searchHelpContent,
  highlightSearchTerms,
  getSearchSuggestions,
  debounce
} from '../../../src/options/utils/helpSearch'
import type { HelpSection } from '../../../src/options/data/helpContent'

// 测试数据
const mockSections: HelpSection[] = [
  {
    id: 'section-1',
    title: '如何添加收藏',
    content: '点击扩展图标，然后点击添加收藏按钮。这样就可以将当前页面添加到收藏夹中。',
    category: 'guide',
    keywords: ['添加', '收藏', '扩展', '按钮']
  },
  {
    id: 'section-2',
    title: '搜索功能使用',
    content: '在搜索框中输入关键词进行搜索。支持模糊搜索和精确匹配。',
    category: 'guide',
    keywords: ['搜索', '关键词', '查找', '模糊搜索']
  },
  {
    id: 'section-3',
    title: '常见问题解答',
    content: '这里是一些常见问题的解答。如果您遇到问题，可以先查看这里。',
    category: 'faq',
    keywords: ['问题', '解答', 'FAQ', '帮助']
  },
  {
    id: 'section-4',
    title: '扩展无法工作',
    content: '如果扩展无法正常工作，请尝试重启浏览器或重新安装扩展。',
    category: 'troubleshooting',
    keywords: ['扩展', '无法工作', '重启', '安装']
  }
]

describe('helpSearch', () => {
  describe('searchHelpContent', () => {
    it('应该返回空数组当查询为空时', () => {
      const results = searchHelpContent(mockSections, '')
      expect(results).toEqual([])
    })

    it('应该返回空数组当查询只有空格时', () => {
      const results = searchHelpContent(mockSections, '   ')
      expect(results).toEqual([])
    })

    it('应该根据标题搜索', () => {
      const results = searchHelpContent(mockSections, '添加收藏')
      
      expect(results).toHaveLength(1)
      expect(results[0].section.id).toBe('section-1')
      expect(results[0].relevance).toBeGreaterThan(0)
      expect(results[0].matchedKeywords).toContain('title')
    })

    it('应该根据内容搜索', () => {
      const results = searchHelpContent(mockSections, '扩展图标')
      
      expect(results).toHaveLength(1)
      expect(results[0].section.id).toBe('section-1')
      expect(results[0].matchedKeywords).toContain('content')
    })

    it('应该根据关键词搜索', () => {
      const results = searchHelpContent(mockSections, '模糊搜索')
      
      expect(results).toHaveLength(1)
      expect(results[0].section.id).toBe('section-2')
      expect(results[0].matchedKeywords).toContain('模糊搜索')
    })

    it('应该返回多个匹配结果', () => {
      const results = searchHelpContent(mockSections, '扩展')
      
      expect(results.length).toBeGreaterThan(1)
      // 应该包含 section-1 和 section-4
      const sectionIds = results.map(r => r.section.id)
      expect(sectionIds).toContain('section-1')
      expect(sectionIds).toContain('section-4')
    })

    it('应该按相关性排序结果', () => {
      const results = searchHelpContent(mockSections, '扩展')
      
      // 结果应该按相关性降序排列
      for (let i = 1; i < results.length; i++) {
        expect(results[i - 1].relevance).toBeGreaterThanOrEqual(results[i].relevance)
      }
    })

    it('应该支持自定义搜索配置', () => {
      const results = searchHelpContent(mockSections, '扩展', {
        maxResults: 1,
        weights: {
          title: 5,
          content: 1,
          keywords: 1
        }
      })
      
      expect(results).toHaveLength(1)
    })

    it('应该支持大小写不敏感搜索', () => {
      const results1 = searchHelpContent(mockSections, '添加收藏')
      const results2 = searchHelpContent(mockSections, '添加收藏')
      
      expect(results1).toEqual(results2)
    })

    it('应该支持模糊搜索', () => {
      const results = searchHelpContent(mockSections, '收藏夹', {
        fuzzySearch: true
      })
      
      // 应该能找到包含"收藏"的结果
      expect(results.length).toBeGreaterThan(0)
    })

    it('应该限制结果数量', () => {
      const results = searchHelpContent(mockSections, '扩展', {
        maxResults: 1
      })
      
      expect(results).toHaveLength(1)
    })
  })

  describe('highlightSearchTerms', () => {
    it('应该高亮搜索关键词', () => {
      const text = '这是一个测试文本'
      const highlighted = highlightSearchTerms(text, '测试')
      
      expect(highlighted).toContain('<mark class="bg-yellow-200">测试</mark>')
    })

    it('应该处理多个匹配', () => {
      const text = '测试文本中的测试内容'
      const highlighted = highlightSearchTerms(text, '测试')
      
      const matches = highlighted.match(/<mark[^>]*>测试<\/mark>/g)
      expect(matches).toHaveLength(2)
    })

    it('应该处理空查询', () => {
      const text = '这是一个测试文本'
      const highlighted = highlightSearchTerms(text, '')
      
      expect(highlighted).toBe(text)
    })

    it('应该处理特殊字符', () => {
      const text = '这是一个 (测试) 文本'
      const highlighted = highlightSearchTerms(text, '(测试)')
      
      expect(highlighted).toContain('<mark class="bg-yellow-200">(测试)</mark>')
    })

    it('应该支持大小写不敏感', () => {
      const text = '这是一个测试文本'
      const highlighted = highlightSearchTerms(text, '测试')
      
      expect(highlighted).toContain('<mark class="bg-yellow-200">测试</mark>')
    })
  })

  describe('getSearchSuggestions', () => {
    it('应该返回搜索建议', () => {
      const suggestions = getSearchSuggestions(mockSections, '搜索')
      
      expect(suggestions.length).toBeGreaterThan(0)
      expect(suggestions).toContain('搜索功能使用')
    })

    it('应该从关键词中提取建议', () => {
      const suggestions = getSearchSuggestions(mockSections, '模糊')
      
      expect(suggestions).toContain('模糊搜索')
    })

    it('应该限制建议数量', () => {
      const suggestions = getSearchSuggestions(mockSections, '扩展', 2)
      
      expect(suggestions.length).toBeLessThanOrEqual(2)
    })

    it('应该处理空查询', () => {
      const suggestions = getSearchSuggestions(mockSections, '')
      
      expect(suggestions).toEqual([])
    })

    it('应该去重建议', () => {
      const suggestions = getSearchSuggestions(mockSections, '扩展')
      const uniqueSuggestions = [...new Set(suggestions)]
      
      expect(suggestions).toEqual(uniqueSuggestions)
    })

    it('应该支持部分匹配', () => {
      const suggestions = getSearchSuggestions(mockSections, '收')
      
      expect(suggestions.some(s => s.includes('收藏'))).toBe(true)
    })
  })

  describe('debounce', () => {
    beforeEach(() => {
      vi.useFakeTimers()
    })

    afterEach(() => {
      vi.useRealTimers()
    })

    it('应该延迟执行函数', () => {
      const mockFn = vi.fn()
      const debouncedFn = debounce(mockFn, 100)
      
      debouncedFn('test')
      expect(mockFn).not.toHaveBeenCalled()
      
      vi.advanceTimersByTime(100)
      expect(mockFn).toHaveBeenCalledWith('test')
    })

    it('应该取消之前的调用', () => {
      const mockFn = vi.fn()
      const debouncedFn = debounce(mockFn, 100)
      
      debouncedFn('first')
      debouncedFn('second')
      
      vi.advanceTimersByTime(100)
      
      expect(mockFn).toHaveBeenCalledTimes(1)
      expect(mockFn).toHaveBeenCalledWith('second')
    })

    it('应该传递所有参数', () => {
      const mockFn = vi.fn()
      const debouncedFn = debounce(mockFn, 100)
      
      debouncedFn('arg1', 'arg2', 'arg3')
      vi.advanceTimersByTime(100)
      
      expect(mockFn).toHaveBeenCalledWith('arg1', 'arg2', 'arg3')
    })

    it('应该支持不同的延迟时间', () => {
      const mockFn = vi.fn()
      const debouncedFn = debounce(mockFn, 200)
      
      debouncedFn('test')
      
      vi.advanceTimersByTime(100)
      expect(mockFn).not.toHaveBeenCalled()
      
      vi.advanceTimersByTime(100)
      expect(mockFn).toHaveBeenCalled()
    })
  })
})