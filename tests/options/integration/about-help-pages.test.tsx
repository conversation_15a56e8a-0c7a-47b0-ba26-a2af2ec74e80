/**
 * 关于我们和帮助中心页面集成测试
 */

import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import '@testing-library/jest-dom'
import OptionsApp from '../../../src/options/OptionsApp'

// 模拟 Chrome 扩展 API
const mockChrome = {
  runtime: {
    sendMessage: jest.fn().mockResolvedValue({ success: true, data: [] }),
    getManifest: jest.fn(() => ({
      name: 'Universe Bag（乾坤袋）',
      version: '1.0.0',
      description: '智能收藏管理工具，支持AI自动分类和云端同步',
      manifest_version: 3,
      permissions: ['storage', 'activeTab', 'contextMenus']
    }))
  }
}

// 模拟 localStorage
const mockLocalStorage = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn()
}
Object.defineProperty(window, 'localStorage', { value: mockLocalStorage })

// 模拟 matchMedia
Object.defineProperty(window, 'matchMedia', {
  value: jest.fn(() => ({
    matches: false,
    addEventListener: jest.fn(),
    removeEventListener: jest.fn()
  }))
})

// 模拟其他组件
jest.mock('../../../src/components/ImportExportTab', () => {
  return function MockImportExportTab() {
    return <div data-testid="import-export-tab">导入导出页面</div>
  }
})

jest.mock('../../../src/components/CategoryManagementTab', () => {
  return function MockCategoryManagementTab() {
    return <div data-testid="category-management-tab">分类管理页面</div>
  }
})

jest.mock('../../../src/components/TagsTab', () => {
  return function MockTagsTab() {
    return <div data-testid="tags-tab">标签管理页面</div>
  }
})

// 模拟其他依赖
jest.mock('../../../src/hooks/useViewMode', () => ({
  useViewMode: () => ({
    viewMode: 'card',
    setViewMode: jest.fn(),
    isLoading: false
  })
}))

jest.mock('../../../src/utils/layoutStability', () => ({
  useViewSwitchStability: () => ({
    containerRef: { current: null },
    displayView: 'card',
    isTransitioning: false
  }),
  useScrollPositionLock: () => ({
    lockScrollPosition: jest.fn()
  })
}))

jest.mock('../../../src/hooks/useAdvancedSearch', () => ({
  useAdvancedSearch: () => ({
    query: '',
    setQuery: jest.fn(),
    results: [],
    suggestions: [],
    isSearching: false,
    hasResults: false,
    totalResults: 0,
    searchTime: 0,
    addFilter: jest.fn(),
    clearFilters: jest.fn()
  })
}))

describe('关于我们和帮助中心页面集成测试', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    ;(global as any).chrome = mockChrome
    mockLocalStorage.getItem.mockReturnValue(null)
    
    // 重置 location
    Object.defineProperty(window, 'location', {
      value: {
        hash: '',
        pathname: '/options.html'
      },
      writable: true
    })
    
    // 模拟 history API
    Object.defineProperty(window, 'history', {
      value: {
        replaceState: jest.fn(),
        pushState: jest.fn()
      },
      writable: true
    })
  })

  afterEach(() => {
    delete (global as any).chrome
  })

  describe('完整的用户流程测试', () => {
    it('应该支持完整的关于我们页面访问流程', async () => {
      const user = userEvent.setup()
      render(<OptionsApp />)
      
      // 等待应用初始化
      await waitFor(() => {
        expect(screen.getByText('Universe Bag')).toBeInTheDocument()
      })
      
      // 点击关于我们导航
      const aboutButton = screen.getByText('关于我们')
      await user.click(aboutButton)
      
      // 验证页面切换
      await waitFor(() => {
        expect(screen.getByText('关于我们')).toBeInTheDocument()
      })
      
      // 验证扩展信息显示
      expect(screen.getByText('Universe Bag（乾坤袋）')).toBeInTheDocument()
      expect(screen.getByText('1.0.0')).toBeInTheDocument()
      expect(screen.getByText('智能收藏管理工具，支持AI自动分类和云端同步')).toBeInTheDocument()
      
      // 验证开发者信息
      expect(screen.getByText('开发者信息')).toBeInTheDocument()
      expect(screen.getByText('coffeebean')).toBeInTheDocument()
      
      // 验证技术信息
      expect(screen.getByText('技术信息')).toBeInTheDocument()
      expect(screen.getByText('扩展环境')).toBeInTheDocument()
      
      // 验证权限信息
      expect(screen.getByText('权限详情')).toBeInTheDocument()
      expect(screen.getByText('storage')).toBeInTheDocument()
      expect(screen.getByText('activeTab')).toBeInTheDocument()
      
      // 验证许可证信息
      expect(screen.getByText('许可证信息')).toBeInTheDocument()
      expect(screen.getByText('MIT License')).toBeInTheDocument()
    })

    it('应该支持完整的帮助中心页面访问流程', async () => {
      const user = userEvent.setup()
      render(<OptionsApp />)
      
      // 等待应用初始化
      await waitFor(() => {
        expect(screen.getByText('Universe Bag')).toBeInTheDocument()
      })
      
      // 点击帮助中心导航
      const helpButton = screen.getByText('帮助中心')
      await user.click(helpButton)
      
      // 等待帮助内容加载
      await waitFor(() => {
        expect(screen.getByText('帮助中心')).toBeInTheDocument()
      }, { timeout: 3000 })
      
      // 验证搜索功能
      const searchInput = screen.getByPlaceholderText('搜索帮助内容...')
      expect(searchInput).toBeInTheDocument()
      
      // 验证分类筛选
      expect(screen.getByText('全部')).toBeInTheDocument()
      expect(screen.getByText('使用指南')).toBeInTheDocument()
      expect(screen.getByText('常见问题')).toBeInTheDocument()
      expect(screen.getByText('故障排除')).toBeInTheDocument()
      
      // 验证帮助内容
      expect(screen.getByText('快速开始')).toBeInTheDocument()
      expect(screen.getByText('收藏管理')).toBeInTheDocument()
      
      // 测试搜索功能
      await user.type(searchInput, '收藏')
      
      // 验证搜索结果统计
      await waitFor(() => {
        expect(screen.getByText(/找到.*个相关结果/)).toBeInTheDocument()
      })
      
      // 测试内容展开
      const sectionButton = screen.getByText('快速开始')
      await user.click(sectionButton)
      
      // 验证内容展开
      await waitFor(() => {
        expect(screen.getByText(/欢迎使用 Universe Bag/)).toBeInTheDocument()
      })
    })

    it('应该支持页面间的导航切换', async () => {
      const user = userEvent.setup()
      render(<OptionsApp />)
      
      await waitFor(() => {
        expect(screen.getByText('Universe Bag')).toBeInTheDocument()
      })
      
      // 切换到关于我们
      await user.click(screen.getByText('关于我们'))
      await waitFor(() => {
        expect(screen.getByText('开发者信息')).toBeInTheDocument()
      })
      
      // 切换到帮助中心
      await user.click(screen.getByText('帮助中心'))
      await waitFor(() => {
        expect(screen.getByPlaceholderText('搜索帮助内容...')).toBeInTheDocument()
      }, { timeout: 3000 })
      
      // 切换回收藏管理
      await user.click(screen.getByText('收藏管理'))
      await waitFor(() => {
        expect(screen.getByText('添加收藏')).toBeInTheDocument()
      })
      
      // 验证导航状态
      expect(screen.getByText('收藏管理')).toHaveAttribute('aria-selected', 'true')
      expect(screen.getByText('关于我们')).toHaveAttribute('aria-selected', 'false')
      expect(screen.getByText('帮助中心')).toHaveAttribute('aria-selected', 'false')
    })
  })

  describe('错误处理集成测试', () => {
    it('应该处理关于页面的加载错误', async () => {
      // 模拟 manifest 读取失败
      mockChrome.runtime.getManifest.mockImplementation(() => {
        throw new Error('Manifest 读取失败')
      })
      
      const user = userEvent.setup()
      render(<OptionsApp />)
      
      await waitFor(() => {
        expect(screen.getByText('Universe Bag')).toBeInTheDocument()
      })
      
      // 切换到关于我们页面
      await user.click(screen.getByText('关于我们'))
      
      // 验证仍然能显示默认信息
      await waitFor(() => {
        expect(screen.getByText('Universe Bag（乾坤袋）')).toBeInTheDocument()
      })
      
      // 验证显示开发环境状态
      expect(screen.getByText('开发环境')).toBeInTheDocument()
    })

    it('应该处理帮助中心的加载错误', async () => {
      const user = userEvent.setup()
      render(<OptionsApp />)
      
      await waitFor(() => {
        expect(screen.getByText('Universe Bag')).toBeInTheDocument()
      })
      
      // 切换到帮助中心
      await user.click(screen.getByText('帮助中心'))
      
      // 即使出错也应该显示基本界面
      await waitFor(() => {
        expect(screen.getByText('帮助中心')).toBeInTheDocument()
      }, { timeout: 3000 })
      
      expect(screen.getByPlaceholderText('搜索帮助内容...')).toBeInTheDocument()
    })
  })

  describe('响应式设计集成测试', () => {
    it('应该在移动设备上正确显示', async () => {
      // 模拟移动设备尺寸
      Object.defineProperty(window, 'innerWidth', { value: 640, writable: true })
      Object.defineProperty(window, 'innerHeight', { value: 960, writable: true })
      
      const user = userEvent.setup()
      render(<OptionsApp />)
      
      await waitFor(() => {
        expect(screen.getByText('Universe Bag')).toBeInTheDocument()
      })
      
      // 验证移动端导航布局
      const navigation = screen.getByRole('navigation')
      expect(navigation).toBeInTheDocument()
      
      // 切换到关于我们页面
      await user.click(screen.getByText('关于我们'))
      
      // 验证移动端内容布局
      await waitFor(() => {
        expect(screen.getByText('关于我们')).toBeInTheDocument()
      })
    })
  })

  describe('主题切换集成测试', () => {
    it('应该支持主题切换功能', async () => {
      const user = userEvent.setup()
      render(<OptionsApp />)
      
      await waitFor(() => {
        expect(screen.getByText('Universe Bag')).toBeInTheDocument()
      })
      
      // 查找主题切换按钮
      const themeToggle = screen.getByRole('button', { name: /切换主题/ })
      expect(themeToggle).toBeInTheDocument()
      
      // 点击切换主题
      await user.click(themeToggle)
      
      // 验证主题已切换（通过 DOM 类名检查）
      await waitFor(() => {
        expect(document.documentElement.classList.contains('dark')).toBe(true)
      })
    })
  })

  describe('键盘导航集成测试', () => {
    it('应该支持键盘快捷键导航', async () => {
      render(<OptionsApp />)
      
      await waitFor(() => {
        expect(screen.getByText('Universe Bag')).toBeInTheDocument()
      })
      
      // 测试 Alt+6 切换到关于我们
      fireEvent.keyDown(document, { key: '6', altKey: true })
      
      await waitFor(() => {
        expect(screen.getByText('开发者信息')).toBeInTheDocument()
      })
      
      // 测试 Alt+7 切换到帮助中心
      fireEvent.keyDown(document, { key: '7', altKey: true })
      
      await waitFor(() => {
        expect(screen.getByPlaceholderText('搜索帮助内容...')).toBeInTheDocument()
      }, { timeout: 3000 })
    })

    it('应该支持 Tab 键导航', async () => {
      const user = userEvent.setup()
      render(<OptionsApp />)
      
      await waitFor(() => {
        expect(screen.getByText('Universe Bag')).toBeInTheDocument()
      })
      
      // 使用 Tab 键导航到关于我们按钮
      const aboutButton = screen.getByText('关于我们')
      aboutButton.focus()
      
      // 使用 Enter 键激活
      await user.keyboard('{Enter}')
      
      await waitFor(() => {
        expect(screen.getByText('开发者信息')).toBeInTheDocument()
      })
    })
  })

  describe('URL 路由集成测试', () => {
    it('应该支持直接访问关于我们页面', async () => {
      window.location.hash = '#about'
      
      render(<OptionsApp />)
      
      await waitFor(() => {
        expect(screen.getByText('开发者信息')).toBeInTheDocument()
      })
      
      expect(screen.getByText('关于我们')).toHaveAttribute('aria-selected', 'true')
    })

    it('应该支持直接访问帮助中心页面', async () => {
      window.location.hash = '#help'
      
      render(<OptionsApp />)
      
      await waitFor(() => {
        expect(screen.getByPlaceholderText('搜索帮助内容...')).toBeInTheDocument()
      }, { timeout: 3000 })
      
      expect(screen.getByText('帮助中心')).toHaveAttribute('aria-selected', 'true')
    })
  })

  describe('性能和稳定性测试', () => {
    it('应该在快速切换页面时保持稳定', async () => {
      const user = userEvent.setup()
      render(<OptionsApp />)
      
      await waitFor(() => {
        expect(screen.getByText('Universe Bag')).toBeInTheDocument()
      })
      
      // 快速切换多个页面
      const pages = ['关于我们', '帮助中心', '收藏管理', '关于我们', '帮助中心']
      
      for (const page of pages) {
        await user.click(screen.getByText(page))
        // 短暂等待确保页面切换
        await waitFor(() => {
          expect(screen.getByText(page)).toHaveAttribute('aria-selected', 'true')
        })
      }
      
      // 验证最终状态
      expect(screen.getByText('帮助中心')).toHaveAttribute('aria-selected', 'true')
    })

    it('应该正确处理并发操作', async () => {
      const user = userEvent.setup()
      render(<OptionsApp />)
      
      await waitFor(() => {
        expect(screen.getByText('Universe Bag')).toBeInTheDocument()
      })
      
      // 同时触发多个操作
      const aboutButton = screen.getByText('关于我们')
      const helpButton = screen.getByText('帮助中心')
      
      // 快速连续点击
      await user.click(aboutButton)
      await user.click(helpButton)
      
      // 验证最终状态是正确的
      await waitFor(() => {
        expect(screen.getByText('帮助中心')).toHaveAttribute('aria-selected', 'true')
      }, { timeout: 3000 })
    })
  })
})