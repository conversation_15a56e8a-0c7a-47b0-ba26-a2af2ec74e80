/**
 * 测试环境全局设置
 * 配置所有测试所需的模拟和工具
 */

import '@testing-library/jest-dom'
import { setupChromeApiMock, mockChrome } from './setup/chrome-api-mock'
import { setupBrowserApiMocks } from './setup/browser-api-mock'
import { extendExpectMatchers } from './setup/style-test-fixes'

// 设置全局 Chrome API 模拟
setupChromeApiMock()

// 设置浏览器 API 模拟
const { localStorageMock } = setupBrowserApiMocks()

// 扩展 expect 匹配器
extendExpectMatchers()

// 导出模拟对象供测试使用
export { mockChrome, localStorageMock }