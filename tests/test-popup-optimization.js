// 测试弹窗优化功能的脚本
// 在浏览器控制台中运行此脚本来测试已收藏状态的优化显示

console.log('🧪 测试弹窗优化功能...')

// 测试步骤
async function testPopupOptimization() {
  console.log('📋 测试步骤:')
  console.log('1. 首先收藏当前页面')
  console.log('2. 然后重新打开扩展弹窗')
  console.log('3. 验证是否显示"您已收藏过"状态')
  console.log('4. 验证是否显示"编辑"和"收藏夹管理"按钮')
  
  try {
    // 步骤1: 收藏当前页面
    console.log('\n🔖 步骤1: 收藏当前页面...')
    const bookmarkResponse = await chrome.runtime.sendMessage({
      type: 'QUICK_BOOKMARK',
      data: {
        title: document.title,
        url: window.location.href,
        favIconUrl: document.querySelector('link[rel="icon"]')?.href || '/favicon.ico',
        timestamp: new Date().toISOString()
      }
    })
    
    if (bookmarkResponse?.success) {
      console.log('✅ 收藏成功，ID:', bookmarkResponse.data.bookmarkId)
      
      // 等待状态更新
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // 步骤2: 检查收藏状态
      console.log('\n🔍 步骤2: 检查收藏状态...')
      const statusResponse = await chrome.runtime.sendMessage({
        type: 'CHECK_BOOKMARK_STATUS',
        data: { url: window.location.href }
      })
      
      if (statusResponse?.success && statusResponse.data.isBookmarked) {
        console.log('✅ 收藏状态正确，bookmarkId:', statusResponse.data.bookmarkId)
        
        // 步骤3: 测试获取收藏详情
        console.log('\n📄 步骤3: 测试获取收藏详情...')
        const bookmarkDetailResponse = await chrome.runtime.sendMessage({
          type: 'GET_BOOKMARK',
          data: { id: statusResponse.data.bookmarkId }
        })
        
        if (bookmarkDetailResponse?.success) {
          console.log('✅ 获取收藏详情成功')
          console.log('收藏详情:', {
            id: bookmarkDetailResponse.data.id,
            title: bookmarkDetailResponse.data.title,
            url: bookmarkDetailResponse.data.url,
            category: bookmarkDetailResponse.data.category,
            tags: bookmarkDetailResponse.data.tags
          })
          
          console.log('\n🎉 所有功能测试通过！')
          console.log('\n📋 现在请手动验证弹窗界面:')
          console.log('1. 点击扩展图标打开弹窗')
          console.log('2. 应该看到"您已收藏过"的绿色提示框')
          console.log('3. 应该看到"编辑"和"收藏夹管理"两个按钮')
          console.log('4. 点击"编辑"按钮应该打开编辑表单')
          console.log('5. 编辑表单标题应该显示"编辑收藏"')
          console.log('6. 保存按钮应该显示"保存修改"')
          
        } else {
          console.log('❌ 获取收藏详情失败:', bookmarkDetailResponse?.error)
        }
        
      } else {
        console.log('❌ 收藏状态检查失败')
      }
      
    } else {
      console.log('❌ 收藏失败:', bookmarkResponse?.error)
    }
    
  } catch (error) {
    console.error('❌ 测试过程异常:', error)
  }
}

// 检查环境并运行测试
if (typeof chrome !== 'undefined' && chrome.runtime) {
  console.log('🚀 开始测试...')
  testPopupOptimization()
} else {
  console.log('⚠️ 请在Chrome扩展环境中运行此测试')
  console.log('1. 构建扩展: npm run build')
  console.log('2. 在Chrome中重新加载扩展')
  console.log('3. 在任意网页的控制台中运行此脚本')
}

// 额外的手动测试指导
console.log('\n📖 手动测试指导:')
console.log('==================')
console.log('测试场景1: 未收藏页面')
console.log('- 访问一个新页面')
console.log('- 点击扩展图标')
console.log('- 应该看到蓝色的"收藏当前页面"按钮')
console.log('')
console.log('测试场景2: 已收藏页面')
console.log('- 在已收藏的页面上')
console.log('- 点击扩展图标')
console.log('- 应该看到绿色的"您已收藏过"提示')
console.log('- 应该看到"编辑"和"收藏夹管理"按钮')
console.log('')
console.log('测试场景3: 编辑功能')
console.log('- 在已收藏页面点击"编辑"按钮')
console.log('- 应该打开编辑表单')
console.log('- 表单应该预填充现有数据')
console.log('- 标题应该显示"编辑收藏"')
console.log('- 保存按钮应该显示"保存修改"')