// 简单的收藏功能测试脚本
// 在浏览器控制台中运行此脚本来测试收藏功能

console.log('🧪 开始测试收藏功能...')

// 模拟收藏数据
const testBookmarkData = {
  title: '测试页面',
  url: 'https://example.com/test',
  favIconUrl: 'https://example.com/favicon.ico',
  timestamp: new Date().toISOString()
}

// 测试快速收藏
async function testQuickBookmark() {
  try {
    console.log('📝 测试快速收藏...')
    
    const response = await chrome.runtime.sendMessage({
      type: 'QUICK_BOOKMARK',
      data: testBookmarkData
    })
    
    if (response.success) {
      console.log('✅ 快速收藏成功:', response.data.bookmarkId)
      return response.data.bookmarkId
    } else {
      console.error('❌ 快速收藏失败:', response.error)
      return null
    }
  } catch (error) {
    console.error('❌ 快速收藏异常:', error)
    return null
  }
}

// 测试收藏状态检查
async function testBookmarkStatus(url) {
  try {
    console.log('🔍 测试收藏状态检查...')
    
    const response = await chrome.runtime.sendMessage({
      type: 'CHECK_BOOKMARK_STATUS',
      data: { url }
    })
    
    if (response.success) {
      console.log('✅ 收藏状态检查成功:', response.data)
      return response.data
    } else {
      console.error('❌ 收藏状态检查失败:', response.error)
      return null
    }
  } catch (error) {
    console.error('❌ 收藏状态检查异常:', error)
    return null
  }
}

// 测试收藏选中文字
async function testBookmarkSelectedText() {
  try {
    console.log('📄 测试收藏选中文字...')
    
    const response = await chrome.runtime.sendMessage({
      type: 'BOOKMARK_SELECTED_TEXT',
      data: {
        selectedText: '这是一段测试文字内容',
        url: 'https://example.com/test',
        title: '测试页面'
      }
    })
    
    if (response.success) {
      console.log('✅ 收藏选中文字成功:', response.data.bookmarkId)
      return response.data.bookmarkId
    } else {
      console.error('❌ 收藏选中文字失败:', response.error)
      return null
    }
  } catch (error) {
    console.error('❌ 收藏选中文字异常:', error)
    return null
  }
}

// 运行所有测试
async function runAllTests() {
  console.log('🚀 开始运行所有测试...')
  
  // 测试1: 快速收藏
  const bookmarkId = await testQuickBookmark()
  
  // 测试2: 检查收藏状态
  if (bookmarkId) {
    await testBookmarkStatus(testBookmarkData.url)
  }
  
  // 测试3: 收藏选中文字
  await testBookmarkSelectedText()
  
  console.log('🎉 所有测试完成！')
}

// 如果在扩展环境中运行
if (typeof chrome !== 'undefined' && chrome.runtime) {
  runAllTests()
} else {
  console.log('⚠️ 请在Chrome扩展环境中运行此测试')
  console.log('1. 构建扩展: npm run build')
  console.log('2. 在Chrome中加载扩展')
  console.log('3. 在任意网页的控制台中运行此脚本')
}