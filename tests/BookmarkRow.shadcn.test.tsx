// BookmarkRow组件shadcn集成测试

import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { describe, it, expect, beforeEach, vi } from 'vitest'
import '@testing-library/jest-dom'
import BookmarkRow from '../src/components/BookmarkRow'
import type { Bookmark } from '../src/types'

// 模拟TruncatedTitle组件
vi.mock('../src/components/TruncatedTitle', () => ({
  default: function MockTruncatedTitle({ title, className }: any) {
    return <span className={className}>{title}</span>
  }
}))

// 创建测试用的收藏数据
const createMockBookmark = (overrides: Partial<Bookmark> = {}): Bookmark => ({
  id: 'test-bookmark-1',
  type: 'url',
  title: '测试收藏标题',
  url: 'https://example.com',
  description: '测试描述',
  tags: ['测试', '标签'],
  category: '测试分类',
  favicon: 'https://example.com/favicon.ico',
  createdAt: new Date('2024-01-01'),
  updatedAt: new Date('2024-01-01'),
  metadata: {
    pageTitle: '测试页面标题',
    siteName: 'example.com',
    aiGenerated: false
  },
  ...overrides
})

describe('BookmarkRow shadcn集成测试', () => {
  const mockOnEdit = vi.fn()
  const mockOnDelete = vi.fn()
  const mockOnClick = vi.fn()

  beforeEach(() => {
    vi.clearAllMocks()
    global.open = vi.fn()
  })

  describe('shadcn组件集成', () => {
    it('应该使用shadcn Card组件作为容器', () => {
      const bookmark = createMockBookmark()
      
      const { container } = render(
        <BookmarkRow bookmark={bookmark} />
      )
      
      // 验证Card组件的基础样式类
      const cardElement = container.querySelector('[class*="rounded-lg"]')
      expect(cardElement).toBeInTheDocument()
      expect(cardElement).toHaveClass('border')
    })

    it('应该使用shadcn Badge组件显示分类', () => {
      const bookmark = createMockBookmark({ category: '测试分类' })
      
      render(<BookmarkRow bookmark={bookmark} />)
      
      // 验证Badge组件的存在和内容
      const badgeElement = screen.getByText('测试分类')
      expect(badgeElement).toBeInTheDocument()
      expect(badgeElement).toHaveClass('inline-flex', 'items-center', 'rounded-full')
    })

    it('应该使用shadcn Button组件作为操作按钮', async () => {
      const bookmark = createMockBookmark()
      
      const { container } = render(
        <BookmarkRow 
          bookmark={bookmark}
          onEdit={mockOnEdit}
          onDelete={mockOnDelete}
        />
      )
      
      // 悬停以显示操作按钮
      const row = container.firstChild as HTMLElement
      fireEvent.mouseEnter(row)
      
      await waitFor(() => {
        // 验证Button组件的样式类
        const buttons = container.querySelectorAll('button')
        buttons.forEach(button => {
          expect(button).toHaveClass('inline-flex', 'items-center', 'justify-center')
        })
      })
    })

    it('应该使用shadcn Tooltip组件提供操作提示', async () => {
      const bookmark = createMockBookmark()
      
      const { container } = render(
        <BookmarkRow 
          bookmark={bookmark}
          onEdit={mockOnEdit}
        />
      )
      
      // 悬停以显示操作按钮
      const row = container.firstChild as HTMLElement
      fireEvent.mouseEnter(row)
      
      await waitFor(() => {
        const editButton = screen.getByLabelText('编辑收藏')
        expect(editButton).toBeInTheDocument()
        
        // 验证按钮具有Tooltip相关的属性
        expect(editButton).toHaveAttribute('data-state')
      })
    })
  })

  describe('shadcn颜色系统', () => {
    it('应该使用shadcn的颜色变量', () => {
      const bookmark = createMockBookmark()
      
      render(<BookmarkRow bookmark={bookmark} />)
      
      // 验证使用了shadcn的颜色类
      const titleElement = screen.getByText('测试收藏标题')
      expect(titleElement).toHaveClass('text-foreground')
      
      const urlElement = screen.getByText('https://example.com')
      expect(urlElement).toHaveClass('text-muted-foreground')
    })

    it('高亮状态应该使用shadcn的accent颜色', () => {
      const bookmark = createMockBookmark()
      
      const { container } = render(
        <BookmarkRow bookmark={bookmark} isHighlighted={true} />
      )
      
      const cardElement = container.querySelector('[class*="bg-accent"]')
      expect(cardElement).toHaveClass('bg-accent', 'border-primary')
    })
  })

  describe('shadcn Button变体', () => {
    it('操作按钮应该使用ghost变体', async () => {
      const bookmark = createMockBookmark()
      
      const { container } = render(
        <BookmarkRow 
          bookmark={bookmark}
          onEdit={mockOnEdit}
          onDelete={mockOnDelete}
        />
      )
      
      // 悬停以显示操作按钮
      const row = container.firstChild as HTMLElement
      fireEvent.mouseEnter(row)
      
      await waitFor(() => {
        const buttons = container.querySelectorAll('button')
        buttons.forEach(button => {
          // 验证ghost变体的hover样式类
          expect(button).toHaveClass('hover:bg-accent')
        })
      })
    })

    it('删除按钮应该有destructive颜色', async () => {
      const bookmark = createMockBookmark()
      
      const { container } = render(
        <BookmarkRow 
          bookmark={bookmark}
          onDelete={mockOnDelete}
        />
      )
      
      // 悬停以显示操作按钮
      const row = container.firstChild as HTMLElement
      fireEvent.mouseEnter(row)
      
      await waitFor(() => {
        const deleteButton = screen.getByLabelText('删除收藏')
        expect(deleteButton).toHaveClass('hover:text-destructive')
      })
    })
  })

  describe('shadcn Badge变体', () => {
    it('分类标签应该使用secondary变体', () => {
      const bookmark = createMockBookmark({ category: '测试分类' })
      
      render(<BookmarkRow bookmark={bookmark} />)
      
      const badgeElement = screen.getByText('测试分类')
      expect(badgeElement).toHaveClass('bg-secondary', 'text-secondary-foreground')
    })

    it('没有分类时不应该显示Badge', () => {
      const bookmark = createMockBookmark({ category: undefined })
      
      const { container } = render(<BookmarkRow bookmark={bookmark} />)
      
      const badgeElement = container.querySelector('[class*="rounded-full"]')
      expect(badgeElement).not.toBeInTheDocument()
    })
  })

  describe('响应式和无障碍性', () => {
    it('应该保持响应式布局类', () => {
      const bookmark = createMockBookmark()
      
      const { container } = render(<BookmarkRow bookmark={bookmark} />)
      
      // 验证flex布局和响应式类
      const contentArea = container.querySelector('.flex-1')
      expect(contentArea).toHaveClass('min-w-0', 'flex', 'items-center')
    })

    it('操作按钮应该有正确的aria-label', async () => {
      const bookmark = createMockBookmark()
      
      const { container } = render(
        <BookmarkRow 
          bookmark={bookmark}
          onEdit={mockOnEdit}
          onDelete={mockOnDelete}
        />
      )
      
      // 悬停以显示操作按钮
      const row = container.firstChild as HTMLElement
      fireEvent.mouseEnter(row)
      
      await waitFor(() => {
        expect(screen.getByLabelText('在新标签页中打开')).toBeInTheDocument()
        expect(screen.getByLabelText('编辑收藏')).toBeInTheDocument()
        expect(screen.getByLabelText('删除收藏')).toBeInTheDocument()
      })
    })
  })

  describe('TooltipProvider集成', () => {
    it('应该正确包装TooltipProvider', () => {
      const bookmark = createMockBookmark()
      
      const { container } = render(<BookmarkRow bookmark={bookmark} />)
      
      // 验证组件被正确渲染（TooltipProvider不会添加特定的DOM元素）
      expect(container.firstChild).toBeInTheDocument()
    })
  })
})