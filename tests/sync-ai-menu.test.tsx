/**
 * 同步和AI辅助菜单功能测试
 * 测试新增菜单项的正确性和功能完整性
 */

import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import '@testing-library/jest-dom'
import OptionsApp from '../src/options/OptionsApp'

// Mock Chrome API
const mockChrome = {
  runtime: {
    id: 'test-extension-id',
    getManifest: () => ({
      version: '1.0.0',
      name: 'Universe Bag'
    })
  }
}

// 设置全局 chrome 对象
Object.defineProperty(global, 'chrome', {
  value: mockChrome,
  writable: true
})

// Mock localStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
}
Object.defineProperty(window, 'localStorage', {
  value: localStorageMock
})

describe('同步和AI辅助菜单测试', () => {
  beforeEach(() => {
    // 清理 localStorage mock
    localStorageMock.getItem.mockClear()
    localStorageMock.setItem.mockClear()
    
    // Mock window.location.hash
    delete (window as any).location
    window.location = { hash: '' } as any
  })

  test('应该渲染同步菜单项', async () => {
    render(<OptionsApp />)
    
    await waitFor(() => {
      expect(screen.getByText('同步')).toBeInTheDocument()
    })
  })

  test('应该渲染AI辅助菜单项', async () => {
    render(<OptionsApp />)
    
    await waitFor(() => {
      expect(screen.getByText('AI辅助')).toBeInTheDocument()
    })
  })

  test('点击同步菜单应该切换到同步页面', async () => {
    render(<OptionsApp />)
    
    await waitFor(() => {
      const syncButton = screen.getByText('同步')
      expect(syncButton).toBeInTheDocument()
    })

    const syncButton = screen.getByText('同步')
    fireEvent.click(syncButton)

    await waitFor(() => {
      expect(screen.getByText('管理您的收藏数据同步，确保多设备间数据一致性')).toBeInTheDocument()
    })
  })

  test('点击AI辅助菜单应该切换到AI辅助页面', async () => {
    render(<OptionsApp />)
    
    await waitFor(() => {
      const aiButton = screen.getByText('AI辅助')
      expect(aiButton).toBeInTheDocument()
    })

    const aiButton = screen.getByText('AI辅助')
    fireEvent.click(aiButton)

    await waitFor(() => {
      expect(screen.getByText('使用人工智能技术提升您的收藏管理体验')).toBeInTheDocument()
    })
  })

  test('同步页面应该包含必要的功能元素', async () => {
    render(<OptionsApp />)
    
    await waitFor(() => {
      const syncButton = screen.getByText('同步')
      fireEvent.click(syncButton)
    })

    await waitFor(() => {
      expect(screen.getByText('自动同步')).toBeInTheDocument()
      expect(screen.getByText('同步频率')).toBeInTheDocument()
      expect(screen.getByText('立即同步')).toBeInTheDocument()
      expect(screen.getByText('同步功能开发中')).toBeInTheDocument()
    })
  })

  test('AI辅助页面应该包含必要的功能元素', async () => {
    render(<OptionsApp />)
    
    await waitFor(() => {
      const aiButton = screen.getByText('AI辅助')
      fireEvent.click(aiButton)
    })

    await waitFor(() => {
      expect(screen.getByText('智能标签建议')).toBeInTheDocument()
      expect(screen.getByText('内容摘要生成')).toBeInTheDocument()
      expect(screen.getByText('智能分类推荐')).toBeInTheDocument()
      expect(screen.getByText('AI助手功能开发中')).toBeInTheDocument()
    })
  })

  test('应该支持通过URL hash直接访问同步页面', async () => {
    // 设置 hash 为 sync
    window.location.hash = '#sync'
    
    render(<OptionsApp />)
    
    await waitFor(() => {
      expect(screen.getByText('管理您的收藏数据同步，确保多设备间数据一致性')).toBeInTheDocument()
    })
  })

  test('应该支持通过URL hash直接访问AI辅助页面', async () => {
    // 设置 hash 为 ai-assistant
    window.location.hash = '#ai-assistant'
    
    render(<OptionsApp />)
    
    await waitFor(() => {
      expect(screen.getByText('使用人工智能技术提升您的收藏管理体验')).toBeInTheDocument()
    })
  })

  test('菜单项应该按正确顺序排列', async () => {
    render(<OptionsApp />)
    
    await waitFor(() => {
      const menuItems = screen.getAllByRole('tab')
      const menuTexts = menuItems.map(item => item.textContent)
      
      // 检查同步和AI辅助菜单的位置
      const syncIndex = menuTexts.findIndex(text => text?.includes('同步'))
      const aiIndex = menuTexts.findIndex(text => text?.includes('AI辅助'))
      const settingsIndex = menuTexts.findIndex(text => text?.includes('设置'))
      
      expect(syncIndex).toBeGreaterThan(-1)
      expect(aiIndex).toBeGreaterThan(-1)
      expect(settingsIndex).toBeGreaterThan(-1)
      
      // 同步应该在AI辅助之前，AI辅助应该在设置之前
      expect(syncIndex).toBeLessThan(aiIndex)
      expect(aiIndex).toBeLessThan(settingsIndex)
    })
  })

  test('同步页面的同步频率选择器应该工作正常', async () => {
    render(<OptionsApp />)
    
    await waitFor(() => {
      const syncButton = screen.getByText('同步')
      fireEvent.click(syncButton)
    })

    await waitFor(() => {
      const frequencySelector = screen.getByDisplayValue('每15分钟')
      expect(frequencySelector).toBeInTheDocument()
    })
  })

  test('AI辅助页面的功能开关应该存在', async () => {
    render(<OptionsApp />)
    
    await waitFor(() => {
      const aiButton = screen.getByText('AI辅助')
      fireEvent.click(aiButton)
    })

    await waitFor(() => {
      // 检查是否有复选框元素
      const checkboxes = screen.getAllByRole('checkbox')
      expect(checkboxes.length).toBeGreaterThanOrEqual(3) // 至少3个功能开关
    })
  })
})

describe('菜单导航功能测试', () => {
  test('应该保存选中的标签页到localStorage', async () => {
    render(<OptionsApp />)
    
    await waitFor(() => {
      const syncButton = screen.getByText('同步')
      fireEvent.click(syncButton)
    })

    await waitFor(() => {
      expect(localStorageMock.setItem).toHaveBeenCalledWith('options-active-tab', 'sync')
    })
  })

  test('应该从localStorage恢复标签页状态', async () => {
    localStorageMock.getItem.mockReturnValue('ai-assistant')
    
    render(<OptionsApp />)
    
    await waitFor(() => {
      expect(screen.getByText('使用人工智能技术提升您的收藏管理体验')).toBeInTheDocument()
    })
  })
})

describe('响应式设计测试', () => {
  test('在移动端应该隐藏菜单文字', async () => {
    // Mock移动端视口
    Object.defineProperty(window, 'innerWidth', {
      writable: true,
      configurable: true,
      value: 375,
    })

    render(<OptionsApp />)
    
    await waitFor(() => {
      // 在移动端，菜单文字应该通过sr-only类隐藏
      const syncText = screen.getByText('同步')
      expect(syncText).toHaveClass('sr-only')
    })
  })
})