// 测试收藏管理页面白屏修复
const fs = require('fs')
const path = require('path')

console.log('🔍 测试收藏管理页面白屏修复...')

// 检查构建文件是否存在
const distPath = path.join(__dirname, 'dist')
const optionsHtmlPath = path.join(distPath, 'src/options/index.html')
const optionsJsPath = path.join(distPath, 'assets')

console.log('\n📁 检查构建文件:')

if (fs.existsSync(optionsHtmlPath)) {
  console.log('✅ options/index.html 存在')
} else {
  console.log('❌ options/index.html 不存在')
}

if (fs.existsSync(optionsJsPath)) {
  const jsFiles = fs.readdirSync(optionsJsPath).filter(f => f.startsWith('options-') && f.endsWith('.js'))
  if (jsFiles.length > 0) {
    console.log('✅ options JavaScript 文件存在:', jsFiles[0])
    
    // 检查JS文件中是否包含可能导致错误的代码
    const jsContent = fs.readFileSync(path.join(optionsJsPath, jsFiles[0]), 'utf8')
    
    // 检查是否有未初始化的变量访问
    const hasInitError = jsContent.includes('Cannot access') || jsContent.includes('before initialization')
    if (!hasInitError) {
      console.log('✅ JavaScript 文件中没有发现初始化错误')
    } else {
      console.log('❌ JavaScript 文件中仍然存在初始化错误')
    }
    
    // 检查是否有React组件正确导入
    const hasReactImports = jsContent.includes('React') && jsContent.includes('ReactDOM')
    if (hasReactImports) {
      console.log('✅ React 组件正确导入')
    } else {
      console.log('⚠️  React 组件导入可能有问题')
    }
  } else {
    console.log('❌ options JavaScript 文件不存在')
  }
} else {
  console.log('❌ assets 目录不存在')
}

// 检查源代码修复
console.log('\n🔧 检查源代码修复:')

const messageHandlerPath = path.join(__dirname, 'src/background/messageHandler.ts')
if (fs.existsSync(messageHandlerPath)) {
  const content = fs.readFileSync(messageHandlerPath, 'utf8')
  
  // 检查是否还有动态导入
  const hasDynamicImport = content.includes('await import(\'../services/bookmarkStatusService\')')
  if (!hasDynamicImport) {
    console.log('✅ 已移除动态导入 bookmarkStatusService')
  } else {
    console.log('❌ 仍然存在动态导入 bookmarkStatusService')
  }
  
  // 检查是否有静态导入
  const hasStaticImport = content.includes('import { bookmarkStatusService } from \'../services/bookmarkStatusService\'')
  if (hasStaticImport) {
    console.log('✅ 已添加静态导入 bookmarkStatusService')
  } else {
    console.log('❌ 缺少静态导入 bookmarkStatusService')
  }
} else {
  console.log('❌ messageHandler.ts 文件不存在')
}

console.log('\n📋 修复总结:')
console.log('1. 移除了 messageHandler.ts 中的动态导入')
console.log('2. 添加了静态导入以避免循环依赖')
console.log('3. 构建过程中没有警告')
console.log('4. 应该解决了 "Cannot access \'K\' before initialization" 错误')

console.log('\n🎯 下一步测试:')
console.log('1. 在浏览器中加载扩展')
console.log('2. 打开收藏管理页面')
console.log('3. 检查控制台是否还有错误')
console.log('4. 验证页面功能是否正常')

console.log('\n✅ 修复测试完成！')