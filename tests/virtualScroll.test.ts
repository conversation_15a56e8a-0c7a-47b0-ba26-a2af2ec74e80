// 虚拟滚动测试

import { describe, it, expect, beforeEach } from 'vitest'
import { VirtualScrollManager } from '../src/utils/virtualScroll'

describe('VirtualScrollManager测试', () => {
  let manager: VirtualScrollManager
  const mockItems = Array.from({ length: 1000 }, (_, i) => ({
    id: `item-${i}`,
    title: `Item ${i}`,
    content: `Content for item ${i}`
  }))

  beforeEach(() => {
    manager = new VirtualScrollManager({
      containerHeight: 400,
      itemHeight: 50,
      overscan: 5
    })
  })

  describe('基本功能测试', () => {
    it('应该正确初始化', () => {
      expect(manager).toBeDefined()
      expect(manager.getScrollTop()).toBe(0)
    })

    it('应该正确计算可见范围', () => {
      const result = manager.calculateVisibleRange(mockItems)
      
      expect(result.startIndex).toBe(0)
      expect(result.endIndex).toBeGreaterThan(0)
      expect(result.visibleItems).toHaveLength(result.endIndex - result.startIndex + 1)
      expect(result.totalHeight).toBe(mockItems.length * 50)
      expect(result.offsetY).toBe(0)
    })

    it('应该正确处理空列表', () => {
      const result = manager.calculateVisibleRange([])
      
      expect(result.startIndex).toBe(0)
      expect(result.endIndex).toBe(0)
      expect(result.visibleItems).toHaveLength(0)
      expect(result.totalHeight).toBe(0)
      expect(result.offsetY).toBe(0)
    })
  })

  describe('滚动功能测试', () => {
    it('应该正确更新滚动位置', () => {
      manager.updateScrollTop(100)
      expect(manager.getScrollTop()).toBe(100)
      
      // 负值应该被重置为0
      manager.updateScrollTop(-50)
      expect(manager.getScrollTop()).toBe(0)
    })

    it('应该根据滚动位置计算正确的可见范围', () => {
      manager.updateScrollTop(250) // 滚动到第5个项目附近
      
      const result = manager.calculateVisibleRange(mockItems)
      
      expect(result.startIndex).toBeGreaterThanOrEqual(0)
      expect(result.endIndex).toBeGreaterThan(result.startIndex)
      expect(result.offsetY).toBeGreaterThanOrEqual(0)
      
      // 验证滚动位置影响了可见范围
      const scrollTop = manager.getScrollTop()
      expect(scrollTop).toBe(250)
    })

    it('应该正确滚动到指定索引', () => {
      // 先初始化位置信息
      manager.calculateVisibleRange(mockItems)
      
      const targetIndex = 10
      const scrollTop = manager.scrollToIndex(targetIndex)
      
      expect(scrollTop).toBe(targetIndex * 50)
      
      // 超出范围的索引应该返回当前位置
      const invalidScrollTop = manager.scrollToIndex(-1)
      expect(invalidScrollTop).toBe(manager.getScrollTop())
    })
  })

  describe('缓冲区测试', () => {
    it('应该正确应用缓冲区', () => {
      manager.updateScrollTop(250) // 滚动到中间位置
      
      const result = manager.calculateVisibleRange(mockItems)
      
      // 开始索引应该考虑缓冲区
      const expectedStartWithoutOverscan = Math.floor(250 / 50)
      expect(result.startIndex).toBeLessThanOrEqual(expectedStartWithoutOverscan)
      
      // 结束索引也应该考虑缓冲区
      const visibleCount = Math.ceil(400 / 50)
      const expectedEndWithoutOverscan = expectedStartWithoutOverscan + visibleCount - 1
      expect(result.endIndex).toBeGreaterThanOrEqual(expectedEndWithoutOverscan)
    })
  })

  describe('动态高度测试', () => {
    beforeEach(() => {
      manager = new VirtualScrollManager({
        containerHeight: 400,
        getItemHeight: (index, item) => {
          // 奇数索引的项目高度为80，偶数为50
          return index % 2 === 0 ? 50 : 80
        },
        overscan: 3
      })
    })

    it('应该正确处理动态高度', () => {
      const result = manager.calculateVisibleRange(mockItems)
      
      expect(result.totalHeight).toBeGreaterThan(mockItems.length * 50)
      expect(result.visibleItems.length).toBeGreaterThan(0)
    })

    it('应该正确更新项目高度', () => {
      const initialResult = manager.calculateVisibleRange(mockItems)
      const initialTotalHeight = initialResult.totalHeight
      
      // 更新第一个项目的高度
      manager.updateItemHeight(0, 100)
      
      const updatedResult = manager.calculateVisibleRange(mockItems)
      expect(updatedResult.totalHeight).toBeGreaterThan(initialTotalHeight)
    })
  })

  describe('配置更新测试', () => {
    it('应该正确更新配置', () => {
      manager.updateConfig({
        containerHeight: 600,
        itemHeight: 80,
        overscan: 10
      })
      
      const result = manager.calculateVisibleRange(mockItems)
      
      // 新的配置应该影响计算结果
      expect(result.totalHeight).toBe(mockItems.length * 80)
    })
  })

  describe('边界情况测试', () => {
    it('应该正确处理单个项目', () => {
      const singleItem = [mockItems[0]]
      const result = manager.calculateVisibleRange(singleItem)
      
      expect(result.startIndex).toBe(0)
      expect(result.endIndex).toBe(0)
      expect(result.visibleItems).toHaveLength(1)
      expect(result.totalHeight).toBe(50)
    })

    it('应该正确处理滚动到底部', () => {
      const totalHeight = mockItems.length * 50
      manager.updateScrollTop(totalHeight - 200) // 滚动到接近底部
      
      const result = manager.calculateVisibleRange(mockItems)
      
      expect(result.endIndex).toBe(mockItems.length - 1)
      expect(result.visibleItems.length).toBeGreaterThan(0)
    })

    it('应该正确处理容器高度大于总内容高度', () => {
      const shortList = mockItems.slice(0, 5) // 只有5个项目
      const result = manager.calculateVisibleRange(shortList)
      
      expect(result.startIndex).toBe(0)
      expect(result.endIndex).toBe(4)
      expect(result.visibleItems).toHaveLength(5)
    })
  })

  describe('性能测试', () => {
    it('应该在合理时间内完成大量数据的计算', () => {
      const largeItems = Array.from({ length: 10000 }, (_, i) => ({
        id: `item-${i}`,
        title: `Item ${i}`
      }))
      
      const startTime = performance.now()
      const result = manager.calculateVisibleRange(largeItems)
      const endTime = performance.now()
      
      expect(endTime - startTime).toBeLessThan(50) // 应该在50ms内完成
      expect(result.visibleItems.length).toBeGreaterThan(0)
    })

    it('应该正确处理频繁的滚动更新', () => {
      const scrollPositions = [0, 50, 100, 150, 200, 250, 300]
      
      scrollPositions.forEach(scrollTop => {
        manager.updateScrollTop(scrollTop)
        const result = manager.calculateVisibleRange(mockItems)
        
        expect(result.visibleItems.length).toBeGreaterThan(0)
        expect(result.startIndex).toBeGreaterThanOrEqual(0)
        expect(result.endIndex).toBeLessThan(mockItems.length)
      })
    })
  })
})