// BookmarkCompact组件shadcn重构测试

import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import '@testing-library/jest-dom'
import BookmarkCompact from '../src/components/BookmarkCompact'
import type { Bookmark } from '../src/types'

// 模拟TruncatedTitle组件
vi.mock('../src/components/TruncatedTitle', () => ({
  default: function MockTruncatedTitle({ title, className }: any) {
    return <span className={className}>{title}</span>
  }
}))

// 创建测试用的收藏数据
const createMockBookmark = (overrides: Partial<Bookmark> = {}): Bookmark => ({
  id: 'test-bookmark-1',
  type: 'url',
  title: '测试收藏标题',
  url: 'https://example.com',
  description: '这是一个测试描述，用于验证紧凑视图的显示效果',
  tags: ['测试', '标签', '紧凑视图'],
  category: '测试分类',
  favicon: 'https://example.com/favicon.ico',
  createdAt: new Date('2024-01-01'),
  updatedAt: new Date('2024-01-01'),
  metadata: {
    pageTitle: '测试页面标题',
    siteName: 'example.com',
    aiGenerated: false
  },
  ...overrides
})

describe('BookmarkCompact组件 - shadcn重构版本', () => {
  const mockOnEdit = vi.fn()
  const mockOnDelete = vi.fn()
  const mockOnClick = vi.fn()

  beforeEach(() => {
    vi.clearAllMocks()
    // 模拟window.open
    global.open = vi.fn()
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('shadcn组件集成', () => {
    it('应该使用shadcn Card组件作为容器', () => {
      const bookmark = createMockBookmark()
      
      const { container } = render(
        <BookmarkCompact bookmark={bookmark} />
      )

      // 验证Card组件的存在
      const cardElement = container.querySelector('[class*="rounded-lg"][class*="border"]')
      expect(cardElement).toBeInTheDocument()
    })

    it('应该使用shadcn Button组件替换操作按钮', async () => {
      const bookmark = createMockBookmark()
      
      const { container } = render(
        <BookmarkCompact 
          bookmark={bookmark}
          onEdit={mockOnEdit}
          onDelete={mockOnDelete}
        />
      )
      
      // 悬停以显示操作按钮
      const card = container.firstChild as HTMLElement
      fireEvent.mouseEnter(card)
      
      await waitFor(() => {
        // 验证shadcn Button组件的存在
        const buttons = container.querySelectorAll('button[class*="inline-flex"]')
        expect(buttons.length).toBeGreaterThan(0)
      })
    })

    it('应该使用shadcn Badge组件显示分类和标签', () => {
      const bookmark = createMockBookmark()
      
      const { container } = render(
        <BookmarkCompact bookmark={bookmark} />
      )

      // 验证Badge组件的存在（分类）
      const categoryBadge = container.querySelector('[class*="inline-flex"][class*="rounded-full"]')
      expect(categoryBadge).toBeInTheDocument()
      expect(categoryBadge).toHaveTextContent('测试分类')

      // 验证Badge组件的存在（标签）
      const tagBadges = container.querySelectorAll('[class*="inline-flex"][class*="rounded-full"]')
      expect(tagBadges.length).toBeGreaterThanOrEqual(2) // 至少有分类和标签
    })

    it('应该使用shadcn Tooltip组件提供操作提示', async () => {
      const bookmark = createMockBookmark()
      
      const { container } = render(
        <BookmarkCompact 
          bookmark={bookmark}
          onEdit={mockOnEdit}
          onDelete={mockOnDelete}
        />
      )
      
      // 悬停以显示操作按钮
      const card = container.firstChild as HTMLElement
      fireEvent.mouseEnter(card)
      
      await waitFor(() => {
        // 验证Tooltip触发器的存在（通过data-state属性）
        const tooltipTriggers = container.querySelectorAll('[data-state]')
        expect(tooltipTriggers.length).toBeGreaterThan(0)
      })
    })
  })

  describe('shadcn主题系统集成', () => {
    it('应该使用shadcn颜色变量', () => {
      const bookmark = createMockBookmark()
      
      const { container } = render(
        <BookmarkCompact bookmark={bookmark} />
      )

      // 验证使用了shadcn的颜色类
      const titleElement = screen.getByText('测试收藏标题')
      expect(titleElement).toHaveClass('text-foreground')

      const urlElement = screen.getByText('https://example.com')
      expect(urlElement).toHaveClass('text-muted-foreground')
    })

    it('高亮状态应该使用shadcn主题颜色', () => {
      const bookmark = createMockBookmark()
      
      const { container } = render(
        <BookmarkCompact bookmark={bookmark} isHighlighted={true} />
      )
      
      const cardElement = container.firstChild as HTMLElement
      expect(cardElement).toHaveClass('ring-2', 'ring-primary', 'border-primary', 'bg-accent')
    })

    it('应该使用shadcn的响应式和间距系统', () => {
      const bookmark = createMockBookmark()
      
      const { container } = render(
        <BookmarkCompact bookmark={bookmark} />
      )

      // 验证使用了shadcn的间距类
      const cardContent = container.querySelector('[class*="p-3"]')
      expect(cardContent).toBeInTheDocument()
    })
  })

  describe('shadcn组件交互行为', () => {
    it('shadcn Button组件应该正确处理点击事件', async () => {
      const bookmark = createMockBookmark()
      
      const { container } = render(
        <BookmarkCompact 
          bookmark={bookmark}
          onEdit={mockOnEdit}
        />
      )
      
      // 悬停以显示操作按钮
      const card = container.firstChild as HTMLElement
      fireEvent.mouseEnter(card)
      
      await waitFor(() => {
        const editButton = screen.getByLabelText('编辑收藏')
        fireEvent.click(editButton)
      })
      
      expect(mockOnEdit).toHaveBeenCalledWith(bookmark)
    })

    it('shadcn Tooltip应该在悬停时显示提示', async () => {
      const bookmark = createMockBookmark()
      
      const { container } = render(
        <BookmarkCompact 
          bookmark={bookmark}
          onEdit={mockOnEdit}
        />
      )
      
      // 悬停以显示操作按钮
      const card = container.firstChild as HTMLElement
      fireEvent.mouseEnter(card)
      
      await waitFor(() => {
        // 验证Tooltip组件已经被渲染（通过data-state属性）
        const tooltipTriggers = container.querySelectorAll('[data-state]')
        expect(tooltipTriggers.length).toBeGreaterThan(0)
      })
    })

    it('shadcn Badge组件应该正确显示标签内容', () => {
      const bookmark = createMockBookmark({
        tags: ['标签1', '标签2', '标签3', '标签4']
      })
      
      render(<BookmarkCompact bookmark={bookmark} />)
      
      // 验证前两个标签显示
      expect(screen.getByText('标签1')).toBeInTheDocument()
      expect(screen.getByText('标签2')).toBeInTheDocument()
      expect(screen.getByText('+2')).toBeInTheDocument()
    })
  })

  describe('自定义样式移除验证', () => {
    it('不应该包含旧的自定义CSS类', () => {
      const bookmark = createMockBookmark()
      
      const { container } = render(
        <BookmarkCompact bookmark={bookmark} />
      )

      // 验证不再使用旧的自定义样式类
      expect(container.innerHTML).not.toContain('bg-white')
      expect(container.innerHTML).not.toContain('border-gray-200')
      expect(container.innerHTML).not.toContain('text-gray-900')
      expect(container.innerHTML).not.toContain('text-gray-500')
      expect(container.innerHTML).not.toContain('bg-gray-100')
    })

    it('应该使用shadcn原生样式类', () => {
      const bookmark = createMockBookmark()
      
      const { container } = render(
        <BookmarkCompact bookmark={bookmark} />
      )

      // 验证使用了shadcn的样式类
      expect(container.innerHTML).toContain('text-foreground')
      expect(container.innerHTML).toContain('text-muted-foreground')
      // bg-muted只在有文本内容时才出现，这里测试基础样式
      expect(container.innerHTML).toContain('bg-card')
    })
  })

  describe('shadcn组件可访问性', () => {
    it('shadcn Button组件应该有正确的aria属性', async () => {
      const bookmark = createMockBookmark()
      
      const { container } = render(
        <BookmarkCompact 
          bookmark={bookmark}
          onEdit={mockOnEdit}
          onDelete={mockOnDelete}
        />
      )
      
      // 悬停以显示操作按钮
      const card = container.firstChild as HTMLElement
      fireEvent.mouseEnter(card)
      
      await waitFor(() => {
        expect(screen.getByLabelText('在新标签页中打开')).toBeInTheDocument()
        expect(screen.getByLabelText('编辑收藏')).toBeInTheDocument()
        expect(screen.getByLabelText('删除收藏')).toBeInTheDocument()
      })
    })

    it('shadcn组件应该支持键盘导航', async () => {
      const bookmark = createMockBookmark()
      
      const { container } = render(
        <BookmarkCompact 
          bookmark={bookmark}
          onEdit={mockOnEdit}
        />
      )
      
      // 悬停以显示操作按钮
      const card = container.firstChild as HTMLElement
      fireEvent.mouseEnter(card)
      
      await waitFor(() => {
        const editButton = screen.getByLabelText('编辑收藏')
        
        // 测试键盘焦点
        editButton.focus()
        expect(editButton).toHaveFocus()
        
        // 测试点击事件（shadcn Button组件通过点击处理键盘事件）
        fireEvent.click(editButton)
        expect(mockOnEdit).toHaveBeenCalledWith(bookmark)
      })
    })
  })

  describe('shadcn组件性能', () => {
    it('应该正确使用React.memo优化渲染', () => {
      const bookmark = createMockBookmark()
      
      const { rerender } = render(
        <BookmarkCompact bookmark={bookmark} />
      )
      
      // 重新渲染相同的props不应该导致重新渲染
      rerender(<BookmarkCompact bookmark={bookmark} />)
      
      // 验证组件有displayName
      expect(BookmarkCompact.displayName).toBe('BookmarkCompact')
    })

    it('shadcn组件应该支持懒加载', () => {
      const bookmark = createMockBookmark()
      
      const { container } = render(
        <BookmarkCompact bookmark={bookmark} />
      )
      
      // 验证组件正确渲染
      expect(container.firstChild).toBeInTheDocument()
    })
  })

  describe('边界情况处理', () => {
    it('应该正确处理无标签的情况', () => {
      const bookmark = createMockBookmark({ tags: [] })
      
      const { container } = render(<BookmarkCompact bookmark={bookmark} />)
      
      // 不应该有Tag图标
      const tagIcon = container.querySelector('.lucide-tag')
      expect(tagIcon).not.toBeInTheDocument()
    })

    it('应该正确处理无分类的情况', () => {
      const bookmark = createMockBookmark({ category: undefined })
      
      render(<BookmarkCompact bookmark={bookmark} />)
      
      // 分类Badge不应该显示
      expect(screen.queryByText('测试分类')).not.toBeInTheDocument()
    })

    it('应该正确处理文本类型的内容预览', () => {
      const bookmark = createMockBookmark({
        type: 'text',
        content: '这是一段测试文本内容'
      })
      
      const { container } = render(<BookmarkCompact bookmark={bookmark} />)
      
      // 验证使用了shadcn的背景色
      const contentPreview = container.querySelector('.bg-muted')
      expect(contentPreview).toBeInTheDocument()
      expect(screen.getByText('这是一段测试文本内容')).toBeInTheDocument()
    })
  })
})