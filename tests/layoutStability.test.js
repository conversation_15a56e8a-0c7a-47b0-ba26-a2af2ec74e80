/**
 * 布局稳定性工具测试
 */

import { renderHook, act } from '@testing-library/react'
import { 
  useContainerSize, 
  useSmoothTransition, 
  useLayoutLock, 
  useContentLoading,
  useViewSwitchStability 
} from '../src/utils/layoutStability'
import { vi } from 'vitest'

// Mock ResizeObserver
global.ResizeObserver = vi.fn().mockImplementation((callback) => ({
  observe: vi.fn(),
  disconnect: vi.fn(),
  unobserve: vi.fn(),
}))

// Mock requestAnimationFrame
global.requestAnimationFrame = vi.fn((cb) => setTimeout(cb, 16))

beforeEach(() => {
  vi.useFakeTimers()
})

afterEach(() => {
  vi.runOnlyPendingTimers()
  vi.useRealTimers()
})

describe('useContainerSize Hook', () => {
  test('应该初始化容器尺寸为0', () => {
    const { result } = renderHook(() => useContainerSize())
    
    expect(result.current.size).toEqual({ width: 0, height: 0 })
    expect(result.current.isStable).toBe(true)
    expect(result.current.containerRef).toBeDefined()
  })
})

describe('useSmoothTransition Hook', () => {
  test('应该初始化显示值为传入值', () => {
    const { result } = renderHook(() => useSmoothTransition('initial'))
    
    expect(result.current.displayValue).toBe('initial')
    expect(result.current.isTransitioning).toBe(false)
  })

  test('应该在值变化时触发过渡', () => {
    const { result, rerender } = renderHook(
      ({ value }) => useSmoothTransition(value, 300),
      { initialProps: { value: 'initial' } }
    )

    // 更新值
    rerender({ value: 'updated' })

    expect(result.current.isTransitioning).toBe(true)
    expect(result.current.displayValue).toBe('initial') // 还是旧值

    // 等待过渡时间的一半
    act(() => {
      vi.advanceTimersByTime(150)
    })

    expect(result.current.displayValue).toBe('updated') // 现在是新值
    expect(result.current.isTransitioning).toBe(false)
  })
})

describe('useViewSwitchStability Hook', () => {
  test('应该管理视图切换的稳定性', () => {
    const { result, rerender } = renderHook(
      ({ view }) => useViewSwitchStability(view, 300),
      { initialProps: { view: 'card' } }
    )

    expect(result.current.displayView).toBe('card')
    expect(result.current.isTransitioning).toBe(false)
    expect(result.current.containerRef).toBeDefined()

    // 切换视图
    rerender({ view: 'row' })

    expect(result.current.isTransitioning).toBe(true)
    expect(result.current.displayView).toBe('card') // 还是旧视图

    // 等待过渡时间的一半
    act(() => {
      vi.advanceTimersByTime(150)
    })

    expect(result.current.displayView).toBe('row') // 现在是新视图
  })
})