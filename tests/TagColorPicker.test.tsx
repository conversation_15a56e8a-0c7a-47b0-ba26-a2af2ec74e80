import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { TagColorPicker } from '../src/components/TagColorPicker'
import { ColorUtils } from '../src/utils/colorUtils'
import { vi } from 'vitest'

// Mock ColorUtils
vi.mock('../src/utils/colorUtils', () => ({
  ColorUtils: {
    PRESET_COLORS: [
      '#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6'
    ],
    isValidColor: vi.fn(),
    getContrastColor: vi.fn(),
    generateColorFromString: vi.fn()
  }
}))

const mockColorUtils = ColorUtils as any

describe('TagColorPicker', () => {
  const defaultProps = {
    onChange: vi.fn()
  }

  beforeEach(() => {
    vi.clearAllMocks()
    mockColorUtils.isValidColor.mockImplementation((color: string) => {
      return /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/.test(color) ||
             /^rgb\(\s*\d{1,3}\s*,\s*\d{1,3}\s*,\s*\d{1,3}\s*\)$/.test(color)
    })
    mockColorUtils.getContrastColor.mockReturnValue('#000000')
  })

  describe('基本渲染', () => {
    it('应该正确渲染基本组件', () => {
      render(<TagColorPicker {...defaultProps} />)
      
      expect(screen.getByText('选择颜色')).toBeInTheDocument()
      expect(screen.getByText('预设颜色')).toBeInTheDocument()
      expect(screen.getByText('自定义颜色')).toBeInTheDocument()
    })

    it('应该显示自定义占位符文本', () => {
      render(<TagColorPicker {...defaultProps} placeholder="选择标签颜色" />)
      
      expect(screen.getByText('选择标签颜色')).toBeInTheDocument()
    })

    it('应该渲染预设颜色网格', () => {
      render(<TagColorPicker {...defaultProps} />)
      
      // 检查预设颜色按钮数量
      const colorButtons = screen.getAllByRole('button').filter(button => 
        button.getAttribute('title')?.includes('选择颜色:')
      )
      expect(colorButtons).toHaveLength(mockColorUtils.PRESET_COLORS.length)
    })

    it('应该显示当前选中的颜色', () => {
      const selectedColor = '#3B82F6'
      render(<TagColorPicker {...defaultProps} value={selectedColor} />)
      
      expect(screen.getByText(selectedColor)).toBeInTheDocument()
    })

    it('应该在未选择颜色时显示默认状态', () => {
      render(<TagColorPicker {...defaultProps} />)
      
      expect(screen.getByText('未选择颜色')).toBeInTheDocument()
      expect(screen.getByText('?')).toBeInTheDocument()
    })
  })

  describe('预设颜色选择', () => {
    it('应该能够选择预设颜色', async () => {
      const user = userEvent.setup()
      const onChange = vi.fn()
      render(<TagColorPicker onChange={onChange} />)
      
      const firstColorButton = screen.getAllByRole('button').find(button => 
        button.getAttribute('title')?.includes('#3B82F6')
      )
      
      if (firstColorButton) {
        await user.click(firstColorButton)
        expect(onChange).toHaveBeenCalledWith('#3B82F6')
      }
    })

    it('应该高亮显示当前选中的预设颜色', () => {
      const selectedColor = '#3B82F6'
      render(<TagColorPicker {...defaultProps} value={selectedColor} />)
      
      const selectedButton = screen.getAllByRole('button').find(button => 
        button.getAttribute('title')?.includes(selectedColor)
      )
      
      expect(selectedButton).toHaveClass('ring-2', 'ring-blue-500', 'scale-110')
    })

    it('应该在选择预设颜色时隐藏自定义输入', async () => {
      const user = userEvent.setup()
      const onChange = vi.fn()
      render(<TagColorPicker onChange={onChange} />)
      
      // 先显示自定义输入
      await user.click(screen.getByText('自定义'))
      expect(screen.getByPlaceholderText(/输入颜色值/)).toBeInTheDocument()
      
      // 选择预设颜色
      const firstColorButton = screen.getAllByRole('button').find(button => 
        button.getAttribute('title')?.includes('#3B82F6')
      )
      
      if (firstColorButton) {
        await user.click(firstColorButton)
        expect(screen.queryByPlaceholderText(/输入颜色值/)).not.toBeInTheDocument()
      }
    })
  })

  describe('自定义颜色功能', () => {
    it('应该能够切换自定义颜色输入显示', async () => {
      const user = userEvent.setup()
      render(<TagColorPicker {...defaultProps} />)
      
      // 初始状态不显示自定义输入
      expect(screen.queryByPlaceholderText(/输入颜色值/)).not.toBeInTheDocument()
      
      // 点击自定义按钮显示输入
      await user.click(screen.getByText('自定义'))
      expect(screen.getByPlaceholderText(/输入颜色值/)).toBeInTheDocument()
      
      // 点击取消按钮隐藏输入
      await user.click(screen.getByText('取消'))
      expect(screen.queryByPlaceholderText(/输入颜色值/)).not.toBeInTheDocument()
    })

    it('应该能够输入自定义颜色', async () => {
      const user = userEvent.setup()
      const onChange = vi.fn()
      mockColorUtils.isValidColor.mockReturnValue(true)
      
      render(<TagColorPicker onChange={onChange} />)
      
      // 显示自定义输入
      await user.click(screen.getByText('自定义'))
      
      const input = screen.getByPlaceholderText(/输入颜色值/)
      await user.type(input, '#ff0000')
      
      expect(onChange).toHaveBeenCalledWith('#ff0000')
    })

    it('应该验证自定义颜色格式', async () => {
      const user = userEvent.setup()
      const onChange = vi.fn()
      mockColorUtils.isValidColor.mockReturnValue(false)
      
      render(<TagColorPicker onChange={onChange} />)
      
      // 显示自定义输入
      await user.click(screen.getByText('自定义'))
      
      const input = screen.getByPlaceholderText(/输入颜色值/)
      await user.type(input, 'invalid-color')
      
      expect(screen.getByText('无效的颜色格式')).toBeInTheDocument()
      expect(onChange).not.toHaveBeenCalled()
    })

    it('应该在失焦时显示详细的验证错误', async () => {
      const user = userEvent.setup()
      mockColorUtils.isValidColor.mockReturnValue(false)
      
      render(<TagColorPicker {...defaultProps} />)
      
      // 显示自定义输入
      await user.click(screen.getByText('自定义'))
      
      const input = screen.getByPlaceholderText(/输入颜色值/)
      await user.type(input, 'invalid')
      await user.tab() // 触发失焦
      
      expect(screen.getByText(/请输入有效的颜色格式/)).toBeInTheDocument()
    })

    it('应该支持HTML颜色选择器', async () => {
      const user = userEvent.setup()
      const onChange = vi.fn()
      mockColorUtils.isValidColor.mockReturnValue(true)
      
      render(<TagColorPicker onChange={onChange} />)
      
      // 显示自定义输入
      await user.click(screen.getByText('自定义'))
      
      const colorInput = screen.getByTitle('使用颜色选择器')
      fireEvent.change(colorInput, { target: { value: '#00ff00' } })
      
      expect(onChange).toHaveBeenCalledWith('#00ff00')
    })

    it('应该在allowCustom为false时隐藏自定义颜色功能', () => {
      render(<TagColorPicker {...defaultProps} allowCustom={false} />)
      
      expect(screen.queryByText('自定义颜色')).not.toBeInTheDocument()
      expect(screen.queryByText('自定义')).not.toBeInTheDocument()
    })
  })

  describe('颜色预览和反馈', () => {
    it('应该显示当前颜色的预览', () => {
      const selectedColor = '#3B82F6'
      render(<TagColorPicker {...defaultProps} value={selectedColor} />)
      
      // 查找颜色预览容器（通过类名和样式）
      const preview = document.querySelector('.w-8.h-8.rounded-md.border-2')
      expect(preview).toHaveStyle(`background-color: ${selectedColor}`)
    })

    it('应该在未选择颜色时显示默认预览', () => {
      render(<TagColorPicker {...defaultProps} />)
      
      const preview = screen.getByText('?').parentElement
      expect(preview).toHaveStyle('background-color: #f3f4f6')
    })

    it('应该显示颜色值文本', () => {
      const selectedColor = '#3B82F6'
      render(<TagColorPicker {...defaultProps} value={selectedColor} />)
      
      expect(screen.getByText(selectedColor)).toBeInTheDocument()
    })
  })

  describe('清除颜色功能', () => {
    it('应该在有颜色时显示清除按钮', () => {
      render(<TagColorPicker {...defaultProps} value="#3B82F6" />)
      
      expect(screen.getByText('清除颜色')).toBeInTheDocument()
    })

    it('应该在无颜色时隐藏清除按钮', () => {
      render(<TagColorPicker {...defaultProps} />)
      
      expect(screen.queryByText('清除颜色')).not.toBeInTheDocument()
    })

    it('应该能够清除选中的颜色', async () => {
      const user = userEvent.setup()
      const onChange = vi.fn()
      render(<TagColorPicker onChange={onChange} value="#3B82F6" />)
      
      await user.click(screen.getByText('清除颜色'))
      
      expect(onChange).toHaveBeenCalledWith('')
    })
  })

  describe('禁用状态', () => {
    it('应该在禁用时禁用所有交互', () => {
      render(<TagColorPicker {...defaultProps} disabled={true} value="#3B82F6" />)
      
      // 检查预设颜色按钮被禁用
      const colorButtons = screen.getAllByRole('button').filter(button => 
        button.getAttribute('title')?.includes('选择颜色:')
      )
      colorButtons.forEach(button => {
        expect(button).toBeDisabled()
      })
      
      // 检查清除按钮被禁用
      expect(screen.getByText('清除颜色')).toBeDisabled()
    })

    it('应该在禁用时不能切换自定义输入', async () => {
      const user = userEvent.setup()
      render(<TagColorPicker {...defaultProps} disabled={true} />)
      
      const customButton = screen.getByText('自定义')
      expect(customButton).toHaveClass('cursor-not-allowed')
      
      await user.click(customButton)
      expect(screen.queryByPlaceholderText(/输入颜色值/)).not.toBeInTheDocument()
    })
  })

  describe('自定义预设颜色', () => {
    it('应该使用自定义的预设颜色列表', () => {
      const customColors = ['#ff0000', '#00ff00', '#0000ff']
      render(<TagColorPicker {...defaultProps} presetColors={customColors} />)
      
      const colorButtons = screen.getAllByRole('button').filter(button => 
        button.getAttribute('title')?.includes('选择颜色:')
      )
      expect(colorButtons).toHaveLength(customColors.length)
    })
  })

  describe('键盘导航', () => {
    it('应该支持Tab键导航', async () => {
      const user = userEvent.setup()
      render(<TagColorPicker {...defaultProps} />)
      
      // Tab到第一个颜色按钮
      await user.tab()
      const firstButton = screen.getAllByRole('button')[0]
      expect(firstButton).toHaveFocus()
    })

    it('应该在显示自定义输入时自动聚焦', async () => {
      const user = userEvent.setup()
      render(<TagColorPicker {...defaultProps} />)
      
      await user.click(screen.getByText('自定义'))
      
      await waitFor(() => {
        const input = screen.getByPlaceholderText(/输入颜色值/)
        expect(input).toHaveFocus()
      })
    })
  })

  describe('无障碍性', () => {
    it('应该为颜色按钮提供适当的aria-label', () => {
      render(<TagColorPicker {...defaultProps} />)
      
      const firstColorButton = screen.getAllByRole('button').find(button => 
        button.getAttribute('aria-label')?.includes('选择预设颜色')
      )
      expect(firstColorButton).toBeInTheDocument()
    })

    it('应该为颜色按钮提供title属性', () => {
      render(<TagColorPicker {...defaultProps} />)
      
      const colorButtons = screen.getAllByRole('button').filter(button => 
        button.getAttribute('title')?.includes('选择颜色:')
      )
      expect(colorButtons.length).toBeGreaterThan(0)
    })
  })

  describe('边界情况', () => {
    it('应该处理空的预设颜色列表', () => {
      render(<TagColorPicker {...defaultProps} presetColors={[]} />)
      
      const colorButtons = screen.getAllByRole('button').filter(button => 
        button.getAttribute('title')?.includes('选择颜色:')
      )
      expect(colorButtons).toHaveLength(0)
    })

    it('应该处理undefined的value', () => {
      render(<TagColorPicker {...defaultProps} value={undefined} />)
      
      expect(screen.getByText('未选择颜色')).toBeInTheDocument()
    })

    it('应该处理非预设颜色的初始值', () => {
      const customColor = '#123456'
      mockColorUtils.isValidColor.mockReturnValue(true)
      
      render(<TagColorPicker {...defaultProps} value={customColor} />)
      
      expect(screen.getByText(customColor)).toBeInTheDocument()
      expect(screen.getByPlaceholderText(/输入颜色值/)).toBeInTheDocument()
    })
  })

  describe('性能优化', () => {
    it('应该在onChange回调中传递正确的颜色值', async () => {
      const user = userEvent.setup()
      const onChange = vi.fn()
      render(<TagColorPicker onChange={onChange} />)
      
      const firstColorButton = screen.getAllByRole('button').find(button => 
        button.getAttribute('title')?.includes('#3B82F6')
      )
      
      if (firstColorButton) {
        await user.click(firstColorButton)
        expect(onChange).toHaveBeenCalledTimes(1)
        expect(onChange).toHaveBeenCalledWith('#3B82F6')
      }
    })

    it('应该在输入无效颜色时不调用onChange', async () => {
      const user = userEvent.setup()
      const onChange = vi.fn()
      mockColorUtils.isValidColor.mockReturnValue(false)
      
      render(<TagColorPicker onChange={onChange} />)
      
      await user.click(screen.getByText('自定义'))
      const input = screen.getByPlaceholderText(/输入颜色值/)
      await user.type(input, 'invalid')
      
      expect(onChange).not.toHaveBeenCalled()
    })
  })
})