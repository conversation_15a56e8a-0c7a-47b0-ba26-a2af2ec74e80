// OpenAI集成单元测试

import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest'
import { AIProviderService } from '../src/services/aiProviderService'

// Mock fetch
global.fetch = vi.fn()

describe('AIProviderService - OpenAI集成', () => {
  let aiProviderService: AIProviderService
  const mockFetch = fetch as vi.MockedFunction<typeof fetch>

  beforeEach(() => {
    aiProviderService = new AIProviderService()
    vi.clearAllMocks()
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('testOpenAIConnection', () => {
    const baseUrl = 'https://api.openai.com/v1'
    const validApiKey = 'sk-test123456789'
    const invalidApiKey = 'invalid-key'

    it('应该成功测试有效的OpenAI连接', async () => {
      // 模拟成功的API响应
      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        json: async () => ({
          data: [
            { id: 'gpt-4', object: 'model' },
            { id: 'gpt-3.5-turbo', object: 'model' }
          ]
        })
      } as Response)

      const result = await aiProviderService.testOpenAIConnection(baseUrl, validApiKey)

      expect(result.success).toBe(true)
      expect(result.modelCount).toBe(2)
      expect(result.error).toBeUndefined()
      expect(mockFetch).toHaveBeenCalledWith(
        `${baseUrl}/models`,
        expect.objectContaining({
          method: 'GET',
          headers: expect.objectContaining({
            'Authorization': `Bearer ${validApiKey}`,
            'Content-Type': 'application/json',
            'User-Agent': 'BookmarkExtension/1.0'
          })
        })
      )
    })

    it('应该处理空API密钥错误', async () => {
      const result = await aiProviderService.testOpenAIConnection(baseUrl, '')

      expect(result.success).toBe(false)
      expect(result.error).toBe('API密钥不能为空')
      expect(mockFetch).not.toHaveBeenCalled()
    })

    it('应该处理无效的API密钥格式', async () => {
      const result = await aiProviderService.testOpenAIConnection(baseUrl, invalidApiKey)

      expect(result.success).toBe(false)
      expect(result.error).toBe('无效的OpenAI API密钥格式，应以"sk-"开头')
      expect(mockFetch).not.toHaveBeenCalled()
    })

    it('应该处理401未授权错误', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 401,
        statusText: 'Unauthorized'
      } as Response)

      const result = await aiProviderService.testOpenAIConnection(baseUrl, validApiKey)

      expect(result.success).toBe(false)
      expect(result.error).toBe('API密钥无效或已过期')
    })

    it('应该处理403权限不足错误', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 403,
        statusText: 'Forbidden'
      } as Response)

      const result = await aiProviderService.testOpenAIConnection(baseUrl, validApiKey)

      expect(result.success).toBe(false)
      expect(result.error).toBe('API密钥权限不足')
    })

    it('应该处理429频率限制错误', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 429,
        statusText: 'Too Many Requests'
      } as Response)

      const result = await aiProviderService.testOpenAIConnection(baseUrl, validApiKey)

      expect(result.success).toBe(false)
      expect(result.error).toBe('API请求频率限制，请稍后重试')
    })

    it('应该处理500服务器错误', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 500,
        statusText: 'Internal Server Error'
      } as Response)

      const result = await aiProviderService.testOpenAIConnection(baseUrl, validApiKey)

      expect(result.success).toBe(false)
      expect(result.error).toBe('OpenAI服务器内部错误')
    })

    it('应该处理503服务不可用错误', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 503,
        statusText: 'Service Unavailable'
      } as Response)

      const result = await aiProviderService.testOpenAIConnection(baseUrl, validApiKey)

      expect(result.success).toBe(false)
      expect(result.error).toBe('OpenAI服务暂时不可用')
    })

    it('应该处理网络超时错误', async () => {
      mockFetch.mockRejectedValueOnce(new DOMException('The operation was aborted', 'AbortError'))

      const result = await aiProviderService.testOpenAIConnection(baseUrl, validApiKey)

      expect(result.success).toBe(false)
      expect(result.error).toBe('连接超时，请检查网络连接或API服务状态')
    })

    it('应该处理网络连接错误', async () => {
      mockFetch.mockRejectedValueOnce(new Error('fetch failed'))

      const result = await aiProviderService.testOpenAIConnection(baseUrl, validApiKey)

      expect(result.success).toBe(false)
      expect(result.error).toBe('网络连接失败，请检查网络设置')
    })
  })

  describe('getOpenAIModels', () => {
    const baseUrl = 'https://api.openai.com/v1'
    const validApiKey = 'sk-test123456789'

    it('应该成功获取OpenAI模型列表', async () => {
      const mockModels = [
        { id: 'gpt-4-turbo', object: 'model', created: **********, owned_by: 'openai' },
        { id: 'gpt-4', object: 'model', created: **********, owned_by: 'openai' },
        { id: 'gpt-3.5-turbo', object: 'model', created: **********, owned_by: 'openai' },
        { id: 'gpt-3.5-turbo-16k', object: 'model', created: **********, owned_by: 'openai' },
        { id: 'text-davinci-003', object: 'model', created: **********, owned_by: 'openai' },
        { id: 'whisper-1', object: 'model', created: **********, owned_by: 'openai' },
        { id: 'dall-e-3', object: 'model', created: **********, owned_by: 'openai' },
        // 应该被过滤掉的模型
        { id: 'ft:gpt-3.5-turbo:custom', object: 'model', created: **********, owned_by: 'user' },
        { id: 'org-123456', object: 'model', created: **********, owned_by: 'user' }
      ]

      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        json: async () => ({ data: mockModels })
      } as Response)

      const result = await aiProviderService.getOpenAIModels(baseUrl, validApiKey)

      expect(result).toHaveLength(7) // 应该过滤掉2个无效模型
      expect(result.every(model => model.providerId === 'openai')).toBe(true)
      
      // 检查推荐模型
      const gpt4Turbo = result.find(m => m.id === 'gpt-4-turbo')
      expect(gpt4Turbo?.isRecommended).toBe(true)
      expect(gpt4Turbo?.displayName).toBe('GPT-4 Turbo')
      expect(gpt4Turbo?.capabilities).toContain('chat')
      expect(gpt4Turbo?.capabilities).toContain('coding')
      expect(gpt4Turbo?.contextLength).toBe(128000)

      // 检查热门模型
      const gpt35Turbo = result.find(m => m.id === 'gpt-3.5-turbo')
      expect(gpt35Turbo?.isPopular).toBe(true)
      expect(gpt35Turbo?.displayName).toBe('GPT-3.5 Turbo')

      // 检查特殊功能模型
      const whisper = result.find(m => m.id === 'whisper-1')
      expect(whisper?.capabilities).toContain('speech-to-text')
      expect(whisper?.tags).toContain('语音识别')

      const dalle = result.find(m => m.id === 'dall-e-3')
      expect(dalle?.capabilities).toContain('image-generation')
      expect(dalle?.tags).toContain('图像生成')
    })

    it('应该正确排序模型（推荐 > 热门 > 字母顺序）', async () => {
      const mockModels = [
        { id: 'text-ada-001', object: 'model' }, // 普通模型
        { id: 'gpt-3.5-turbo', object: 'model' }, // 推荐且热门
        { id: 'gpt-4', object: 'model' }, // 推荐且热门
        { id: 'text-davinci-003', object: 'model' } // 热门
      ]

      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        json: async () => ({ data: mockModels })
      } as Response)

      const result = await aiProviderService.getOpenAIModels(baseUrl, validApiKey)

      // 检查推荐模型都在前面（gpt-4和gpt-3.5-turbo都是推荐模型）
      const recommendedModels = result.filter(m => m.isRecommended)
      expect(recommendedModels.length).toBeGreaterThan(0)
      
      // 检查第一个模型是推荐模型
      expect(result[0].isRecommended).toBe(true)
      
      // 检查推荐模型按字母顺序排列（gpt-3.5-turbo在gpt-4之前）
      const firstRecommended = result.find(m => m.isRecommended)
      expect(firstRecommended).toBeDefined()
      expect(['gpt-3.5-turbo', 'gpt-4']).toContain(firstRecommended!.id)

      // 检查非推荐的热门模型在推荐模型之后
      const nonRecommendedPopular = result.filter(m => m.isPopular && !m.isRecommended)
      const nonRecommendedPopularIndex = result.findIndex(m => m.isPopular && !m.isRecommended)
      const lastRecommendedIndex = result.map((m, i) => m.isRecommended ? i : -1).filter(i => i >= 0).pop() || -1
      
      if (nonRecommendedPopularIndex >= 0 && lastRecommendedIndex >= 0) {
        expect(nonRecommendedPopularIndex).toBeGreaterThan(lastRecommendedIndex)
      }
    })

    it('应该处理API错误响应', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 401,
        statusText: 'Unauthorized'
      } as Response)

      const result = await aiProviderService.getOpenAIModels(baseUrl, validApiKey)

      expect(result).toEqual([])
    })

    it('应该处理网络错误', async () => {
      mockFetch.mockRejectedValueOnce(new Error('Network error'))

      const result = await aiProviderService.getOpenAIModels(baseUrl, validApiKey)

      expect(result).toEqual([])
    })

    it('应该处理空的模型列表响应', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        json: async () => ({ data: [] })
      } as Response)

      const result = await aiProviderService.getOpenAIModels(baseUrl, validApiKey)

      expect(result).toEqual([])
    })

    it('应该处理缺少data字段的响应', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        json: async () => ({})
      } as Response)

      const result = await aiProviderService.getOpenAIModels(baseUrl, validApiKey)

      expect(result).toEqual([])
    })
  })

  describe('OpenAI模型处理辅助方法', () => {
    it('应该正确验证有效的OpenAI模型', () => {
      const service = new AIProviderService()
      
      // 使用反射访问私有方法进行测试
      const isValidOpenAIModel = (service as any).isValidOpenAIModel.bind(service)

      // 有效模型
      expect(isValidOpenAIModel('gpt-4')).toBe(true)
      expect(isValidOpenAIModel('gpt-3.5-turbo')).toBe(true)
      expect(isValidOpenAIModel('text-davinci-003')).toBe(true)
      expect(isValidOpenAIModel('code-davinci-002')).toBe(true)
      expect(isValidOpenAIModel('whisper-1')).toBe(true)
      expect(isValidOpenAIModel('dall-e-3')).toBe(true)

      // 无效模型
      expect(isValidOpenAIModel('ft:gpt-3.5-turbo:custom')).toBe(false)
      expect(isValidOpenAIModel('org-123456')).toBe(false)
      expect(isValidOpenAIModel('user-123456')).toBe(false)
      expect(isValidOpenAIModel('if:123456')).toBe(false)
      expect(isValidOpenAIModel('random-model')).toBe(false)
    })

    it('应该正确格式化模型名称', () => {
      const service = new AIProviderService()
      const formatOpenAIModelName = (service as any).formatOpenAIModelName.bind(service)

      expect(formatOpenAIModelName('gpt-4-turbo')).toBe('GPT-4 Turbo')
      expect(formatOpenAIModelName('gpt-4')).toBe('GPT-4')
      expect(formatOpenAIModelName('gpt-3.5-turbo')).toBe('GPT-3.5 Turbo')
      expect(formatOpenAIModelName('text-davinci-003')).toBe('Davinci 003')
      expect(formatOpenAIModelName('whisper-1')).toBe('Whisper')
      expect(formatOpenAIModelName('dall-e-3')).toBe('DALL-E 3')
      expect(formatOpenAIModelName('unknown-model')).toBe('unknown-model')
    })

    it('应该正确确定模型能力', () => {
      const service = new AIProviderService()
      const determineOpenAICapabilities = (service as any).determineOpenAICapabilities.bind(service)

      // GPT-4 模型
      const gpt4Capabilities = determineOpenAICapabilities('gpt-4')
      expect(gpt4Capabilities).toContain('chat')
      expect(gpt4Capabilities).toContain('completion')
      expect(gpt4Capabilities).toContain('coding')
      expect(gpt4Capabilities).toContain('instruction-following')
      expect(gpt4Capabilities).toContain('function-calling')

      // 代码模型
      const codeCapabilities = determineOpenAICapabilities('code-davinci-002')
      expect(codeCapabilities).toContain('coding')

      // 语音模型
      const whisperCapabilities = determineOpenAICapabilities('whisper-1')
      expect(whisperCapabilities).toContain('speech-to-text')

      // 图像模型
      const dalleCapabilities = determineOpenAICapabilities('dall-e-3')
      expect(dalleCapabilities).toContain('image-generation')

      // 嵌入模型
      const embeddingCapabilities = determineOpenAICapabilities('text-embedding-ada-002')
      expect(embeddingCapabilities).toContain('embedding')
    })

    it('应该正确获取模型上下文长度', () => {
      const service = new AIProviderService()
      const getOpenAIModelContextLength = (service as any).getOpenAIModelContextLength.bind(service)

      expect(getOpenAIModelContextLength('gpt-4-turbo')).toBe(128000)
      expect(getOpenAIModelContextLength('gpt-4-32k')).toBe(32768)
      expect(getOpenAIModelContextLength('gpt-4')).toBe(8192)
      expect(getOpenAIModelContextLength('gpt-3.5-turbo-16k')).toBe(16384)
      expect(getOpenAIModelContextLength('gpt-3.5-turbo')).toBe(4096)
      expect(getOpenAIModelContextLength('text-davinci-003')).toBe(4097)
      expect(getOpenAIModelContextLength('unknown-model')).toBe(2048)
    })

    it('应该正确判断推荐和热门模型', () => {
      const service = new AIProviderService()
      const isOpenAIModelRecommended = (service as any).isOpenAIModelRecommended.bind(service)
      const isOpenAIModelPopular = (service as any).isOpenAIModelPopular.bind(service)

      // 推荐模型
      expect(isOpenAIModelRecommended('gpt-4-turbo')).toBe(true)
      expect(isOpenAIModelRecommended('gpt-4')).toBe(true)
      expect(isOpenAIModelRecommended('gpt-3.5-turbo')).toBe(true)
      expect(isOpenAIModelRecommended('text-ada-001')).toBe(false)

      // 热门模型
      expect(isOpenAIModelPopular('gpt-4-turbo')).toBe(true)
      expect(isOpenAIModelPopular('gpt-4')).toBe(true)
      expect(isOpenAIModelPopular('gpt-3.5-turbo')).toBe(true)
      expect(isOpenAIModelPopular('text-davinci-003')).toBe(true)
      expect(isOpenAIModelPopular('text-ada-001')).toBe(false)
    })
  })
})