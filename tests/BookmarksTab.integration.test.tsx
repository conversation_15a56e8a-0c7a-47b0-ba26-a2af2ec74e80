// BookmarksTab集成测试

import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import '@testing-library/jest-dom'

// 模拟Chrome API
const mockChrome = {
  runtime: {
    sendMessage: vi.fn()
  }
}

// 设置全局chrome对象
global.chrome = mockChrome as any

// 模拟组件
vi.mock('../src/components/TruncatedTitle', () => ({
  default: function MockTruncatedTitle({ title, className }: any) {
    return <span className={className}>{title}</span>
  }
}))

vi.mock('../src/components/ViewModeSelector', () => ({
  default: function MockViewModeSelector({ currentMode, onModeChange }: any) {
    return (
      <div data-testid="view-mode-selector">
        <button onClick={() => onModeChange('card')}>卡片</button>
        <button onClick={() => onModeChange('row')}>行</button>
        <button onClick={() => onModeChange('compact')}>紧凑</button>
        <span>当前模式: {currentMode}</span>
      </div>
    )
  }
}))

vi.mock('../src/components/BookmarkRow', () => ({
  default: function MockBookmarkRow({ bookmark, onEdit, onDelete }: any) {
    return (
      <div data-testid="bookmark-row">
        <span>{bookmark.title}</span>
        <button onClick={() => onEdit(bookmark)}>编辑</button>
        <button onClick={() => onDelete(bookmark)}>删除</button>
      </div>
    )
  }
}))

vi.mock('../src/components/BookmarkCompact', () => ({
  default: function MockBookmarkCompact({ bookmark, onEdit, onDelete }: any) {
    return (
      <div data-testid="bookmark-compact">
        <span>{bookmark.title}</span>
        <button onClick={() => onEdit(bookmark)}>编辑</button>
        <button onClick={() => onDelete(bookmark)}>删除</button>
      </div>
    )
  }
}))

vi.mock('../src/components/BookmarkEditModal', () => ({
  default: function MockBookmarkEditModal({ isOpen, bookmark, onSave, onCancel }: any) {
    if (!isOpen) return null
    return (
      <div data-testid="edit-modal">
        <span>编辑: {bookmark?.title}</span>
        <button onClick={() => onSave({ ...bookmark, title: '更新的标题' })}>保存</button>
        <button onClick={onCancel}>取消</button>
      </div>
    )
  }
}))

vi.mock('../src/hooks/useViewMode', () => ({
  useViewMode: () => ({
    viewMode: 'card',
    setViewMode: vi.fn(),
    isLoading: false
  })
}))

// 创建测试用的收藏数据
const mockBookmarks = [
  {
    id: '1',
    title: '测试收藏1',
    url: 'https://example1.com',
    description: '测试描述1',
    category: '测试分类',
    tags: ['标签1'],
    createdAt: '2024-01-01T00:00:00.000Z',
    favicon: 'https://example1.com/favicon.ico'
  },
  {
    id: '2',
    title: '测试收藏2',
    url: 'https://example2.com',
    description: '测试描述2',
    category: '其他分类',
    tags: ['标签2'],
    createdAt: '2024-01-02T00:00:00.000Z',
    favicon: 'https://example2.com/favicon.ico'
  }
]

describe('BookmarksTab集成测试', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    
    // 模拟成功的API响应
    mockChrome.runtime.sendMessage.mockResolvedValue({
      success: true,
      data: mockBookmarks
    })

    // 模拟window.confirm
    global.confirm = vi.fn(() => true)
    global.open = vi.fn()
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  it('应该正确加载和显示收藏列表', async () => {
    // 动态导入组件以避免模块加载问题
    const { default: OptionsApp } = await import('../src/options/OptionsApp')
    
    render(<OptionsApp />)

    // 等待数据加载
    await waitFor(() => {
      expect(screen.getByRole('heading', { name: '收藏管理' })).toBeInTheDocument()
    })

    // 验证API调用
    expect(mockChrome.runtime.sendMessage).toHaveBeenCalledWith({
      type: 'GET_BOOKMARKS',
      data: {}
    })

    // 等待收藏数据显示
    await waitFor(() => {
      expect(screen.getByText('测试收藏1')).toBeInTheDocument()
      expect(screen.getByText('测试收藏2')).toBeInTheDocument()
    })
  })

  it('应该正确显示视图模式选择器', async () => {
    const { default: OptionsApp } = await import('../src/options/OptionsApp')
    
    render(<OptionsApp />)

    await waitFor(() => {
      expect(screen.getByTestId('view-mode-selector')).toBeInTheDocument()
    })
  })

  it('应该支持搜索功能', async () => {
    const { default: OptionsApp } = await import('../src/options/OptionsApp')
    
    render(<OptionsApp />)

    // 等待数据加载
    await waitFor(() => {
      expect(screen.getByText('测试收藏1')).toBeInTheDocument()
    })

    // 输入搜索关键词
    const searchInput = screen.getByPlaceholderText('搜索收藏...')
    fireEvent.change(searchInput, { target: { value: '测试收藏1' } })

    // 验证搜索结果
    await waitFor(() => {
      expect(screen.getByText('测试收藏1')).toBeInTheDocument()
      expect(screen.queryByText('测试收藏2')).not.toBeInTheDocument()
    })
  })

  it('应该支持分类筛选', async () => {
    const { default: OptionsApp } = await import('../src/options/OptionsApp')
    
    render(<OptionsApp />)

    // 等待数据加载
    await waitFor(() => {
      expect(screen.getByText('测试收藏1')).toBeInTheDocument()
    })

    // 选择特定分类
    const categorySelect = screen.getByDisplayValue('所有分类')
    fireEvent.change(categorySelect, { target: { value: '测试分类' } })

    // 验证筛选结果
    await waitFor(() => {
      expect(screen.getByText('测试收藏1')).toBeInTheDocument()
      expect(screen.queryByText('测试收藏2')).not.toBeInTheDocument()
    })
  })

  it('应该处理编辑功能', async () => {
    const { default: OptionsApp } = await import('../src/options/OptionsApp')
    
    render(<OptionsApp />)

    // 等待数据加载
    await waitFor(() => {
      expect(screen.getByText('测试收藏1')).toBeInTheDocument()
    })

    // 点击编辑按钮
    const editButtons = screen.getAllByTitle('编辑收藏')
    fireEvent.click(editButtons[0])

    // 验证编辑模态打开
    await waitFor(() => {
      expect(screen.getByTestId('edit-modal')).toBeInTheDocument()
    })
  })

  it('应该处理删除功能', async () => {
    const { default: OptionsApp } = await import('../src/options/OptionsApp')
    
    // 模拟删除API成功响应
    mockChrome.runtime.sendMessage.mockImplementation((message) => {
      if (message.type === 'DELETE_BOOKMARK') {
        return Promise.resolve({ success: true })
      }
      return Promise.resolve({ success: true, data: mockBookmarks })
    })
    
    render(<OptionsApp />)

    // 等待数据加载
    await waitFor(() => {
      expect(screen.getByText('测试收藏1')).toBeInTheDocument()
    })

    // 点击删除按钮
    const deleteButtons = screen.getAllByTitle('删除收藏')
    fireEvent.click(deleteButtons[0])

    // 验证删除API调用
    await waitFor(() => {
      expect(mockChrome.runtime.sendMessage).toHaveBeenCalledWith({
        type: 'DELETE_BOOKMARK',
        data: { id: '1' }
      })
    })
  })

  it('应该显示空状态', async () => {
    // 模拟空数据响应
    mockChrome.runtime.sendMessage.mockResolvedValue({
      success: true,
      data: []
    })

    const { default: OptionsApp } = await import('../src/options/OptionsApp')
    
    render(<OptionsApp />)

    // 验证空状态显示
    await waitFor(() => {
      expect(screen.getByText('暂无收藏内容')).toBeInTheDocument()
    })
  })

  it('应该处理加载错误', async () => {
    // 模拟API错误
    mockChrome.runtime.sendMessage.mockResolvedValue({
      success: false,
      error: '加载失败'
    })

    const { default: OptionsApp } = await import('../src/options/OptionsApp')
    
    render(<OptionsApp />)

    // 验证错误处理
    await waitFor(() => {
      expect(screen.getByText('暂无收藏内容')).toBeInTheDocument()
    })
  })
})