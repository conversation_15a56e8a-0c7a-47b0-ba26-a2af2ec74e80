// Gemini集成单元测试

import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest'
import { AIProviderService } from '../src/services/aiProviderService'

// Mock fetch
global.fetch = vi.fn()

describe('AIProviderService - Gemini集成', () => {
  let aiProviderService: AIProviderService
  const mockFetch = fetch as vi.MockedFunction<typeof fetch>

  beforeEach(() => {
    aiProviderService = new AIProviderService()
    vi.clearAllMocks()
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('testGeminiConnection', () => {
    const baseUrl = 'https://generativelanguage.googleapis.com/v1'
    const validApiKey = 'AIzaTest123456789'
    const invalidApiKey = 'invalid-key'

    it('应该成功测试有效的Gemini连接', async () => {
      // 模拟成功的API响应
      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        json: async () => ({
          models: [
            {
              name: 'models/gemini-1.5-pro',
              displayName: 'Gemini 1.5 Pro',
              description: 'The best model for scaling across a wide range of tasks'
            },
            {
              name: 'models/gemini-1.5-flash',
              displayName: 'Gemini 1.5 Flash',
              description: 'Fast and versatile performance across a diverse variety of tasks'
            }
          ]
        })
      } as Response)

      const result = await aiProviderService.testGeminiConnection(baseUrl, validApiKey)

      expect(result.success).toBe(true)
      expect(result.modelCount).toBe(2)
      expect(result.error).toBeUndefined()
      expect(mockFetch).toHaveBeenCalledWith(
        `${baseUrl}/models?key=${validApiKey}`,
        expect.objectContaining({
          method: 'GET',
          headers: expect.objectContaining({
            'Content-Type': 'application/json',
            'User-Agent': 'BookmarkExtension/1.0'
          })
        })
      )
    })

    it('应该处理空API密钥错误', async () => {
      const result = await aiProviderService.testGeminiConnection(baseUrl, '')

      expect(result.success).toBe(false)
      expect(result.error).toBe('API密钥不能为空')
      expect(mockFetch).not.toHaveBeenCalled()
    })

    it('应该处理无效的API密钥格式', async () => {
      const result = await aiProviderService.testGeminiConnection(baseUrl, invalidApiKey)

      expect(result.success).toBe(false)
      expect(result.error).toBe('无效的Gemini API密钥格式，应以"AIza"开头')
      expect(mockFetch).not.toHaveBeenCalled()
    })

    it('应该处理400请求错误', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 400,
        statusText: 'Bad Request',
        json: async () => ({
          error: {
            message: 'Invalid API key format'
          }
        })
      } as Response)

      const result = await aiProviderService.testGeminiConnection(baseUrl, validApiKey)

      expect(result.success).toBe(false)
      expect(result.error).toBe('请求错误: Invalid API key format')
    })

    it('应该处理400通用请求错误', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 400,
        statusText: 'Bad Request',
        json: async () => ({})
      } as Response)

      const result = await aiProviderService.testGeminiConnection(baseUrl, validApiKey)

      expect(result.success).toBe(false)
      expect(result.error).toBe('请求参数无效')
    })

    it('应该处理401未授权错误', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 401,
        statusText: 'Unauthorized'
      } as Response)

      const result = await aiProviderService.testGeminiConnection(baseUrl, validApiKey)

      expect(result.success).toBe(false)
      expect(result.error).toBe('API密钥无效或已过期')
    })

    it('应该处理403权限不足错误', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 403,
        statusText: 'Forbidden'
      } as Response)

      const result = await aiProviderService.testGeminiConnection(baseUrl, validApiKey)

      expect(result.success).toBe(false)
      expect(result.error).toBe('API密钥权限不足或服务被禁用')
    })

    it('应该处理429频率限制错误', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 429,
        statusText: 'Too Many Requests'
      } as Response)

      const result = await aiProviderService.testGeminiConnection(baseUrl, validApiKey)

      expect(result.success).toBe(false)
      expect(result.error).toBe('API请求频率限制，请稍后重试')
    })

    it('应该处理500服务器错误', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 500,
        statusText: 'Internal Server Error'
      } as Response)

      const result = await aiProviderService.testGeminiConnection(baseUrl, validApiKey)

      expect(result.success).toBe(false)
      expect(result.error).toBe('Gemini服务器内部错误')
    })

    it('应该处理503服务不可用错误', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 503,
        statusText: 'Service Unavailable'
      } as Response)

      const result = await aiProviderService.testGeminiConnection(baseUrl, validApiKey)

      expect(result.success).toBe(false)
      expect(result.error).toBe('Gemini服务暂时不可用')
    })

    it('应该处理网络超时错误', async () => {
      mockFetch.mockRejectedValueOnce(new DOMException('The operation was aborted', 'AbortError'))

      const result = await aiProviderService.testGeminiConnection(baseUrl, validApiKey)

      expect(result.success).toBe(false)
      expect(result.error).toBe('连接超时，请检查网络连接或API服务状态')
    })

    it('应该处理网络连接错误', async () => {
      mockFetch.mockRejectedValueOnce(new Error('fetch failed'))

      const result = await aiProviderService.testGeminiConnection(baseUrl, validApiKey)

      expect(result.success).toBe(false)
      expect(result.error).toBe('网络连接失败，请检查网络设置')
    })

    it('应该处理异常的API响应格式', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        json: async () => ({
          // 缺少models字段
          data: []
        })
      } as Response)

      const result = await aiProviderService.testGeminiConnection(baseUrl, validApiKey)

      expect(result.success).toBe(false)
      expect(result.error).toBe('Gemini API响应格式异常')
    })
  })

  describe('getGeminiModels', () => {
    const baseUrl = 'https://generativelanguage.googleapis.com/v1'
    const validApiKey = 'AIzaTest123456789'

    it('应该成功获取Gemini模型列表', async () => {
      const mockModels = [
        {
          name: 'models/gemini-1.5-pro',
          displayName: 'Gemini 1.5 Pro',
          description: 'The best model for scaling across a wide range of tasks'
        },
        {
          name: 'models/gemini-1.5-flash',
          displayName: 'Gemini 1.5 Flash',
          description: 'Fast and versatile performance across a diverse variety of tasks'
        },
        {
          name: 'models/gemini-1.0-pro',
          displayName: 'Gemini 1.0 Pro',
          description: 'The best model for scaling across a wide range of tasks'
        },
        {
          name: 'models/text-bison-001',
          displayName: 'PaLM 2 (Text Bison)',
          description: 'A legacy text-only model optimized for a variety of natural language tasks'
        },
        {
          name: 'models/chat-bison-001',
          displayName: 'PaLM 2 (Chat Bison)',
          description: 'A legacy model that excels at multi-turn conversations'
        },
        // 应该被过滤掉的模型
        {
          name: 'models/embedding-001',
          displayName: 'Embedding 001',
          description: 'Embedding model'
        }
      ]

      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        json: async () => ({ models: mockModels })
      } as Response)

      const result = await aiProviderService.getGeminiModels(baseUrl, validApiKey)

      expect(result).toHaveLength(5) // 应该过滤掉1个无效模型
      expect(result.every(model => model.providerId === 'gemini')).toBe(true)
      
      // 检查推荐模型
      const gemini15Pro = result.find(m => m.id === 'gemini-1.5-pro')
      expect(gemini15Pro?.isRecommended).toBe(true)
      expect(gemini15Pro?.displayName).toBe('Gemini 1.5 Pro')
      expect(gemini15Pro?.capabilities).toContain('chat')
      expect(gemini15Pro?.capabilities).toContain('vision')
      expect(gemini15Pro?.capabilities).toContain('long-context')
      expect(gemini15Pro?.contextLength).toBe(2000000)
      expect(gemini15Pro?.maxTokens).toBe(8192)

      // 检查快速模型
      const gemini15Flash = result.find(m => m.id === 'gemini-1.5-flash')
      expect(gemini15Flash?.capabilities).toContain('fast-response')
      expect(gemini15Flash?.tags).toContain('快速响应')

      // 检查PaLM 2模型
      const textBison = result.find(m => m.id === 'text-bison-001')
      expect(textBison?.displayName).toBe('PaLM 2 Text Bison')
      expect(textBison?.tags).toContain('PaLM 2')

      const chatBison = result.find(m => m.id === 'chat-bison-001')
      expect(chatBison?.capabilities).toContain('chat')
      expect(chatBison?.tags).toContain('对话优化')
    })

    it('应该正确排序模型（推荐 > 热门 > 字母顺序）', async () => {
      const mockModels = [
        { name: 'models/text-bison-001', displayName: 'Text Bison' },
        { name: 'models/gemini-1.0-pro', displayName: 'Gemini 1.0 Pro' }, // 推荐
        { name: 'models/gemini-1.5-flash', displayName: 'Gemini 1.5 Flash' }, // 推荐且热门
        { name: 'models/chat-bison-001', displayName: 'Chat Bison' } // 热门
      ]

      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        json: async () => ({ models: mockModels })
      } as Response)

      const result = await aiProviderService.getGeminiModels(baseUrl, validApiKey)

      // 推荐模型应该在前面
      const recommendedModels = result.filter(m => m.isRecommended)
      expect(recommendedModels.length).toBeGreaterThan(0)
      
      // 检查第一个模型是推荐模型
      expect(result[0].isRecommended).toBe(true)
    })

    it('应该处理API错误响应', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 401,
        statusText: 'Unauthorized'
      } as Response)

      const result = await aiProviderService.getGeminiModels(baseUrl, validApiKey)

      expect(result).toEqual([])
    })

    it('应该处理网络错误', async () => {
      mockFetch.mockRejectedValueOnce(new Error('Network error'))

      const result = await aiProviderService.getGeminiModels(baseUrl, validApiKey)

      expect(result).toEqual([])
    })

    it('应该处理空的模型列表响应', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        json: async () => ({ models: [] })
      } as Response)

      const result = await aiProviderService.getGeminiModels(baseUrl, validApiKey)

      expect(result).toEqual([])
    })

    it('应该处理缺少models字段的响应', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        json: async () => ({})
      } as Response)

      const result = await aiProviderService.getGeminiModels(baseUrl, validApiKey)

      expect(result).toEqual([])
    })
  })

  describe('Gemini模型处理辅助方法', () => {
    it('应该正确验证有效的Gemini模型', () => {
      const service = new AIProviderService()
      
      // 使用反射访问私有方法进行测试
      const isValidGeminiModel = (service as any).isValidGeminiModel.bind(service)

      // 有效模型
      expect(isValidGeminiModel('models/gemini-1.5-pro')).toBe(true)
      expect(isValidGeminiModel('models/gemini-1.5-flash')).toBe(true)
      expect(isValidGeminiModel('models/gemini-1.0-pro')).toBe(true)
      expect(isValidGeminiModel('models/gemini-pro')).toBe(true)
      expect(isValidGeminiModel('models/text-bison-001')).toBe(true)
      expect(isValidGeminiModel('models/chat-bison-001')).toBe(true)
      expect(isValidGeminiModel('models/code-bison-001')).toBe(true)

      // 无效模型
      expect(isValidGeminiModel('models/embedding-001')).toBe(false)
      expect(isValidGeminiModel('models/unknown-model')).toBe(false)
      expect(isValidGeminiModel('gemini-pro')).toBe(false) // 缺少models/前缀
      expect(isValidGeminiModel('random-model')).toBe(false)
    })

    it('应该正确提取模型ID', () => {
      const service = new AIProviderService()
      const extractGeminiModelId = (service as any).extractGeminiModelId.bind(service)

      expect(extractGeminiModelId('models/gemini-1.5-pro')).toBe('gemini-1.5-pro')
      expect(extractGeminiModelId('models/text-bison-001')).toBe('text-bison-001')
    })

    it('应该正确格式化模型名称', () => {
      const service = new AIProviderService()
      const formatGeminiModelName = (service as any).formatGeminiModelName.bind(service)

      expect(formatGeminiModelName('gemini-1.5-pro')).toBe('Gemini 1.5 Pro')
      expect(formatGeminiModelName('gemini-1.5-flash')).toBe('Gemini 1.5 Flash')
      expect(formatGeminiModelName('gemini-1.0-pro')).toBe('Gemini 1.0 Pro')
      expect(formatGeminiModelName('text-bison-001')).toBe('PaLM 2 Text Bison')
      expect(formatGeminiModelName('chat-bison-001')).toBe('PaLM 2 Chat Bison')
      expect(formatGeminiModelName('code-bison-001')).toBe('PaLM 2 Code Bison')
      expect(formatGeminiModelName('unknown-model')).toBe('unknown-model')
    })

    it('应该正确确定模型能力', () => {
      const service = new AIProviderService()
      const determineGeminiCapabilities = (service as any).determineGeminiCapabilities.bind(service)

      // Gemini 1.5 Pro 模型
      const gemini15ProCapabilities = determineGeminiCapabilities('gemini-1.5-pro')
      expect(gemini15ProCapabilities).toContain('chat')
      expect(gemini15ProCapabilities).toContain('completion')
      expect(gemini15ProCapabilities).toContain('vision')
      expect(gemini15ProCapabilities).toContain('multimodal')
      expect(gemini15ProCapabilities).toContain('long-context')
      expect(gemini15ProCapabilities).toContain('instruction-following')

      // Gemini 1.5 Flash 模型
      const gemini15FlashCapabilities = determineGeminiCapabilities('gemini-1.5-flash')
      expect(gemini15FlashCapabilities).toContain('fast-response')

      // 代码模型
      const codeCapabilities = determineGeminiCapabilities('code-bison-001')
      expect(codeCapabilities).toContain('coding')

      // 视觉模型
      const visionCapabilities = determineGeminiCapabilities('gemini-pro-vision')
      expect(visionCapabilities).toContain('vision')
      expect(visionCapabilities).toContain('multimodal')
    })

    it('应该正确获取模型上下文长度', () => {
      const service = new AIProviderService()
      const getGeminiModelContextLength = (service as any).getGeminiModelContextLength.bind(service)

      expect(getGeminiModelContextLength('gemini-1.5-pro')).toBe(2000000)
      expect(getGeminiModelContextLength('gemini-1.5-flash')).toBe(1000000)
      expect(getGeminiModelContextLength('gemini-1.0-pro')).toBe(32768)
      expect(getGeminiModelContextLength('gemini-pro')).toBe(32768)
      expect(getGeminiModelContextLength('text-bison-001')).toBe(8192)
      expect(getGeminiModelContextLength('unknown-model')).toBe(8192)
    })

    it('应该正确获取模型最大输出tokens', () => {
      const service = new AIProviderService()
      const getGeminiModelMaxTokens = (service as any).getGeminiModelMaxTokens.bind(service)

      expect(getGeminiModelMaxTokens('gemini-1.5-pro')).toBe(8192)
      expect(getGeminiModelMaxTokens('gemini-1.5-flash')).toBe(8192)
      expect(getGeminiModelMaxTokens('gemini-1.0-pro')).toBe(2048)
      expect(getGeminiModelMaxTokens('gemini-pro')).toBe(2048)
      expect(getGeminiModelMaxTokens('text-bison-001')).toBe(1024)
      expect(getGeminiModelMaxTokens('unknown-model')).toBe(1024)
    })

    it('应该正确判断推荐和热门模型', () => {
      const service = new AIProviderService()
      const isGeminiModelRecommended = (service as any).isGeminiModelRecommended.bind(service)
      const isGeminiModelPopular = (service as any).isGeminiModelPopular.bind(service)

      // 推荐模型
      expect(isGeminiModelRecommended('gemini-1.5-pro')).toBe(true)
      expect(isGeminiModelRecommended('gemini-1.5-flash')).toBe(true)
      expect(isGeminiModelRecommended('gemini-1.0-pro')).toBe(true)
      expect(isGeminiModelRecommended('text-bison-001')).toBe(false)

      // 热门模型
      expect(isGeminiModelPopular('gemini-1.5-pro')).toBe(true)
      expect(isGeminiModelPopular('gemini-pro')).toBe(true)
      expect(isGeminiModelPopular('chat-bison-001')).toBe(true)
      expect(isGeminiModelPopular('code-bison-001')).toBe(true)
      expect(isGeminiModelPopular('text-bison-001')).toBe(false)
    })
  })
})