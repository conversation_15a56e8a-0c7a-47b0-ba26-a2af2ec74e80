// AI文本生成组件单元测试

import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest'
import AITextGenerator from '../src/components/AITextGenerator'

// Mock chrome API
const mockChrome = {
  runtime: {
    sendMessage: vi.fn()
  }
}

// @ts-ignore
global.chrome = mockChrome

describe('AITextGenerator', () => {
  const defaultProps = {
    value: '',
    onChange: vi.fn(),
    context: {
      title: '测试标题',
      url: 'https://example.com',
      category: '测试分类',
      tags: ['标签1', '标签2']
    }
  }

  beforeEach(() => {
    vi.clearAllMocks()
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  it('应该正确渲染基本组件', () => {
    render(<AITextGenerator {...defaultProps} />)
    
    expect(screen.getByRole('textbox')).toBeInTheDocument()
    expect(screen.getByText('AI生成')).toBeInTheDocument()
  })

  it('应该显示自定义标签', () => {
    render(
      <AITextGenerator 
        {...defaultProps} 
        label="自定义标签" 
      />
    )
    
    expect(screen.getByText('自定义标签')).toBeInTheDocument()
  })

  it('应该显示自定义占位符', () => {
    const placeholder = '自定义占位符文本'
    render(
      <AITextGenerator 
        {...defaultProps} 
        placeholder={placeholder}
      />
    )
    
    expect(screen.getByPlaceholderText(placeholder)).toBeInTheDocument()
  })

  it('应该正确处理文本变化', () => {
    const onChange = vi.fn()
    render(
      <AITextGenerator 
        {...defaultProps} 
        onChange={onChange}
      />
    )
    
    const textarea = screen.getByRole('textbox')
    fireEvent.change(textarea, { target: { value: '新文本内容' } })
    
    expect(onChange).toHaveBeenCalledWith('新文本内容')
  })

  it('应该在禁用状态下禁用输入和按钮', () => {
    render(
      <AITextGenerator 
        {...defaultProps} 
        disabled={true}
      />
    )
    
    const textarea = screen.getByRole('textbox')
    const button = screen.getByText('AI生成')
    
    expect(textarea).toBeDisabled()
    expect(button).toBeDisabled()
  })

  it('应该正确处理AI生成请求', async () => {
    const mockResponse = {
      success: true,
      data: {
        content: 'AI生成的测试内容',
        suggestions: ['建议1', '建议2']
      }
    }
    
    mockChrome.runtime.sendMessage.mockResolvedValue(mockResponse)
    
    render(<AITextGenerator {...defaultProps} />)
    
    const button = screen.getByText('AI生成')
    fireEvent.click(button)
    
    // 检查按钮状态变化
    expect(screen.getByText('生成中...')).toBeInTheDocument()
    
    await waitFor(() => {
      expect(mockChrome.runtime.sendMessage).toHaveBeenCalledWith({
        type: 'AI_GENERATE_TEXT',
        data: expect.objectContaining({
          generationType: 'description',
          context: defaultProps.context
        })
      })
    })
    
    // 等待生成完成
    await waitFor(() => {
      expect(screen.getByText('AI生成的测试内容')).toBeInTheDocument()
    })
  })

  it('应该正确处理错误情况', async () => {
    const mockError = {
      success: false,
      error: 'AI服务不可用'
    }
    
    mockChrome.runtime.sendMessage.mockResolvedValue(mockError)
    
    render(<AITextGenerator {...defaultProps} />)
    
    const button = screen.getByText('AI生成')
    fireEvent.click(button)
    
    await waitFor(() => {
      expect(screen.getByText('AI服务不可用')).toBeInTheDocument()
    })
  })

  it('应该根据生成类型构建不同的提示词', () => {
    render(
      <AITextGenerator 
        {...defaultProps} 
        generationType="description"
      />
    )
    
    const button = screen.getByText('AI生成')
    fireEvent.click(button)
    
    expect(mockChrome.runtime.sendMessage).toHaveBeenCalledWith(
      expect.objectContaining({
        data: expect.objectContaining({
          generationType: 'description'
        })
      })
    )
  })

  it('应该正确设置最大行数', () => {
    render(
      <AITextGenerator 
        {...defaultProps} 
        maxRows={6}
      />
    )
    
    const textarea = screen.getByRole('textbox')
    expect(textarea).toHaveAttribute('rows', '4') // 实际限制为Math.min(maxRows, 4)
  })
})