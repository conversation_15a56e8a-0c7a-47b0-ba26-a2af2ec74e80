import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { TagForm, TagFormData } from '../src/components/TagForm'
import { TagUtils } from '../src/utils/tagUtils'
import { ColorUtils } from '../src/utils/colorUtils'
import { vi } from 'vitest'

// Mock TagUtils
vi.mock('../src/utils/tagUtils', () => ({
  TagUtils: {
    validateTagName: vi.fn(),
    normalizeTagName: vi.fn(),
    generateTagColor: vi.fn()
  }
}))

// Mock ColorUtils
vi.mock('../src/utils/colorUtils', () => ({
  ColorUtils: {
    PRESET_COLORS: [
      '#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6'
    ],
    generateColorFromString: vi.fn(),
    isValidColor: vi.fn(),
    getContrastColor: vi.fn()
  }
}))

const mockTagUtils = TagUtils as any
const mockColorUtils = ColorUtils as any

describe('TagForm', () => {
  const defaultProps = {
    onSave: vi.fn(),
    onCancel: vi.fn()
  }

  const existingTags = [
    { id: '1', name: '技术' },
    { id: '2', name: '学习' },
    { id: '3', name: '工具' }
  ]

  beforeEach(() => {
    vi.clearAllMocks()
    
    // 设置默认的mock返回值
    mockTagUtils.validateTagName.mockReturnValue({ isValid: true })
    mockTagUtils.normalizeTagName.mockImplementation((name: string) => name.toLowerCase().trim())
    mockColorUtils.generateColorFromString.mockReturnValue('#3B82F6')
    mockColorUtils.isValidColor.mockReturnValue(true)
    mockColorUtils.getContrastColor.mockReturnValue('#000000')
  })

  describe('基本渲染', () => {
    it('应该正确渲染创建模式的表单', () => {
      render(<TagForm {...defaultProps} />)
      
      expect(screen.getByText('创建新标签')).toBeInTheDocument()
      expect(screen.getByText('填写标签信息以创建新标签')).toBeInTheDocument()
      expect(screen.getByLabelText(/标签名称/)).toBeInTheDocument()
      expect(screen.getByText('选择标签颜色')).toBeInTheDocument()
      expect(screen.getByText('创建标签')).toBeInTheDocument()
      expect(screen.getByText('取消')).toBeInTheDocument()
    })

    it('应该正确渲染编辑模式的表单', () => {
      const tag = { id: '1', name: '测试标签', color: '#ff0000' }
      render(<TagForm {...defaultProps} tag={tag} />)
      
      expect(screen.getByText('编辑标签')).toBeInTheDocument()
      expect(screen.getByText('修改标签信息')).toBeInTheDocument()
      expect(screen.getByDisplayValue('测试标签')).toBeInTheDocument()
      expect(screen.getByText('保存修改')).toBeInTheDocument()
    })

    it('应该显示必填字段标识', () => {
      render(<TagForm {...defaultProps} />)
      
      expect(screen.getByText('*')).toBeInTheDocument()
    })

    it('应该显示字符计数', () => {
      render(<TagForm {...defaultProps} />)
      
      expect(screen.getByText('0/50')).toBeInTheDocument()
    })
  })

  describe('表单验证', () => {
    it('应该验证标签名称不能为空', async () => {
      const user = userEvent.setup()
      render(<TagForm {...defaultProps} />)
      
      const submitButton = screen.getByText('创建标签')
      await user.click(submitButton)
      
      expect(screen.getByText('标签名称不能为空')).toBeInTheDocument()
      expect(defaultProps.onSave).not.toHaveBeenCalled()
    })

    it('应该验证标签名称格式', async () => {
      const user = userEvent.setup()
      mockTagUtils.validateTagName.mockReturnValue({ 
        isValid: false, 
        error: '标签名称格式无效' 
      })
      
      render(<TagForm {...defaultProps} />)
      
      const nameInput = screen.getByLabelText(/标签名称/)
      await user.type(nameInput, '无效标签名')
      
      await waitFor(() => {
        expect(screen.getByText('标签名称格式无效')).toBeInTheDocument()
      })
    })

    it('应该检查标签名称重复', async () => {
      const user = userEvent.setup()
      mockTagUtils.normalizeTagName.mockReturnValue('技术')
      
      render(<TagForm {...defaultProps} existingTags={existingTags} />)
      
      const nameInput = screen.getByLabelText(/标签名称/)
      await user.type(nameInput, '技术')
      
      await waitFor(() => {
        expect(screen.getByText('标签名称已存在，请使用其他名称')).toBeInTheDocument()
      })
    })

    it('应该在编辑模式下排除当前标签的重复检查', async () => {
      const user = userEvent.setup()
      const tag = { id: '1', name: '技术', color: '#ff0000' }
      mockTagUtils.normalizeTagName.mockReturnValue('技术')
      
      render(<TagForm {...defaultProps} tag={tag} existingTags={existingTags} />)
      
      const nameInput = screen.getByLabelText(/标签名称/)
      await user.clear(nameInput)
      await user.type(nameInput, '技术')
      
      // 不应该显示重复错误，因为是编辑当前标签
      await waitFor(() => {
        expect(screen.queryByText('标签名称已存在，请使用其他名称')).not.toBeInTheDocument()
      })
    })

    it('应该验证颜色格式', async () => {
      const user = userEvent.setup()
      mockColorUtils.isValidColor.mockReturnValue(false)
      
      render(<TagForm {...defaultProps} />)
      
      // 模拟颜色选择器返回无效颜色
      const nameInput = screen.getByLabelText(/标签名称/)
      await user.type(nameInput, '测试标签')
      
      // 触发颜色验证（这里需要模拟颜色选择器的交互）
      // 由于颜色选择器是子组件，我们主要测试验证逻辑
      expect(mockColorUtils.isValidColor).toBeDefined()
    })

    it('应该实时验证标签名称', async () => {
      const user = userEvent.setup()
      render(<TagForm {...defaultProps} />)
      
      const nameInput = screen.getByLabelText(/标签名称/)
      await user.type(nameInput, 'a')
      
      // 应该显示验证中的状态（检查SVG图标）
      await waitFor(() => {
        const svg = document.querySelector('svg.animate-spin')
        expect(svg).toBeInTheDocument()
      })
    })

    it('应该更新字符计数', async () => {
      const user = userEvent.setup()
      render(<TagForm {...defaultProps} />)
      
      const nameInput = screen.getByLabelText(/标签名称/)
      await user.type(nameInput, '测试')
      
      expect(screen.getByText('2/50')).toBeInTheDocument()
    })
  })

  describe('表单交互', () => {
    it('应该处理标签名称输入', async () => {
      const user = userEvent.setup()
      render(<TagForm {...defaultProps} />)
      
      const nameInput = screen.getByLabelText(/标签名称/)
      await user.type(nameInput, '新标签')
      
      expect(nameInput).toHaveValue('新标签')
    })

    it('应该在输入时清除相关错误', async () => {
      const user = userEvent.setup()
      render(<TagForm {...defaultProps} />)
      
      // 先触发错误
      const submitButton = screen.getByText('创建标签')
      await user.click(submitButton)
      expect(screen.getByText('标签名称不能为空')).toBeInTheDocument()
      
      // 输入内容应该清除错误
      const nameInput = screen.getByLabelText(/标签名称/)
      await user.type(nameInput, '新标签')
      
      await waitFor(() => {
        expect(screen.queryByText('标签名称不能为空')).not.toBeInTheDocument()
      })
    })

    it('应该显示标签预览', async () => {
      const user = userEvent.setup()
      render(<TagForm {...defaultProps} />)
      
      const nameInput = screen.getByLabelText(/标签名称/)
      await user.type(nameInput, '测试标签')
      
      expect(screen.getByText('标签预览')).toBeInTheDocument()
      expect(screen.getByText('测试标签')).toBeInTheDocument()
      expect(screen.getByText('这是标签在书签中的显示效果')).toBeInTheDocument()
    })

    it('应该在没有名称时不显示预览', () => {
      render(<TagForm {...defaultProps} />)
      
      expect(screen.queryByText('标签预览')).not.toBeInTheDocument()
    })
  })

  describe('表单提交', () => {
    it('应该成功提交有效的表单数据', async () => {
      const user = userEvent.setup()
      const onSave = vi.fn().mockResolvedValue(undefined)
      
      render(<TagForm {...defaultProps} onSave={onSave} />)
      
      const nameInput = screen.getByLabelText(/标签名称/)
      await user.type(nameInput, '新标签')
      
      const submitButton = screen.getByText('创建标签')
      await user.click(submitButton)
      
      await waitFor(() => {
        expect(onSave).toHaveBeenCalledWith({
          name: '新标签',
          color: '#3B82F6' // mock返回值
        })
      })
    })

    it('应该在提交时标准化标签名称', async () => {
      const user = userEvent.setup()
      const onSave = vi.fn().mockResolvedValue(undefined)
      mockTagUtils.normalizeTagName.mockReturnValue('标准化名称')
      
      render(<TagForm {...defaultProps} onSave={onSave} />)
      
      const nameInput = screen.getByLabelText(/标签名称/)
      await user.type(nameInput, '  原始名称  ')
      
      const submitButton = screen.getByText('创建标签')
      await user.click(submitButton)
      
      await waitFor(() => {
        expect(onSave).toHaveBeenCalledWith({
          name: '标准化名称',
          color: '#3B82F6'
        })
      })
    })

    it('应该处理提交错误', async () => {
      const user = userEvent.setup()
      const onSave = vi.fn().mockRejectedValue(new Error('保存失败'))
      
      render(<TagForm {...defaultProps} onSave={onSave} />)
      
      const nameInput = screen.getByLabelText(/标签名称/)
      await user.type(nameInput, '新标签')
      
      const submitButton = screen.getByText('创建标签')
      await user.click(submitButton)
      
      await waitFor(() => {
        expect(screen.getByText(/保存失败/)).toBeInTheDocument()
      })
    })

    it('应该在加载时禁用提交按钮', async () => {
      render(<TagForm {...defaultProps} loading={true} />)
      
      const submitButton = screen.getByRole('button', { name: /保存中/ })
      expect(submitButton).toBeDisabled()
    })

    it('应该在表单无效时禁用提交按钮', () => {
      render(<TagForm {...defaultProps} />)
      
      const submitButton = screen.getByText('创建标签')
      expect(submitButton).toBeDisabled()
    })
  })

  describe('取消操作', () => {
    it('应该直接取消未修改的表单', async () => {
      const user = userEvent.setup()
      const onCancel = vi.fn()
      
      render(<TagForm {...defaultProps} onCancel={onCancel} />)
      
      const cancelButton = screen.getByText('取消')
      await user.click(cancelButton)
      
      expect(onCancel).toHaveBeenCalled()
    })

    it('应该在有修改时显示确认对话框', async () => {
      const user = userEvent.setup()
      const onCancel = vi.fn()
      
      // Mock window.confirm
      const confirmSpy = vi.spyOn(window, 'confirm').mockReturnValue(true)
      
      render(<TagForm {...defaultProps} onCancel={onCancel} />)
      
      // 修改表单
      const nameInput = screen.getByLabelText(/标签名称/)
      await user.type(nameInput, '修改内容')
      
      const cancelButton = screen.getByText('取消')
      await user.click(cancelButton)
      
      expect(confirmSpy).toHaveBeenCalledWith('您有未保存的更改，确定要取消吗？')
      expect(onCancel).toHaveBeenCalled()
      
      confirmSpy.mockRestore()
    })

    it('应该在用户拒绝确认时不取消', async () => {
      const user = userEvent.setup()
      const onCancel = vi.fn()
      
      // Mock window.confirm返回false
      const confirmSpy = vi.spyOn(window, 'confirm').mockReturnValue(false)
      
      render(<TagForm {...defaultProps} onCancel={onCancel} />)
      
      // 修改表单
      const nameInput = screen.getByLabelText(/标签名称/)
      await user.type(nameInput, '修改内容')
      
      const cancelButton = screen.getByText('取消')
      await user.click(cancelButton)
      
      expect(confirmSpy).toHaveBeenCalled()
      expect(onCancel).not.toHaveBeenCalled()
      
      confirmSpy.mockRestore()
    })
  })

  describe('加载状态', () => {
    it('应该在加载时禁用所有输入', () => {
      render(<TagForm {...defaultProps} loading={true} />)
      
      const nameInput = screen.getByLabelText(/标签名称/)
      const cancelButton = screen.getByText('取消')
      
      expect(nameInput).toBeDisabled()
      expect(cancelButton).toBeDisabled()
    })

    it('应该显示加载中的提交按钮', () => {
      render(<TagForm {...defaultProps} loading={true} />)
      
      expect(screen.getByText('保存中...')).toBeInTheDocument()
      // 检查SVG加载图标
      const svg = document.querySelector('svg.animate-spin')
      expect(svg).toBeInTheDocument()
    })
  })

  describe('编辑模式', () => {
    it('应该预填充编辑数据', () => {
      const tag = { id: '1', name: '编辑标签', color: '#ff0000' }
      render(<TagForm {...defaultProps} tag={tag} />)
      
      const nameInput = screen.getByLabelText(/标签名称/)
      expect(nameInput).toHaveValue('编辑标签')
    })

    it('应该在标签数据变化时更新表单', () => {
      const tag1 = { id: '1', name: '标签1', color: '#ff0000' }
      const { rerender } = render(<TagForm {...defaultProps} tag={tag1} />)
      
      expect(screen.getByDisplayValue('标签1')).toBeInTheDocument()
      
      const tag2 = { id: '2', name: '标签2', color: '#00ff00' }
      rerender(<TagForm {...defaultProps} tag={tag2} />)
      
      expect(screen.getByDisplayValue('标签2')).toBeInTheDocument()
    })
  })

  describe('无障碍性', () => {
    it('应该有适当的标签和描述', () => {
      render(<TagForm {...defaultProps} />)
      
      expect(screen.getByLabelText(/标签名称/)).toBeInTheDocument()
      expect(screen.getByRole('button', { name: /创建标签/ })).toBeInTheDocument()
      expect(screen.getByRole('button', { name: /取消/ })).toBeInTheDocument()
    })

    it('应该支持键盘导航', async () => {
      const user = userEvent.setup()
      render(<TagForm {...defaultProps} />)
      
      // Tab到名称输入框
      await user.tab()
      expect(screen.getByLabelText(/标签名称/)).toHaveFocus()
    })
  })

  describe('边界情况', () => {
    it('应该处理空的现有标签列表', () => {
      render(<TagForm {...defaultProps} existingTags={[]} />)
      
      expect(screen.getByText('创建新标签')).toBeInTheDocument()
    })

    it('应该处理undefined的标签数据', () => {
      render(<TagForm {...defaultProps} tag={undefined} />)
      
      expect(screen.getByText('创建新标签')).toBeInTheDocument()
    })

    it('应该处理最大长度的标签名称', async () => {
      const user = userEvent.setup()
      render(<TagForm {...defaultProps} />)
      
      const longName = 'a'.repeat(50)
      const nameInput = screen.getByLabelText(/标签名称/)
      await user.type(nameInput, longName)
      
      expect(nameInput).toHaveValue(longName)
      expect(screen.getByText('50/50')).toBeInTheDocument()
    })

    it('应该截断超长的标签名称', async () => {
      const user = userEvent.setup()
      render(<TagForm {...defaultProps} />)
      
      const tooLongName = 'a'.repeat(60)
      const nameInput = screen.getByLabelText(/标签名称/)
      
      // 由于maxLength属性，输入应该被截断
      await user.type(nameInput, tooLongName)
      expect(nameInput.value.length).toBeLessThanOrEqual(50)
    })
  })
})