// AddBookmarkModal shadcn重构验证测试

import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { vi, describe, it, expect, beforeEach } from 'vitest'
import AddBookmarkModal from '../src/components/AddBookmarkModal'
import { BookmarkInput } from '../src/types'

// Mock组件的props
const mockProps = {
  isOpen: true,
  onSave: vi.fn(),
  onCancel: vi.fn(),
  loading: false
}

describe('AddBookmarkModal shadcn重构验证测试', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('shadcn组件集成测试', () => {
    it('应该使用shadcn Dialog组件', () => {
      render(<AddBookmarkModal {...mockProps} />)
      
      // 验证Dialog组件的存在
      const dialog = screen.getByRole('dialog')
      expect(dialog).toBeInTheDocument()
      expect(dialog).toHaveAttribute('aria-labelledby')
      expect(dialog).toHaveAttribute('aria-describedby')
    })

    it('应该使用shadcn Button组件', () => {
      render(<AddBookmarkModal {...mockProps} />)
      
      // 验证各种Button组件
      expect(screen.getByRole('button', { name: '取消' })).toBeInTheDocument()
      expect(screen.getByRole('button', { name: '保存收藏' })).toBeInTheDocument()
      expect(screen.getByRole('button', { name: 'Close' })).toBeInTheDocument()
      expect(screen.getByRole('button', { name: '检查' })).toBeInTheDocument()
      expect(screen.getByRole('button', { name: '添加' })).toBeInTheDocument()
      expect(screen.getByRole('button', { name: 'AI建议' })).toBeInTheDocument()
    })

    it('应该使用shadcn Input组件', () => {
      render(<AddBookmarkModal {...mockProps} />)
      
      // 验证Input组件
      const titleInput = screen.getByPlaceholderText('请输入收藏标题')
      const urlInput = screen.getByPlaceholderText('https://example.com')
      const tagInput = screen.getByPlaceholderText('输入标签后按回车添加')
      
      expect(titleInput).toBeInTheDocument()
      expect(urlInput).toBeInTheDocument()
      expect(tagInput).toBeInTheDocument()
      
      // 验证Input组件的shadcn样式类
      expect(titleInput).toHaveClass('flex', 'h-10', 'w-full', 'rounded-md', 'border')
      expect(urlInput).toHaveClass('flex', 'h-10', 'w-full', 'rounded-md', 'border')
    })

    it('应该使用shadcn Textarea组件', () => {
      render(<AddBookmarkModal {...mockProps} />)
      
      // 切换到文本类型以显示Textarea
      const textRadio = screen.getByRole('radio', { name: /文本摘录/ })
      fireEvent.click(textRadio)
      
      const contentTextarea = screen.getByPlaceholderText('请输入要收藏的文本内容')
      const descriptionTextarea = screen.getByPlaceholderText('请输入收藏描述（可选）')
      
      expect(contentTextarea).toBeInTheDocument()
      expect(descriptionTextarea).toBeInTheDocument()
      
      // 验证Textarea组件的shadcn样式类
      expect(contentTextarea).toHaveClass('flex', 'min-h-[80px]', 'w-full', 'rounded-md', 'border')
      expect(descriptionTextarea).toHaveClass('flex', 'min-h-[80px]', 'w-full', 'rounded-md', 'border')
    })

    it('应该使用shadcn Select组件', () => {
      render(<AddBookmarkModal {...mockProps} />)
      
      // 验证Select组件
      const categorySelect = screen.getByRole('combobox', { name: '分类 *' })
      expect(categorySelect).toBeInTheDocument()
      
      // 验证Select组件的shadcn样式类
      expect(categorySelect).toHaveClass('flex', 'h-10', 'w-full', 'items-center', 'justify-between')
    })

    it('应该使用shadcn Badge组件显示标签', async () => {
      render(<AddBookmarkModal {...mockProps} />)
      
      // 添加一个标签
      const tagInput = screen.getByPlaceholderText('输入标签后按回车添加')
      fireEvent.change(tagInput, { target: { value: '测试标签' } })
      fireEvent.keyDown(tagInput, { key: 'Enter', code: 'Enter' })
      
      await waitFor(() => {
        const badge = screen.getByTestId('tag-测试标签')
        expect(badge).toBeInTheDocument()
        
        // 验证Badge组件的shadcn样式类
        expect(badge).toHaveClass('flex', 'items-center', 'gap-1')
      })
    })

    it('应该使用shadcn Form组件进行表单验证', async () => {
      render(<AddBookmarkModal {...mockProps} />)
      
      // 提交空表单触发验证
      const saveButton = screen.getByRole('button', { name: '保存收藏' })
      fireEvent.click(saveButton)
      
      await waitFor(() => {
        // 验证表单验证错误消息的显示
        expect(screen.getByText('标题不能为空')).toBeInTheDocument()
        expect(screen.getByText('URL不能为空')).toBeInTheDocument()
      })
    })
  })

  describe('功能完整性验证', () => {
    it('应该保持原有的表单提交功能', async () => {
      render(<AddBookmarkModal {...mockProps} />)
      
      // 填写表单
      const titleInput = screen.getByPlaceholderText('请输入收藏标题')
      const urlInput = screen.getByPlaceholderText('https://example.com')
      
      fireEvent.change(titleInput, { target: { value: '测试标题' } })
      fireEvent.change(urlInput, { target: { value: 'https://example.com' } })
      
      // 提交表单
      const saveButton = screen.getByRole('button', { name: '保存收藏' })
      fireEvent.click(saveButton)
      
      await waitFor(() => {
        expect(mockProps.onSave).toHaveBeenCalledWith({
          type: 'url',
          title: '测试标题',
          url: 'https://example.com',
          category: '默认分类',
          tags: [],
          metadata: {
            aiGenerated: false
          }
        })
      })
    })

    it('应该保持原有的标签管理功能', async () => {
      render(<AddBookmarkModal {...mockProps} />)
      
      const tagInput = screen.getByPlaceholderText('输入标签后按回车添加')
      
      // 添加标签
      fireEvent.change(tagInput, { target: { value: '标签1' } })
      fireEvent.keyDown(tagInput, { key: 'Enter', code: 'Enter' })
      
      await waitFor(() => {
        expect(screen.getByTestId('tag-标签1')).toBeInTheDocument()
      })
      
      // 删除标签
      const deleteButton = screen.getByLabelText('删除标签 标签1')
      fireEvent.click(deleteButton)
      
      await waitFor(() => {
        expect(screen.queryByTestId('tag-标签1')).not.toBeInTheDocument()
      })
    })

    it('应该保持原有的类型切换功能', () => {
      render(<AddBookmarkModal {...mockProps} />)
      
      // 默认应该是URL类型
      expect(screen.getByRole('radio', { name: /网页链接/ })).toBeChecked()
      expect(screen.queryByPlaceholderText('请输入要收藏的文本内容')).not.toBeInTheDocument()
      
      // 切换到文本类型
      const textRadio = screen.getByRole('radio', { name: /文本摘录/ })
      fireEvent.click(textRadio)
      
      expect(textRadio).toBeChecked()
      expect(screen.getByPlaceholderText('请输入要收藏的文本内容')).toBeInTheDocument()
    })
  })

  describe('样式和主题验证', () => {
    it('应该应用shadcn的主题样式', () => {
      render(<AddBookmarkModal {...mockProps} />)
      
      const dialog = screen.getByRole('dialog')
      
      // 验证Dialog的shadcn主题样式
      expect(dialog).toHaveClass('bg-background', 'border', 'shadow-lg')
    })

    it('应该使用shadcn的间距系统', () => {
      render(<AddBookmarkModal {...mockProps} />)
      
      const form = document.querySelector('form')
      expect(form).toHaveClass('space-y-6')
    })

    it('应该使用shadcn的颜色系统', () => {
      render(<AddBookmarkModal {...mockProps} />)
      
      const saveButton = screen.getByRole('button', { name: '保存收藏' })
      expect(saveButton).toHaveClass('bg-primary', 'text-primary-foreground')
      
      const cancelButton = screen.getByRole('button', { name: '取消' })
      expect(cancelButton).toHaveClass('border-input', 'bg-background')
    })
  })
})