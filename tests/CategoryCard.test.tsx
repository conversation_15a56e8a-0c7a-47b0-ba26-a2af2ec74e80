// CategoryCard组件单元测试

import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { vi, describe, it, expect, beforeEach } from 'vitest'
import CategoryCard from '../src/components/CategoryCard'
import type { Category } from '../src/types'

// 模拟分类数据
const mockCategory: Category = {
  id: 'category-1',
  name: '技术',
  description: '技术相关的书签分类',
  color: '#3B82F6',
  createdAt: new Date('2024-01-01T10:00:00Z'),
  updatedAt: new Date('2024-01-02T10:00:00Z'),
  bookmarkCount: 0
}

const mockCategoryWithoutDescription: Category = {
  id: 'category-2',
  name: '学习',
  createdAt: new Date('2024-01-01T10:00:00Z'),
  updatedAt: new Date('2024-01-01T10:00:00Z'),
  bookmarkCount: 0
}

const mockCategoryWithParent: Category = {
  id: 'category-3',
  name: '前端开发',
  description: '前端开发相关内容',
  color: '#10B981',
  parentId: 'category-1',
  createdAt: new Date('2024-01-01T10:00:00Z'),
  updatedAt: new Date('2024-01-01T10:00:00Z'),
  bookmarkCount: 0
}

describe('CategoryCard组件', () => {
  const mockOnEdit = vi.fn()
  const mockOnDelete = vi.fn()
  const mockOnClick = vi.fn()

  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('基本渲染', () => {
    it('应该正确渲染分类基本信息', () => {
      render(
        <CategoryCard
          category={mockCategory}
          bookmarkCount={5}
          onEdit={mockOnEdit}
          onDelete={mockOnDelete}
        />
      )

      // 检查分类名称
      expect(screen.getByText('技术')).toBeInTheDocument()
      
      // 检查分类描述
      expect(screen.getByText('技术相关的书签分类')).toBeInTheDocument()
      
      // 检查书签数量
      expect(screen.getByText('5')).toBeInTheDocument()
      expect(screen.getByText('书签数量')).toBeInTheDocument()
    })

    it('应该正确渲染没有描述的分类', () => {
      render(
        <CategoryCard
          category={mockCategoryWithoutDescription}
          bookmarkCount={3}
          onEdit={mockOnEdit}
          onDelete={mockOnDelete}
        />
      )

      // 检查分类名称
      expect(screen.getByText('学习')).toBeInTheDocument()
      
      // 检查没有描述文本
      expect(screen.queryByText('技术相关的书签分类')).not.toBeInTheDocument()
      
      // 检查书签数量
      expect(screen.getByText('3')).toBeInTheDocument()
    })

    it('应该显示正确的分类状态', () => {
      // 测试空分类状态
      render(
        <CategoryCard
          category={mockCategory}
          bookmarkCount={0}
          onEdit={mockOnEdit}
          onDelete={mockOnDelete}
        />
      )

      expect(screen.getByText('空分类')).toBeInTheDocument()
    })

    it('应该显示活跃分类状态', () => {
      render(
        <CategoryCard
          category={mockCategory}
          bookmarkCount={5}
          onEdit={mockOnEdit}
          onDelete={mockOnDelete}
        />
      )

      expect(screen.getByText('活跃')).toBeInTheDocument()
    })

    it('应该显示子分类指示器', () => {
      render(
        <CategoryCard
          category={mockCategoryWithParent}
          bookmarkCount={2}
          onEdit={mockOnEdit}
          onDelete={mockOnDelete}
        />
      )

      expect(screen.getByText('子分类')).toBeInTheDocument()
    })
  })

  describe('颜色显示', () => {
    it('应该正确应用分类颜色', () => {
      const { container } = render(
        <CategoryCard
          category={mockCategory}
          bookmarkCount={5}
          onEdit={mockOnEdit}
          onDelete={mockOnDelete}
        />
      )

      // 检查颜色指示器
      const colorIndicator = container.querySelector('[title="分类颜色: #3B82F6"]')
      expect(colorIndicator).toBeInTheDocument()
      expect(colorIndicator).toHaveStyle('background-color: #3B82F6')
    })

    it('应该为没有颜色的分类使用默认颜色', () => {
      const categoryWithoutColor = { ...mockCategory, color: undefined }
      const { container } = render(
        <CategoryCard
          category={categoryWithoutColor}
          bookmarkCount={5}
          onEdit={mockOnEdit}
          onDelete={mockOnDelete}
        />
      )

      // 检查默认颜色指示器
      const colorIndicator = container.querySelector('[title="分类颜色: 默认"]')
      expect(colorIndicator).toBeInTheDocument()
      expect(colorIndicator).toHaveStyle('background-color: #6B7280')
    })
  })

  describe('时间显示', () => {
    it('应该正确格式化创建时间', () => {
      // 使用固定的当前时间进行测试
      const fixedDate = new Date('2024-01-03T10:00:00Z')
      vi.setSystemTime(fixedDate)

      render(
        <CategoryCard
          category={mockCategory}
          bookmarkCount={5}
          onEdit={mockOnEdit}
          onDelete={mockOnDelete}
        />
      )

      // 应该显示相对时间
      expect(screen.getByText('2天前')).toBeInTheDocument()

      vi.useRealTimers()
    })

    it('应该显示更新时间（如果与创建时间不同）', () => {
      render(
        <CategoryCard
          category={mockCategory}
          bookmarkCount={5}
          onEdit={mockOnEdit}
          onDelete={mockOnDelete}
        />
      )

      // 应该显示更新时间标签
      expect(screen.getByText('更新时间')).toBeInTheDocument()
    })

    it('应该不显示更新时间（如果与创建时间相同）', () => {
      render(
        <CategoryCard
          category={mockCategoryWithoutDescription}
          bookmarkCount={5}
          onEdit={mockOnEdit}
          onDelete={mockOnDelete}
        />
      )

      // 不应该显示更新时间标签
      expect(screen.queryByText('更新时间')).not.toBeInTheDocument()
    })
  })

  describe('交互功能', () => {
    it('应该在点击编辑按钮时调用onEdit', async () => {
      render(
        <CategoryCard
          category={mockCategory}
          bookmarkCount={5}
          onEdit={mockOnEdit}
          onDelete={mockOnDelete}
        />
      )

      const editButton = screen.getByLabelText('编辑分类')
      fireEvent.click(editButton)

      expect(mockOnEdit).toHaveBeenCalledTimes(1)
    })

    it('应该在点击删除按钮时调用onDelete', async () => {
      render(
        <CategoryCard
          category={mockCategory}
          bookmarkCount={5}
          onEdit={mockOnEdit}
          onDelete={mockOnDelete}
        />
      )

      const deleteButton = screen.getByLabelText('删除分类')
      fireEvent.click(deleteButton)

      expect(mockOnDelete).toHaveBeenCalledTimes(1)
    })

    it('应该在点击卡片时调用onClick', async () => {
      render(
        <CategoryCard
          category={mockCategory}
          bookmarkCount={5}
          onEdit={mockOnEdit}
          onDelete={mockOnDelete}
          onClick={mockOnClick}
        />
      )

      const card = screen.getByText('技术').closest('div')
      fireEvent.click(card!)

      expect(mockOnClick).toHaveBeenCalledTimes(1)
    })

    it('应该在点击操作按钮时不触发卡片点击', async () => {
      render(
        <CategoryCard
          category={mockCategory}
          bookmarkCount={5}
          onEdit={mockOnEdit}
          onDelete={mockOnDelete}
          onClick={mockOnClick}
        />
      )

      const editButton = screen.getByLabelText('编辑分类')
      fireEvent.click(editButton)

      expect(mockOnEdit).toHaveBeenCalledTimes(1)
      expect(mockOnClick).not.toHaveBeenCalled()
    })
  })

  describe('悬停效果', () => {
    it('应该在悬停时显示操作按钮', async () => {
      const { container } = render(
        <CategoryCard
          category={mockCategory}
          bookmarkCount={5}
          onEdit={mockOnEdit}
          onDelete={mockOnDelete}
        />
      )

      const card = container.firstChild as HTMLElement
      
      // 初始状态下操作按钮应该是隐藏的
      const actionsContainer = container.querySelector('.category-card-actions')
      expect(actionsContainer).toHaveClass('opacity-0')

      // 悬停时应该显示操作按钮
      fireEvent.mouseEnter(card)
      await waitFor(() => {
        expect(actionsContainer).toHaveClass('group-hover:opacity-100')
      })
    })
  })

  describe('可访问性', () => {
    it('应该为操作按钮提供正确的aria-label', () => {
      render(
        <CategoryCard
          category={mockCategory}
          bookmarkCount={5}
          onEdit={mockOnEdit}
          onDelete={mockOnDelete}
        />
      )

      expect(screen.getByLabelText('编辑分类')).toBeInTheDocument()
      expect(screen.getByLabelText('删除分类')).toBeInTheDocument()
    })

    it('应该为颜色指示器提供title属性', () => {
      const { container } = render(
        <CategoryCard
          category={mockCategory}
          bookmarkCount={5}
          onEdit={mockOnEdit}
          onDelete={mockOnDelete}
        />
      )

      const colorIndicator = container.querySelector('[title="分类颜色: #3B82F6"]')
      expect(colorIndicator).toBeInTheDocument()
    })
  })

  describe('自定义样式', () => {
    it('应该应用自定义className', () => {
      const { container } = render(
        <CategoryCard
          category={mockCategory}
          bookmarkCount={5}
          onEdit={mockOnEdit}
          onDelete={mockOnDelete}
          className="custom-class"
        />
      )

      expect(container.firstChild).toHaveClass('custom-class')
    })
  })

  describe('边界情况', () => {
    it('应该处理无效的日期', () => {
      const categoryWithInvalidDate = {
        ...mockCategory,
        createdAt: new Date('invalid-date'),
        updatedAt: new Date('invalid-date')
      }

      render(
        <CategoryCard
          category={categoryWithInvalidDate}
          bookmarkCount={5}
          onEdit={mockOnEdit}
          onDelete={mockOnDelete}
        />
      )

      // 应该有两个"未知时间"文本（创建时间和更新时间）
      const unknownTimeElements = screen.getAllByText('未知时间')
      expect(unknownTimeElements).toHaveLength(2)
    })

    it('应该处理非常长的分类名称', () => {
      const categoryWithLongName = {
        ...mockCategory,
        name: '这是一个非常非常非常非常非常非常长的分类名称，应该被正确截断显示'
      }

      const { container } = render(
        <CategoryCard
          category={categoryWithLongName}
          bookmarkCount={5}
          onEdit={mockOnEdit}
          onDelete={mockOnDelete}
        />
      )

      const nameElement = screen.getByText(categoryWithLongName.name)
      expect(nameElement).toHaveClass('truncate')
    })

    it('应该处理非常长的描述', () => {
      const categoryWithLongDescription = {
        ...mockCategory,
        description: '这是一个非常非常非常非常非常非常长的描述文本，应该被正确截断显示，并且不会影响整体布局的美观性和可读性'
      }

      const { container } = render(
        <CategoryCard
          category={categoryWithLongDescription}
          bookmarkCount={5}
          onEdit={mockOnEdit}
          onDelete={mockOnDelete}
        />
      )

      const descriptionElement = screen.getByText(categoryWithLongDescription.description)
      expect(descriptionElement).toHaveClass('text-truncate-2')
    })
  })
})