// BookmarkCompact组件集成测试 - 验证shadcn重构后的基本功能

import React from 'react'
import { render, screen, fireEvent } from '@testing-library/react'
import { describe, it, expect, beforeEach, vi } from 'vitest'
import '@testing-library/jest-dom'
import BookmarkCompact from '../src/components/BookmarkCompact'
import type { Bookmark } from '../src/types'

// 模拟TruncatedTitle组件
vi.mock('../src/components/TruncatedTitle', () => ({
  default: function MockTruncatedTitle({ title, className }: any) {
    return <span className={className}>{title}</span>
  }
}))

// 创建测试用的收藏数据
const createMockBookmark = (overrides: Partial<Bookmark> = {}): Bookmark => ({
  id: 'test-bookmark-1',
  type: 'url',
  title: '测试收藏标题',
  url: 'https://example.com',
  description: '这是一个测试描述',
  tags: ['测试', '标签'],
  category: '测试分类',
  favicon: 'https://example.com/favicon.ico',
  createdAt: new Date('2024-01-01'),
  updatedAt: new Date('2024-01-01'),
  metadata: {
    pageTitle: '测试页面标题',
    siteName: 'example.com',
    aiGenerated: false
  },
  ...overrides
})

describe('BookmarkCompact组件 - 集成测试', () => {
  const mockOnEdit = vi.fn()
  const mockOnDelete = vi.fn()
  const mockOnClick = vi.fn()

  beforeEach(() => {
    vi.clearAllMocks()
    global.open = vi.fn()
  })

  it('应该正确渲染shadcn重构后的组件', () => {
    const bookmark = createMockBookmark()
    
    render(
      <BookmarkCompact 
        bookmark={bookmark}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
        onClick={mockOnClick}
      />
    )

    // 验证基本内容显示
    expect(screen.getByText('测试收藏标题')).toBeInTheDocument()
    expect(screen.getByText('https://example.com')).toBeInTheDocument()
    expect(screen.getByText('这是一个测试描述')).toBeInTheDocument()
    expect(screen.getByText('测试分类')).toBeInTheDocument()
    expect(screen.getByText('测试')).toBeInTheDocument()
    expect(screen.getByText('标签')).toBeInTheDocument()
  })

  it('应该使用shadcn组件样式', () => {
    const bookmark = createMockBookmark()
    
    const { container } = render(
      <BookmarkCompact bookmark={bookmark} />
    )

    // 验证使用了shadcn Card组件的样式
    const cardElement = container.querySelector('[class*="rounded-lg"][class*="border"]')
    expect(cardElement).toBeInTheDocument()

    // 验证使用了shadcn Badge组件的样式
    const badgeElements = container.querySelectorAll('[class*="inline-flex"][class*="rounded-full"]')
    expect(badgeElements.length).toBeGreaterThan(0)
  })

  it('应该正确处理点击事件', () => {
    const bookmark = createMockBookmark()
    
    const { container } = render(
      <BookmarkCompact 
        bookmark={bookmark}
        onClick={mockOnClick}
      />
    )
    
    const card = container.firstChild as HTMLElement
    fireEvent.click(card)
    
    expect(mockOnClick).toHaveBeenCalledWith(bookmark)
  })

  it('应该正确处理高亮状态', () => {
    const bookmark = createMockBookmark()
    
    const { container } = render(
      <BookmarkCompact bookmark={bookmark} isHighlighted={true} />
    )
    
    const cardElement = container.firstChild as HTMLElement
    expect(cardElement).toHaveClass('ring-2', 'ring-primary', 'border-primary', 'bg-accent')
  })

  it('应该正确显示操作按钮', () => {
    const bookmark = createMockBookmark()
    
    render(
      <BookmarkCompact 
        bookmark={bookmark}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
      />
    )
    
    // 验证按钮通过aria-label可以找到
    expect(screen.getByLabelText('在新标签页中打开')).toBeInTheDocument()
    expect(screen.getByLabelText('编辑收藏')).toBeInTheDocument()
    expect(screen.getByLabelText('删除收藏')).toBeInTheDocument()
  })

  it('应该正确处理文本类型的内容预览', () => {
    const bookmark = createMockBookmark({
      type: 'text',
      content: '这是一段测试文本内容'
    })
    
    const { container } = render(<BookmarkCompact bookmark={bookmark} />)
    
    // 验证使用了shadcn的背景色
    const contentPreview = container.querySelector('.bg-muted')
    expect(contentPreview).toBeInTheDocument()
    expect(screen.getByText('这是一段测试文本内容')).toBeInTheDocument()
  })

  it('应该正确处理边界情况', () => {
    const bookmark = createMockBookmark({
      title: '',
      url: undefined,
      description: undefined,
      tags: [],
      category: undefined
    })
    
    render(<BookmarkCompact bookmark={bookmark} />)
    
    // 验证无标题时显示默认文本
    expect(screen.getByText('无标题')).toBeInTheDocument()
    
    // 验证无URL时不显示外部链接按钮
    expect(screen.queryByLabelText('在新标签页中打开')).not.toBeInTheDocument()
  })
})