// 选项页面错误边界组件测试

import React from 'react'
import { render, screen, fireEvent } from '@testing-library/react'
import '@testing-library/jest-dom'
import { vi } from 'vitest'
import OptionsPageErrorBoundary from '../src/components/OptionsPageErrorBoundary'

// 模拟Chrome API
const mockChrome = {
  action: {
    openPopup: vi.fn()
  },
  runtime: {
    getURL: vi.fn((path: string) => `chrome-extension://test/${path}`)
  }
}

// 设置全局Chrome对象
Object.defineProperty(global, 'chrome', {
  value: mockChrome,
  writable: true
})

// 模拟localStorage
const mockLocalStorage = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn()
}

Object.defineProperty(global, 'localStorage', {
  value: mockLocalStorage,
  writable: true
})

// 模拟window.location
Object.defineProperty(global.window, 'location', {
  value: {
    reload: vi.fn()
  },
  writable: true
})

// 创建一个会抛出错误的测试组件
const ThrowError: React.FC<{ shouldThrow: boolean }> = ({ shouldThrow }) => {
  if (shouldThrow) {
    throw new Error('Cannot access \'K\' before initialization')
  }
  return <div>正常组件</div>
}

describe('OptionsPageErrorBoundary', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    mockLocalStorage.getItem.mockReturnValue('[]')
  })

  afterEach(() => {
    // 清理控制台错误输出
    vi.restoreAllMocks()
  })

  it('应该正常渲染子组件', () => {
    render(
      <OptionsPageErrorBoundary>
        <div>测试内容</div>
      </OptionsPageErrorBoundary>
    )

    expect(screen.getByText('测试内容')).toBeInTheDocument()
  })

  it('应该捕获初始化错误并显示错误界面', () => {
    // 抑制控制台错误输出
    const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})

    render(
      <OptionsPageErrorBoundary>
        <ThrowError shouldThrow={true} />
      </OptionsPageErrorBoundary>
    )

    // 检查错误界面是否显示
    expect(screen.getByText('页面加载失败')).toBeInTheDocument()
    expect(screen.getByText('收藏管理页面遇到了问题，无法正常显示')).toBeInTheDocument()
    expect(screen.getByText('模块初始化顺序错误')).toBeInTheDocument()

    consoleSpy.mockRestore()
  })

  it('应该显示正确的错误类型描述', () => {
    const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})

    render(
      <OptionsPageErrorBoundary>
        <ThrowError shouldThrow={true} />
      </OptionsPageErrorBoundary>
    )

    expect(screen.getByText('模块初始化顺序错误')).toBeInTheDocument()
    expect(screen.getByText('Cannot access \'K\' before initialization')).toBeInTheDocument()

    consoleSpy.mockRestore()
  })

  it('应该显示解决建议', () => {
    const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})

    render(
      <OptionsPageErrorBoundary>
        <ThrowError shouldThrow={true} />
      </OptionsPageErrorBoundary>
    )

    expect(screen.getByText('解决建议')).toBeInTheDocument()
    expect(screen.getByText('这通常是由于模块导入顺序问题导致的')).toBeInTheDocument()
    expect(screen.getByText('请尝试刷新页面或重新加载扩展')).toBeInTheDocument()

    consoleSpy.mockRestore()
  })

  it('应该提供重试功能', () => {
    const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})

    const TestComponent: React.FC = () => {
      const [shouldThrow, setShouldThrow] = React.useState(true)
      
      React.useEffect(() => {
        // 模拟第一次重试后成功
        const timer = setTimeout(() => {
          setShouldThrow(false)
        }, 100)
        return () => clearTimeout(timer)
      }, [])

      return (
        <OptionsPageErrorBoundary>
          <ThrowError shouldThrow={shouldThrow} />
        </OptionsPageErrorBoundary>
      )
    }

    render(<TestComponent />)

    // 检查重试按钮是否存在
    const retryButton = screen.getByText(/重试/)
    expect(retryButton).toBeInTheDocument()

    consoleSpy.mockRestore()
  })

  it('应该提供刷新页面功能', () => {
    const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})

    render(
      <OptionsPageErrorBoundary>
        <ThrowError shouldThrow={true} />
      </OptionsPageErrorBoundary>
    )

    const refreshButton = screen.getByText('刷新页面')
    expect(refreshButton).toBeInTheDocument()

    fireEvent.click(refreshButton)
    expect(window.location.reload).toHaveBeenCalled()

    consoleSpy.mockRestore()
  })

  it('应该提供返回主页功能', () => {
    const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})

    render(
      <OptionsPageErrorBoundary>
        <ThrowError shouldThrow={true} />
      </OptionsPageErrorBoundary>
    )

    const homeButton = screen.getByText('返回主页')
    expect(homeButton).toBeInTheDocument()

    fireEvent.click(homeButton)
    expect(mockChrome.action.openPopup).toHaveBeenCalled()

    consoleSpy.mockRestore()
  })

  it('应该提供清除错误日志功能', () => {
    const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})
    const alertSpy = vi.spyOn(window, 'alert').mockImplementation(() => {})

    render(
      <OptionsPageErrorBoundary>
        <ThrowError shouldThrow={true} />
      </OptionsPageErrorBoundary>
    )

    const clearLogsButton = screen.getByText('清除错误日志')
    expect(clearLogsButton).toBeInTheDocument()

    fireEvent.click(clearLogsButton)
    expect(mockLocalStorage.removeItem).toHaveBeenCalledWith('universe-bag-errors')
    expect(alertSpy).toHaveBeenCalledWith('错误日志已清除')

    consoleSpy.mockRestore()
    alertSpy.mockRestore()
  })

  it('应该存储错误报告到localStorage', () => {
    const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})
    mockLocalStorage.getItem.mockReturnValue('[]')

    render(
      <OptionsPageErrorBoundary>
        <ThrowError shouldThrow={true} />
      </OptionsPageErrorBoundary>
    )

    // 检查是否调用了localStorage.setItem来存储错误
    expect(mockLocalStorage.setItem).toHaveBeenCalledWith(
      'universe-bag-errors',
      expect.stringContaining('Cannot access')
    )

    consoleSpy.mockRestore()
  })

  it('应该限制错误日志数量', () => {
    const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})
    
    // 模拟已有10个错误的情况
    const existingErrors = Array(10).fill(null).map((_, i) => ({
      message: `Error ${i}`,
      timestamp: new Date().toISOString()
    }))
    mockLocalStorage.getItem.mockReturnValue(JSON.stringify(existingErrors))

    render(
      <OptionsPageErrorBoundary>
        <ThrowError shouldThrow={true} />
      </OptionsPageErrorBoundary>
    )

    // 检查是否正确处理了错误日志数量限制
    expect(mockLocalStorage.setItem).toHaveBeenCalled()
    const setItemCall = mockLocalStorage.setItem.mock.calls[0]
    const storedErrors = JSON.parse(setItemCall[1])
    expect(storedErrors.length).toBeLessThanOrEqual(10)

    consoleSpy.mockRestore()
  })

  it('应该在开发模式下显示详细错误信息', () => {
    const originalEnv = process.env.NODE_ENV
    process.env.NODE_ENV = 'development'
    
    const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})

    render(
      <OptionsPageErrorBoundary>
        <ThrowError shouldThrow={true} />
      </OptionsPageErrorBoundary>
    )

    // 检查是否有显示详细错误信息的元素
    const detailsElement = screen.getByText('显示详细错误信息（开发模式）')
    expect(detailsElement).toBeInTheDocument()

    process.env.NODE_ENV = originalEnv
    consoleSpy.mockRestore()
  })

  it('应该正确识别不同类型的错误', () => {
    const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})

    // 测试React渲染错误
    const ReactError: React.FC = () => {
      throw new Error('React render error')
    }

    const { unmount } = render(
      <OptionsPageErrorBoundary>
        <ReactError />
      </OptionsPageErrorBoundary>
    )

    expect(screen.getByText('React渲染错误')).toBeInTheDocument()

    // 清理第一个测试
    unmount()

    // 测试模块加载错误
    const ModuleError: React.FC = () => {
      throw new Error('import module failed')
    }

    render(
      <OptionsPageErrorBoundary>
        <ModuleError />
      </OptionsPageErrorBoundary>
    )

    expect(screen.getByText('模块加载错误')).toBeInTheDocument()

    consoleSpy.mockRestore()
  })
})