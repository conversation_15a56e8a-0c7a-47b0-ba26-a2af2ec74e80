// TagsTab shadcn重构测试

import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { vi, describe, it, expect, beforeEach } from 'vitest'
import TagsTab from '../src/components/TagsTab'
import { tagService } from '../src/services/tagService'

// Mock tagService
vi.mock('../src/services/tagService', () => ({
  tagService: {
    syncTagsFromBookmarks: vi.fn()
  }
}))

// Mock TagManagementTab组件
vi.mock('../src/components/TagManagementTab', () => ({
  default: ({ className }: any) => (
    <div data-testid="tag-management-tab" className={className}>
      Tag Management Content
    </div>
  )
}))

// Mock Chrome API
Object.defineProperty(global, 'chrome', {
  value: {
    runtime: {
      id: 'test-extension-id'
    }
  },
  writable: true
})

describe('TagsTab shadcn重构测试', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    vi.mocked(tagService.syncTagsFromBookmarks).mockResolvedValue(undefined)
  })

  it('应该使用shadcn Card组件显示初始化加载状态', () => {
    // Mock一个永不resolve的Promise来保持loading状态
    vi.mocked(tagService.syncTagsFromBookmarks).mockImplementation(
      () => new Promise(() => {})
    )
    
    render(<TagsTab />)
    
    expect(screen.getByText('正在初始化标签管理...')).toBeInTheDocument()
  })

  it('应该使用shadcn Card和Button组件显示初始化错误状态', async () => {
    // Mock Chrome API不可用
    Object.defineProperty(global, 'chrome', {
      value: undefined,
      writable: true
    })
    
    render(<TagsTab />)
    
    await waitFor(() => {
      expect(screen.getByText('标签管理初始化失败')).toBeInTheDocument()
      expect(screen.getByText('Chrome扩展API不可用，请确保在扩展环境中运行')).toBeInTheDocument()
      expect(screen.getByRole('button', { name: /重试/ })).toBeInTheDocument()
      expect(screen.getByRole('button', { name: /刷新页面/ })).toBeInTheDocument()
    })
    
    // 恢复Chrome API
    Object.defineProperty(global, 'chrome', {
      value: {
        runtime: {
          id: 'test-extension-id'
        }
      },
      writable: true
    })
  })

  it('应该使用shadcn Alert组件显示同步状态', async () => {
    render(<TagsTab />)
    
    await waitFor(() => {
      expect(screen.getByTestId('tag-management-tab')).toBeInTheDocument()
    })
    
    // 检查功能说明Alert
    expect(screen.getByText('标签管理功能')).toBeInTheDocument()
    expect(screen.getByText(/在这里您可以查看、创建、编辑和删除标签/)).toBeInTheDocument()
    expect(screen.getByRole('button', { name: /手动同步/ })).toBeInTheDocument()
  })

  it('应该正确处理手动同步操作', async () => {
    render(<TagsTab />)
    
    await waitFor(() => {
      const syncButton = screen.getByRole('button', { name: /手动同步/ })
      fireEvent.click(syncButton)
    })
    
    expect(tagService.syncTagsFromBookmarks).toHaveBeenCalled()
  })

  it('应该使用shadcn Alert组件显示同步错误', async () => {
    const errorMessage = '同步失败'
    vi.mocked(tagService.syncTagsFromBookmarks).mockRejectedValue(new Error(errorMessage))
    
    render(<TagsTab />)
    
    await waitFor(() => {
      const syncButton = screen.getByRole('button', { name: /手动同步/ })
      fireEvent.click(syncButton)
    })
    
    await waitFor(() => {
      expect(screen.getByText('标签同步失败')).toBeInTheDocument()
      expect(screen.getByText(errorMessage)).toBeInTheDocument()
      expect(screen.getByRole('button', { name: /重试同步/ })).toBeInTheDocument()
    })
  })

  it('应该正确处理重试操作', async () => {
    // 先让初始化失败
    Object.defineProperty(global, 'chrome', {
      value: undefined,
      writable: true
    })
    
    render(<TagsTab />)
    
    await waitFor(() => {
      expect(screen.getByText('标签管理初始化失败')).toBeInTheDocument()
    })
    
    // 恢复Chrome API
    Object.defineProperty(global, 'chrome', {
      value: {
        runtime: {
          id: 'test-extension-id'
        }
      },
      writable: true
    })
    
    const retryButton = screen.getByRole('button', { name: /重试/ })
    fireEvent.click(retryButton)
    
    await waitFor(() => {
      expect(screen.getByTestId('tag-management-tab')).toBeInTheDocument()
    })
  })

  it('应该正确应用自定义className', async () => {
    const customClass = 'custom-tags-tab'
    const { container } = render(<TagsTab className={customClass} />)
    
    await waitFor(() => {
      expect(container.firstChild).toHaveClass(customClass)
    })
  })

  it('应该正确传递className给TagManagementTab', async () => {
    render(<TagsTab />)
    
    await waitFor(() => {
      const tagManagementTab = screen.getByTestId('tag-management-tab')
      expect(tagManagementTab).toHaveClass('border-t', 'border-gray-200')
    })
  })

  it('应该正确处理最大重试次数限制', async () => {
    // Mock Chrome API不可用
    Object.defineProperty(global, 'chrome', {
      value: undefined,
      writable: true
    })
    
    render(<TagsTab />)
    
    await waitFor(() => {
      expect(screen.getByText('标签管理初始化失败')).toBeInTheDocument()
    })
    
    // 连续点击重试按钮3次
    for (let i = 0; i < 3; i++) {
      const retryButton = screen.getByRole('button', { name: /重试/ })
      fireEvent.click(retryButton)
      
      await waitFor(() => {
        expect(screen.getByText('标签管理初始化失败')).toBeInTheDocument()
      })
    }
    
    // 第4次应该显示最大重试次数提示
    await waitFor(() => {
      expect(screen.getByText(/已达到最大重试次数/)).toBeInTheDocument()
      expect(screen.queryByRole('button', { name: /重试/ })).not.toBeInTheDocument()
    })
    
    // 恢复Chrome API
    Object.defineProperty(global, 'chrome', {
      value: {
        runtime: {
          id: 'test-extension-id'
        }
      },
      writable: true
    })
  })

  it('应该正确显示同步进行中状态', async () => {
    // Mock一个慢的同步操作
    let resolveSync: () => void
    const syncPromise = new Promise<void>((resolve) => {
      resolveSync = resolve
    })
    vi.mocked(tagService.syncTagsFromBookmarks).mockReturnValue(syncPromise)
    
    render(<TagsTab />)
    
    await waitFor(() => {
      const syncButton = screen.getByRole('button', { name: /手动同步/ })
      fireEvent.click(syncButton)
    })
    
    // 应该显示同步中状态
    expect(screen.getByText('正在同步标签数据...')).toBeInTheDocument()
    expect(screen.getByRole('button', { name: /同步中.../ })).toBeDisabled()
    
    // 完成同步
    resolveSync!()
    await waitFor(() => {
      expect(screen.queryByText('正在同步标签数据...')).not.toBeInTheDocument()
    })
  })
})