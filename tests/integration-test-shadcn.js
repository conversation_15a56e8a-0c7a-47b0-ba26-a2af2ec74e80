#!/usr/bin/env node

/**
 * DetailedBookmarkForm shadcn集成测试
 * 验证重构后的组件在构建产物中的正确性
 */

import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

console.log('🧪 开始 DetailedBookmarkForm shadcn 集成测试...\n')

// 测试配置
const tests = [
  {
    name: '构建产物中包含shadcn组件',
    test: () => {
      const distPath = path.join(__dirname, '..', 'dist')
      if (!fs.existsSync(distPath)) {
        throw new Error('dist目录不存在，请先运行 npm run build')
      }
      
      // 检查是否包含shadcn相关的CSS和JS
      const cssFiles = fs.readdirSync(path.join(distPath, 'assets'))
        .filter(file => file.endsWith('.css'))
      
      const jsFiles = fs.readdirSync(path.join(distPath, 'assets'))
        .filter(file => file.endsWith('.js'))
      
      if (cssFiles.length === 0) {
        throw new Error('未找到CSS文件')
      }
      
      if (jsFiles.length === 0) {
        throw new Error('未找到JS文件')
      }
      
      // 检查CSS文件中是否包含shadcn相关的类名
      const cssContent = fs.readFileSync(
        path.join(distPath, 'assets', cssFiles[0]), 
        'utf8'
      )
      
      const shadcnClasses = [
        'inline-flex', // Button基础类
        'ring-offset-background', // Focus ring
        'border-input', // Input边框
        'text-muted-foreground', // 文本颜色
        'bg-background', // 背景色
        'rounded-md' // 圆角
      ]
      
      const missingClasses = shadcnClasses.filter(cls => !cssContent.includes(cls))
      if (missingClasses.length > 0) {
        throw new Error(`CSS中缺少shadcn类名: ${missingClasses.join(', ')}`)
      }
      
      return true
    }
  },
  
  {
    name: '组件文件包含shadcn导入',
    test: () => {
      const componentPath = path.join(__dirname, '..', 'src', 'popup', 'components', 'DetailedBookmarkForm.tsx')
      const content = fs.readFileSync(componentPath, 'utf8')
      
      const requiredImports = [
        "from '@/components/ui/button'",
        "from '@/components/ui/input'",
        "from '@/components/ui/textarea'",
        "from '@/components/ui/label'",
        "from '@/components/ui/badge'",
        "from '@/components/ui/select'",
        "from '@/components/ui/form'"
      ]
      
      const missingImports = requiredImports.filter(imp => !content.includes(imp))
      if (missingImports.length > 0) {
        throw new Error(`缺少shadcn组件导入: ${missingImports.join(', ')}`)
      }
      
      return true
    }
  },
  
  {
    name: '组件使用react-hook-form',
    test: () => {
      const componentPath = path.join(__dirname, '..', 'src', 'popup', 'components', 'DetailedBookmarkForm.tsx')
      const content = fs.readFileSync(componentPath, 'utf8')
      
      const requiredPatterns = [
        'useForm<BookmarkFormData>',
        'form.control',
        'form.handleSubmit',
        'form.watch()',
        'FormField',
        'FormItem',
        'FormLabel',
        'FormControl',
        'FormMessage'
      ]
      
      const missingPatterns = requiredPatterns.filter(pattern => !content.includes(pattern))
      if (missingPatterns.length > 0) {
        throw new Error(`缺少react-hook-form使用模式: ${missingPatterns.join(', ')}`)
      }
      
      return true
    }
  },
  
  {
    name: '移除了自定义CSS类名',
    test: () => {
      const componentPath = path.join(__dirname, '..', 'src', 'popup', 'components', 'DetailedBookmarkForm.tsx')
      const content = fs.readFileSync(componentPath, 'utf8')
      
      // 检查是否还有旧的自定义类名
      const oldClasses = [
        'input w-full', // 旧的input类名
        'bg-primary-600', // 旧的背景色
        'text-primary-800', // 旧的文本色
        'tag-item', // 旧的标签类名
        'tags-container' // 旧的标签容器类名
      ]
      
      const foundOldClasses = oldClasses.filter(cls => content.includes(cls))
      if (foundOldClasses.length > 0) {
        throw new Error(`仍然包含旧的CSS类名: ${foundOldClasses.join(', ')}`)
      }
      
      return true
    }
  },
  
  {
    name: '包含所有必需的shadcn组件使用',
    test: () => {
      const componentPath = path.join(__dirname, '..', 'src', 'popup', 'components', 'DetailedBookmarkForm.tsx')
      const content = fs.readFileSync(componentPath, 'utf8')
      
      const requiredComponents = [
        '<Button', // Button组件
        '<Input', // Input组件
        '<Textarea', // Textarea组件
        '<Label', // Label组件
        '<Badge', // Badge组件
        '<Select', // Select组件
        '<SelectTrigger', // SelectTrigger组件
        '<SelectContent', // SelectContent组件
        '<SelectItem', // SelectItem组件
        '<Form ', // Form组件
        '<FormField', // FormField组件
        '<FormItem', // FormItem组件
        '<FormLabel', // FormLabel组件
        '<FormControl', // FormControl组件
        '<FormMessage' // FormMessage组件
      ]
      
      const missingComponents = requiredComponents.filter(comp => !content.includes(comp))
      if (missingComponents.length > 0) {
        throw new Error(`缺少shadcn组件使用: ${missingComponents.join(', ')}`)
      }
      
      return true
    }
  },
  
  {
    name: '测试文件存在且完整',
    test: () => {
      const testPath = path.join(__dirname, 'DetailedBookmarkForm.shadcn.test.tsx')
      if (!fs.existsSync(testPath)) {
        throw new Error('shadcn测试文件不存在')
      }
      
      const content = fs.readFileSync(testPath, 'utf8')
      
      // 检查测试覆盖的关键功能
      const requiredTests = [
        'shadcn组件渲染测试',
        'shadcn Form表单验证测试',
        'shadcn Select组件测试',
        'shadcn Badge组件标签功能测试',
        'shadcn Button组件交互测试',
        'AI助手功能测试',
        '初始数据填充测试',
        'shadcn主题样式测试'
      ]
      
      const missingTests = requiredTests.filter(test => !content.includes(test))
      if (missingTests.length > 0) {
        throw new Error(`缺少测试用例: ${missingTests.join(', ')}`)
      }
      
      return true
    }
  },
  
  {
    name: '文档完整性检查',
    test: () => {
      const docPath = path.join(__dirname, '..', 'docs', 'task-13-detailedbookmarkform-shadcn-refactor.md')
      if (!fs.existsSync(docPath)) {
        throw new Error('重构文档不存在')
      }
      
      const content = fs.readFileSync(docPath, 'utf8')
      
      const requiredSections = [
        '## 概述',
        '## 重构内容',
        '## 技术实现细节',
        '## 测试覆盖',
        '## 构建验证',
        '## 符合需求验证'
      ]
      
      const missingSections = requiredSections.filter(section => !content.includes(section))
      if (missingSections.length > 0) {
        throw new Error(`文档缺少章节: ${missingSections.join(', ')}`)
      }
      
      return true
    }
  }
]

// 运行测试
let passed = 0
let failed = 0

console.log(`运行 ${tests.length} 个集成测试用例...\n`)

for (const test of tests) {
  try {
    test.test()
    console.log(`✅ ${test.name}`)
    passed++
  } catch (error) {
    console.log(`❌ ${test.name}`)
    console.log(`   错误: ${error.message}`)
    failed++
  }
}

console.log('\n==================================================')
console.log(`集成测试结果: ${passed} 通过, ${failed} 失败`)

if (failed > 0) {
  console.log('\n❌ 集成测试失败，请检查上述错误并修复')
  process.exit(1)
} else {
  console.log('\n🎉 所有集成测试通过！DetailedBookmarkForm shadcn重构成功')
  console.log('\n📋 下一步建议:')
  console.log('1. 在Chrome中手动测试扩展功能')
  console.log('2. 验证用户界面和交互体验')
  console.log('3. 确认所有原有功能正常工作')
  console.log('4. 可以继续下一个组件的shadcn迁移')
}

process.exit(0)