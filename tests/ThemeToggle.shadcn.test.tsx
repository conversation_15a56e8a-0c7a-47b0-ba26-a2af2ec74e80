import React from 'react'
import { render, screen, fireEvent } from '@testing-library/react'
import { describe, it, expect, vi, beforeEach } from 'vitest'
import ThemeToggle from '../src/options/components/ThemeToggle'

// Mock useTheme hook
const mockSetTheme = vi.fn()
const mockUseTheme = {
  theme: 'light' as const,
  actualTheme: 'light' as const,
  setTheme: mockSetTheme
}

vi.mock('../src/options/hooks/useTheme', () => ({
  useTheme: () => mockUseTheme
}))

describe('ThemeToggle - shadcn重构测试', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('shadcn颜色系统使用验证', () => {
    it('应该使用shadcn的secondary背景色系统', () => {
      render(<ThemeToggle />)
      
      const button = screen.getByRole('button')
      
      // 验证使用了shadcn的secondary颜色系统
      expect(button).toHaveClass('bg-secondary')
      expect(button).toHaveClass('hover:bg-secondary/80')
      expect(button).toHaveClass('text-secondary-foreground')
    })

    it('应该使用shadcn的border颜色系统', () => {
      render(<ThemeToggle />)
      
      const button = screen.getByRole('button')
      
      // 验证使用了shadcn的border颜色系统
      expect(button).toHaveClass('border-border')
    })

    it('应该使用shadcn的focus ring系统', () => {
      render(<ThemeToggle />)
      
      const button = screen.getByRole('button')
      
      // 验证使用了shadcn的focus ring系统
      expect(button).toHaveClass('focus:ring-ring')
      expect(button).toHaveClass('focus:ring-2')
    })

    it('应该不再使用旧的gray颜色类', () => {
      render(<ThemeToggle />)
      
      const button = screen.getByRole('button')
      
      // 验证不再使用旧的gray颜色类
      expect(button).not.toHaveClass('bg-gray-100')
      expect(button).not.toHaveClass('hover:bg-gray-200')
      expect(button).not.toHaveClass('text-gray-700')
      expect(button).not.toHaveClass('border-gray-300')
      expect(button).not.toHaveClass('focus:ring-primary-500')
    })
  })

  describe('showLabel模式的shadcn样式验证', () => {
    it('应该在showLabel模式下使用shadcn颜色系统', () => {
      render(<ThemeToggle showLabel={true} />)
      
      const select = screen.getByRole('combobox')
      
      // 验证select使用了shadcn颜色系统
      expect(select).toHaveClass('bg-background')
      expect(select).toHaveClass('border-border')
      expect(select).toHaveClass('text-foreground')
      expect(select).toHaveClass('focus:ring-ring')
      expect(select).toHaveClass('hover:bg-accent')
      expect(select).toHaveClass('hover:text-accent-foreground')
    })

    it('应该在标签文本中使用shadcn文本颜色', () => {
      render(<ThemeToggle showLabel={true} />)
      
      const label = screen.getByText('主题:')
      
      // 验证标签使用了shadcn文本颜色
      expect(label).toHaveClass('text-foreground')
    })
  })

  describe('主题切换功能验证', () => {
    it('应该正确处理主题切换', () => {
      render(<ThemeToggle />)
      
      const button = screen.getByRole('button')
      fireEvent.click(button)
      
      // 验证调用了setTheme函数
      expect(mockSetTheme).toHaveBeenCalledWith('dark')
    })

    it('应该显示正确的无障碍标签', () => {
      render(<ThemeToggle />)
      
      const button = screen.getByRole('button')
      
      // 验证无障碍标签
      expect(button).toHaveAttribute('aria-label', '切换主题，当前: 浅色')
      expect(button).toHaveAttribute('title', '当前: 浅色 (浅色)')
    })
  })

  describe('尺寸配置验证', () => {
    it('应该正确应用小尺寸配置', () => {
      render(<ThemeToggle size="sm" />)
      
      const button = screen.getByRole('button')
      
      // 验证小尺寸类名
      expect(button).toHaveClass('w-8', 'h-8')
    })

    it('应该正确应用中等尺寸配置', () => {
      render(<ThemeToggle size="md" />)
      
      const button = screen.getByRole('button')
      
      // 验证中等尺寸类名
      expect(button).toHaveClass('w-10', 'h-10')
    })

    it('应该正确应用大尺寸配置', () => {
      render(<ThemeToggle size="lg" />)
      
      const button = screen.getByRole('button')
      
      // 验证大尺寸类名
      expect(button).toHaveClass('w-12', 'h-12')
    })
  })

  describe('性能优化验证', () => {
    it('应该正确渲染图标组件', () => {
      render(<ThemeToggle />)
      
      // 验证图标存在（通过SVG元素检查）
      const icon = document.querySelector('svg')
      expect(icon).toBeInTheDocument()
    })

    it('应该支持自定义className', () => {
      const customClass = 'custom-theme-toggle'
      render(<ThemeToggle className={customClass} />)
      
      const button = screen.getByRole('button')
      expect(button).toHaveClass(customClass)
    })
  })

  describe('不同主题状态验证', () => {
    it('应该在深色主题下显示正确的图标颜色', () => {
      // 模拟深色主题
      mockUseTheme.theme = 'dark'
      mockUseTheme.actualTheme = 'dark'
      
      render(<ThemeToggle />)
      
      const icon = document.querySelector('svg')
      expect(icon).toHaveClass('text-yellow-400')
    })

    it('应该在浅色主题下显示正确的图标颜色', () => {
      // 模拟浅色主题
      mockUseTheme.theme = 'light'
      mockUseTheme.actualTheme = 'light'
      
      render(<ThemeToggle />)
      
      const icon = document.querySelector('svg')
      expect(icon).toHaveClass('text-orange-500')
    })

    it('应该在系统主题下正确显示', () => {
      // 模拟系统主题
      mockUseTheme.theme = 'system'
      mockUseTheme.actualTheme = 'light'
      
      render(<ThemeToggle />)
      
      const button = screen.getByRole('button')
      expect(button).toHaveAttribute('aria-label', '切换主题，当前: 系统')
    })
  })

  describe('shadcn主题一致性验证', () => {
    it('应该与shadcn设计系统保持一致', () => {
      render(<ThemeToggle />)
      
      const button = screen.getByRole('button')
      
      // 验证使用了shadcn的标准类名模式
      expect(button.className).toMatch(/bg-secondary/)
      expect(button.className).toMatch(/text-secondary-foreground/)
      expect(button.className).toMatch(/border-border/)
      expect(button.className).toMatch(/focus:ring-ring/)
      
      // 验证使用了shadcn的标准过渡效果
      expect(button).toHaveClass('transition-all', 'duration-200', 'ease-in-out')
    })

    it('应该支持shadcn的group hover效果', () => {
      render(<ThemeToggle />)
      
      const button = screen.getByRole('button')
      const icon = document.querySelector('svg')
      
      // 验证group类和hover效果
      expect(button).toHaveClass('group')
      expect(icon).toHaveClass('group-hover:scale-110')
    })
  })
})