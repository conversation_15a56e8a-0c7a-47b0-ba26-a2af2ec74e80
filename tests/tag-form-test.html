<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>标签表单测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #333;
        }
        input[type="text"] {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 6px;
            font-size: 16px;
            transition: border-color 0.3s;
        }
        input[type="text"]:focus {
            outline: none;
            border-color: #3B82F6;
        }
        .color-picker {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            margin-top: 10px;
        }
        .color-option {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            border: 3px solid transparent;
            cursor: pointer;
            transition: all 0.3s;
        }
        .color-option:hover {
            transform: scale(1.1);
        }
        .color-option.selected {
            border-color: #333;
            transform: scale(1.2);
        }
        .buttons {
            display: flex;
            gap: 15px;
            justify-content: flex-end;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #eee;
        }
        button {
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s;
        }
        .btn-cancel {
            background: #f3f4f6;
            color: #374151;
        }
        .btn-cancel:hover {
            background: #e5e7eb;
        }
        .btn-submit {
            background: #3B82F6;
            color: white;
        }
        .btn-submit:hover:not(:disabled) {
            background: #2563eb;
        }
        .btn-submit:disabled {
            background: #9ca3af;
            cursor: not-allowed;
        }
        .preview {
            margin-top: 20px;
            padding: 15px;
            background: #f9fafb;
            border-radius: 6px;
        }
        .tag-preview {
            display: inline-block;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 500;
        }
        .error {
            color: #ef4444;
            font-size: 14px;
            margin-top: 5px;
        }
        .success {
            color: #10b981;
            font-size: 14px;
            margin-top: 10px;
            padding: 10px;
            background: #ecfdf5;
            border-radius: 6px;
        }
        .debug {
            margin-top: 20px;
            padding: 15px;
            background: #f3f4f6;
            border-radius: 6px;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>标签创建测试</h1>
        <p>测试标签表单的创建功能</p>
        
        <form id="tagForm">
            <div class="form-group">
                <label for="tagName">标签名称 *</label>
                <input type="text" id="tagName" placeholder="输入标签名称" maxlength="50">
                <div id="nameError" class="error" style="display: none;"></div>
            </div>
            
            <div class="form-group">
                <label>选择颜色</label>
                <div class="color-picker">
                    <div class="color-option" data-color="#3B82F6" style="background-color: #3B82F6;"></div>
                    <div class="color-option" data-color="#10B981" style="background-color: #10B981;"></div>
                    <div class="color-option" data-color="#F59E0B" style="background-color: #F59E0B;"></div>
                    <div class="color-option" data-color="#EF4444" style="background-color: #EF4444;"></div>
                    <div class="color-option" data-color="#8B5CF6" style="background-color: #8B5CF6;"></div>
                    <div class="color-option" data-color="#06B6D4" style="background-color: #06B6D4;"></div>
                </div>
            </div>
            
            <div class="form-group">
                <label>预览</label>
                <div class="preview">
                    <span id="tagPreview" class="tag-preview" style="background-color: #3B82F6; color: white;">
                        标签名称
                    </span>
                </div>
            </div>
            
            <div class="buttons">
                <button type="button" class="btn-cancel" onclick="resetForm()">取消</button>
                <button type="submit" class="btn-submit" id="submitBtn" disabled>创建标签</button>
            </div>
        </form>
        
        <div id="result" style="display: none;"></div>
        
        <div class="debug">
            <h3>调试信息</h3>
            <div id="debugInfo">等待输入...</div>
        </div>
    </div>

    <script>
        // 模拟标签工具类
        const TagUtils = {
            validateTagName: function(name) {
                const errors = [];
                
                if (!name || name.trim().length === 0) {
                    errors.push({
                        field: 'name',
                        message: '标签名称不能为空',
                        code: 'NAME_REQUIRED'
                    });
                } else {
                    const trimmedName = name.trim();
                    if (trimmedName.length > 50) {
                        errors.push({
                            field: 'name',
                            message: '标签名称长度不能超过50个字符',
                            code: 'NAME_TOO_LONG'
                        });
                    }
                    
                    const invalidChars = /[<>"'&]/;
                    if (invalidChars.test(trimmedName)) {
                        errors.push({
                            field: 'name',
                            message: '标签名称不能包含特殊字符 < > " \' &',
                            code: 'INVALID_CHARACTERS'
                        });
                    }
                }
                
                return {
                    isValid: errors.length === 0,
                    errors: errors
                };
            },
            
            normalizeTagName: function(name) {
                if (!name) return '';
                return name.trim().replace(/\s+/g, ' ');
            }
        };
        
        // 模拟颜色工具类
        const ColorUtils = {
            generateColorFromString: function(str) {
                const colors = ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6', '#06B6D4'];
                let hash = 0;
                for (let i = 0; i < str.length; i++) {
                    hash = ((hash << 5) - hash + str.charCodeAt(i)) & 0xffffffff;
                }
                return colors[Math.abs(hash) % colors.length];
            },
            
            getContrastColor: function(backgroundColor) {
                // 简单的对比色计算
                const hex = backgroundColor.replace('#', '');
                const r = parseInt(hex.substr(0, 2), 16);
                const g = parseInt(hex.substr(2, 2), 16);
                const b = parseInt(hex.substr(4, 2), 16);
                const luminance = (0.299 * r + 0.587 * g + 0.114 * b) / 255;
                return luminance > 0.5 ? '#000000' : '#ffffff';
            }
        };
        
        // 表单状态
        let formData = {
            name: '',
            color: '#3B82F6'
        };
        
        let errors = {};
        let isValidating = false;
        let existingTags = ['技术', '学习', '工作']; // 模拟现有标签
        
        // DOM 元素
        const nameInput = document.getElementById('tagName');
        const submitBtn = document.getElementById('submitBtn');
        const nameError = document.getElementById('nameError');
        const tagPreview = document.getElementById('tagPreview');
        const debugInfo = document.getElementById('debugInfo');
        const result = document.getElementById('result');
        const colorOptions = document.querySelectorAll('.color-option');
        
        // 事件监听
        nameInput.addEventListener('input', handleNameChange);
        nameInput.addEventListener('blur', handleNameBlur);
        
        colorOptions.forEach(option => {
            option.addEventListener('click', function() {
                const color = this.dataset.color;
                selectColor(color);
            });
        });
        
        document.getElementById('tagForm').addEventListener('submit', handleSubmit);
        
        // 初始化
        selectColor('#3B82F6');
        updateDebugInfo();
        
        function handleNameChange(e) {
            formData.name = e.target.value;
            
            // 清除错误
            if (errors.name) {
                delete errors.name;
                showError('');
            }
            
            // 实时验证（防抖）
            clearTimeout(window.validationTimeout);
            isValidating = true;
            updateDebugInfo();
            
            window.validationTimeout = setTimeout(() => {
                validateForm();
                isValidating = false;
                updateDebugInfo();
                updateSubmitButton();
            }, 300);
            
            updatePreview();
            updateSubmitButton();
        }
        
        function handleNameBlur() {
            if (formData.name.trim()) {
                validateForm();
                updateSubmitButton();
            }
        }
        
        function validateForm() {
            const newErrors = {};
            
            // 验证名称
            if (!formData.name.trim()) {
                newErrors.name = '标签名称不能为空';
            } else {
                const nameValidation = TagUtils.validateTagName(formData.name.trim());
                if (!nameValidation || !nameValidation.isValid) {
                    newErrors.name = nameValidation?.errors?.[0]?.message || '标签名称格式无效';
                } else {
                    // 检查重复
                    const normalizedName = TagUtils.normalizeTagName(formData.name.trim()).toLowerCase();
                    const isDuplicate = existingTags.some(tag => 
                        TagUtils.normalizeTagName(tag).toLowerCase() === normalizedName
                    );
                    
                    if (isDuplicate) {
                        newErrors.name = '标签名称已存在，请使用其他名称';
                    }
                }
            }
            
            errors = newErrors;
            showError(errors.name || '');
            updateDebugInfo();
            
            return Object.keys(errors).length === 0;
        }
        
        function selectColor(color) {
            formData.color = color;
            
            // 更新选中状态
            colorOptions.forEach(option => {
                option.classList.remove('selected');
                if (option.dataset.color === color) {
                    option.classList.add('selected');
                }
            });
            
            updatePreview();
            updateDebugInfo();
        }
        
        function updatePreview() {
            const name = formData.name || '标签名称';
            const color = formData.color || ColorUtils.generateColorFromString(name);
            const textColor = ColorUtils.getContrastColor(color);
            
            tagPreview.textContent = name;
            tagPreview.style.backgroundColor = color;
            tagPreview.style.color = textColor;
        }
        
        function updateSubmitButton() {
            const hasName = formData.name.trim().length > 0;
            const hasFieldErrors = Object.keys(errors).length > 0;
            const isCurrentlyValidating = isValidating;
            
            const isValid = hasName && !hasFieldErrors && !isCurrentlyValidating;
            
            submitBtn.disabled = !isValid;
            submitBtn.textContent = isCurrentlyValidating ? '验证中...' : '创建标签';
        }
        
        function showError(message) {
            if (message) {
                nameError.textContent = message;
                nameError.style.display = 'block';
                nameInput.style.borderColor = '#ef4444';
            } else {
                nameError.style.display = 'none';
                nameInput.style.borderColor = '#ddd';
            }
        }
        
        function updateDebugInfo() {
            const info = {
                formData: formData,
                errors: errors,
                isValidating: isValidating,
                hasName: formData.name.trim().length > 0,
                hasFieldErrors: Object.keys(errors).length > 0,
                isFormValid: formData.name.trim().length > 0 && Object.keys(errors).length === 0 && !isValidating
            };
            
            debugInfo.innerHTML = '<pre>' + JSON.stringify(info, null, 2) + '</pre>';
        }
        
        function handleSubmit(e) {
            e.preventDefault();
            
            if (submitBtn.disabled) {
                return;
            }
            
            // 最终验证
            if (!validateForm()) {
                return;
            }
            
            // 模拟提交
            submitBtn.disabled = true;
            submitBtn.textContent = '创建中...';
            
            setTimeout(() => {
                const submitData = {
                    name: TagUtils.normalizeTagName(formData.name.trim()),
                    color: formData.color || ColorUtils.generateColorFromString(formData.name.trim())
                };
                
                result.innerHTML = '<div class="success">✅ 标签创建成功！<br>数据: ' + JSON.stringify(submitData, null, 2) + '</div>';
                result.style.display = 'block';
                
                // 添加到现有标签列表
                existingTags.push(submitData.name);
                
                // 重置表单
                setTimeout(() => {
                    resetForm();
                }, 2000);
                
            }, 1000);
        }
        
        function resetForm() {
            formData = { name: '', color: '#3B82F6' };
            errors = {};
            isValidating = false;
            
            nameInput.value = '';
            selectColor('#3B82F6');
            showError('');
            updatePreview();
            updateSubmitButton();
            updateDebugInfo();
            
            result.style.display = 'none';
        }
    </script>
</body>
</html>