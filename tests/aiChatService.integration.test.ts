// AI聊天服务集成测试 - 测试与默认AI模型服务的集成

import { describe, it, expect, beforeEach, vi } from 'vitest'
import { aiChatService } from '../src/services/aiChatService'
import { ChromeStorageService } from '../src/utils/chromeStorage'

// Mock Chrome Storage Service
vi.mock('../src/utils/chromeStorage', () => ({
  ChromeStorageService: {
    getSyncSetting: vi.fn(),
    saveSyncSetting: vi.fn(),
    getLocalSetting: vi.fn(),
    saveLocalSetting: vi.fn(),
  }
}))

// Mock Default AI Model API
vi.mock('../src/services/defaultAIModelAPI', () => ({
  DefaultAIModelAPI: {
    getDefaultChatModel: vi.fn().mockResolvedValue({
      id: 'test-provider_test-model',
      name: 'test-model',
      displayName: 'Test Model',
      provider: 'Test Provider',
      providerId: 'test-provider',
      modelType: 'chat',
      enabled: true,
      status: 'connected'
    })
  }
}))

// Mock AI Integration Service
vi.mock('../src/services/aiIntegrationService', () => ({
  aiIntegrationService: {
    getConfiguredProviders: vi.fn().mockResolvedValue([
      {
        id: 'test-provider',
        name: 'Test Provider',
        type: 'openai',
        enabled: true,
        baseUrl: 'https://api.test.com',
        apiKey: 'test-key'
      }
    ]),
    getAvailableModels: vi.fn().mockResolvedValue([
      {
        id: 'test-model',
        name: 'test-model',
        displayName: 'Test Model',
        isRecommended: true,
        isPopular: true
      }
    ])
  }
}))

describe('AIChatService Integration', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('与默认AI模型服务集成', () => {
    it('应该能够从默认AI模型服务获取聊天模型', async () => {
      // Mock存储返回空值，强制使用新的默认AI模型服务
      vi.mocked(ChromeStorageService.getSyncSetting).mockResolvedValue(null)
      vi.mocked(ChromeStorageService.saveSyncSetting).mockResolvedValue()
      vi.mocked(ChromeStorageService.getLocalSetting).mockResolvedValue([])
      vi.mocked(ChromeStorageService.saveLocalSetting).mockResolvedValue()

      const request = {
        prompt: '请为这个网站生成一个描述',
        generationType: 'description' as const,
        maxLength: 200
      }

      const response = await aiChatService.generateText(request)

      expect(response).toBeDefined()
      expect(response.content).toBeTruthy()
      expect(response.metadata?.model).toBe('test-model')
      expect(response.metadata?.provider).toBe('Test Provider')
    })

    it('应该在没有配置默认模型时提供有用的错误信息', async () => {
      // Mock默认AI模型服务返回null
      const mockDefaultAIModelAPI = await import('../src/services/defaultAIModelAPI')
      vi.mocked(mockDefaultAIModelAPI.DefaultAIModelAPI.getDefaultChatModel).mockResolvedValue(null)
      
      // Mock存储也返回null
      vi.mocked(ChromeStorageService.getSyncSetting).mockResolvedValue(null)
      
      // Mock没有可用的提供商
      const mockAIIntegrationService = await import('../src/services/aiIntegrationService')
      vi.mocked(mockAIIntegrationService.aiIntegrationService.getConfiguredProviders).mockResolvedValue([])

      const request = {
        prompt: '测试提示',
        generationType: 'description' as const
      }

      await expect(aiChatService.generateText(request))
        .rejects.toThrow('未找到可用的AI模型，请先在"默认AI模型"页面配置模型')
    })

    it('应该能够处理不同类型的文本生成请求', async () => {
      vi.mocked(ChromeStorageService.getSyncSetting).mockResolvedValue(null)
      vi.mocked(ChromeStorageService.saveSyncSetting).mockResolvedValue()
      vi.mocked(ChromeStorageService.getLocalSetting).mockResolvedValue([])
      vi.mocked(ChromeStorageService.saveLocalSetting).mockResolvedValue()

      const testCases = [
        { generationType: 'description' as const, prompt: '生成描述' },
        { generationType: 'summary' as const, prompt: '生成摘要' },
        { generationType: 'tags' as const, prompt: '生成标签' },
        { generationType: 'title' as const, prompt: '生成标题' },
        { generationType: 'notes' as const, prompt: '生成笔记' }
      ]

      for (const testCase of testCases) {
        const response = await aiChatService.generateText(testCase)
        
        expect(response).toBeDefined()
        expect(response.content).toBeTruthy()
        expect(response.metadata?.model).toBe('test-model')
        expect(response.metadata?.provider).toBe('Test Provider')
        expect(response.metadata?.timestamp).toBeTruthy()
      }
    })

    it('应该能够设置和获取默认模型（向后兼容）', async () => {
      vi.mocked(ChromeStorageService.saveSyncSetting).mockResolvedValue()
      vi.mocked(ChromeStorageService.getSyncSetting).mockResolvedValue({
        providerId: 'test-provider',
        modelId: 'test-model'
      })

      await aiChatService.setDefaultModel('test-provider', 'test-model')

      expect(ChromeStorageService.saveSyncSetting).toHaveBeenCalledWith(
        'ai_default_model',
        {
          providerId: 'test-provider',
          modelId: 'test-model'
        }
      )
    })

    it('应该能够处理模型ID解析错误', async () => {
      // Mock返回无效格式的模型ID
      const mockDefaultAIModelAPI = await import('../src/services/defaultAIModelAPI')
      vi.mocked(mockDefaultAIModelAPI.DefaultAIModelAPI.getDefaultChatModel).mockResolvedValue({
        id: 'invalid-format', // 没有下划线分隔符
        name: 'test-model',
        displayName: 'Test Model',
        provider: 'Test Provider',
        providerId: 'test-provider',
        modelType: 'chat',
        enabled: true,
        status: 'connected'
      })

      // Mock存储返回null，强制使用备用逻辑
      vi.mocked(ChromeStorageService.getSyncSetting).mockResolvedValue(null)

      const request = {
        prompt: '测试提示',
        generationType: 'description' as const
      }

      // 应该回退到自动发现模型
      const response = await aiChatService.generateText(request)
      expect(response).toBeDefined()
    })
  })

  describe('聊天历史管理', () => {
    it('应该能够保存和获取聊天历史', async () => {
      const mockHistory = [
        { role: 'user' as const, content: '测试消息1', timestamp: '2023-01-01T00:00:00Z' },
        { role: 'assistant' as const, content: '测试回复1', timestamp: '2023-01-01T00:00:01Z' }
      ]

      vi.mocked(ChromeStorageService.getLocalSetting).mockResolvedValue(mockHistory)
      vi.mocked(ChromeStorageService.saveLocalSetting).mockResolvedValue()

      // 测试获取历史
      const history = await aiChatService.getChatHistory()
      expect(history).toEqual(mockHistory)

      // 测试保存历史
      const newMessages = [
        { role: 'user' as const, content: '测试消息2', timestamp: '2023-01-01T00:00:02Z' }
      ]
      
      await aiChatService.saveChatHistory(newMessages)
      
      expect(ChromeStorageService.saveLocalSetting).toHaveBeenCalledWith(
        'ai_chat_history',
        [...mockHistory, ...newMessages]
      )
    })

    it('应该能够清除聊天历史', async () => {
      vi.mocked(ChromeStorageService.saveLocalSetting).mockResolvedValue()

      await aiChatService.clearChatHistory()

      expect(ChromeStorageService.saveLocalSetting).toHaveBeenCalledWith(
        'ai_chat_history',
        []
      )
    })
  })
})