// AI推荐功能修复测试

import { aiProviderService } from '../src/services/aiProviderService'
import { aiChatService } from '../src/services/aiChatService'
import { aiRecommendationService } from '../src/services/aiRecommendationService'

describe('AI推荐功能修复测试', () => {
  
  test('aiProviderService应该有generateText方法', () => {
    expect(typeof aiProviderService.generateText).toBe('function')
  })

  test('aiChatService应该能正确调用aiProviderService', async () => {
    // 模拟一个简单的请求
    const mockRequest = {
      prompt: '测试提示词',
      generationType: 'tags' as const,
      maxLength: 100
    }

    // 这个测试主要验证方法存在且不会立即抛出错误
    try {
      await aiChatService.generateText(mockRequest)
    } catch (error) {
      // 预期会有错误，因为没有配置真实的AI提供商
      expect(error).toBeDefined()
    }
  })

  test('aiRecommendationService应该能处理标签推荐', async () => {
    const mockRequest = {
      title: '测试标题',
      content: '测试内容',
      maxRecommendations: 5
    }

    try {
      const result = await aiRecommendationService.recommendTags(mockRequest)
      expect(result).toHaveProperty('existingTags')
      expect(result).toHaveProperty('newTags')
      expect(result).toHaveProperty('confidence')
    } catch (error) {
      // 如果AI服务不可用，应该有降级策略
      console.log('AI服务不可用，使用降级策略:', error.message)
    }
  })

  test('aiRecommendationService应该能处理文件夹推荐', async () => {
    const mockRequest = {
      title: '测试标题',
      content: '测试内容',
      maxRecommendations: 3
    }

    try {
      const result = await aiRecommendationService.recommendFolders(mockRequest)
      expect(result).toHaveProperty('recommendedFolders')
      expect(Array.isArray(result.recommendedFolders)).toBe(true)
    } catch (error) {
      // 如果AI服务不可用，应该有降级策略
      console.log('AI服务不可用，使用降级策略:', error.message)
    }
  })

  test('aiRecommendationService应该能处理批量推荐', async () => {
    const mockRequest = {
      title: '测试标题',
      content: '测试内容',
      maxRecommendations: 5
    }

    try {
      const result = await aiRecommendationService.recommendBoth(mockRequest)
      expect(result).toHaveProperty('tags')
      expect(result).toHaveProperty('folders')
      expect(result.tags).toHaveProperty('existingTags')
      expect(result.folders).toHaveProperty('recommendedFolders')
    } catch (error) {
      // 如果AI服务不可用，应该有降级策略
      console.log('AI服务不可用，使用降级策略:', error.message)
    }
  })
})