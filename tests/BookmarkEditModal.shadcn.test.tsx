// BookmarkEditModal shadcn组件测试

import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { describe, it, expect, vi, beforeEach } from 'vitest'
import BookmarkEditModal from '../src/components/BookmarkEditModal'

// 模拟shadcn组件
vi.mock('@/components/ui/dialog', () => ({
  Dialog: ({ children, open }: any) => open ? <div data-testid="dialog">{children}</div> : null,
  DialogContent: ({ children }: any) => <div data-testid="dialog-content">{children}</div>,
  DialogHeader: ({ children }: any) => <div data-testid="dialog-header">{children}</div>,
  DialogTitle: ({ children }: any) => <h2 data-testid="dialog-title">{children}</h2>,
  DialogDescription: ({ children }: any) => <p data-testid="dialog-description">{children}</p>,
  DialogFooter: ({ children }: any) => <div data-testid="dialog-footer">{children}</div>,
}))

vi.mock('@/components/ui/form', () => ({
  Form: ({ children }: any) => <div data-testid="form">{children}</div>,
  FormField: ({ render }: any) => render({ field: { value: '', onChange: vi.fn() } }),
  FormItem: ({ children }: any) => <div data-testid="form-item">{children}</div>,
  FormLabel: ({ children }: any) => <label data-testid="form-label">{children}</label>,
  FormControl: ({ children }: any) => <div data-testid="form-control">{children}</div>,
  FormMessage: () => <div data-testid="form-message"></div>,
}))

vi.mock('@/components/ui/input', () => ({
  Input: (props: any) => <input data-testid="input" {...props} />
}))

vi.mock('@/components/ui/textarea', () => ({
  Textarea: (props: any) => <textarea data-testid="textarea" {...props} />
}))

vi.mock('@/components/ui/button', () => ({
  Button: ({ children, ...props }: any) => <button data-testid="button" {...props}>{children}</button>
}))

vi.mock('@/components/ui/select', () => ({
  Select: ({ children, onValueChange, value }: any) => (
    <div data-testid="select">
      <select onChange={(e) => onValueChange?.(e.target.value)} value={value}>
        {children}
      </select>
    </div>
  ),
  SelectContent: ({ children }: any) => <div>{children}</div>,
  SelectItem: ({ children, value }: any) => <option value={value}>{children}</option>,
  SelectTrigger: ({ children }: any) => <div data-testid="select-trigger">{children}</div>,
  SelectValue: ({ placeholder }: any) => <span>{placeholder}</span>,
}))

vi.mock('@/components/ui/badge', () => ({
  Badge: ({ children, ...props }: any) => <span data-testid="badge" {...props}>{children}</span>
}))

vi.mock('react-hook-form', () => ({
  useForm: () => ({
    control: {},
    handleSubmit: (fn: any) => (e: any) => {
      e.preventDefault()
      fn({
        title: '测试标题',
        url: 'https://example.com',
        description: '测试描述',
        category: '工作',
        tags: ['测试标签']
      })
    },
    watch: (field: string) => {
      if (field === 'tags') return ['测试标签']
      return ''
    },
    setValue: vi.fn(),
    getValues: (field?: string) => {
      if (field === 'tags') return ['测试标签']
      return field ? '' : {
        title: '测试标题',
        url: 'https://example.com',
        description: '测试描述',
        category: '工作',
        tags: ['测试标签']
      }
    },
    reset: vi.fn(),
  })
}))

describe('BookmarkEditModal shadcn组件测试', () => {
  const mockBookmark = {
    id: '1',
    title: '测试收藏',
    url: 'https://example.com',
    description: '测试描述',
    category: '工作',
    tags: ['测试', '标签']
  }

  const defaultProps = {
    bookmark: mockBookmark,
    isOpen: true,
    onSave: vi.fn(),
    onCancel: vi.fn(),
    loading: false
  }

  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('应该正确渲染shadcn Dialog组件', () => {
    render(<BookmarkEditModal {...defaultProps} />)
    
    expect(screen.getByTestId('dialog')).toBeInTheDocument()
    expect(screen.getByTestId('dialog-content')).toBeInTheDocument()
    expect(screen.getByTestId('dialog-header')).toBeInTheDocument()
    expect(screen.getByTestId('dialog-title')).toBeInTheDocument()
    expect(screen.getByTestId('dialog-footer')).toBeInTheDocument()
  })

  it('应该显示正确的标题和描述', () => {
    render(<BookmarkEditModal {...defaultProps} />)
    
    expect(screen.getByTestId('dialog-title')).toHaveTextContent('编辑收藏')
    expect(screen.getByTestId('dialog-description')).toHaveTextContent('编辑收藏项的详细信息')
  })

  it('应该正确渲染shadcn Form组件', () => {
    render(<BookmarkEditModal {...defaultProps} />)
    
    expect(screen.getByTestId('form')).toBeInTheDocument()
    expect(screen.getAllByTestId('form-item')).toHaveLength(5) // 标题、URL、描述、分类、标签
    expect(screen.getAllByTestId('form-label')).toHaveLength(5) // 标题、URL、描述、分类、标签都有标签
  })

  it('应该正确渲染shadcn Input组件', () => {
    render(<BookmarkEditModal {...defaultProps} />)
    
    const inputs = screen.getAllByTestId('input')
    expect(inputs.length).toBeGreaterThanOrEqual(2) // 至少有标题和URL输入框
  })

  it('应该正确渲染shadcn Textarea组件', () => {
    render(<BookmarkEditModal {...defaultProps} />)
    
    expect(screen.getByTestId('textarea')).toBeInTheDocument()
  })

  it('应该正确渲染shadcn Select组件', () => {
    render(<BookmarkEditModal {...defaultProps} />)
    
    expect(screen.getByTestId('select')).toBeInTheDocument()
    expect(screen.getByTestId('select-trigger')).toBeInTheDocument()
  })

  it('应该正确渲染shadcn Button组件', () => {
    render(<BookmarkEditModal {...defaultProps} />)
    
    const buttons = screen.getAllByTestId('button')
    expect(buttons.length).toBeGreaterThanOrEqual(2) // 至少有取消和保存按钮
  })

  it('应该正确渲染shadcn Badge组件用于标签显示', () => {
    render(<BookmarkEditModal {...defaultProps} />)
    
    // 检查标签显示（使用具体的标签测试ID）
    expect(screen.getByTestId('tag-测试标签')).toBeInTheDocument()
  })

  it('应该在模态窗口关闭时不渲染内容', () => {
    render(<BookmarkEditModal {...defaultProps} isOpen={false} />)
    
    expect(screen.queryByTestId('dialog')).not.toBeInTheDocument()
  })

  it('应该在加载状态下禁用表单元素', () => {
    render(<BookmarkEditModal {...defaultProps} loading={true} />)
    
    const inputs = screen.getAllByTestId('input')
    inputs.forEach(input => {
      expect(input).toBeDisabled()
    })
    
    expect(screen.getByTestId('textarea')).toBeDisabled()
    
    const buttons = screen.getAllByTestId('button')
    buttons.forEach(button => {
      expect(button).toBeDisabled()
    })
  })

  it('应该正确处理表单提交', async () => {
    const mockOnSave = vi.fn().mockResolvedValue(undefined)
    render(<BookmarkEditModal {...defaultProps} onSave={mockOnSave} />)
    
    const form = screen.getByTestId('form')
    fireEvent.submit(form)
    
    await waitFor(() => {
      expect(mockOnSave).toHaveBeenCalledWith({
        id: '1',
        title: '测试标题',
        url: 'https://example.com',
        description: '测试描述',
        category: '工作',
        tags: ['测试标签']
      })
    })
  })

  it('应该正确处理取消操作', () => {
    const mockOnCancel = vi.fn()
    render(<BookmarkEditModal {...defaultProps} onCancel={mockOnCancel} />)
    
    const cancelButton = screen.getAllByTestId('button').find(btn => 
      btn.textContent?.includes('取消')
    )
    
    if (cancelButton) {
      fireEvent.click(cancelButton)
      expect(mockOnCancel).toHaveBeenCalled()
    }
  })

  it('应该显示加载状态', () => {
    render(<BookmarkEditModal {...defaultProps} loading={true} />)
    
    const saveButton = screen.getAllByTestId('button').find(btn => 
      btn.textContent?.includes('保存中')
    )
    
    expect(saveButton).toBeInTheDocument()
  })

  it('应该正确处理标签操作', () => {
    render(<BookmarkEditModal {...defaultProps} />)
    
    // 检查标签容器存在
    expect(screen.getByTestId('tags-container')).toBeInTheDocument()
    
    // 检查现有标签显示（使用具体的标签测试ID）
    expect(screen.getByTestId('tag-测试标签')).toBeInTheDocument()
  })

  it('应该包含所有必需的表单字段', () => {
    render(<BookmarkEditModal {...defaultProps} />)
    
    // 检查标题字段
    expect(screen.getByText('标题 *')).toBeInTheDocument()
    
    // 检查URL字段
    expect(screen.getByText('网址 *')).toBeInTheDocument()
    
    // 检查描述字段
    expect(screen.getByText('描述')).toBeInTheDocument()
    
    // 检查分类字段
    expect(screen.getByText('分类 *')).toBeInTheDocument()
    
    // 检查标签字段
    expect(screen.getByText('标签')).toBeInTheDocument()
  })

  it('应该正确显示必填字段标识', () => {
    render(<BookmarkEditModal {...defaultProps} />)
    
    // 检查必填字段标识
    expect(screen.getByText('标题 *')).toBeInTheDocument()
    expect(screen.getByText('网址 *')).toBeInTheDocument()
    expect(screen.getByText('分类 *')).toBeInTheDocument()
  })
})