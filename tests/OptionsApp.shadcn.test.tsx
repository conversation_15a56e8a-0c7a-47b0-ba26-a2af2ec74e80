import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { describe, it, expect, vi, beforeEach } from 'vitest'
import OptionsApp from '../src/options/OptionsApp'

// Mock Chrome API
const mockChrome = {
  runtime: {
    sendMessage: vi.fn(),
    onMessage: {
      addListener: vi.fn(),
      removeListener: vi.fn()
    }
  },
  storage: {
    local: {
      get: vi.fn(),
      set: vi.fn()
    }
  }
}

// 设置全局Chrome对象
Object.defineProperty(global, 'chrome', {
  value: mockChrome,
  writable: true
})

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn()
}
Object.defineProperty(global, 'localStorage', {
  value: localStorageMock,
  writable: true
})

// Mock window.location
Object.defineProperty(window, 'location', {
  value: {
    hash: '',
    search: ''
  },
  writable: true
})

// Mock window.history
Object.defineProperty(window, 'history', {
  value: {
    replaceState: vi.fn()
  },
  writable: true
})

// Mock window.matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: vi.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: vi.fn(), // deprecated
    removeListener: vi.fn(), // deprecated
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn(),
  })),
})

describe('OptionsApp shadcn重构测试', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    // 重置Chrome API mock
    mockChrome.runtime.sendMessage.mockResolvedValue({
      success: true,
      data: []
    })
  })

  it('应该使用shadcn Card组件作为主容器', async () => {
    render(<OptionsApp />)
    
    // 等待组件加载完成
    await waitFor(() => {
      expect(screen.queryByText('正在初始化收藏管理页面...')).not.toBeInTheDocument()
    })

    // 验证使用了shadcn Card组件的样式类
    const mainContainer = document.querySelector('.min-h-screen.bg-background')
    expect(mainContainer).toBeInTheDocument()
    
    // 验证头部使用了Card组件
    const headerCard = document.querySelector('.rounded-none.border-x-0.border-t-0')
    expect(headerCard).toBeInTheDocument()
  })

  it('应该使用shadcn颜色系统替换gray背景色', async () => {
    render(<OptionsApp />)
    
    await waitFor(() => {
      expect(screen.queryByText('正在初始化收藏管理页面...')).not.toBeInTheDocument()
    })

    // 验证主背景使用了shadcn的background颜色
    const mainContainer = document.querySelector('.bg-background')
    expect(mainContainer).toBeInTheDocument()
    
    // 验证不再使用gray-50背景色
    const grayBackground = document.querySelector('.bg-gray-50')
    expect(grayBackground).not.toBeInTheDocument()
  })

  it('应该使用shadcn文本样式系统', async () => {
    render(<OptionsApp />)
    
    await waitFor(() => {
      expect(screen.queryByText('正在初始化收藏管理页面...')).not.toBeInTheDocument()
    })

    // 验证标题使用了shadcn的foreground颜色
    const title = screen.getByText('Universe Bag')
    expect(title).toHaveClass('text-foreground')
    
    // 验证副标题使用了shadcn的muted-foreground颜色
    const subtitle = screen.getByText('智能收藏管理工具')
    expect(subtitle).toHaveClass('text-muted-foreground')
  })

  it('应该使用shadcn Button组件替换自定义按钮', async () => {
    render(<OptionsApp />)
    
    await waitFor(() => {
      expect(screen.queryByText('正在初始化收藏管理页面...')).not.toBeInTheDocument()
    })

    // 验证AI辅助按钮使用了shadcn Button组件
    const aiButton = screen.getByRole('button', { name: /AI 辅助/i })
    expect(aiButton).toBeInTheDocument()
    
    // 验证导航按钮使用了shadcn Button组件
    const bookmarkButton = screen.getByRole('button', { name: /收藏管理/i })
    expect(bookmarkButton).toBeInTheDocument()
  })

  it('应该使用shadcn边框和阴影系统', async () => {
    render(<OptionsApp />)
    
    await waitFor(() => {
      expect(screen.queryByText('正在初始化收藏管理页面...')).not.toBeInTheDocument()
    })

    // 验证分割线使用了shadcn的border颜色
    const divider = document.querySelector('.border-border')
    expect(divider).toBeInTheDocument()
    
    // 验证不再使用gray边框色
    const grayBorder = document.querySelector('.border-gray-200')
    expect(grayBorder).not.toBeInTheDocument()
  })

  it('应该在加载状态使用shadcn Card组件', () => {
    // Mock loading state
    const LoadingComponent = () => {
      return (
        <div className="min-h-screen bg-background flex items-center justify-center">
          <div className="rounded-lg border bg-card text-card-foreground shadow-sm p-8">
            <div className="p-6 pt-0 text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
              <p className="text-foreground">正在初始化收藏管理页面...</p>
            </div>
          </div>
        </div>
      )
    }

    render(<LoadingComponent />)
    
    // 验证加载状态使用了shadcn颜色系统
    const loadingContainer = document.querySelector('.bg-background')
    expect(loadingContainer).toBeInTheDocument()
    
    const loadingCard = document.querySelector('.bg-card')
    expect(loadingCard).toBeInTheDocument()
    
    const loadingText = screen.getByText('正在初始化收藏管理页面...')
    expect(loadingText).toHaveClass('text-foreground')
  })

  it('应该在错误状态使用shadcn Card和颜色系统', () => {
    // Mock error state
    const ErrorComponent = () => {
      return (
        <div className="min-h-screen bg-background flex items-center justify-center p-4">
          <div className="rounded-lg border bg-card text-card-foreground shadow-sm max-w-md w-full">
            <div className="p-6">
              <div className="text-center">
                <div className="w-12 h-12 bg-destructive/10 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg className="w-6 h-6 text-destructive" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                  </svg>
                </div>
                <h2 className="text-lg font-semibold text-foreground mb-2">初始化失败</h2>
                <p className="text-muted-foreground mb-4">测试错误信息</p>
              </div>
            </div>
          </div>
        </div>
      )
    }

    render(<ErrorComponent />)
    
    // 验证错误状态使用了shadcn颜色系统
    const errorContainer = document.querySelector('.bg-background')
    expect(errorContainer).toBeInTheDocument()
    
    const errorCard = document.querySelector('.bg-card')
    expect(errorCard).toBeInTheDocument()
    
    const errorIcon = document.querySelector('.text-destructive')
    expect(errorIcon).toBeInTheDocument()
    
    const errorTitle = screen.getByText('初始化失败')
    expect(errorTitle).toHaveClass('text-foreground')
    
    const errorMessage = screen.getByText('测试错误信息')
    expect(errorMessage).toHaveClass('text-muted-foreground')
  })

  it('应该在设置页面使用shadcn组件', async () => {
    render(<OptionsApp />)
    
    await waitFor(() => {
      expect(screen.queryByText('正在初始化收藏管理页面...')).not.toBeInTheDocument()
    })

    // 切换到设置标签页
    const settingsButton = screen.getByRole('button', { name: /设置/i })
    fireEvent.click(settingsButton)

    await waitFor(() => {
      expect(screen.getByText('基础设置')).toBeInTheDocument()
    })

    // 验证设置页面使用了shadcn组件
    expect(screen.getByText('界面主题')).toBeInTheDocument()
    expect(screen.getByText('语言设置')).toBeInTheDocument()
    expect(screen.getByText('AI 功能设置')).toBeInTheDocument()
    expect(screen.getByText('自动标签生成')).toBeInTheDocument()
    expect(screen.getByText('智能分类')).toBeInTheDocument()
  })

  it('应该确保与shadcn主题的一致性', async () => {
    render(<OptionsApp />)
    
    await waitFor(() => {
      expect(screen.queryByText('正在初始化收藏管理页面...')).not.toBeInTheDocument()
    })

    // 验证所有文本颜色都使用了shadcn主题变量
    const foregroundTexts = document.querySelectorAll('.text-foreground')
    expect(foregroundTexts.length).toBeGreaterThan(0)
    
    const mutedTexts = document.querySelectorAll('.text-muted-foreground')
    expect(mutedTexts.length).toBeGreaterThan(0)
    
    // 验证所有背景色都使用了shadcn主题变量
    const backgroundElements = document.querySelectorAll('.bg-background')
    expect(backgroundElements.length).toBeGreaterThan(0)
    
    const cardElements = document.querySelectorAll('.bg-card')
    expect(cardElements.length).toBeGreaterThan(0)
  })

  it('应该正确处理标签页切换的shadcn样式', async () => {
    render(<OptionsApp />)
    
    await waitFor(() => {
      expect(screen.queryByText('正在初始化收藏管理页面...')).not.toBeInTheDocument()
    })

    // 验证默认选中的标签页使用了shadcn的primary样式
    const activeTab = screen.getByRole('button', { name: /收藏管理/i })
    expect(activeTab).toHaveClass('bg-primary')
    
    // 切换到其他标签页
    const categoriesTab = screen.getByRole('button', { name: /分类管理/i })
    fireEvent.click(categoriesTab)
    
    await waitFor(() => {
      // 验证新选中的标签页使用了shadcn的primary样式
      expect(categoriesTab).toHaveClass('bg-primary')
    })
  })
})