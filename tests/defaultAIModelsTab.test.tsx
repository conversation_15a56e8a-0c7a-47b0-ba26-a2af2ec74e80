// DefaultAIModelsTab组件测试

import { describe, it, expect, beforeEach, vi } from 'vitest'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import DefaultAIModelsTab from '../src/components/DefaultAIModelsTab'

// Mock services
vi.mock('../src/services/defaultAIModelService', () => ({
  defaultAIModelService: {
    getDefaultModelUsages: vi.fn().mockResolvedValue([
      {
        id: 'default-chat',
        name: '默认聊天',
        description: '通用对话和问答场景',
        category: 'chat',
        selectedModelId: null,
        fallbackModelId: null,
        enabled: true,
        priority: 1
      }
    ]),
    getAvailableModels: vi.fn().mockResolvedValue([
      {
        id: 'test-model-1',
        name: 'test-model-1',
        displayName: 'Test Model 1',
        provider: 'Test Provider',
        providerId: 'test-provider',
        modelType: 'chat',
        enabled: true,
        status: 'connected'
      }
    ]),
    updateUsageModel: vi.fn().mockResolvedValue(undefined),
    setRecommendedConfiguration: vi.fn().mockResolvedValue(undefined),
    resetToDefaults: vi.fn().mockResolvedValue(undefined)
  }
}))

describe('DefaultAIModelsTab', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('应该正确渲染组件', async () => {
    render(<DefaultAIModelsTab />)
    
    // 等待加载完成
    await waitFor(() => {
      expect(screen.getByText('默认AI模型')).toBeInTheDocument()
    })
    
    // 检查统计信息
    expect(screen.getByText('可用模型')).toBeInTheDocument()
    expect(screen.getByText('使用场景')).toBeInTheDocument()
    expect(screen.getByText('已配置')).toBeInTheDocument()
    expect(screen.getByText('需要配置')).toBeInTheDocument()
  })

  it('应该显示使用场景配置', async () => {
    render(<DefaultAIModelsTab />)
    
    await waitFor(() => {
      expect(screen.getByText('默认聊天')).toBeInTheDocument()
    })
    
    expect(screen.getByText('通用对话和问答场景')).toBeInTheDocument()
    expect(screen.getByText('对话')).toBeInTheDocument() // 分类标签
  })

  it('应该显示操作按钮', async () => {
    render(<DefaultAIModelsTab />)
    
    await waitFor(() => {
      expect(screen.getByText('一键设置推荐配置')).toBeInTheDocument()
    })
    
    expect(screen.getByText('重置为默认')).toBeInTheDocument()
  })

  it('应该正确处理Select组件的value', async () => {
    render(<DefaultAIModelsTab />)
    
    await waitFor(() => {
      expect(screen.getByText('默认聊天')).toBeInTheDocument()
    })
    
    // 检查Select组件是否正确渲染，没有空字符串value的错误
    const selectTriggers = screen.getAllByRole('combobox')
    expect(selectTriggers.length).toBeGreaterThan(0)
    
    // 验证Select组件存在且没有抛出空字符串value错误
    // 这个测试主要是确保组件能正常渲染而不报错
    expect(selectTriggers[0]).toBeInTheDocument()
  })

  it('应该在没有可用模型时显示警告', async () => {
    // Mock返回空的可用模型列表
    const mockService = await import('../src/services/defaultAIModelService')
    vi.mocked(mockService.defaultAIModelService.getAvailableModels).mockResolvedValue([])
    
    render(<DefaultAIModelsTab />)
    
    await waitFor(() => {
      expect(screen.getByText('没有可用的AI模型')).toBeInTheDocument()
    })
    
    expect(screen.getByText('请先在"AI集成"页面配置并启用至少一个聊天模型')).toBeInTheDocument()
  })

  it('应该正确处理错误状态', async () => {
    // Mock服务抛出错误
    const mockService = await import('../src/services/defaultAIModelService')
    vi.mocked(mockService.defaultAIModelService.getDefaultModelUsages).mockRejectedValue(new Error('测试错误'))
    
    render(<DefaultAIModelsTab />)
    
    await waitFor(() => {
      expect(screen.getByText('加载配置失败，请稍后重试')).toBeInTheDocument()
    })
  })
})