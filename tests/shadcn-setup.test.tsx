import { describe, it, expect } from 'vitest';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import { Button } from '@/components/ui/button';
import { ShadcnTest } from '@/components/test/ShadcnTest';

describe('shadcn/ui 基础环境测试', () => {
  describe('Button组件', () => {
    it('应该正确渲染默认Button', () => {
      render(<Button>测试按钮</Button>);
      const button = screen.getByRole('button', { name: '测试按钮' });
      expect(button).toBeInTheDocument();
      expect(button).toHaveClass('inline-flex', 'items-center', 'justify-center');
    });

    it('应该正确应用不同的变体样式', () => {
      const { rerender } = render(<Button variant="default">默认</Button>);
      let button = screen.getByRole('button');
      expect(button).toHaveClass('bg-primary');

      rerender(<Button variant="secondary">次要</Button>);
      button = screen.getByRole('button');
      expect(button).toHaveClass('bg-secondary');

      rerender(<Button variant="outline">轮廓</Button>);
      button = screen.getByRole('button');
      expect(button).toHaveClass('border');
    });

    it('应该正确应用不同的尺寸样式', () => {
      const { rerender } = render(<Button size="sm">小</Button>);
      let button = screen.getByRole('button');
      expect(button).toHaveClass('h-9');

      rerender(<Button size="lg">大</Button>);
      button = screen.getByRole('button');
      expect(button).toHaveClass('h-11');
    });

    it('应该支持disabled状态', () => {
      render(<Button disabled>禁用按钮</Button>);
      const button = screen.getByRole('button');
      expect(button).toBeDisabled();
      expect(button).toHaveClass('disabled:opacity-50');
    });
  });

  describe('ShadcnTest组件', () => {
    it('应该正确渲染测试组件', () => {
      render(<ShadcnTest />);
      
      // 检查标题
      expect(screen.getByText('shadcn/ui 组件测试')).toBeInTheDocument();
      
      // 检查不同变体的按钮
      expect(screen.getAllByText('默认按钮')).toHaveLength(2); // 有两个默认按钮（变体和尺寸各一个）
      expect(screen.getByText('次要按钮')).toBeInTheDocument();
      expect(screen.getByText('轮廓按钮')).toBeInTheDocument();
      expect(screen.getByText('幽灵按钮')).toBeInTheDocument();
      expect(screen.getByText('危险按钮')).toBeInTheDocument();
      
      // 检查不同尺寸的按钮
      expect(screen.getByText('小按钮')).toBeInTheDocument();
      expect(screen.getByText('大按钮')).toBeInTheDocument();
      
      // 检查卡片内容
      expect(screen.getByText('这是一个使用shadcn主题变量的卡片组件')).toBeInTheDocument();
    });
  });

  describe('工具函数测试', () => {
    it('cn函数应该正确合并类名', async () => {
      // 动态导入cn函数
      const { cn } = await import('@/lib/utils');
      
      // 测试基本合并
      expect(cn('class1', 'class2')).toBe('class1 class2');
      
      // 测试条件合并
      expect(cn('class1', false && 'class2', 'class3')).toBe('class1 class3');
      
      // 测试Tailwind类名冲突解决
      expect(cn('px-2', 'px-4')).toBe('px-4');
      expect(cn('text-red-500', 'text-blue-500')).toBe('text-blue-500');
    });
  });
});