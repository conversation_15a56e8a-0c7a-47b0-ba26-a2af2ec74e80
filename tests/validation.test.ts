// 验证工具类单元测试

import { describe, it, expect, beforeEach } from 'vitest'
import { ValidationUtils } from '../src/utils/validation'
import type { BookmarkInput, CategoryInput, TagInput, ImportData } from '../src/types'

describe('ValidationUtils', () => {
  describe('书签验证', () => {
    it('应该验证有效的书签数据', () => {
      const validBookmark: BookmarkInput = {
        type: 'url',
        title: '测试书签',
        url: 'https://example.com',
        description: '这是一个测试书签',
        tags: ['测试', '书签'],
        category: '技术'
      }

      const result = ValidationUtils.validateBookmarkInput(validBookmark)
      expect(result.isValid).toBe(true)
      expect(result.errors).toHaveLength(0)
    })

    it('应该检测空标题错误', () => {
      const invalidBookmark: BookmarkInput = {
        type: 'url',
        title: '',
        url: 'https://example.com'
      }

      const result = ValidationUtils.validateBookmarkInput(invalidBookmark)
      expect(result.isValid).toBe(false)
      expect(result.errors).toContainEqual({
        field: 'title',
        message: '标题不能为空',
        code: 'TITLE_REQUIRED'
      })
    })

    it('应该检测标题过长错误', () => {
      const invalidBookmark: BookmarkInput = {
        type: 'url',
        title: 'a'.repeat(501),
        url: 'https://example.com'
      }

      const result = ValidationUtils.validateBookmarkInput(invalidBookmark)
      expect(result.isValid).toBe(false)
      expect(result.errors).toContainEqual({
        field: 'title',
        message: '标题长度不能超过500个字符',
        code: 'TITLE_TOO_LONG'
      })
    })

    it('应该检测无效的书签类型', () => {
      const invalidBookmark: BookmarkInput = {
        type: 'invalid' as any,
        title: '测试书签'
      }

      const result = ValidationUtils.validateBookmarkInput(invalidBookmark)
      expect(result.isValid).toBe(false)
      expect(result.errors).toContainEqual({
        field: 'type',
        message: '无效的书签类型',
        code: 'INVALID_TYPE'
      })
    })

    it('应该验证URL类型书签的URL字段', () => {
      const invalidBookmark: BookmarkInput = {
        type: 'url',
        title: '测试书签',
        url: ''
      }

      const result = ValidationUtils.validateBookmarkInput(invalidBookmark)
      expect(result.isValid).toBe(false)
      expect(result.errors).toContainEqual({
        field: 'url',
        message: 'URL不能为空',
        code: 'URL_REQUIRED'
      })
    })

    it('应该检测无效的URL格式', () => {
      const invalidBookmark: BookmarkInput = {
        type: 'url',
        title: '测试书签',
        url: 'invalid-url'
      }

      const result = ValidationUtils.validateBookmarkInput(invalidBookmark)
      expect(result.isValid).toBe(false)
      expect(result.errors).toContainEqual({
        field: 'url',
        message: '无效的URL格式',
        code: 'INVALID_URL'
      })
    })

    it('应该验证文本类型书签的内容字段', () => {
      const invalidBookmark: BookmarkInput = {
        type: 'text',
        title: '测试书签',
        content: ''
      }

      const result = ValidationUtils.validateBookmarkInput(invalidBookmark)
      expect(result.isValid).toBe(false)
      expect(result.errors).toContainEqual({
        field: 'content',
        message: '内容不能为空',
        code: 'CONTENT_REQUIRED'
      })
    })

    it('应该检测内容过长错误', () => {
      const invalidBookmark: BookmarkInput = {
        type: 'text',
        title: '测试书签',
        content: 'a'.repeat(10001)
      }

      const result = ValidationUtils.validateBookmarkInput(invalidBookmark)
      expect(result.isValid).toBe(false)
      expect(result.errors).toContainEqual({
        field: 'content',
        message: '内容长度不能超过10000个字符',
        code: 'CONTENT_TOO_LONG'
      })
    })

    it('应该检测标签数量过多错误', () => {
      const invalidBookmark: BookmarkInput = {
        type: 'url',
        title: '测试书签',
        url: 'https://example.com',
        tags: Array.from({ length: 21 }, (_, i) => `标签${i}`)
      }

      const result = ValidationUtils.validateBookmarkInput(invalidBookmark)
      expect(result.isValid).toBe(false)
      expect(result.errors).toContainEqual({
        field: 'tags',
        message: '标签数量不能超过20个',
        code: 'TOO_MANY_TAGS'
      })
    })

    it('应该检测单个标签过长错误', () => {
      const invalidBookmark: BookmarkInput = {
        type: 'url',
        title: '测试书签',
        url: 'https://example.com',
        tags: ['a'.repeat(51)]
      }

      const result = ValidationUtils.validateBookmarkInput(invalidBookmark)
      expect(result.isValid).toBe(false)
      expect(result.errors).toContainEqual({
        field: 'tags',
        message: '单个标签长度不能超过50个字符',
        code: 'TAG_TOO_LONG'
      })
    })

    it('应该处理空输入', () => {
      const result = ValidationUtils.validateBookmarkInput(null as any)
      expect(result.isValid).toBe(false)
      expect(result.errors).toContainEqual({
        field: 'input',
        message: '输入数据不能为空',
        code: 'INPUT_NULL'
      })
    })
  })

  describe('分类验证', () => {
    it('应该验证有效的分类数据', () => {
      const validCategory: CategoryInput = {
        name: '技术分类',
        description: '技术相关的收藏',
        color: '#3B82F6'
      }

      const result = ValidationUtils.validateCategoryInput(validCategory)
      expect(result.isValid).toBe(true)
      expect(result.errors).toHaveLength(0)
    })

    it('应该检测空名称错误', () => {
      const invalidCategory: CategoryInput = {
        name: ''
      }

      const result = ValidationUtils.validateCategoryInput(invalidCategory)
      expect(result.isValid).toBe(false)
      expect(result.errors).toContainEqual({
        field: 'name',
        message: '分类名称不能为空',
        code: 'NAME_REQUIRED'
      })
    })

    it('应该检测名称过长错误', () => {
      const invalidCategory: CategoryInput = {
        name: 'a'.repeat(101)
      }

      const result = ValidationUtils.validateCategoryInput(invalidCategory)
      expect(result.isValid).toBe(false)
      expect(result.errors).toContainEqual({
        field: 'name',
        message: '分类名称长度不能超过100个字符',
        code: 'NAME_TOO_LONG'
      })
    })

    it('应该检测无效的颜色格式', () => {
      const invalidCategory: CategoryInput = {
        name: '技术分类',
        color: 'invalid-color'
      }

      const result = ValidationUtils.validateCategoryInput(invalidCategory)
      expect(result.isValid).toBe(false)
      expect(result.errors).toContainEqual({
        field: 'color',
        message: '无效的颜色格式',
        code: 'INVALID_COLOR'
      })
    })

    it('应该接受有效的颜色格式', () => {
      const validColors = ['#FF0000', '#f00', 'rgb(255,0,0)', 'rgba(255,0,0,0.5)']
      
      for (const color of validColors) {
        const category: CategoryInput = {
          name: '技术分类',
          color
        }

        const result = ValidationUtils.validateCategoryInput(category)
        expect(result.isValid).toBe(true)
      }
    })

    it('应该处理空输入', () => {
      const result = ValidationUtils.validateCategoryInput(null as any)
      expect(result.isValid).toBe(false)
      expect(result.errors).toContainEqual({
        field: 'input',
        message: '输入数据不能为空',
        code: 'INPUT_NULL'
      })
    })
  })

  describe('标签验证', () => {
    it('应该验证有效的标签数据', () => {
      const validTag: TagInput = {
        name: 'JavaScript',
        color: '#F59E0B'
      }

      const result = ValidationUtils.validateTagInput(validTag)
      expect(result.isValid).toBe(true)
      expect(result.errors).toHaveLength(0)
    })

    it('应该检测空名称错误', () => {
      const invalidTag: TagInput = {
        name: ''
      }

      const result = ValidationUtils.validateTagInput(invalidTag)
      expect(result.isValid).toBe(false)
      expect(result.errors).toContainEqual({
        field: 'name',
        message: '标签名称不能为空',
        code: 'NAME_REQUIRED'
      })
    })

    it('应该检测名称过长错误', () => {
      const invalidTag: TagInput = {
        name: 'a'.repeat(51)
      }

      const result = ValidationUtils.validateTagInput(invalidTag)
      expect(result.isValid).toBe(false)
      expect(result.errors).toContainEqual({
        field: 'name',
        message: '标签名称长度不能超过50个字符',
        code: 'NAME_TOO_LONG'
      })
    })

    it('应该处理空输入', () => {
      const result = ValidationUtils.validateTagInput(null as any)
      expect(result.isValid).toBe(false)
      expect(result.errors).toContainEqual({
        field: 'input',
        message: '输入数据不能为空',
        code: 'INPUT_NULL'
      })
    })
  })

  describe('数据清理', () => {
    it('应该清理书签数据', () => {
      const dirtyBookmark: BookmarkInput = {
        type: 'url',
        title: '  测试书签  ',
        url: '  https://example.com  ',
        description: '  描述  ',
        tags: ['  标签1  ', '', '  标签2  '],
        category: '  技术  '
      }

      const cleaned = ValidationUtils.sanitizeBookmarkInput(dirtyBookmark)
      expect(cleaned.title).toBe('测试书签')
      expect(cleaned.url).toBe('https://example.com')
      expect(cleaned.description).toBe('描述')
      expect(cleaned.tags).toEqual(['标签1', '标签2'])
      expect(cleaned.category).toBe('技术')
    })

    it('应该清理分类数据', () => {
      const dirtyCategory: CategoryInput = {
        name: '  技术分类  ',
        description: '  描述  ',
        color: '  #FF0000  '
      }

      const cleaned = ValidationUtils.sanitizeCategoryInput(dirtyCategory)
      expect(cleaned.name).toBe('技术分类')
      expect(cleaned.description).toBe('描述')
      expect(cleaned.color).toBe('#FF0000')
    })

    it('应该清理标签数据', () => {
      const dirtyTag: TagInput = {
        name: '  JavaScript  ',
        color: '  #F59E0B  '
      }

      const cleaned = ValidationUtils.sanitizeTagInput(dirtyTag)
      expect(cleaned.name).toBe('JavaScript')
      expect(cleaned.color).toBe('#F59E0B')
    })
  })

  describe('批量验证', () => {
    it('应该批量验证书签数据', () => {
      const bookmarks: BookmarkInput[] = [
        {
          type: 'url',
          title: '有效书签',
          url: 'https://example.com'
        },
        {
          type: 'url',
          title: '',
          url: 'https://example.com'
        }
      ]

      const results = ValidationUtils.validateBookmarkInputBatch(bookmarks)
      expect(results).toHaveLength(2)
      expect(results[0].result.isValid).toBe(true)
      expect(results[1].result.isValid).toBe(false)
    })
  })

  describe('导入数据验证', () => {
    it('应该验证有效的导入数据', () => {
      const validImportData: ImportData = {
        version: '2.0',
        exportDate: '2024-01-01T00:00:00.000Z',
        exportType: 'all',
        metadata: {
          source: 'Test',
          totalBookmarks: 1,
          totalCategories: 0,
          totalTags: 0,
          exportOptions: {}
        },
        bookmarks: [{
          type: 'url',
          title: '测试书签',
          url: 'https://example.com'
        }]
      }

      const result = ValidationUtils.validateImportData(validImportData)
      expect(result.isValid).toBe(true)
      expect(result.errors).toHaveLength(0)
    })

    it('应该检测缺少版本信息', () => {
      const invalidImportData: ImportData = {
        version: '',
        exportDate: '2024-01-01T00:00:00.000Z',
        exportType: 'all',
        metadata: {
          source: 'Test',
          totalBookmarks: 0,
          totalCategories: 0,
          totalTags: 0,
          exportOptions: {}
        }
      }

      const result = ValidationUtils.validateImportData(invalidImportData)
      expect(result.isValid).toBe(false)
      expect(result.errors).toContainEqual({
        field: 'version',
        message: '缺少版本信息',
        code: 'VERSION_REQUIRED'
      })
    })

    it('应该检测空数据', () => {
      const invalidImportData: ImportData = {
        version: '2.0',
        exportDate: '2024-01-01T00:00:00.000Z',
        exportType: 'all',
        metadata: {
          source: 'Test',
          totalBookmarks: 0,
          totalCategories: 0,
          totalTags: 0,
          exportOptions: {}
        }
      }

      const result = ValidationUtils.validateImportData(invalidImportData)
      expect(result.isValid).toBe(false)
      expect(result.errors).toContainEqual({
        field: 'data',
        message: '导入数据不能为空',
        code: 'NO_DATA'
      })
    })
  })

  describe('验证规则获取', () => {
    it('应该返回验证规则配置', () => {
      const rules = ValidationUtils.getValidationRules()
      expect(rules.TITLE_MAX_LENGTH).toBe(500)
      expect(rules.CONTENT_MAX_LENGTH).toBe(10000)
      expect(rules.MAX_TAGS_COUNT).toBe(20)
    })
  })
})