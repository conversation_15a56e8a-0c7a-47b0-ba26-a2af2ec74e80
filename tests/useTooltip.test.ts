// useTooltip Hook单元测试

import { renderHook, act } from '@testing-library/react'
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { useTooltip } from '../src/components/HelpTooltip/useTooltip'

// Mock定时器
vi.useFakeTimers()

describe('useTooltip Hook测试', () => {
  beforeEach(() => {
    vi.clearAllTimers()
  })

  afterEach(() => {
    vi.runOnlyPendingTimers()
    vi.useRealTimers()
    vi.useFakeTimers()
  })

  describe('基础功能', () => {
    it('应该返回初始状态', () => {
      const { result } = renderHook(() => useTooltip())
      
      expect(result.current.isVisible).toBe(false)
      expect(typeof result.current.show).toBe('function')
      expect(typeof result.current.hide).toBe('function')
      expect(typeof result.current.toggle).toBe('function')
      expect(typeof result.current.triggerProps).toBe('object')
    })

    it('应该能够显示tooltip', () => {
      const { result } = renderHook(() => useTooltip())
      
      act(() => {
        result.current.show()
      })
      
      expect(result.current.isVisible).toBe(true)
    })

    it('应该能够隐藏tooltip', () => {
      const { result } = renderHook(() => useTooltip())
      
      act(() => {
        result.current.show()
      })
      
      expect(result.current.isVisible).toBe(true)
      
      act(() => {
        result.current.hide()
      })
      
      expect(result.current.isVisible).toBe(false)
    })

    it('应该能够切换tooltip状态', () => {
      const { result } = renderHook(() => useTooltip())
      
      expect(result.current.isVisible).toBe(false)
      
      act(() => {
        result.current.toggle()
      })
      
      expect(result.current.isVisible).toBe(true)
      
      act(() => {
        result.current.toggle()
      })
      
      expect(result.current.isVisible).toBe(false)
    })
  })

  describe('延迟功能', () => {
    it('应该支持显示延迟', () => {
      const { result } = renderHook(() => useTooltip({ delay: 100 }))
      
      act(() => {
        result.current.show()
      })
      
      // 立即检查，应该还没有显示
      expect(result.current.isVisible).toBe(false)
      
      // 快进时间
      act(() => {
        vi.advanceTimersByTime(100)
      })
      
      expect(result.current.isVisible).toBe(true)
    })

    it('应该支持隐藏延迟', () => {
      const { result } = renderHook(() => useTooltip({ hideDelay: 100 }))
      
      act(() => {
        result.current.show()
      })
      
      expect(result.current.isVisible).toBe(true)
      
      act(() => {
        result.current.hide()
      })
      
      // 立即检查，应该还在显示
      expect(result.current.isVisible).toBe(true)
      
      // 快进时间
      act(() => {
        vi.advanceTimersByTime(100)
      })
      
      expect(result.current.isVisible).toBe(false)
    })

    it('应该能够取消延迟显示', () => {
      const { result } = renderHook(() => useTooltip({ delay: 100 }))
      
      act(() => {
        result.current.show()
      })
      
      // 在延迟期间隐藏
      act(() => {
        result.current.hide()
      })
      
      // 快进时间
      act(() => {
        vi.advanceTimersByTime(100)
      })
      
      // 应该没有显示
      expect(result.current.isVisible).toBe(false)
    })
  })

  describe('禁用功能', () => {
    it('应该在禁用时不显示tooltip', () => {
      const { result } = renderHook(() => useTooltip({ disabled: true }))
      
      act(() => {
        result.current.show()
      })
      
      expect(result.current.isVisible).toBe(false)
    })

    it('应该在禁用时不响应鼠标事件', () => {
      const { result } = renderHook(() => useTooltip({ disabled: true }))
      
      act(() => {
        result.current.triggerProps.onMouseEnter()
      })
      
      expect(result.current.isVisible).toBe(false)
    })
  })

  describe('事件处理', () => {
    it('应该在鼠标进入时显示tooltip', () => {
      const { result } = renderHook(() => useTooltip())
      
      act(() => {
        result.current.triggerProps.onMouseEnter()
      })
      
      expect(result.current.isVisible).toBe(true)
    })

    it('应该在鼠标离开时隐藏tooltip', () => {
      const { result } = renderHook(() => useTooltip())
      
      act(() => {
        result.current.triggerProps.onMouseEnter()
      })
      
      expect(result.current.isVisible).toBe(true)
      
      act(() => {
        result.current.triggerProps.onMouseLeave()
      })
      
      expect(result.current.isVisible).toBe(false)
    })

    it('应该在获得焦点时显示tooltip', () => {
      const { result } = renderHook(() => useTooltip())
      
      act(() => {
        result.current.triggerProps.onFocus()
      })
      
      expect(result.current.isVisible).toBe(true)
    })

    it('应该在失去焦点时隐藏tooltip', () => {
      const { result } = renderHook(() => useTooltip())
      
      act(() => {
        result.current.triggerProps.onFocus()
      })
      
      expect(result.current.isVisible).toBe(true)
      
      act(() => {
        result.current.triggerProps.onBlur()
      })
      
      expect(result.current.isVisible).toBe(false)
    })
  })

  describe('ARIA属性', () => {
    it('应该在显示时提供aria-describedby', () => {
      const { result } = renderHook(() => useTooltip())
      
      expect(result.current.triggerProps['aria-describedby']).toBeUndefined()
      
      act(() => {
        result.current.show()
      })
      
      expect(result.current.triggerProps['aria-describedby']).toBe('tooltip-content')
    })

    it('应该在隐藏时移除aria-describedby', () => {
      const { result } = renderHook(() => useTooltip())
      
      act(() => {
        result.current.show()
      })
      
      expect(result.current.triggerProps['aria-describedby']).toBe('tooltip-content')
      
      act(() => {
        result.current.hide()
      })
      
      expect(result.current.triggerProps['aria-describedby']).toBeUndefined()
    })
  })

  describe('内存泄漏防护', () => {
    it('应该在组件卸载时清理定时器', () => {
      const { result, unmount } = renderHook(() => useTooltip({ delay: 100 }))
      
      act(() => {
        result.current.show()
      })
      
      // 卸载组件
      unmount()
      
      // 快进时间，不应该有任何副作用
      act(() => {
        vi.advanceTimersByTime(100)
      })
      
      // 这里主要是确保没有抛出错误
      expect(true).toBe(true)
    })
  })
})