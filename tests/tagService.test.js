// 标签服务单元测试

import { describe, it, expect, beforeEach, vi } from 'vitest'

// 模拟依赖
vi.mock('../src/utils/indexedDB', () => ({
  indexedDBService: {
    getTags: vi.fn(),
    getBookmarks: vi.fn(),
    saveTag: vi.fn(),
    getTag: vi.fn(),
    updateTag: vi.fn(),
    deleteTag: vi.fn(),
    getBookmark: vi.fn(),
    updateBookmark: vi.fn()
  }
}))

describe('TagService', () => {
  let tagService
  let indexedDBService

  beforeEach(async () => {
    // 动态导入以确保模拟生效
    const module = await import('../src/services/tagService')
    tagService = module.tagService
    
    const dbModule = await import('../src/utils/indexedDB')
    indexedDBService = dbModule.indexedDBService
    
    // 重置所有模拟
    vi.clearAllMocks()
  })

  // 测试数据
  const mockTags = [
    {
      id: 'tag1',
      name: '技术',
      color: '#3B82F6',
      usageCount: 5,
      createdAt: new Date('2024-01-01'),
      updatedAt: new Date('2024-01-01')
    },
    {
      id: 'tag2',
      name: '学习',
      color: '#10B981',
      usageCount: 3,
      createdAt: new Date('2024-01-02'),
      updatedAt: new Date('2024-01-02')
    }
  ]

  const mockBookmarks = [
    {
      id: 'bookmark1',
      title: 'JavaScript教程',
      tags: ['技术', '学习'],
      category: '编程',
      type: 'url',
      url: 'https://example.com/js',
      createdAt: new Date('2024-01-01'),
      updatedAt: new Date('2024-01-01'),
      metadata: { aiGenerated: false }
    },
    {
      id: 'bookmark2',
      title: 'React文档',
      tags: ['技术'],
      category: '编程',
      type: 'url',
      url: 'https://react.dev',
      createdAt: new Date('2024-01-02'),
      updatedAt: new Date('2024-01-02'),
      metadata: { aiGenerated: false }
    }
  ]

  describe('基本功能测试', () => {
    it('应该能够导入TagService', () => {
      expect(tagService).toBeDefined()
      expect(typeof tagService.getAllTagsWithStats).toBe('function')
      expect(typeof tagService.createTag).toBe('function')
      expect(typeof tagService.updateTag).toBe('function')
      expect(typeof tagService.deleteTag).toBe('function')
    })
  })

  describe('getAllTagsWithStats', () => {
    it('应该返回包含统计信息的标签列表', async () => {
      // 设置模拟返回值
      indexedDBService.getTags.mockResolvedValue(mockTags)
      indexedDBService.getBookmarks.mockResolvedValue(mockBookmarks)

      const result = await tagService.getAllTagsWithStats()

      expect(result).toHaveLength(2)
      expect(result[0]).toMatchObject({
        id: 'tag1',
        name: '技术',
        usageCount: 2 // 在2个书签中使用
      })
      expect(result[1]).toMatchObject({
        id: 'tag2',
        name: '学习',
        usageCount: 1 // 在1个书签中使用
      })
    })

    it('应该处理空标签列表', async () => {
      indexedDBService.getTags.mockResolvedValue([])
      indexedDBService.getBookmarks.mockResolvedValue([])
      
      const result = await tagService.getAllTagsWithStats()
      
      expect(result).toEqual([])
    })

    it('应该处理获取数据失败的情况', async () => {
      indexedDBService.getTags.mockRejectedValue(new Error('数据库错误'))
      
      await expect(tagService.getAllTagsWithStats()).rejects.toThrow('获取标签统计信息失败')
    })
  })

  describe('syncTagsFromBookmarks', () => {
    it('应该从书签中同步新标签', async () => {
      // 模拟现有标签只有部分
      const existingTags = [mockTags[0]] // 只有'技术'标签
      indexedDBService.getTags
        .mockResolvedValueOnce(existingTags) // 第一次调用返回现有标签
        .mockResolvedValueOnce(existingTags) // 第二次调用（updateAllTagUsageCounts中）
        .mockResolvedValue(mockTags) // 最后返回所有标签
      
      indexedDBService.getBookmarks.mockResolvedValue(mockBookmarks)
      indexedDBService.saveTag.mockResolvedValue('new-tag-id')

      const result = await tagService.syncTagsFromBookmarks()

      // 验证保存了新标签（学习）
      expect(indexedDBService.saveTag).toHaveBeenCalledTimes(1)
      expect(result).toHaveLength(2)
    })
  })

  describe('createTag', () => {
    it('应该成功创建新标签', async () => {
      const tagData = {
        name: '新标签',
        color: '#FF0000'
      }
      
      // 模拟名称唯一性验证通过
      indexedDBService.getTags.mockResolvedValue(mockTags)
      indexedDBService.saveTag.mockResolvedValue('new-tag-id')

      const result = await tagService.createTag(tagData)

      expect(result).toMatchObject({
        name: '新标签',
        color: '#FF0000',
        usageCount: 0
      })
      expect(result.id).toBeDefined()
      expect(result.createdAt).toBeInstanceOf(Date)
      expect(result.updatedAt).toBeInstanceOf(Date)
      expect(indexedDBService.saveTag).toHaveBeenCalledWith(result)
    })

    it('应该在名称重复时抛出错误', async () => {
      const tagData = {
        name: '技术' // 已存在的标签名称
      }
      
      indexedDBService.getTags.mockResolvedValue(mockTags)

      await expect(tagService.createTag(tagData)).rejects.toThrow('标签名称 "技术" 已存在')
      expect(indexedDBService.saveTag).not.toHaveBeenCalled()
    })

    it('应该自动生成颜色', async () => {
      const tagData = {
        name: '无颜色标签'
      }
      
      indexedDBService.getTags.mockResolvedValue([])
      indexedDBService.saveTag.mockResolvedValue('new-tag-id')

      const result = await tagService.createTag(tagData)

      expect(result.color).toBeDefined()
      expect(result.color).toMatch(/^#[0-9A-F]{6}$/i) // 验证是否为有效的十六进制颜色
    })
  })

  describe('updateTag', () => {
    it('应该成功更新标签', async () => {
      const tagId = 'tag1'
      const updates = {
        name: '更新的技术',
        color: '#FF0000'
      }
      
      indexedDBService.getTag.mockResolvedValue(mockTags[0])
      indexedDBService.getTags.mockResolvedValue(mockTags)
      indexedDBService.saveTag.mockResolvedValue(tagId)
      indexedDBService.getBookmarks.mockResolvedValue(mockBookmarks)
      indexedDBService.updateBookmark.mockResolvedValue()

      const result = await tagService.updateTag(tagId, updates)

      expect(result).toMatchObject({
        id: tagId,
        name: '更新的技术',
        color: '#FF0000'
      })
      expect(result.updatedAt).toBeInstanceOf(Date)
      expect(indexedDBService.saveTag).toHaveBeenCalled()
    })

    it('应该在标签不存在时抛出错误', async () => {
      const tagId = 'nonexistent'
      const updates = { name: '新名称' }
      
      indexedDBService.getTag.mockResolvedValue(null)

      await expect(tagService.updateTag(tagId, updates)).rejects.toThrow('标签不存在: nonexistent')
    })
  })

  describe('deleteTag', () => {
    it('应该成功删除标签并从书签中移除', async () => {
      const tagId = 'tag1'
      
      indexedDBService.getTag.mockResolvedValue(mockTags[0])
      indexedDBService.getBookmarks.mockResolvedValue(mockBookmarks)
      indexedDBService.updateBookmark.mockResolvedValue()
      indexedDBService.deleteTag.mockResolvedValue()

      await tagService.deleteTag(tagId)

      expect(indexedDBService.deleteTag).toHaveBeenCalledWith(tagId)
      // 验证从书签中移除了标签
      expect(indexedDBService.updateBookmark).toHaveBeenCalledTimes(2) // 2个书签包含'技术'标签
    })

    it('应该在标签不存在时抛出错误', async () => {
      const tagId = 'nonexistent'
      
      indexedDBService.getTag.mockResolvedValue(null)

      await expect(tagService.deleteTag(tagId)).rejects.toThrow('标签不存在: nonexistent')
      expect(indexedDBService.deleteTag).not.toHaveBeenCalled()
    })
  })

  describe('validateTagName', () => {
    it('应该验证唯一的标签名称', async () => {
      indexedDBService.getTags.mockResolvedValue(mockTags)

      const result = await tagService.validateTagName('新标签')
      
      expect(result).toBe(true)
    })

    it('应该拒绝重复的标签名称', async () => {
      indexedDBService.getTags.mockResolvedValue(mockTags)

      const result = await tagService.validateTagName('技术')
      
      expect(result).toBe(false)
    })

    it('应该在更新时排除当前标签', async () => {
      indexedDBService.getTags.mockResolvedValue(mockTags)

      const result = await tagService.validateTagName('技术', 'tag1')
      
      expect(result).toBe(true) // 排除自己后应该通过验证
    })
  })

  describe('getTagUsageCount', () => {
    it('应该返回正确的使用次数', async () => {
      const tagId = 'tag1'
      
      indexedDBService.getTag.mockResolvedValue(mockTags[0])
      indexedDBService.getBookmarks.mockResolvedValue(mockBookmarks)

      const result = await tagService.getTagUsageCount(tagId)
      
      expect(result).toBe(2) // '技术'标签在2个书签中使用
    })

    it('应该在标签不存在时返回0', async () => {
      const tagId = 'nonexistent'
      
      indexedDBService.getTag.mockResolvedValue(null)

      const result = await tagService.getTagUsageCount(tagId)
      
      expect(result).toBe(0)
    })
  })

  describe('getBookmarksByTag', () => {
    it('应该返回包含指定标签的书签', async () => {
      const tagId = 'tag1'
      
      indexedDBService.getTag.mockResolvedValue(mockTags[0])
      indexedDBService.getBookmarks.mockResolvedValue(mockBookmarks)

      const result = await tagService.getBookmarksByTag(tagId)
      
      expect(result).toHaveLength(2) // 2个书签都包含'技术'标签
      expect(result.every(bookmark => bookmark.tags.includes('技术'))).toBe(true)
    })

    it('应该在标签不存在时返回空数组', async () => {
      const tagId = 'nonexistent'
      
      indexedDBService.getTag.mockResolvedValue(null)

      const result = await tagService.getBookmarksByTag(tagId)
      
      expect(result).toEqual([])
    })
  })

  describe('searchTags', () => {
    it('应该返回匹配的标签', async () => {
      indexedDBService.getTags.mockResolvedValue(mockTags)

      const result = await tagService.searchTags('技')
      
      expect(result).toHaveLength(1)
      expect(result[0].name).toBe('技术')
    })

    it('应该在空查询时返回所有标签', async () => {
      indexedDBService.getTags.mockResolvedValue(mockTags)

      const result = await tagService.searchTags('')
      
      expect(result).toEqual(mockTags)
    })
  })

  describe('getPopularTags', () => {
    it('应该返回按使用次数排序的热门标签', async () => {
      indexedDBService.getTags.mockResolvedValue(mockTags)
      indexedDBService.getBookmarks.mockResolvedValue(mockBookmarks)

      const result = await tagService.getPopularTags(1)
      
      expect(result).toHaveLength(1)
      expect(result[0].name).toBe('技术')
      expect(result[0].usageCount).toBe(2)
    })
  })

  describe('sortTags', () => {
    const tagsWithStats = [
      { ...mockTags[0], usageCount: 2 },
      { ...mockTags[1], usageCount: 1 }
    ]

    it('应该按名称升序排序', () => {
      const result = tagService.sortTags(tagsWithStats, 'name-asc')
      
      expect(result[0].name).toBe('学习')
      expect(result[1].name).toBe('技术')
    })

    it('应该按使用次数降序排序', () => {
      const result = tagService.sortTags(tagsWithStats, 'usage-desc')
      
      expect(result[0].usageCount).toBe(2)
      expect(result[1].usageCount).toBe(1)
    })
  })

  describe('filterTags', () => {
    const tagsWithStats = [
      { ...mockTags[0], usageCount: 2 },
      { ...mockTags[1], usageCount: 1 }
    ]

    it('应该筛选匹配的标签', () => {
      const result = tagService.filterTags(tagsWithStats, '技')
      
      expect(result).toHaveLength(1)
      expect(result[0].name).toBe('技术')
    })

    it('应该在空查询时返回所有标签', () => {
      const result = tagService.filterTags(tagsWithStats, '')
      
      expect(result).toEqual(tagsWithStats)
    })
  })
})