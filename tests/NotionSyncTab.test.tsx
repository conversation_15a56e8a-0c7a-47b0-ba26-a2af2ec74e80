import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { describe, it, expect, vi } from 'vitest'
import NotionSyncTab from '../src/components/NotionSyncTab'

// Mock Chrome API
const mockChrome = {
  runtime: {
    sendMessage: vi.fn(),
  },
  storage: {
    sync: {
      get: vi.fn(),
      set: vi.fn(),
    },
  },
}

// @ts-ignore
global.chrome = mockChrome

describe('NotionSyncTab', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('应该正确渲染Notion集成页面', () => {
    render(<NotionSyncTab />)
    
    // 检查标题
    expect(screen.getByText('Notion集成')).toBeInTheDocument()
    expect(screen.getByText('将您的收藏数据与Notion数据库同步，实现跨平台数据管理')).toBeInTheDocument()
    
    // 检查连接配置区域
    expect(screen.getByText('连接配置')).toBeInTheDocument()
    expect(screen.getByLabelText('Notion API Token')).toBeInTheDocument()
    expect(screen.getByPlaceholderText('输入您的Notion API Token')).toBeInTheDocument()
  })

  it('应该显示连接按钮并处理点击事件', () => {
    render(<NotionSyncTab />)
    
    const connectButton = screen.getByRole('button', { name: /连接Notion/i })
    expect(connectButton).toBeInTheDocument()
    expect(connectButton).toBeDisabled() // 初始状态下应该是禁用的，因为没有输入token
  })

  it('应该在输入token后启用连接按钮', async () => {
    render(<NotionSyncTab />)
    
    const tokenInput = screen.getByLabelText('Notion API Token')
    const connectButton = screen.getByRole('button', { name: /连接Notion/i })
    
    // 输入token
    fireEvent.change(tokenInput, { target: { value: 'test-token-123' } })
    
    // 按钮应该被启用
    expect(connectButton).not.toBeDisabled()
  })

  it('应该显示使用说明', () => {
    render(<NotionSyncTab />)
    
    expect(screen.getByText('使用说明')).toBeInTheDocument()
    expect(screen.getByText('1. 获取API Token:')).toBeInTheDocument()
    expect(screen.getByText('2. 连接数据库:')).toBeInTheDocument()
    expect(screen.getByText('3. 配置同步:')).toBeInTheDocument()
    expect(screen.getByText('4. 开始同步:')).toBeInTheDocument()
  })

  it('应该显示教程链接', () => {
    render(<NotionSyncTab />)
    
    const tutorialLink = screen.getByRole('link', { name: /查看教程/i })
    expect(tutorialLink).toBeInTheDocument()
    expect(tutorialLink).toHaveAttribute('href', 'https://developers.notion.com/docs/getting-started')
    expect(tutorialLink).toHaveAttribute('target', '_blank')
  })

  it('应该处理连接过程', async () => {
    render(<NotionSyncTab />)
    
    const tokenInput = screen.getByLabelText('Notion API Token')
    const connectButton = screen.getByRole('button', { name: /连接Notion/i })
    
    // 输入token
    fireEvent.change(tokenInput, { target: { value: 'test-token-123' } })
    
    // 点击连接按钮
    fireEvent.click(connectButton)
    
    // 应该显示连接中状态
    expect(screen.getByText('连接中...')).toBeInTheDocument()
    
    // 等待连接完成
    await waitFor(() => {
      expect(screen.getByText('已连接到Notion')).toBeInTheDocument()
    }, { timeout: 3000 })
    
    // 应该显示同步设置
    expect(screen.getByText('同步设置')).toBeInTheDocument()
    expect(screen.getByText('已连接的数据库')).toBeInTheDocument()
  })
})