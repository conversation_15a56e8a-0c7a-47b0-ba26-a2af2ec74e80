#!/usr/bin/env node

/**
 * Popup 组件单元测试
 * 测试 Toggle 和 DetailedBookmarkForm 组件的功能
 */

import { fileURLToPath } from 'url'
import path from 'path'
import fs from 'fs'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

console.log('🧪 开始测试 Popup 组件...\n')

/**
 * 简单的测试框架
 */
class TestRunner {
  constructor() {
    this.tests = []
    this.passed = 0
    this.failed = 0
  }

  /**
   * 添加测试用例
   * @param {string} name - 测试名称
   * @param {Function} testFn - 测试函数
   */
  test(name, testFn) {
    this.tests.push({ name, testFn })
  }

  /**
   * 断言函数
   * @param {boolean} condition - 断言条件
   * @param {string} message - 错误消息
   */
  assert(condition, message) {
    if (!condition) {
      throw new Error(message)
    }
  }

  /**
   * 运行所有测试
   */
  async run() {
    console.log(`运行 ${this.tests.length} 个测试用例...\n`)

    for (const test of this.tests) {
      try {
        await test.testFn()
        console.log(`✅ ${test.name}`)
        this.passed++
      } catch (error) {
        console.log(`❌ ${test.name}: ${error.message}`)
        this.failed++
      }
    }

    console.log('\n' + '='.repeat(50))
    console.log(`测试结果: ${this.passed} 通过, ${this.failed} 失败`)

    if (this.failed > 0) {
      process.exit(1)
    }
  }
}

const runner = new TestRunner()

// 测试组件文件存在性
runner.test('组件文件存在性检查', () => {
  const componentFiles = [
    'src/popup/components/index.ts',
    'src/popup/components/Toggle.tsx',
    'src/popup/components/DetailedBookmarkForm.tsx'
  ]

  componentFiles.forEach(file => {
    const filePath = path.resolve(process.cwd(), file)
    runner.assert(
      fs.existsSync(filePath),
      `组件文件不存在: ${file}`
    )
  })
})

// 测试组件导出文件结构
runner.test('组件导出文件结构验证', () => {
  const indexPath = path.resolve(process.cwd(), 'src/popup/components/index.ts')
  const indexContent = fs.readFileSync(indexPath, 'utf8')

  // 检查 Toggle 组件导出
  runner.assert(
    indexContent.includes("export { default as Toggle } from './Toggle'"),
    '缺少 Toggle 组件导出'
  )

  // 检查 DetailedBookmarkForm 组件导出
  runner.assert(
    indexContent.includes("export { default as DetailedBookmarkForm } from './DetailedBookmarkForm'"),
    '缺少 DetailedBookmarkForm 组件导出'
  )
})

// 测试 Toggle 组件结构
runner.test('Toggle 组件结构验证', () => {
  const togglePath = path.resolve(process.cwd(), 'src/popup/components/Toggle.tsx')
  const toggleContent = fs.readFileSync(togglePath, 'utf8')

  // 检查接口定义
  runner.assert(
    toggleContent.includes('interface ToggleProps'),
    'Toggle 组件缺少 ToggleProps 接口定义'
  )

  // 检查必需属性
  runner.assert(
    toggleContent.includes('checked: boolean'),
    'Toggle 组件缺少 checked 属性定义'
  )

  runner.assert(
    toggleContent.includes('onChange: (checked: boolean) => void'),
    'Toggle 组件缺少 onChange 属性定义'
  )

  // 检查可选属性
  runner.assert(
    toggleContent.includes('disabled?: boolean'),
    'Toggle 组件缺少 disabled 可选属性'
  )

  runner.assert(
    toggleContent.includes("size?: 'sm' | 'md'"),
    'Toggle 组件缺少 size 可选属性'
  )

  // 检查组件导出
  runner.assert(
    toggleContent.includes('export default Toggle'),
    'Toggle 组件缺少默认导出'
  )
})

// 测试 DetailedBookmarkForm 组件结构
runner.test('DetailedBookmarkForm 组件结构验证', () => {
  const formPath = path.resolve(process.cwd(), 'src/popup/components/DetailedBookmarkForm.tsx')
  const formContent = fs.readFileSync(formPath, 'utf8')

  // 检查数据接口定义
  runner.assert(
    formContent.includes('interface BookmarkFormData'),
    'DetailedBookmarkForm 组件缺少 BookmarkFormData 接口定义'
  )

  // 检查组件属性接口
  runner.assert(
    formContent.includes('interface DetailedBookmarkFormProps'),
    'DetailedBookmarkForm 组件缺少 DetailedBookmarkFormProps 接口定义'
  )

  // 检查必需的表单字段
  const requiredFields = ['title', 'url', 'description', 'tags', 'category', 'notes']
  requiredFields.forEach(field => {
    runner.assert(
      formContent.includes(`${field}: string`) || formContent.includes(`${field}: string[]`),
      `DetailedBookmarkForm 组件缺少 ${field} 字段定义`
    )
  })

  // 检查回调函数定义
  runner.assert(
    formContent.includes('onSave: (data: BookmarkFormData) => Promise<void>'),
    'DetailedBookmarkForm 组件缺少 onSave 回调定义'
  )

  runner.assert(
    formContent.includes('onCancel: () => void'),
    'DetailedBookmarkForm 组件缺少 onCancel 回调定义'
  )

  // 检查组件导出
  runner.assert(
    formContent.includes('export default DetailedBookmarkForm'),
    'DetailedBookmarkForm 组件缺少默认导出'
  )
})

// 测试 Toggle 组件功能逻辑
runner.test('Toggle 组件功能逻辑验证', () => {
  const togglePath = path.resolve(process.cwd(), 'src/popup/components/Toggle.tsx')
  const toggleContent = fs.readFileSync(togglePath, 'utf8')

  // 检查尺寸配置
  runner.assert(
    toggleContent.includes('sizeClasses'),
    'Toggle 组件缺少尺寸配置'
  )

  // 检查尺寸选项
  runner.assert(
    toggleContent.includes('sm:') && toggleContent.includes('md:'),
    'Toggle 组件缺少完整的尺寸选项'
  )

  // 检查点击处理
  runner.assert(
    toggleContent.includes('onClick') || toggleContent.includes('handleClick'),
    'Toggle 组件缺少点击处理逻辑'
  )

  // 检查禁用状态处理
  runner.assert(
    toggleContent.includes('disabled') && toggleContent.includes('cursor-not-allowed'),
    'Toggle 组件缺少禁用状态处理'
  )

  // 检查无障碍属性
  runner.assert(
    toggleContent.includes('role="switch"') && toggleContent.includes('aria-checked'),
    'Toggle 组件缺少无障碍属性'
  )
})

// 测试 DetailedBookmarkForm 组件功能逻辑
runner.test('DetailedBookmarkForm 组件功能逻辑验证', () => {
  const formPath = path.resolve(process.cwd(), 'src/popup/components/DetailedBookmarkForm.tsx')
  const formContent = fs.readFileSync(formPath, 'utf8')

  // 检查状态管理
  runner.assert(
    formContent.includes('useForm<BookmarkFormData>') || formContent.includes('useState<BookmarkFormData>'),
    'DetailedBookmarkForm 组件缺少表单数据状态管理'
  )

  // 检查表单处理函数
  const requiredFunctions = [
    'handleAddTag',
    'handleRemoveTag',
    'handleSubmit'
  ]

  requiredFunctions.forEach(funcName => {
    runner.assert(
      formContent.includes(funcName),
      `DetailedBookmarkForm 组件缺少 ${funcName} 函数`
    )
  })

  // 检查AI功能
  runner.assert(
    formContent.includes('handleAIAssist') && formContent.includes('AI_GENERATE_SUGGESTIONS'),
    'DetailedBookmarkForm 组件缺少AI辅助功能'
  )

  // 检查标签管理
  runner.assert(
    formContent.includes('tagInput') && formContent.includes('suggestedTags'),
    'DetailedBookmarkForm 组件缺少标签管理功能'
  )

  // 检查键盘交互
  runner.assert(
    formContent.includes('handleTagInputKeyDown') && formContent.includes('onKeyDown'),
    'DetailedBookmarkForm 组件缺少键盘交互功能'
  )
})

// 测试组件样式和UI
runner.test('组件样式和UI验证', () => {
  const togglePath = path.resolve(process.cwd(), 'src/popup/components/Toggle.tsx')
  const formPath = path.resolve(process.cwd(), 'src/popup/components/DetailedBookmarkForm.tsx')
  
  const toggleContent = fs.readFileSync(togglePath, 'utf8')
  const formContent = fs.readFileSync(formPath, 'utf8')

  // 检查 Toggle 组件样式
  runner.assert(
    toggleContent.includes('bg-primary-600') && toggleContent.includes('bg-gray-200'),
    'Toggle 组件缺少正确的颜色样式'
  )

  runner.assert(
    toggleContent.includes('transition') && toggleContent.includes('duration'),
    'Toggle 组件缺少动画过渡效果'
  )

  // 检查 DetailedBookmarkForm 组件样式
  runner.assert(
    formContent.includes('input') && formContent.includes('textarea'),
    'DetailedBookmarkForm 组件缺少表单输入样式'
  )

  runner.assert(
    formContent.includes('lucide-react'),
    'DetailedBookmarkForm 组件缺少图标导入'
  )

  // 检查响应式设计
  runner.assert(
    formContent.includes('flex') && formContent.includes('space-'),
    'DetailedBookmarkForm 组件缺少响应式布局'
  )
})

// 测试组件集成
runner.test('组件集成验证', () => {
  const popupAppPath = path.resolve(process.cwd(), 'src/popup/PopupApp.tsx')
  
  if (fs.existsSync(popupAppPath)) {
    const popupContent = fs.readFileSync(popupAppPath, 'utf8')

    // 检查 DetailedBookmarkForm 组件导入和使用
    runner.assert(
      popupContent.includes("from './components/DetailedBookmarkForm'") || 
      popupContent.includes("from './components'"),
      'PopupApp 缺少 DetailedBookmarkForm 组件导入'
    )

    runner.assert(
      popupContent.includes('<DetailedBookmarkForm') && popupContent.includes('onSave=') && popupContent.includes('onCancel='),
      'PopupApp 中 DetailedBookmarkForm 组件使用不正确'
    )
  }
})

// 测试类型定义完整性
runner.test('TypeScript 类型定义验证', () => {
  const togglePath = path.resolve(process.cwd(), 'src/popup/components/Toggle.tsx')
  const formPath = path.resolve(process.cwd(), 'src/popup/components/DetailedBookmarkForm.tsx')
  
  const toggleContent = fs.readFileSync(togglePath, 'utf8')
  const formContent = fs.readFileSync(formPath, 'utf8')

  // 检查 Toggle 组件类型定义
  runner.assert(
    toggleContent.includes('React.FC<ToggleProps>'),
    'Toggle 组件缺少正确的 TypeScript 类型定义'
  )

  // 检查 DetailedBookmarkForm 组件类型定义
  runner.assert(
    formContent.includes('React.FC<DetailedBookmarkFormProps>'),
    'DetailedBookmarkForm 组件缺少正确的 TypeScript 类型定义'
  )

  // 检查事件处理函数类型
  runner.assert(
    toggleContent.includes('(checked: boolean) => void'),
    'Toggle 组件事件处理函数类型定义不正确'
  )

  runner.assert(
    formContent.includes('Promise<void>'),
    'DetailedBookmarkForm 组件异步函数类型定义不正确'
  )
})

// 测试错误处理
runner.test('组件错误处理验证', () => {
  const formPath = path.resolve(process.cwd(), 'src/popup/components/DetailedBookmarkForm.tsx')
  const formContent = fs.readFileSync(formPath, 'utf8')

  // 检查 try-catch 错误处理
  runner.assert(
    formContent.includes('try {') && formContent.includes('catch (error)'),
    'DetailedBookmarkForm 组件缺少错误处理机制'
  )

  // 检查表单验证
  runner.assert(
    formContent.includes('required') || formContent.includes('trim()'),
    'DetailedBookmarkForm 组件缺少表单验证'
  )

  // 检查加载状态处理
  runner.assert(
    formContent.includes('loading') && formContent.includes('disabled'),
    'DetailedBookmarkForm 组件缺少加载状态处理'
  )
})

// 测试文档完整性
runner.test('组件文档完整性验证', () => {
  const docPath = path.resolve(process.cwd(), 'docs/popup-components-api.md')
  
  runner.assert(
    fs.existsSync(docPath),
    '缺少组件API文档文件'
  )

  const docContent = fs.readFileSync(docPath, 'utf8')

  // 检查文档结构
  runner.assert(
    docContent.includes('# Popup 组件 API 文档'),
    '文档缺少标题'
  )

  // 检查组件文档
  runner.assert(
    docContent.includes('## Toggle 组件') || docContent.includes('### 1. Toggle 组件'),
    '文档缺少 Toggle 组件说明'
  )

  runner.assert(
    docContent.includes('## DetailedBookmarkForm 组件') || docContent.includes('### 2. DetailedBookmarkForm 组件'),
    '文档缺少 DetailedBookmarkForm 组件说明'
  )

  // 检查使用示例
  runner.assert(
    docContent.includes('使用示例') && docContent.includes('```typescript'),
    '文档缺少使用示例'
  )

  // 检查API参考
  runner.assert(
    docContent.includes('参数说明') && docContent.includes('接口定义'),
    '文档缺少API参考信息'
  )
})

// 运行所有测试
runner.run().catch(error => {
  console.error('测试运行失败:', error)
  process.exit(1)
})