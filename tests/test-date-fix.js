// 测试日期处理修复
// 这个脚本用于验证BookmarkRow和BookmarkCompact组件的日期处理修复

console.log('🧪 测试日期处理修复...')

// 模拟不同格式的日期数据
const testDates = [
  new Date(), // Date对象
  new Date().toISOString(), // ISO字符串
  '2024-01-15T10:30:00.000Z', // ISO字符串
  '2024-01-15', // 日期字符串
  'invalid-date', // 无效日期
  null, // null值
  undefined // undefined值
]

// 模拟formatTime函数（从组件中提取的逻辑）
const formatTime = (date) => {
  // 确保日期是Date对象
  const dateObj = typeof date === 'string' ? new Date(date) : date
  
  // 检查日期是否有效
  if (!dateObj || isNaN(dateObj.getTime())) {
    return '未知时间'
  }
  
  const now = new Date()
  const diffMs = now.getTime() - dateObj.getTime()
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))
  
  if (diffDays === 0) {
    return '今天'
  } else if (diffDays === 1) {
    return '昨天'
  } else if (diffDays < 7) {
    return `${diffDays}天前`
  } else {
    return dateObj.toLocaleDateString('zh-CN', { 
      month: 'short', 
      day: 'numeric' 
    })
  }
}

console.log('\n📅 测试不同日期格式的处理:')
testDates.forEach((date, index) => {
  try {
    const result = formatTime(date)
    console.log(`✅ 测试 ${index + 1}: ${JSON.stringify(date)} -> "${result}"`)
  } catch (error) {
    console.log(`❌ 测试 ${index + 1}: ${JSON.stringify(date)} -> 错误: ${error.message}`)
  }
})

console.log('\n🎉 日期处理修复测试完成！')
console.log('\n📝 修复说明:')
console.log('- 修复了 x.getTime is not a function 错误')
console.log('- 现在可以正确处理字符串格式的日期')
console.log('- 添加了日期有效性检查')
console.log('- 对无效日期显示"未知时间"')