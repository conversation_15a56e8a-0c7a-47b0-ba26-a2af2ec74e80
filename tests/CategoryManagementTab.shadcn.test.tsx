// CategoryManagementTab shadcn重构测试

import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { vi, describe, it, expect, beforeEach } from 'vitest'
import CategoryManagementTab from '../src/components/CategoryManagementTab'
import { categoryService } from '../src/services/categoryService'

// Mock categoryService
vi.mock('../src/services/categoryService', () => ({
  categoryService: {
    getAllCategoriesWithStats: vi.fn(),
    createCategory: vi.fn(),
    updateCategory: vi.fn(),
    deleteCategory: vi.fn()
  }
}))

// Mock CategoryList组件
vi.mock('../src/components/CategoryList', () => ({
  default: ({ categories, loading, onCategoryEdit, onCategoryDelete, onCreateCategory }: any) => (
    <div data-testid="category-list">
      {loading ? (
        <div>Loading categories...</div>
      ) : (
        <div>
          <div>Categories: {categories.length}</div>
          <button onClick={() => onCreateCategory()}>Create Category</button>
          {categories.map((cat: any) => (
            <div key={cat.id}>
              <span>{cat.name}</span>
              <button onClick={() => onCategoryEdit(cat)}>Edit</button>
              <button onClick={() => onCategoryDelete(cat)}>Delete</button>
            </div>
          ))}
        </div>
      )}
    </div>
  )
}))

// Mock CategoryModal组件
vi.mock('../src/components/CategoryModal', () => ({
  default: ({ isOpen, type, onSave, onDelete, onClose }: any) => (
    isOpen ? (
      <div data-testid="category-modal">
        <div>Modal Type: {type}</div>
        <button onClick={() => onSave && onSave({ name: 'Test Category' })}>Save</button>
        <button onClick={() => onDelete && onDelete()}>Delete</button>
        <button onClick={onClose}>Close</button>
      </div>
    ) : null
  )
}))

describe('CategoryManagementTab shadcn重构测试', () => {
  const mockCategories = [
    { id: '1', name: '工作', description: '工作相关', bookmarkCount: 5 },
    { id: '2', name: '学习', description: '学习资料', bookmarkCount: 3 }
  ]

  beforeEach(() => {
    vi.clearAllMocks()
    vi.mocked(categoryService.getAllCategoriesWithStats).mockResolvedValue(mockCategories)
  })

  it('应该使用shadcn Card组件渲染头部区域', async () => {
    render(<CategoryManagementTab />)
    
    await waitFor(() => {
      expect(screen.getByText('分类管理')).toBeInTheDocument()
      expect(screen.getByText('管理您的书签分类，更好地组织收藏内容')).toBeInTheDocument()
    })
  })

  it('应该使用shadcn Button组件渲染操作按钮', async () => {
    render(<CategoryManagementTab />)
    
    await waitFor(() => {
      const refreshButton = screen.getByRole('button', { name: /刷新/ })
      const createButton = screen.getByRole('button', { name: /新建分类/ })
      
      expect(refreshButton).toBeInTheDocument()
      expect(createButton).toBeInTheDocument()
    })
  })

  it('应该正确处理加载状态', () => {
    vi.mocked(categoryService.getAllCategoriesWithStats).mockImplementation(
      () => new Promise(() => {}) // 永不resolve，保持loading状态
    )
    
    render(<CategoryManagementTab />)
    
    expect(screen.getByText('Loading categories...')).toBeInTheDocument()
  })

  it('应该使用shadcn Card组件显示错误状态', async () => {
    const errorMessage = '加载分类失败'
    vi.mocked(categoryService.getAllCategoriesWithStats).mockRejectedValue(new Error(errorMessage))
    
    render(<CategoryManagementTab />)
    
    await waitFor(() => {
      expect(screen.getByText('加载失败')).toBeInTheDocument()
      expect(screen.getByText(errorMessage)).toBeInTheDocument()
      expect(screen.getByRole('button', { name: /重试/ })).toBeInTheDocument()
    })
  })

  it('应该正确处理创建分类操作', async () => {
    render(<CategoryManagementTab />)
    
    await waitFor(() => {
      const createButton = screen.getByRole('button', { name: /新建分类/ })
      fireEvent.click(createButton)
    })
    
    expect(screen.getByTestId('category-modal')).toBeInTheDocument()
    expect(screen.getByText('Modal Type: create')).toBeInTheDocument()
  })

  it('应该正确处理编辑分类操作', async () => {
    render(<CategoryManagementTab />)
    
    await waitFor(() => {
      const editButton = screen.getByText('Edit')
      fireEvent.click(editButton)
    })
    
    expect(screen.getByTestId('category-modal')).toBeInTheDocument()
    expect(screen.getByText('Modal Type: edit')).toBeInTheDocument()
  })

  it('应该正确处理删除分类操作', async () => {
    render(<CategoryManagementTab />)
    
    await waitFor(() => {
      const deleteButton = screen.getByText('Delete')
      fireEvent.click(deleteButton)
    })
    
    expect(screen.getByTestId('category-modal')).toBeInTheDocument()
    expect(screen.getByText('Modal Type: delete')).toBeInTheDocument()
  })

  it('应该正确处理分类保存', async () => {
    vi.mocked(categoryService.createCategory).mockResolvedValue({
      id: '3',
      name: 'Test Category',
      description: '',
      createdAt: new Date(),
      updatedAt: new Date()
    })
    
    render(<CategoryManagementTab />)
    
    await waitFor(() => {
      const createButton = screen.getByRole('button', { name: /新建分类/ })
      fireEvent.click(createButton)
    })
    
    const saveButton = screen.getByText('Save')
    fireEvent.click(saveButton)
    
    await waitFor(() => {
      expect(categoryService.createCategory).toHaveBeenCalledWith({ name: 'Test Category' })
    })
  })

  it('应该正确处理刷新操作', async () => {
    render(<CategoryManagementTab />)
    
    await waitFor(() => {
      const refreshButton = screen.getByRole('button', { name: /刷新/ })
      fireEvent.click(refreshButton)
    })
    
    // 应该再次调用getAllCategoriesWithStats
    expect(categoryService.getAllCategoriesWithStats).toHaveBeenCalledTimes(2)
  })

  it('应该正确应用自定义className', async () => {
    const customClass = 'custom-category-management'
    const { container } = render(<CategoryManagementTab className={customClass} />)
    
    await waitFor(() => {
      expect(container.firstChild).toHaveClass(customClass)
    })
  })

  it('应该正确处理模态窗口关闭', async () => {
    render(<CategoryManagementTab />)
    
    await waitFor(() => {
      const createButton = screen.getByRole('button', { name: /新建分类/ })
      fireEvent.click(createButton)
    })
    
    const closeButton = screen.getByText('Close')
    fireEvent.click(closeButton)
    
    await waitFor(() => {
      expect(screen.queryByTestId('category-modal')).not.toBeInTheDocument()
    })
  })
})