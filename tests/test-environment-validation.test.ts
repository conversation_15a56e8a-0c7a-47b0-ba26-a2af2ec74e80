/**
 * 测试环境验证
 * 验证测试环境配置是否正确工作
 */

import { describe, it, expect, vi, beforeEach } from 'vitest'
import { mockChrome } from './setup/chrome-api-mock'
import { styleTestHelpers } from './setup/style-test-fixes'

describe('测试环境配置验证', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('Chrome API 模拟验证', () => {
    it('应该正确模拟 chrome.runtime API', () => {
      expect(global.chrome).toBeDefined()
      expect(global.chrome.runtime).toBeDefined()
      expect(global.chrome.runtime.sendMessage).toBeDefined()
      expect(global.chrome.runtime.getManifest).toBeDefined()
      expect(global.chrome.runtime.getURL).toBeDefined()
    })

    it('应该正确模拟 chrome.storage API', () => {
      expect(global.chrome.storage).toBeDefined()
      expect(global.chrome.storage.sync).toBeDefined()
      expect(global.chrome.storage.local).toBeDefined()
      expect(global.chrome.storage.sync.get).toBeDefined()
      expect(global.chrome.storage.sync.set).toBeDefined()
    })

    it('应该正确模拟 chrome.tabs API', () => {
      expect(global.chrome.tabs).toBeDefined()
      expect(global.chrome.tabs.query).toBeDefined()
      expect(global.chrome.tabs.get).toBeDefined()
      expect(global.chrome.tabs.create).toBeDefined()
    })

    it('应该正确模拟 chrome.action API', () => {
      expect(global.chrome.action).toBeDefined()
      expect(global.chrome.action.setBadgeText).toBeDefined()
      expect(global.chrome.action.setBadgeBackgroundColor).toBeDefined()
      expect(global.chrome.action.setTitle).toBeDefined()
    })

    it('chrome.runtime.sendMessage 应该返回正确的默认值', async () => {
      const result = await global.chrome.runtime.sendMessage({ type: 'TEST' })
      expect(result).toEqual({ success: true, data: [] })
    })

    it('chrome.storage.sync.get 应该返回正确的默认值', async () => {
      const result = await global.chrome.storage.sync.get('appSettings')
      expect(result).toHaveProperty('appSettings')
      expect(result.appSettings).toHaveProperty('autoTagging', true)
      expect(result.appSettings).toHaveProperty('theme', 'system')
    })
  })

  describe('浏览器 API 模拟验证', () => {
    it('应该正确模拟 window.matchMedia', () => {
      expect(window.matchMedia).toBeDefined()
      
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
      expect(mediaQuery).toHaveProperty('matches')
      expect(mediaQuery).toHaveProperty('addEventListener')
      expect(mediaQuery).toHaveProperty('removeEventListener')
    })

    it('应该正确模拟 localStorage', () => {
      expect(window.localStorage).toBeDefined()
      expect(window.localStorage.getItem).toBeDefined()
      expect(window.localStorage.setItem).toBeDefined()
      expect(window.localStorage.removeItem).toBeDefined()
      expect(window.localStorage.clear).toBeDefined()
    })

    it('应该正确模拟 ResizeObserver', () => {
      expect(global.ResizeObserver).toBeDefined()
      
      const observer = new ResizeObserver(() => {})
      expect(observer.observe).toBeDefined()
      expect(observer.unobserve).toBeDefined()
      expect(observer.disconnect).toBeDefined()
    })

    it('应该正确模拟 IntersectionObserver', () => {
      expect(global.IntersectionObserver).toBeDefined()
      
      const observer = new IntersectionObserver(() => {})
      expect(observer.observe).toBeDefined()
      expect(observer.unobserve).toBeDefined()
      expect(observer.disconnect).toBeDefined()
    })
  })

  describe('样式测试工具验证', () => {
    let testElement: HTMLElement

    beforeEach(() => {
      testElement = document.createElement('button')
      document.body.appendChild(testElement)
    })

    afterEach(() => {
      document.body.removeChild(testElement)
    })

    it('应该正确验证shadcn按钮样式', () => {
      testElement.className = 'bg-primary text-primary-foreground hover:bg-primary/90'
      
      expect(() => {
        styleTestHelpers.expectShadcnButton(testElement, 'primary')
      }).not.toThrow()
    })

    it('应该正确验证shadcn文本颜色', () => {
      testElement.className = 'text-foreground'
      
      expect(() => {
        styleTestHelpers.expectShadcnTextColor(testElement, 'foreground')
      }).not.toThrow()
    })

    it('应该正确验证shadcn背景色', () => {
      testElement.className = 'bg-background'
      
      expect(() => {
        styleTestHelpers.expectShadcnBackground(testElement, 'background')
      }).not.toThrow()
    })
  })

  describe('DOM 环境验证', () => {
    it('应该有正确的document对象', () => {
      expect(document).toBeDefined()
      expect(document.createElement).toBeDefined()
      expect(document.querySelector).toBeDefined()
      expect(document.querySelectorAll).toBeDefined()
    })

    it('应该有正确的window对象', () => {
      expect(window).toBeDefined()
      expect(window.location).toBeDefined()
      expect(window.history).toBeDefined()
    })

    it('应该能够创建和操作DOM元素', () => {
      const div = document.createElement('div')
      div.textContent = '测试内容'
      div.className = 'test-class'
      
      expect(div.textContent).toBe('测试内容')
      expect(div.className).toBe('test-class')
      expect(div.classList.contains('test-class')).toBe(true)
    })
  })

  describe('测试工具验证', () => {
    it('应该正确模拟函数', () => {
      const mockFn = vi.fn()
      mockFn('test')
      
      expect(mockFn).toHaveBeenCalledWith('test')
      expect(mockFn).toHaveBeenCalledTimes(1)
    })

    it('应该正确模拟异步函数', async () => {
      const mockAsyncFn = vi.fn().mockResolvedValue('success')
      const result = await mockAsyncFn()
      
      expect(result).toBe('success')
      expect(mockAsyncFn).toHaveBeenCalledTimes(1)
    })

    it('应该正确清理模拟', () => {
      const mockFn = vi.fn()
      mockFn('test')
      
      expect(mockFn).toHaveBeenCalledTimes(1)
      
      vi.clearAllMocks()
      expect(mockFn).toHaveBeenCalledTimes(0)
    })
  })

  describe('错误处理验证', () => {
    it('应该正确处理Chrome API错误', async () => {
      // 模拟API错误
      const mockError = vi.fn().mockRejectedValue(new Error('API Error'))
      global.chrome.runtime.sendMessage = mockError
      
      try {
        await global.chrome.runtime.sendMessage({ type: 'TEST' })
      } catch (error) {
        expect(error).toBeInstanceOf(Error)
        expect((error as Error).message).toBe('API Error')
      }
    })

    it('应该正确处理DOM操作错误', () => {
      expect(() => {
        const nonExistentElement = document.querySelector('#non-existent')
        expect(nonExistentElement).toBeNull()
      }).not.toThrow()
    })
  })
})