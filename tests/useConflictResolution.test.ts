// 冲突解决 Hook 单元测试

import { renderHook, act } from '@testing-library/react'
import { describe, it, expect, vi, beforeEach } from 'vitest'
import { useConflictResolution } from '../src/hooks/useConflictResolution'
import { ConflictItem } from '../src/types'

// 模拟测试数据
const mockConflicts: ConflictItem[] = [
  {
    id: 'conflict-1',
    type: 'bookmark',
    conflictType: 'duplicate',
    existingData: {
      id: 'existing-1',
      title: '现有收藏',
      url: 'https://example.com'
    },
    importData: {
      title: '导入收藏',
      url: 'https://example.com'
    },
    conflictFields: ['title'],
    similarity: 0.9
  },
  {
    id: 'conflict-2',
    type: 'category',
    conflictType: 'name_conflict',
    existingData: {
      id: 'existing-2',
      name: '技术'
    },
    importData: {
      name: '技术'
    },
    conflictFields: ['name'],
    similarity: 1.0
  }
]

describe('useConflictResolution', () => {
  const mockOnResolve = vi.fn()

  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('应该正确初始化状态', () => {
    const { result } = renderHook(() => 
      useConflictResolution(mockConflicts, mockOnResolve)
    )

    expect(result.current.resolutions).toEqual([])
    expect(result.current.currentConflictIndex).toBe(0)
    expect(result.current.editingConflictId).toBeNull()
    expect(result.current.editedData).toEqual({})
    expect(result.current.showBatchActions).toBe(false)
    expect(result.current.currentConflict).toEqual(mockConflicts[0])
    expect(result.current.currentResolution).toBeUndefined()
  })

  it('应该正确计算统计信息', () => {
    const { result } = renderHook(() => 
      useConflictResolution(mockConflicts, mockOnResolve)
    )

    expect(result.current.stats).toEqual({
      resolved: 0,
      total: 2,
      byType: {
        bookmark: 1,
        category: 1
      }
    })
  })

  it('应该能够处理单个冲突解决', () => {
    const { result } = renderHook(() => 
      useConflictResolution(mockConflicts, mockOnResolve)
    )

    act(() => {
      result.current.handleSingleResolution('conflict-1', 'keep_existing')
    })

    expect(result.current.resolutions).toHaveLength(1)
    expect(result.current.resolutions[0]).toEqual({
      conflictId: 'conflict-1',
      action: 'keep_existing'
    })
    expect(result.current.stats.resolved).toBe(1)
  })

  it('应该能够更新现有的解决方案', () => {
    const { result } = renderHook(() => 
      useConflictResolution(mockConflicts, mockOnResolve)
    )

    // 先添加一个解决方案
    act(() => {
      result.current.handleSingleResolution('conflict-1', 'keep_existing')
    })

    // 更新解决方案
    act(() => {
      result.current.handleSingleResolution('conflict-1', 'use_imported')
    })

    expect(result.current.resolutions).toHaveLength(1)
    expect(result.current.resolutions[0].action).toBe('use_imported')
  })

  it('应该能够自动跳转到下一个未解决的冲突', () => {
    const { result } = renderHook(() => 
      useConflictResolution(mockConflicts, mockOnResolve)
    )

    expect(result.current.currentConflictIndex).toBe(0)

    act(() => {
      result.current.handleSingleResolution('conflict-1', 'keep_existing')
    })

    expect(result.current.currentConflictIndex).toBe(1)
  })

  it('应该能够处理批量操作', () => {
    const { result } = renderHook(() => 
      useConflictResolution(mockConflicts, mockOnResolve)
    )

    act(() => {
      result.current.handleBatchResolution('keep_existing')
    })

    expect(result.current.resolutions).toHaveLength(2)
    expect(result.current.resolutions.every(r => r.action === 'keep_existing')).toBe(true)
    expect(result.current.showBatchActions).toBe(false)
  })

  it('应该能够处理手动编辑', () => {
    const { result } = renderHook(() => 
      useConflictResolution(mockConflicts, mockOnResolve)
    )

    act(() => {
      result.current.handleManualEdit('conflict-1')
    })

    expect(result.current.editingConflictId).toBe('conflict-1')
    expect(result.current.editedData).toEqual(mockConflicts[0].importData)
  })

  it('应该能够保存手动编辑', () => {
    const { result } = renderHook(() => 
      useConflictResolution(mockConflicts, mockOnResolve)
    )

    // 开始手动编辑
    act(() => {
      result.current.handleManualEdit('conflict-1')
    })

    // 修改数据
    act(() => {
      result.current.setEditedData({ title: '修改后的标题' })
    })

    // 保存编辑
    act(() => {
      result.current.handleSaveManualEdit()
    })

    expect(result.current.resolutions).toHaveLength(1)
    expect(result.current.resolutions[0]).toEqual({
      conflictId: 'conflict-1',
      action: 'manual_edit',
      manualData: { title: '修改后的标题' }
    })
    expect(result.current.editingConflictId).toBeNull()
    expect(result.current.editedData).toEqual({})
  })

  it('应该能够取消手动编辑', () => {
    const { result } = renderHook(() => 
      useConflictResolution(mockConflicts, mockOnResolve)
    )

    // 开始手动编辑
    act(() => {
      result.current.handleManualEdit('conflict-1')
    })

    // 取消编辑
    act(() => {
      result.current.handleCancelManualEdit()
    })

    expect(result.current.editingConflictId).toBeNull()
    expect(result.current.editedData).toEqual({})
  })

  it('应该能够导航到指定冲突', () => {
    const { result } = renderHook(() => 
      useConflictResolution(mockConflicts, mockOnResolve)
    )

    act(() => {
      result.current.navigateToConflict(1)
    })

    expect(result.current.currentConflictIndex).toBe(1)
    expect(result.current.currentConflict).toEqual(mockConflicts[1])
  })

  it('应该能够完成解决并调用回调', () => {
    const { result } = renderHook(() => 
      useConflictResolution(mockConflicts, mockOnResolve)
    )

    // 解决所有冲突
    act(() => {
      result.current.handleSingleResolution('conflict-1', 'keep_existing')
      result.current.handleSingleResolution('conflict-2', 'use_imported')
    })

    // 完成解决
    let completed = false
    act(() => {
      completed = result.current.handleComplete()
    })

    expect(completed).toBe(true)
    expect(mockOnResolve).toHaveBeenCalledWith(result.current.resolutions)
  })

  it('当未解决所有冲突时不应该完成', () => {
    const { result } = renderHook(() => 
      useConflictResolution(mockConflicts, mockOnResolve)
    )

    // 只解决一个冲突
    act(() => {
      result.current.handleSingleResolution('conflict-1', 'keep_existing')
    })

    // 尝试完成解决
    let completed = false
    act(() => {
      completed = result.current.handleComplete()
    })

    expect(completed).toBe(false)
    expect(mockOnResolve).not.toHaveBeenCalled()
  })

  it('应该能够重置所有状态', () => {
    const { result } = renderHook(() => 
      useConflictResolution(mockConflicts, mockOnResolve)
    )

    // 设置一些状态
    act(() => {
      result.current.handleSingleResolution('conflict-1', 'keep_existing')
      result.current.navigateToConflict(1)
      result.current.handleManualEdit('conflict-2')
    })

    // 重置状态
    act(() => {
      result.current.handleResetAll()
    })

    expect(result.current.resolutions).toEqual([])
    expect(result.current.currentConflictIndex).toBe(0)
    expect(result.current.editingConflictId).toBeNull()
    expect(result.current.editedData).toEqual({})
  })

  it('应该能够切换批量操作面板', () => {
    const { result } = renderHook(() => 
      useConflictResolution(mockConflicts, mockOnResolve)
    )

    act(() => {
      result.current.setShowBatchActions(true)
    })

    expect(result.current.showBatchActions).toBe(true)

    act(() => {
      result.current.setShowBatchActions(false)
    })

    expect(result.current.showBatchActions).toBe(false)
  })
})