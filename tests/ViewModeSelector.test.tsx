import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { describe, it, expect, beforeEach, vi } from 'vitest'
import ViewModeSelector, { type ViewMode } from '../src/components/ViewModeSelector'

// Mock localStorage
const mockLocalStorage = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn()
}

Object.defineProperty(window, 'localStorage', {
  value: mockLocalStorage
})

describe('ViewModeSelector', () => {
  const mockOnModeChange = vi.fn()

  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('应该渲染所有视图模式按钮', () => {
    render(
      <ViewModeSelector 
        currentMode="card" 
        onModeChange={mockOnModeChange} 
      />
    )

    // 检查所有三个视图模式按钮是否存在
    expect(screen.getByLabelText('切换到卡片视图')).toBeInTheDocument()
    expect(screen.getByLabelText('切换到行视图')).toBeInTheDocument()
    expect(screen.getByLabelText('切换到紧凑视图')).toBeInTheDocument()
  })

  it('应该正确显示当前活动模式', () => {
    render(
      <ViewModeSelector 
        currentMode="row" 
        onModeChange={mockOnModeChange} 
      />
    )

    const rowButton = screen.getByLabelText('切换到行视图')
    const cardButton = screen.getByLabelText('切换到卡片视图')

    // 检查活动状态
    expect(rowButton).toHaveAttribute('aria-pressed', 'true')
    expect(cardButton).toHaveAttribute('aria-pressed', 'false')

    // 检查样式类
    expect(rowButton).toHaveClass('bg-white', 'text-primary-600')
    expect(cardButton).toHaveClass('text-gray-600')
  })

  it('应该在点击时切换视图模式', async () => {
    render(
      <ViewModeSelector 
        currentMode="card" 
        onModeChange={mockOnModeChange} 
      />
    )

    const compactButton = screen.getByLabelText('切换到紧凑视图')
    fireEvent.click(compactButton)

    // 检查回调是否被调用
    expect(mockOnModeChange).toHaveBeenCalledWith('compact')
    
    // 检查本地存储是否被更新
    expect(mockLocalStorage.setItem).toHaveBeenCalledWith('bookmark-view-mode', 'compact')
  })

  it('不应该在点击当前活动模式时触发回调', () => {
    render(
      <ViewModeSelector 
        currentMode="card" 
        onModeChange={mockOnModeChange} 
      />
    )

    const cardButton = screen.getByLabelText('切换到卡片视图')
    fireEvent.click(cardButton)

    // 不应该触发回调，因为已经是当前模式
    expect(mockOnModeChange).not.toHaveBeenCalled()
    expect(mockLocalStorage.setItem).not.toHaveBeenCalled()
  })

  it('应该显示正确的工具提示', () => {
    render(
      <ViewModeSelector 
        currentMode="card" 
        onModeChange={mockOnModeChange} 
      />
    )

    expect(screen.getByTitle('详细卡片布局，显示完整信息')).toBeInTheDocument()
    expect(screen.getByTitle('单行文字视图，仅显示标题和URL')).toBeInTheDocument()
    expect(screen.getByTitle('紧凑多行布局，信息密集显示')).toBeInTheDocument()
  })

  it('应该支持自定义className', () => {
    const { container } = render(
      <ViewModeSelector 
        currentMode="card" 
        onModeChange={mockOnModeChange}
        className="custom-class"
      />
    )

    const selectorContainer = container.firstChild as HTMLElement
    expect(selectorContainer).toHaveClass('custom-class')
  })

  it('应该在不同视图模式间正确切换', async () => {
    const { rerender } = render(
      <ViewModeSelector 
        currentMode="card" 
        onModeChange={mockOnModeChange} 
      />
    )

    // 切换到行视图
    const rowButton = screen.getByLabelText('切换到行视图')
    fireEvent.click(rowButton)

    expect(mockOnModeChange).toHaveBeenCalledWith('row')

    // 重新渲染为行视图
    rerender(
      <ViewModeSelector 
        currentMode="row" 
        onModeChange={mockOnModeChange} 
      />
    )

    // 检查新的活动状态
    expect(rowButton).toHaveAttribute('aria-pressed', 'true')
    expect(screen.getByLabelText('切换到卡片视图')).toHaveAttribute('aria-pressed', 'false')
  })

  it('应该具有正确的可访问性属性', () => {
    render(
      <ViewModeSelector 
        currentMode="card" 
        onModeChange={mockOnModeChange} 
      />
    )

    const buttons = screen.getAllByRole('button')
    
    buttons.forEach(button => {
      expect(button).toHaveAttribute('type', 'button')
      expect(button).toHaveAttribute('aria-label')
      expect(button).toHaveAttribute('aria-pressed')
      expect(button).toHaveAttribute('title')
    })
  })

  it('应该在hover时显示正确的样式', () => {
    render(
      <ViewModeSelector 
        currentMode="card" 
        onModeChange={mockOnModeChange} 
      />
    )

    const rowButton = screen.getByLabelText('切换到行视图')
    
    // 检查hover样式类是否存在
    expect(rowButton).toHaveClass('hover:text-gray-900', 'hover:bg-gray-50')
  })

  it('应该正确处理所有视图模式类型', () => {
    const modes: ViewMode[] = ['card', 'row', 'compact']
    
    modes.forEach(mode => {
      const { rerender } = render(
        <ViewModeSelector 
          currentMode={mode} 
          onModeChange={mockOnModeChange} 
        />
      )

      const activeButton = screen.getByLabelText(`切换到${
        mode === 'card' ? '卡片视图' : 
        mode === 'row' ? '行视图' : '紧凑视图'
      }`)
      
      expect(activeButton).toHaveAttribute('aria-pressed', 'true')
      
      // 清理
      rerender(<div />)
    })
  })

  it('应该在移动端隐藏文本标签', () => {
    render(
      <ViewModeSelector 
        currentMode="card" 
        onModeChange={mockOnModeChange} 
      />
    )

    const textLabels = screen.getAllByText(/视图$/)
    
    textLabels.forEach(label => {
      expect(label).toHaveClass('hidden', 'sm:inline')
    })
  })
})