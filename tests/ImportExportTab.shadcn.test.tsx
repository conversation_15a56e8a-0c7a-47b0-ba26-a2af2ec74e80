// ImportExportTab shadcn重构测试

import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { vi, describe, it, expect, beforeEach } from 'vitest'
import ImportExportTab from '../src/components/ImportExportTab'

// Mock importExportManagerService
const mockImportExportManagerService = {
  exportBookmarks: vi.fn(),
  exportAllData: vi.fn(),
  exportCategories: vi.fn(),
  exportTags: vi.fn(),
  importData: vi.fn(),
  importDataWithResolutions: vi.fn()
}

vi.mock('../src/services/BookmarkImportExportService', () => ({
  importExportManagerService: mockImportExportManagerService
}))

// Mock ConflictResolutionDialog组件
vi.mock('../src/components/ConflictResolutionDialog', () => ({
  default: ({ isOpen, onResolve, onCancel }: any) => (
    isOpen ? (
      <div data-testid="conflict-dialog">
        <button onClick={() => onResolve([])}>Resolve</button>
        <button onClick={onCancel}>Cancel</button>
      </div>
    ) : null
  )
}))

describe('ImportExportTab shadcn重构测试', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('应该使用shadcn Card组件渲染页面标题', () => {
    render(<ImportExportTab />)
    
    expect(screen.getByText('导入导出')).toBeInTheDocument()
    expect(screen.getByText('导出您的收藏数据或从其他来源导入收藏')).toBeInTheDocument()
  })

  it('应该使用shadcn Card组件渲染导出和导入部分', () => {
    render(<ImportExportTab />)
    
    expect(screen.getByText('导出数据')).toBeInTheDocument()
    expect(screen.getByText('导入收藏')).toBeInTheDocument()
  })

  it('应该使用shadcn Button组件渲染导出类型选择', () => {
    render(<ImportExportTab />)
    
    expect(screen.getByRole('button', { name: /全部数据/ })).toBeInTheDocument()
    expect(screen.getByRole('button', { name: /收藏数据/ })).toBeInTheDocument()
    expect(screen.getByRole('button', { name: /分类数据/ })).toBeInTheDocument()
    expect(screen.getByRole('button', { name: /标签数据/ })).toBeInTheDocument()
  })

  it('应该使用shadcn Select组件渲染导入来源选择', () => {
    render(<ImportExportTab />)
    
    // Select组件应该存在
    const selectTrigger = screen.getByRole('combobox')
    expect(selectTrigger).toBeInTheDocument()
  })

  it('应该使用shadcn Checkbox组件渲染导出选项', () => {
    render(<ImportExportTab />)
    
    // 默认选择收藏数据导出类型，应该显示相关选项
    expect(screen.getByLabelText('包含收藏内容')).toBeInTheDocument()
    expect(screen.getByLabelText('包含元数据')).toBeInTheDocument()
  })

  it('应该正确处理导出类型切换', () => {
    render(<ImportExportTab />)
    
    // 切换到全部数据导出
    const allDataButton = screen.getByRole('button', { name: /全部数据/ })
    fireEvent.click(allDataButton)
    
    // 应该显示全部数据导出选项
    expect(screen.getByLabelText('包含收藏数据')).toBeInTheDocument()
    expect(screen.getByLabelText('包含分类数据')).toBeInTheDocument()
    expect(screen.getByLabelText('包含标签数据')).toBeInTheDocument()
  })

  it('应该使用shadcn Input组件渲染文件选择和日期输入', () => {
    render(<ImportExportTab />)
    
    // 文件选择输入
    expect(screen.getByLabelText('选择文件')).toBeInTheDocument()
    
    // 日期范围输入
    const dateInputs = screen.getAllByDisplayValue('')
    expect(dateInputs.length).toBeGreaterThan(0)
  })

  it('应该使用shadcn Button组件渲染导出和导入按钮', () => {
    render(<ImportExportTab />)
    
    expect(screen.getByRole('button', { name: /开始导出/ })).toBeInTheDocument()
    expect(screen.getByRole('button', { name: /开始导入/ })).toBeInTheDocument()
  })

  it('应该正确处理导出操作', async () => {
    const mockExportResult = {
      data: 'export data',
      filename: 'bookmarks.json',
      format: 'json' as const,
      itemCount: 10
    }
    
    mockImportExportManagerService.exportBookmarks.mockResolvedValue(mockExportResult)
    
    // Mock URL.createObjectURL和相关DOM操作
    global.URL.createObjectURL = vi.fn(() => 'blob:url')
    global.URL.revokeObjectURL = vi.fn()
    
    const mockAppendChild = vi.fn()
    const mockRemoveChild = vi.fn()
    const mockClick = vi.fn()
    
    Object.defineProperty(document, 'createElement', {
      value: vi.fn(() => ({
        href: '',
        download: '',
        click: mockClick
      }))
    })
    
    Object.defineProperty(document.body, 'appendChild', {
      value: mockAppendChild
    })
    
    Object.defineProperty(document.body, 'removeChild', {
      value: mockRemoveChild
    })
    
    render(<ImportExportTab />)
    
    const exportButton = screen.getByRole('button', { name: /开始导出/ })
    fireEvent.click(exportButton)
    
    await waitFor(() => {
      expect(mockImportExportManagerService.exportBookmarks).toHaveBeenCalled()
    })
    
    await waitFor(() => {
      expect(screen.getByText('导出成功')).toBeInTheDocument()
      expect(screen.getByText(/已导出 10 个收藏到 bookmarks.json/)).toBeInTheDocument()
    })
  })

  it('应该使用shadcn Alert组件显示错误信息', async () => {
    const errorMessage = '导出失败'
    mockImportExportManagerService.exportBookmarks.mockRejectedValue(new Error(errorMessage))
    
    render(<ImportExportTab />)
    
    const exportButton = screen.getByRole('button', { name: /开始导出/ })
    fireEvent.click(exportButton)
    
    await waitFor(() => {
      expect(screen.getByText('操作失败')).toBeInTheDocument()
      expect(screen.getByText(errorMessage)).toBeInTheDocument()
      expect(screen.getByRole('button', { name: /重新开始/ })).toBeInTheDocument()
    })
  })

  it('应该使用shadcn Progress组件显示导出进度', async () => {
    let progressCallback: (progress: number, message: string) => void
    
    mockImportExportManagerService.exportBookmarks.mockImplementation(
      (options, callback) => {
        progressCallback = callback
        return new Promise((resolve) => {
          setTimeout(() => {
            progressCallback(50, '导出中...')
            setTimeout(() => {
              progressCallback(100, '导出完成')
              resolve({
                data: 'export data',
                filename: 'bookmarks.json',
                format: 'json' as const,
                itemCount: 10
              })
            }, 100)
          }, 100)
        })
      }
    )
    
    // Mock DOM操作
    global.URL.createObjectURL = vi.fn(() => 'blob:url')
    global.URL.revokeObjectURL = vi.fn()
    Object.defineProperty(document, 'createElement', {
      value: vi.fn(() => ({
        href: '',
        download: '',
        click: vi.fn()
      }))
    })
    Object.defineProperty(document.body, 'appendChild', { value: vi.fn() })
    Object.defineProperty(document.body, 'removeChild', { value: vi.fn() })
    
    render(<ImportExportTab />)
    
    const exportButton = screen.getByRole('button', { name: /开始导出/ })
    fireEvent.click(exportButton)
    
    // 应该显示进度条
    await waitFor(() => {
      expect(screen.getByText('导出中...')).toBeInTheDocument()
    })
    
    await waitFor(() => {
      expect(screen.getByText('50%')).toBeInTheDocument()
    })
  })

  it('应该正确处理文件选择', () => {
    render(<ImportExportTab />)
    
    const fileInput = screen.getByLabelText('选择文件')
    const file = new File(['test content'], 'test.json', { type: 'application/json' })
    
    fireEvent.change(fileInput, { target: { files: [file] } })
    
    expect(screen.getByText(/已选择: test.json/)).toBeInTheDocument()
  })

  it('应该正确处理导入操作', async () => {
    const mockImportResult = {
      success: true,
      totalItems: 10,
      importedItems: 8,
      skippedItems: 1,
      errorItems: 1,
      duplicates: [],
      errors: ['Error 1']
    }
    
    mockImportExportManagerService.importData.mockResolvedValue(mockImportResult)
    
    render(<ImportExportTab />)
    
    // 选择文件
    const fileInput = screen.getByLabelText('选择文件')
    const file = new File(['test content'], 'test.json', { type: 'application/json' })
    fireEvent.change(fileInput, { target: { files: [file] } })
    
    // 点击导入
    const importButton = screen.getByRole('button', { name: /开始导入/ })
    fireEvent.click(importButton)
    
    await waitFor(() => {
      expect(mockImportExportManagerService.importData).toHaveBeenCalled()
    })
    
    await waitFor(() => {
      expect(screen.getByText('导入完成')).toBeInTheDocument()
      expect(screen.getByText('总计: 10 个项目')).toBeInTheDocument()
      expect(screen.getByText('成功: 8 个')).toBeInTheDocument()
    })
  })

  it('应该使用shadcn Card组件显示使用说明', () => {
    render(<ImportExportTab />)
    
    expect(screen.getByText('使用说明')).toBeInTheDocument()
    expect(screen.getByText(/JSON格式：包含完整的收藏数据/)).toBeInTheDocument()
    expect(screen.getByText(/支持导入本工具导出的JSON、CSV、HTML文件/)).toBeInTheDocument()
  })

  it('应该正确处理冲突解决对话框', async () => {
    const mockImportResult = {
      success: false,
      totalItems: 10,
      importedItems: 0,
      skippedItems: 0,
      errorItems: 0,
      duplicates: [],
      errors: [],
      conflicts: {
        hasConflicts: true,
        conflicts: []
      }
    }
    
    mockImportExportManagerService.importData.mockResolvedValue(mockImportResult)
    
    render(<ImportExportTab />)
    
    // 选择文件并导入
    const fileInput = screen.getByLabelText('选择文件')
    const file = new File(['test content'], 'test.json', { type: 'application/json' })
    fireEvent.change(fileInput, { target: { files: [file] } })
    
    const importButton = screen.getByRole('button', { name: /开始导入/ })
    fireEvent.click(importButton)
    
    await waitFor(() => {
      expect(screen.getByTestId('conflict-dialog')).toBeInTheDocument()
    })
  })
})