// 调试创建标签按钮状态的脚本

console.log('🔍 开始调试创建标签按钮状态...')

function debugButtonState() {
  console.log('=== 按钮状态调试报告 ===')
  
  // 查找模态窗口
  const modal = document.querySelector('[role="dialog"], .tag-modal, [data-testid="tag-modal"]')
  if (!modal) {
    console.log('❌ 未找到模态窗口，请先点击"新建标签"按钮')
    return
  }
  
  console.log('✅ 找到模态窗口')
  
  // 查找表单元素
  const form = modal.querySelector('form')
  const nameInput = modal.querySelector('input[id="tag-name"], input[type="text"]')
  const submitButton = modal.querySelector('button[type="submit"]')
  
  console.log('📋 表单元素状态:')
  console.log('  - 表单:', form ? '✅ 存在' : '❌ 不存在')
  console.log('  - 输入框:', nameInput ? '✅ 存在' : '❌ 不存在')
  console.log('  - 提交按钮:', submitButton ? '✅ 存在' : '❌ 不存在')
  
  if (!nameInput || !submitButton) {
    console.log('❌ 关键元素缺失，无法继续调试')
    return
  }
  
  // 详细检查输入框
  console.log('📝 输入框详细状态:')
  console.log('  - 值:', `"${nameInput.value}"`)
  console.log('  - 长度:', nameInput.value.length)
  console.log('  - 去空格后长度:', nameInput.value.trim().length)
  console.log('  - 禁用状态:', nameInput.disabled)
  console.log('  - 类名:', nameInput.className)
  
  // 详细检查提交按钮
  console.log('🔘 提交按钮详细状态:')
  console.log('  - 文本:', `"${submitButton.textContent.trim()}"`)
  console.log('  - 禁用状态:', submitButton.disabled)
  console.log('  - 类名:', submitButton.className)
  console.log('  - 样式:', submitButton.style.cssText)
  
  // 检查错误信息
  const errorElements = modal.querySelectorAll('.text-red-600, .text-red-500, .error, [class*="red"]')
  console.log('❗ 错误信息:')
  if (errorElements.length > 0) {
    errorElements.forEach((el, index) => {
      if (el.textContent.trim()) {
        console.log(`  - 错误 ${index + 1}:`, el.textContent.trim())
      }
    })
  } else {
    console.log('  - 无错误信息')
  }
  
  // 检查验证状态
  const validatingElements = modal.querySelectorAll('.animate-spin, [class*="validating"]')
  console.log('⏳ 验证状态:', validatingElements.length > 0 ? '验证中' : '验证完成')
  
  // 分析按钮禁用原因
  console.log('🔍 按钮禁用原因分析:')
  const hasName = nameInput.value.trim().length > 0
  const hasErrors = errorElements.length > 0 && Array.from(errorElements).some(el => el.textContent.trim())
  const isValidating = validatingElements.length > 0
  
  console.log('  - 有名称输入:', hasName ? '✅' : '❌')
  console.log('  - 有验证错误:', hasErrors ? '❌' : '✅')
  console.log('  - 正在验证:', isValidating ? '❌' : '✅')
  
  // 尝试获取React状态（如果可能）
  try {
    const reactFiber = nameInput._reactInternalFiber || nameInput._reactInternals
    if (reactFiber) {
      console.log('⚛️ React状态检查:')
      let current = reactFiber
      let attempts = 0
      while (current && attempts < 10) {
        if (current.stateNode && current.stateNode.state) {
          console.log('  - 组件状态:', current.stateNode.state)
          break
        }
        if (current.memoizedProps) {
          console.log('  - 组件Props:', current.memoizedProps)
          break
        }
        current = current.return
        attempts++
      }
    }
  } catch (e) {
    console.log('⚛️ 无法获取React状态')
  }
  
  // 建议的修复步骤
  console.log('🛠️ 建议的修复步骤:')
  if (!hasName) {
    console.log('  1. 在输入框中输入标签名称')
  }
  if (hasErrors) {
    console.log('  2. 修复验证错误')
  }
  if (isValidating) {
    console.log('  3. 等待验证完成')
  }
  if (hasName && !hasErrors && !isValidating && submitButton.disabled) {
    console.log('  4. 可能存在代码逻辑问题，需要检查isFormValid函数')
  }
}

// 监听输入变化
function setupInputMonitoring() {
  const modal = document.querySelector('[role="dialog"], .tag-modal, [data-testid="tag-modal"]')
  if (!modal) return
  
  const nameInput = modal.querySelector('input[id="tag-name"], input[type="text"]')
  if (!nameInput) return
  
  console.log('👂 开始监听输入变化...')
  
  nameInput.addEventListener('input', function() {
    console.log('📝 输入变化:', {
      value: this.value,
      length: this.value.length,
      trimmedLength: this.value.trim().length
    })
    
    // 延迟检查按钮状态
    setTimeout(() => {
      const submitButton = modal.querySelector('button[type="submit"]')
      if (submitButton) {
        console.log('🔘 按钮状态更新:', {
          disabled: submitButton.disabled,
          className: submitButton.className
        })
      }
    }, 100)
  })
}

// 自动执行
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    setTimeout(() => {
      debugButtonState()
      setupInputMonitoring()
    }, 1000)
  })
} else {
  setTimeout(() => {
    debugButtonState()
    setupInputMonitoring()
  }, 1000)
}

// 导出函数供手动调用
window.debugButtonState = debugButtonState
window.setupInputMonitoring = setupInputMonitoring

console.log('调试脚本已加载，可以手动调用:')
console.log('- debugButtonState() - 检查当前按钮状态')
console.log('- setupInputMonitoring() - 开始监听输入变化')