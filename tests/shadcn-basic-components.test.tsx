import { describe, it, expect } from 'vitest';
import { render, screen } from '@testing-library/react';
import React from 'react';

// 导入所有基础UI组件
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { 
  Dialog, 
  DialogContent, 
  DialogHeader, 
  DialogTitle, 
  DialogDescription,
  DialogFooter,
  DialogTrigger 
} from '@/components/ui/dialog';
import { 
  AlertDialog, 
  AlertDialogContent, 
  AlertDialogHeader, 
  AlertDialogTitle, 
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogTrigger 
} from '@/components/ui/alert-dialog';
import { 
  Form, 
  FormField, 
  FormItem, 
  FormLabel, 
  FormControl, 
  FormDescription, 
  FormMessage 
} from '@/components/ui/form';
import { 
  Card, 
  CardHeader, 
  CardTitle, 
  CardDescription, 
  CardContent, 
  CardFooter 
} from '@/components/ui/card';
import { Label } from '@/components/ui/label';

describe('shadcn基础UI组件安装验证', () => {
  it('Button组件应该正确渲染', () => {
    render(<Button>测试按钮</Button>);
    expect(screen.getByRole('button', { name: '测试按钮' })).toBeInTheDocument();
  });

  it('Button组件应该支持所有变体', () => {
    const { rerender } = render(<Button variant="default">默认按钮</Button>);
    expect(screen.getByRole('button')).toBeInTheDocument();

    rerender(<Button variant="destructive">危险按钮</Button>);
    expect(screen.getByRole('button')).toBeInTheDocument();

    rerender(<Button variant="outline">轮廓按钮</Button>);
    expect(screen.getByRole('button')).toBeInTheDocument();

    rerender(<Button variant="secondary">次要按钮</Button>);
    expect(screen.getByRole('button')).toBeInTheDocument();

    rerender(<Button variant="ghost">幽灵按钮</Button>);
    expect(screen.getByRole('button')).toBeInTheDocument();

    rerender(<Button variant="link">链接按钮</Button>);
    expect(screen.getByRole('button')).toBeInTheDocument();
  });

  it('Input组件应该正确渲染', () => {
    render(<Input placeholder="测试输入框" />);
    expect(screen.getByPlaceholderText('测试输入框')).toBeInTheDocument();
  });

  it('Textarea组件应该正确渲染', () => {
    render(<Textarea placeholder="测试文本域" />);
    expect(screen.getByPlaceholderText('测试文本域')).toBeInTheDocument();
  });

  it('Card组件应该正确渲染', () => {
    render(
      <Card>
        <CardHeader>
          <CardTitle>测试卡片标题</CardTitle>
          <CardDescription>测试卡片描述</CardDescription>
        </CardHeader>
        <CardContent>
          <p>测试卡片内容</p>
        </CardContent>
        <CardFooter>
          <p>测试卡片底部</p>
        </CardFooter>
      </Card>
    );
    
    expect(screen.getByText('测试卡片标题')).toBeInTheDocument();
    expect(screen.getByText('测试卡片描述')).toBeInTheDocument();
    expect(screen.getByText('测试卡片内容')).toBeInTheDocument();
    expect(screen.getByText('测试卡片底部')).toBeInTheDocument();
  });

  it('Label组件应该正确渲染', () => {
    render(<Label htmlFor="test">测试标签</Label>);
    expect(screen.getByText('测试标签')).toBeInTheDocument();
  });

  it('Dialog组件应该正确导入', () => {
    // 测试Dialog组件是否可以正确导入和使用
    expect(Dialog).toBeDefined();
    expect(DialogContent).toBeDefined();
    expect(DialogHeader).toBeDefined();
    expect(DialogTitle).toBeDefined();
    expect(DialogDescription).toBeDefined();
    expect(DialogFooter).toBeDefined();
    expect(DialogTrigger).toBeDefined();
  });

  it('AlertDialog组件应该正确导入', () => {
    // 测试AlertDialog组件是否可以正确导入和使用
    expect(AlertDialog).toBeDefined();
    expect(AlertDialogContent).toBeDefined();
    expect(AlertDialogHeader).toBeDefined();
    expect(AlertDialogTitle).toBeDefined();
    expect(AlertDialogDescription).toBeDefined();
    expect(AlertDialogFooter).toBeDefined();
    expect(AlertDialogAction).toBeDefined();
    expect(AlertDialogCancel).toBeDefined();
    expect(AlertDialogTrigger).toBeDefined();
  });

  it('Form组件应该正确导入', () => {
    // 测试Form组件是否可以正确导入和使用
    expect(Form).toBeDefined();
    expect(FormField).toBeDefined();
    expect(FormItem).toBeDefined();
    expect(FormLabel).toBeDefined();
    expect(FormControl).toBeDefined();
    expect(FormDescription).toBeDefined();
    expect(FormMessage).toBeDefined();
  });
});