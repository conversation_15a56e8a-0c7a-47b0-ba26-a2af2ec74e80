// 标签管理端到端测试 - 验证完整的标签管理流程

import React from 'react'
import { render, screen, fireEvent, waitFor, within } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest'
import TagManagementTab from '../../src/components/TagManagementTab'
import { tagService } from '../../src/services/tagService'
import type { Tag } from '../../src/types'

// Mock 标签服务
vi.mock('../../src/services/tagService', () => ({
  tagService: {
    getAllTagsWithStats: vi.fn(),
    syncTagsFromBookmarks: vi.fn(),
    createTag: vi.fn(),
    updateTag: vi.fn(),
    deleteTag: vi.fn(),
    batchMergeTags: vi.fn(),
    validateTagName: vi.fn()
  }
}))

// Mock IndexedDB
vi.mock('../../src/utils/indexedDB', () => ({
  indexedDBService: {
    getTags: vi.fn(),
    getBookmarks: vi.fn(),
    saveTag: vi.fn(),
    updateTag: vi.fn(),
    deleteTag: vi.fn(),
    getTag: vi.fn(),
    updateBookmark: vi.fn()
  }
}))

describe('标签管理端到端测试', () => {
  const mockTags: (Tag & { usageCount: number })[] = [
    {
      id: 'tag1',
      name: '技术',
      color: '#3B82F6',
      usageCount: 15,
      createdAt: new Date('2024-01-01'),
      updatedAt: new Date('2024-01-01')
    },
    {
      id: 'tag2',
      name: '学习',
      color: '#10B981',
      usageCount: 8,
      createdAt: new Date('2024-01-02'),
      updatedAt: new Date('2024-01-02')
    },
    {
      id: 'tag3',
      name: '工具',
      color: '#F59E0B',
      usageCount: 3,
      createdAt: new Date('2024-01-03'),
      updatedAt: new Date('2024-01-03')
    }
  ]

  beforeEach(() => {
    // 重置所有 mock
    vi.clearAllMocks()
    
    // 设置默认的 mock 返回值
    vi.mocked(tagService.getAllTagsWithStats).mockResolvedValue(mockTags)
    vi.mocked(tagService.syncTagsFromBookmarks).mockResolvedValue([])
    vi.mocked(tagService.validateTagName).mockResolvedValue(true)
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  describe('页面加载和初始化', () => {
    it('应该正确加载标签管理页面', async () => {
      render(<TagManagementTab />)

      // 验证页面标题
      expect(screen.getByText('标签管理')).toBeInTheDocument()
      expect(screen.getByText('管理您的书签标签，更好地分类和查找内容')).toBeInTheDocument()

      // 验证操作按钮
      expect(screen.getByText('新建标签')).toBeInTheDocument()
      expect(screen.getByText('刷新')).toBeInTheDocument()

      // 等待标签加载完成
      await waitFor(() => {
        expect(tagService.syncTagsFromBookmarks).toHaveBeenCalled()
        expect(tagService.getAllTagsWithStats).toHaveBeenCalled()
      })

      // 验证标签卡片显示
      await waitFor(() => {
        expect(screen.getByText('技术')).toBeInTheDocument()
        expect(screen.getByText('学习')).toBeInTheDocument()
        expect(screen.getByText('工具')).toBeInTheDocument()
      })
    })

    it('应该显示标签统计信息', async () => {
      render(<TagManagementTab />)

      await waitFor(() => {
        // 验证统计信息
        expect(screen.getByText('3')).toBeInTheDocument() // 总标签数
        expect(screen.getByText('总标签数')).toBeInTheDocument()
        expect(screen.getByText('活跃标签')).toBeInTheDocument()
        expect(screen.getByText('未使用标签')).toBeInTheDocument()
        expect(screen.getByText('平均使用次数')).toBeInTheDocument()
      })
    })
  })

  describe('标签创建流程', () => {
    it('应该能够创建新标签', async () => {
      const user = userEvent.setup()
      const newTag: Tag = {
        id: 'tag4',
        name: '新标签',
        color: '#8B5CF6',
        usageCount: 0,
        createdAt: new Date(),
        updatedAt: new Date()
      }

      vi.mocked(tagService.createTag).mockResolvedValue(newTag)
      vi.mocked(tagService.getAllTagsWithStats).mockResolvedValue([
        ...mockTags,
        { ...newTag, usageCount: 0 }
      ])

      render(<TagManagementTab />)

      // 等待初始加载完成
      await waitFor(() => {
        expect(screen.getByText('技术')).toBeInTheDocument()
      })

      // 点击新建标签按钮
      const createButton = screen.getByText('新建标签')
      await user.click(createButton)

      // 验证模态窗口打开
      await waitFor(() => {
        expect(screen.getByText('创建标签')).toBeInTheDocument()
      })

      // 填写标签信息
      const nameInput = screen.getByLabelText('标签名称')
      await user.type(nameInput, '新标签')

      // 提交表单
      const saveButton = screen.getByText('创建')
      await user.click(saveButton)

      // 验证服务调用
      await waitFor(() => {
        expect(tagService.createTag).toHaveBeenCalledWith({
          name: '新标签',
          color: expect.any(String)
        })
      })

      // 验证成功提示（通过检查是否重新加载了数据）
      await waitFor(() => {
        expect(tagService.getAllTagsWithStats).toHaveBeenCalledTimes(2)
      })
    })

    it('应该验证标签名称唯一性', async () => {
      const user = userEvent.setup()
      
      // 模拟名称重复
      vi.mocked(tagService.validateTagName).mockResolvedValue(false)

      render(<TagManagementTab />)

      await waitFor(() => {
        expect(screen.getByText('技术')).toBeInTheDocument()
      })

      // 打开创建模态窗口
      const createButton = screen.getByText('新建标签')
      await user.click(createButton)

      await waitFor(() => {
        expect(screen.getByText('创建标签')).toBeInTheDocument()
      })

      // 输入重复的标签名称
      const nameInput = screen.getByLabelText('标签名称')
      await user.type(nameInput, '技术')

      // 验证错误提示
      await waitFor(() => {
        expect(screen.getByText('标签名称已存在')).toBeInTheDocument()
      })

      // 创建按钮应该被禁用
      const saveButton = screen.getByText('创建')
      expect(saveButton).toBeDisabled()
    })
  })

  describe('标签编辑流程', () => {
    it('应该能够编辑标签', async () => {
      const user = userEvent.setup()
      const updatedTag: Tag = {
        ...mockTags[0],
        name: '技术更新',
        color: '#EF4444'
      }

      vi.mocked(tagService.updateTag).mockResolvedValue(updatedTag)

      render(<TagManagementTab />)

      await waitFor(() => {
        expect(screen.getByText('技术')).toBeInTheDocument()
      })

      // 找到技术标签卡片并点击编辑按钮
      const techCard = screen.getByText('技术').closest('.group')
      expect(techCard).toBeInTheDocument()

      // 悬停以显示操作按钮
      await user.hover(techCard!)
      
      // 点击编辑按钮
      const editButton = within(techCard!).getByTitle('编辑标签')
      await user.click(editButton)

      // 验证编辑模态窗口打开
      await waitFor(() => {
        expect(screen.getByText('编辑标签')).toBeInTheDocument()
      })

      // 修改标签名称
      const nameInput = screen.getByDisplayValue('技术')
      await user.clear(nameInput)
      await user.type(nameInput, '技术更新')

      // 保存修改
      const saveButton = screen.getByText('保存')
      await user.click(saveButton)

      // 验证服务调用
      await waitFor(() => {
        expect(tagService.updateTag).toHaveBeenCalledWith('tag1', {
          name: '技术更新',
          color: expect.any(String)
        })
      })
    })
  })

  describe('标签删除流程', () => {
    it('应该能够删除标签', async () => {
      const user = userEvent.setup()

      vi.mocked(tagService.deleteTag).mockResolvedValue(undefined)

      render(<TagManagementTab />)

      await waitFor(() => {
        expect(screen.getByText('技术')).toBeInTheDocument()
      })

      // 找到技术标签卡片并点击删除按钮
      const techCard = screen.getByText('技术').closest('.group')
      expect(techCard).toBeInTheDocument()

      // 悬停以显示操作按钮
      await user.hover(techCard!)
      
      // 点击删除按钮
      const deleteButton = within(techCard!).getByTitle('删除标签')
      await user.click(deleteButton)

      // 验证删除确认模态窗口
      await waitFor(() => {
        expect(screen.getByText('删除标签')).toBeInTheDocument()
        expect(screen.getByText('确认删除')).toBeInTheDocument()
      })

      // 确认删除
      const confirmButton = screen.getByText('确认删除')
      await user.click(confirmButton)

      // 验证服务调用
      await waitFor(() => {
        expect(tagService.deleteTag).toHaveBeenCalledWith('tag1')
      })
    })
  })

  describe('搜索和筛选功能', () => {
    it('应该能够搜索标签', async () => {
      const user = userEvent.setup()

      render(<TagManagementTab />)

      await waitFor(() => {
        expect(screen.getByText('技术')).toBeInTheDocument()
        expect(screen.getByText('学习')).toBeInTheDocument()
        expect(screen.getByText('工具')).toBeInTheDocument()
      })

      // 找到搜索框并输入搜索关键词
      const searchInput = screen.getByPlaceholderText('搜索标签...')
      await user.type(searchInput, '技术')

      // 验证搜索结果
      await waitFor(() => {
        expect(screen.getByText('技术')).toBeInTheDocument()
        expect(screen.queryByText('学习')).not.toBeInTheDocument()
        expect(screen.queryByText('工具')).not.toBeInTheDocument()
      })

      // 清除搜索
      await user.clear(searchInput)

      // 验证所有标签重新显示
      await waitFor(() => {
        expect(screen.getByText('技术')).toBeInTheDocument()
        expect(screen.getByText('学习')).toBeInTheDocument()
        expect(screen.getByText('工具')).toBeInTheDocument()
      })
    })

    it('应该能够排序标签', async () => {
      const user = userEvent.setup()

      render(<TagManagementTab />)

      await waitFor(() => {
        expect(screen.getByText('技术')).toBeInTheDocument()
      })

      // 找到排序选择器
      const sortSelect = screen.getByLabelText('排序方式:')
      
      // 按使用次数降序排序
      await user.selectOptions(sortSelect, 'usage-desc')

      // 验证排序结果（技术15次 > 学习8次 > 工具3次）
      const tagCards = screen.getAllByText(/技术|学习|工具/)
      expect(tagCards[0]).toHaveTextContent('技术')
      expect(tagCards[1]).toHaveTextContent('学习')
      expect(tagCards[2]).toHaveTextContent('工具')
    })
  })

  describe('视图模式切换', () => {
    it('应该能够切换到标签云视图', async () => {
      const user = userEvent.setup()

      render(<TagManagementTab />)

      await waitFor(() => {
        expect(screen.getByText('技术')).toBeInTheDocument()
      })

      // 找到标签云视图按钮
      const cloudViewButton = screen.getByTitle('标签云视图')
      await user.click(cloudViewButton)

      // 验证标签云视图激活
      expect(cloudViewButton).toHaveClass('bg-white', 'text-primary-600')

      // 验证标签云容器存在
      await waitFor(() => {
        const cloudContainer = screen.getByText('技术').closest('.flex-wrap')
        expect(cloudContainer).toBeInTheDocument()
      })
    })

    it('应该能够切换到列表视图', async () => {
      const user = userEvent.setup()

      render(<TagManagementTab />)

      await waitFor(() => {
        expect(screen.getByText('技术')).toBeInTheDocument()
      })

      // 切换到列表视图
      const listViewButton = screen.getByTitle('列表视图')
      await user.click(listViewButton)

      // 验证列表视图激活
      expect(listViewButton).toHaveClass('bg-white', 'text-primary-600')
    })
  })

  describe('批量操作功能', () => {
    it('应该能够进入选择模式并批量删除', async () => {
      const user = userEvent.setup()

      vi.mocked(tagService.deleteTag).mockResolvedValue(undefined)

      render(<TagManagementTab />)

      await waitFor(() => {
        expect(screen.getByText('技术')).toBeInTheDocument()
      })

      // 进入选择模式
      const batchButton = screen.getByText('批量操作')
      await user.click(batchButton)

      // 验证选择模式激活
      expect(screen.getByText('退出选择')).toBeInTheDocument()

      // 选择标签
      const checkboxes = screen.getAllByTitle(/选择标签/)
      await user.click(checkboxes[0]) // 选择技术
      await user.click(checkboxes[1]) // 选择学习

      // 验证批量操作栏显示
      await waitFor(() => {
        expect(screen.getByText('已选择 2 个标签')).toBeInTheDocument()
      })

      // 点击批量删除
      const batchDeleteButton = screen.getByText('删除')
      await user.click(batchDeleteButton)

      // 验证删除确认界面
      await waitFor(() => {
        expect(screen.getByText('确认批量删除')).toBeInTheDocument()
        expect(screen.getByText('您即将删除 2 个标签：')).toBeInTheDocument()
      })

      // 确认删除
      const confirmDeleteButton = screen.getByText('确认删除')
      await user.click(confirmDeleteButton)

      // 验证服务调用
      await waitFor(() => {
        expect(tagService.deleteTag).toHaveBeenCalledTimes(2)
      })
    })

    it('应该能够批量合并标签', async () => {
      const user = userEvent.setup()

      vi.mocked(tagService.batchMergeTags).mockResolvedValue(undefined)

      render(<TagManagementTab />)

      await waitFor(() => {
        expect(screen.getByText('技术')).toBeInTheDocument()
      })

      // 进入选择模式
      const batchButton = screen.getByText('批量操作')
      await user.click(batchButton)

      // 选择要合并的标签
      const checkboxes = screen.getAllByTitle(/选择标签/)
      await user.click(checkboxes[1]) // 选择学习
      await user.click(checkboxes[2]) // 选择工具

      // 点击批量合并
      const batchMergeButton = screen.getByText('合并')
      await user.click(batchMergeButton)

      // 验证合并界面
      await waitFor(() => {
        expect(screen.getByText('批量合并标签')).toBeInTheDocument()
        expect(screen.getByText('选择目标标签：')).toBeInTheDocument()
      })

      // 选择目标标签
      const targetSelect = screen.getByLabelText('选择目标标签：')
      await user.selectOptions(targetSelect, 'tag1') // 选择技术作为目标

      // 确认合并
      const confirmMergeButton = screen.getByText('确认合并')
      await user.click(confirmMergeButton)

      // 验证服务调用
      await waitFor(() => {
        expect(tagService.batchMergeTags).toHaveBeenCalledWith(['tag2', 'tag3'], 'tag1')
      })
    })
  })

  describe('错误处理', () => {
    it('应该处理加载错误', async () => {
      vi.mocked(tagService.getAllTagsWithStats).mockRejectedValue(new Error('加载失败'))

      render(<TagManagementTab />)

      // 验证错误状态显示
      await waitFor(() => {
        expect(screen.getByText('加载失败')).toBeInTheDocument()
        expect(screen.getByText('重试')).toBeInTheDocument()
      })
    })

    it('应该处理创建标签错误', async () => {
      const user = userEvent.setup()

      vi.mocked(tagService.createTag).mockRejectedValue(new Error('创建失败'))

      render(<TagManagementTab />)

      await waitFor(() => {
        expect(screen.getByText('技术')).toBeInTheDocument()
      })

      // 打开创建模态窗口
      const createButton = screen.getByText('新建标签')
      await user.click(createButton)

      await waitFor(() => {
        expect(screen.getByText('创建标签')).toBeInTheDocument()
      })

      // 填写并提交
      const nameInput = screen.getByLabelText('标签名称')
      await user.type(nameInput, '测试标签')

      const saveButton = screen.getByText('创建')
      await user.click(saveButton)

      // 验证错误处理（通过检查模态窗口是否仍然打开）
      await waitFor(() => {
        expect(screen.getByText('创建标签')).toBeInTheDocument()
      })
    })
  })

  describe('响应式设计', () => {
    it('应该在小屏幕上正确显示', async () => {
      // 模拟小屏幕
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 640
      })

      render(<TagManagementTab />)

      await waitFor(() => {
        expect(screen.getByText('技术')).toBeInTheDocument()
      })

      // 验证响应式布局
      const container = screen.getByText('技术').closest('.grid')
      expect(container).toHaveClass('grid-cols-1')
    })
  })
})