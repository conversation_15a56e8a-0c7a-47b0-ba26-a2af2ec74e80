// 标签管理集成测试

import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest'
import TagManagementTab from '../../src/components/TagManagementTab'
import { tagService } from '../../src/services/tagService'
import type { Tag, Bookmark } from '../../src/types'
import type { TagWithStats } from '../../src/components/TagList'

// Mock tagService instead of indexedDBService
vi.mock('../../src/services/tagService', () => ({
  tagService: {
    syncTagsFromBookmarks: vi.fn(),
    getAllTagsWithStats: vi.fn(),
    createTag: vi.fn(),
    updateTag: vi.fn(),
    deleteTag: vi.fn(),
    getTagUsageCount: vi.fn()
  }
}))

describe('标签管理集成测试', () => {
  const mockTagsWithStats: TagWithStats[] = [
    {
      id: 'tag1',
      name: '技术',
      color: '#3B82F6',
      usageCount: 2,
      createdAt: new Date('2024-01-01'),
      updatedAt: new Date('2024-01-01')
    },
    {
      id: 'tag2',
      name: '学习',
      color: '#10B981',
      usageCount: 2,
      createdAt: new Date('2024-01-02'),
      updatedAt: new Date('2024-01-02')
    },
    {
      id: 'tag3',
      name: '工具',
      color: '#F59E0B',
      usageCount: 0,
      createdAt: new Date('2024-01-03'),
      updatedAt: new Date('2024-01-03')
    }
  ]

  beforeEach(() => {
    vi.clearAllMocks()
    
    // 设置默认的mock返回值
    vi.mocked(tagService.syncTagsFromBookmarks).mockResolvedValue([])
    vi.mocked(tagService.getAllTagsWithStats).mockResolvedValue(mockTagsWithStats)
    vi.mocked(tagService.createTag).mockImplementation(async (data) => ({
      id: 'new-tag-id',
      name: data.name,
      color: data.color || '#6B7280',
      usageCount: 0,
      createdAt: new Date(),
      updatedAt: new Date()
    }))
    vi.mocked(tagService.updateTag).mockImplementation(async (id, updates) => {
      const existingTag = mockTagsWithStats.find(t => t.id === id)
      return existingTag ? { ...existingTag, ...updates, updatedAt: new Date() } : existingTag!
    })
    vi.mocked(tagService.deleteTag).mockResolvedValue()
    vi.mocked(tagService.getTagUsageCount).mockImplementation(async (id) => {
      const tag = mockTagsWithStats.find(t => t.id === id)
      return tag?.usageCount || 0
    })
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  describe('完整的标签管理流程', () => {
    it('应该正确加载和显示标签数据', async () => {
      render(<TagManagementTab />)

      // 等待数据加载完成
      await waitFor(() => {
        expect(tagService.syncTagsFromBookmarks).toHaveBeenCalledTimes(1)
        expect(tagService.getAllTagsWithStats).toHaveBeenCalledTimes(1)
      })

      // 检查页面标题
      expect(screen.getByText('标签管理')).toBeInTheDocument()
      expect(screen.getByText('管理您的书签标签，更好地分类和查找内容')).toBeInTheDocument()

      // 检查操作按钮
      expect(screen.getByText('刷新')).toBeInTheDocument()
      expect(screen.getByText('新建标签')).toBeInTheDocument()
    })

    it('应该支持创建新标签', async () => {
      render(<TagManagementTab />)

      await waitFor(() => {
        expect(tagService.getAllTagsWithStats).toHaveBeenCalled()
      })

      // 点击新建标签按钮
      const createButton = screen.getByText('新建标签')
      fireEvent.click(createButton)

      // 检查模态窗口是否打开
      await waitFor(() => {
        expect(screen.getByRole('dialog')).toBeInTheDocument()
      })
    })

    it('应该处理加载错误', async () => {
      // 模拟加载失败
      vi.mocked(tagService.getAllTagsWithStats).mockRejectedValue(new Error('加载失败'))

      render(<TagManagementTab />)

      // 检查错误状态
      await waitFor(() => {
        expect(screen.getAllByText('加载失败')).toHaveLength(2) // 标题和错误消息
      })

      // 检查重试按钮
      expect(screen.getByText('重试')).toBeInTheDocument()
    })

    it('应该支持刷新功能', async () => {
      render(<TagManagementTab />)

      await waitFor(() => {
        expect(tagService.getAllTagsWithStats).toHaveBeenCalledTimes(1)
      })

      // 点击刷新按钮
      const refreshButton = screen.getByText('刷新')
      fireEvent.click(refreshButton)

      // 验证重新加载数据
      await waitFor(() => {
        expect(tagService.syncTagsFromBookmarks).toHaveBeenCalledTimes(2)
        expect(tagService.getAllTagsWithStats).toHaveBeenCalledTimes(2)
      })
    })

    it('应该正确显示空状态', async () => {
      // Mock空的标签列表
      vi.mocked(tagService.getAllTagsWithStats).mockResolvedValue([])

      render(<TagManagementTab />)

      await waitFor(() => {
        expect(tagService.getAllTagsWithStats).toHaveBeenCalled()
      })

      // 由于TagList组件处理空状态显示，我们主要验证数据传递正确
      expect(tagService.getAllTagsWithStats).toHaveBeenCalledWith()
    })
  })

  describe('数据同步和一致性', () => {
    it('应该在初始化时同步书签中的标签', async () => {
      render(<TagManagementTab />)

      await waitFor(() => {
        expect(tagService.syncTagsFromBookmarks).toHaveBeenCalledTimes(1)
      })

      // 验证同步在获取统计信息之前执行
      expect(tagService.syncTagsFromBookmarks).toHaveBeenCalledBefore(
        tagService.getAllTagsWithStats as any
      )
    })

    it('应该在标签操作后重新加载数据', async () => {
      render(<TagManagementTab />)

      await waitFor(() => {
        expect(tagService.getAllTagsWithStats).toHaveBeenCalledTimes(1)
      })

      // 模拟创建标签操作
      const createButton = screen.getByText('新建标签')
      fireEvent.click(createButton)

      // 验证初始数据加载
      expect(tagService.syncTagsFromBookmarks).toHaveBeenCalledTimes(1)
      expect(tagService.getAllTagsWithStats).toHaveBeenCalledTimes(1)
    })
  })

  describe('错误处理和用户体验', () => {
    it('应该处理服务层错误', async () => {
      // 模拟服务层错误
      vi.mocked(tagService.createTag).mockRejectedValue(new Error('创建失败'))
      
      // Mock window.alert
      const alertSpy = vi.spyOn(window, 'alert').mockImplementation(() => {})

      render(<TagManagementTab />)

      await waitFor(() => {
        expect(tagService.getAllTagsWithStats).toHaveBeenCalled()
      })

      // 尝试创建标签
      const createButton = screen.getByText('新建标签')
      fireEvent.click(createButton)

      // 验证错误处理机制存在
      expect(createButton).toBeInTheDocument()

      alertSpy.mockRestore()
    })

    it('应该支持重试机制', async () => {
      // 第一次失败，第二次成功
      vi.mocked(tagService.getAllTagsWithStats)
        .mockRejectedValueOnce(new Error('网络错误'))
        .mockResolvedValueOnce(mockTagsWithStats)

      render(<TagManagementTab />)

      // 等待错误状态显示
      await waitFor(() => {
        expect(screen.getByText('加载失败')).toBeInTheDocument()
      })

      // 点击重试
      const retryButton = screen.getByText('重试')
      fireEvent.click(retryButton)

      // 验证重试调用
      await waitFor(() => {
        expect(tagService.getAllTagsWithStats).toHaveBeenCalledTimes(2)
      })
    })

    it('应该在操作进行中禁用相关按钮', async () => {
      // 让创建操作挂起
      vi.mocked(tagService.createTag).mockImplementation(() => new Promise(() => {}))

      render(<TagManagementTab />)

      await waitFor(() => {
        expect(tagService.getAllTagsWithStats).toHaveBeenCalled()
      })

      // 开始创建操作
      const createButton = screen.getByText('新建标签')
      fireEvent.click(createButton)

      // 验证模态窗口打开
      await waitFor(() => {
        expect(screen.getByRole('dialog')).toBeInTheDocument()
      })
    })
  })
})