import { describe, it, expect, beforeEach, vi } from 'vitest'
import { ConflictResolverService } from '../../src/services/ConflictResolverService'
import { ValidationUtils } from '../../src/utils/validation'
import type { 
  ConflictResolution,
  Bookmark,
  Category,
  Tag,
  ConflictItem
} from '../../src/types'

/**
 * 简化的导入导出功能集成测试
 * 专注于核心逻辑测试，避免依赖外部存储API
 */
describe('ImportExportIntegration - 简化版', () => {
  let conflictResolver: ConflictResolverService

  // 模拟数据
  const mockBookmarks: Bookmark[] = [
    {
      id: '1',
      title: 'Google',
      url: 'https://www.google.com',
      category: '搜索引擎',
      tags: ['搜索', '工具'],
      description: '全球最大的搜索引擎',
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-01T00:00:00Z'
    },
    {
      id: '2',
      title: 'GitHub',
      url: 'https://github.com',
      category: '开发工具',
      tags: ['开发', '代码'],
      description: '代码托管平台',
      createdAt: '2024-01-02T00:00:00Z',
      updatedAt: '2024-01-02T00:00:00Z'
    }
  ]

  const mockCategories: Category[] = [
    {
      id: '1',
      name: '搜索引擎',
      description: '各种搜索引擎',
      parentId: null,
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-01T00:00:00Z'
    },
    {
      id: '2',
      name: '开发工具',
      description: '开发相关工具',
      parentId: null,
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-01T00:00:00Z'
    }
  ]

  const mockTags: Tag[] = [
    {
      id: '1',
      name: '搜索',
      description: '搜索相关',
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-01T00:00:00Z'
    },
    {
      id: '2',
      name: '工具',
      description: '实用工具',
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-01T00:00:00Z'
    }
  ]

  beforeEach(() => {
    conflictResolver = new ConflictResolverService()
  })

  describe('冲突检测功能', () => {
    it('应该检测URL重复冲突', () => {
      const existingBookmarks = mockBookmarks
      const importBookmarks = [
        {
          id: '3',
          title: 'Google Search',
          url: 'https://www.google.com', // 重复URL
          category: '搜索引擎',
          tags: ['搜索'],
          description: '谷歌搜索引擎',
          createdAt: '2024-01-03T00:00:00Z',
          updatedAt: '2024-01-03T00:00:00Z'
        }
      ]

      const conflicts = conflictResolver.detectConflicts(
        existingBookmarks,
        importBookmarks,
        'bookmarks'
      )

      expect(conflicts).toHaveLength(1)
      expect(conflicts[0].conflictType).toBe('duplicate')
      expect(conflicts[0].existing.url).toBe(conflicts[0].imported.url)
    })

    it('应该检测内容相似冲突', () => {
      const existingBookmarks = mockBookmarks
      const importBookmarks = [
        {
          id: '4',
          title: 'Google Homepage', // 相似标题
          url: 'https://google.com', // 相似URL
          category: '搜索引擎',
          tags: ['搜索'],
          description: '谷歌主页',
          createdAt: '2024-01-04T00:00:00Z',
          updatedAt: '2024-01-04T00:00:00Z'
        }
      ]

      const conflicts = conflictResolver.detectConflicts(
        existingBookmarks,
        importBookmarks,
        'bookmarks'
      )

      expect(conflicts).toHaveLength(1)
      expect(conflicts[0].conflictType).toBe('similar')
      expect(conflicts[0].similarity).toBeGreaterThan(0.8)
    })

    it('应该检测分类名称冲突', () => {
      const existingCategories = mockCategories
      const importCategories = [
        {
          id: '3',
          name: '搜索引擎', // 重复名称
          description: '搜索引擎工具', // 不同描述
          parentId: null,
          createdAt: '2024-01-03T00:00:00Z',
          updatedAt: '2024-01-03T00:00:00Z'
        }
      ]

      const conflicts = conflictResolver.detectConflicts(
        existingCategories,
        importCategories,
        'categories'
      )

      expect(conflicts).toHaveLength(1)
      expect(conflicts[0].conflictType).toBe('name_conflict')
      expect(conflicts[0].existing.name).toBe(conflicts[0].imported.name)
    })

    it('应该检测标签名称冲突', () => {
      const existingTags = mockTags
      const importTags = [
        {
          id: '3',
          name: '搜索', // 重复名称
          description: '搜索功能', // 不同描述
          createdAt: '2024-01-03T00:00:00Z',
          updatedAt: '2024-01-03T00:00:00Z'
        }
      ]

      const conflicts = conflictResolver.detectConflicts(
        existingTags,
        importTags,
        'tags'
      )

      expect(conflicts).toHaveLength(1)
      expect(conflicts[0].conflictType).toBe('name_conflict')
      expect(conflicts[0].existing.name).toBe(conflicts[0].imported.name)
    })
  })

  describe('冲突解决功能', () => {
    it('应该支持保留现有数据', () => {
      const conflicts: ConflictItem[] = [
        {
          id: 'conflict1',
          type: 'bookmark',
          conflictType: 'duplicate',
          existing: mockBookmarks[0],
          imported: {
            ...mockBookmarks[0],
            id: '3',
            description: '更新的描述'
          },
          conflictFields: ['description'],
          suggestedResolution: 'keep_existing'
        }
      ]

      const resolutions: ConflictResolution[] = [
        {
          conflictId: 'conflict1',
          action: 'keep_existing'
        }
      ]

      const resolvedData = conflictResolver.resolveConflicts(conflicts, resolutions)

      expect(resolvedData).toHaveLength(1)
      expect(resolvedData[0].description).toBe('全球最大的搜索引擎') // 保持原描述
    })

    it('应该支持使用导入数据', () => {
      const conflicts: ConflictItem[] = [
        {
          id: 'conflict1',
          type: 'bookmark',
          conflictType: 'duplicate',
          existing: mockBookmarks[0],
          imported: {
            ...mockBookmarks[0],
            id: '3',
            description: '更新的描述'
          },
          conflictFields: ['description'],
          suggestedResolution: 'use_imported'
        }
      ]

      const resolutions: ConflictResolution[] = [
        {
          conflictId: 'conflict1',
          action: 'use_imported'
        }
      ]

      const resolvedData = conflictResolver.resolveConflicts(conflicts, resolutions)

      expect(resolvedData).toHaveLength(1)
      expect(resolvedData[0].description).toBe('更新的描述') // 使用新描述
    })

    it('应该支持智能合并', () => {
      const conflicts: ConflictItem[] = [
        {
          id: 'conflict1',
          type: 'bookmark',
          conflictType: 'similar',
          existing: {
            ...mockBookmarks[0],
            tags: ['搜索']
          },
          imported: {
            ...mockBookmarks[0],
            id: '3',
            description: '更详细的描述',
            tags: ['搜索', '工具', '引擎']
          },
          conflictFields: ['description', 'tags'],
          suggestedResolution: 'merge'
        }
      ]

      const resolutions: ConflictResolution[] = [
        {
          conflictId: 'conflict1',
          action: 'merge',
          mergeStrategy: 'smart'
        }
      ]

      const resolvedData = conflictResolver.resolveConflicts(conflicts, resolutions)

      expect(resolvedData).toHaveLength(1)
      expect(resolvedData[0].description).toBe('更详细的描述') // 使用更详细的描述
      expect(resolvedData[0].tags).toEqual(['搜索', '工具', '引擎']) // 合并标签
    })

    it('应该支持手动编辑', () => {
      const conflicts: ConflictItem[] = [
        {
          id: 'conflict1',
          type: 'bookmark',
          conflictType: 'data_mismatch',
          existing: mockBookmarks[0],
          imported: {
            ...mockBookmarks[0],
            id: '3',
            description: '更新的描述'
          },
          conflictFields: ['description'],
          suggestedResolution: 'manual_edit'
        }
      ]

      const resolutions: ConflictResolution[] = [
        {
          conflictId: 'conflict1',
          action: 'manual_edit',
          manualData: {
            ...mockBookmarks[0],
            description: '手动编辑的描述'
          }
        }
      ]

      const resolvedData = conflictResolver.resolveConflicts(conflicts, resolutions)

      expect(resolvedData).toHaveLength(1)
      expect(resolvedData[0].description).toBe('手动编辑的描述')
    })

    it('应该支持批量冲突解决', () => {
      const conflicts: ConflictItem[] = [
        {
          id: 'conflict1',
          type: 'bookmark',
          conflictType: 'duplicate',
          existing: mockBookmarks[0],
          imported: { ...mockBookmarks[0], id: '3', description: '描述1' },
          conflictFields: ['description'],
          suggestedResolution: 'merge'
        },
        {
          id: 'conflict2',
          type: 'bookmark',
          conflictType: 'duplicate',
          existing: mockBookmarks[1],
          imported: { ...mockBookmarks[1], id: '4', description: '描述2' },
          conflictFields: ['description'],
          suggestedResolution: 'merge'
        }
      ]

      const resolutions: ConflictResolution[] = conflicts.map(conflict => ({
        conflictId: conflict.id,
        action: 'merge',
        mergeStrategy: 'smart'
      }))

      const resolvedData = conflictResolver.resolveConflicts(conflicts, resolutions)

      expect(resolvedData).toHaveLength(2)
      expect(resolvedData[0].description).toBe('描述1')
      expect(resolvedData[1].description).toBe('描述2')
    })
  })

  describe('相似度计算', () => {
    it('应该正确计算收藏夹相似度', () => {
      const bookmark1 = mockBookmarks[0]
      const bookmark2 = {
        ...mockBookmarks[0],
        id: '3',
        title: 'Google Search',
        description: '谷歌搜索引擎'
      }

      const similarity = conflictResolver.calculateSimilarity(
        bookmark1,
        bookmark2,
        'bookmarks'
      )

      expect(similarity).toBeGreaterThan(0.8) // 高相似度
      expect(similarity).toBeLessThan(1.0) // 不完全相同
    })

    it('应该正确计算分类相似度', () => {
      const category1 = mockCategories[0]
      const category2 = {
        ...mockCategories[0],
        id: '3',
        description: '搜索引擎工具'
      }

      const similarity = conflictResolver.calculateSimilarity(
        category1,
        category2,
        'categories'
      )

      expect(similarity).toBeGreaterThan(0.5) // 中等相似度
    })

    it('应该正确计算标签相似度', () => {
      const tag1 = mockTags[0]
      const tag2 = {
        ...mockTags[0],
        id: '3',
        description: '搜索功能'
      }

      const similarity = conflictResolver.calculateSimilarity(
        tag1,
        tag2,
        'tags'
      )

      expect(similarity).toBeGreaterThan(0.5) // 中等相似度
    })
  })

  describe('数据验证集成', () => {
    it('应该验证导入数据格式', () => {
      const validImportData = {
        version: '1.0',
        exportDate: '2024-08-01T10:00:00Z',
        exportType: 'all',
        bookmarks: mockBookmarks,
        categories: mockCategories,
        tags: mockTags,
        metadata: {
          totalBookmarks: 2,
          totalCategories: 2,
          totalTags: 2,
          exportedBy: 'Universe Bag v1.0',
          exportedAt: '2024-08-01T10:00:00Z'
        }
      }

      const result = ValidationUtils.validateImportData(validImportData)
      expect(result.isValid).toBe(true)
      expect(result.errors).toHaveLength(0)
    })

    it('应该检测无效的导入数据', () => {
      const invalidImportData = {
        // 缺少必需字段
        bookmarks: [
          {
            title: 'Invalid Bookmark'
            // 缺少URL
          }
        ]
      }

      const result = ValidationUtils.validateImportData(invalidImportData)
      expect(result.isValid).toBe(false)
      expect(result.errors.length).toBeGreaterThan(0)
    })

    it('应该验证导出选项', () => {
      const validExportOptions = {
        format: 'json',
        includeBookmarks: true,
        includeCategories: true,
        includeTags: true,
        includeMetadata: true
      }

      const result = ValidationUtils.validateExportOptions(validExportOptions, 'all')
      expect(result.isValid).toBe(true)
      expect(result.errors).toHaveLength(0)
    })

    it('应该检测无效的导出选项', () => {
      const invalidExportOptions = {
        format: 'xml', // 不支持的格式
        includeBookmarks: 'yes' // 应该是布尔值
      }

      const result = ValidationUtils.validateExportOptions(invalidExportOptions, 'all')
      expect(result.isValid).toBe(false)
      expect(result.errors.length).toBeGreaterThan(0)
    })

    it('应该验证冲突解决方案', () => {
      const validResolutions = [
        {
          conflictId: 'conflict1',
          action: 'merge',
          mergeStrategy: 'smart'
        },
        {
          conflictId: 'conflict2',
          action: 'keep_existing'
        }
      ]

      const result = ValidationUtils.validateConflictResolutions(validResolutions)
      expect(result.isValid).toBe(true)
      expect(result.errors).toHaveLength(0)
    })

    it('应该检测无效的冲突解决方案', () => {
      const invalidResolutions = [
        {
          // 缺少conflictId
          action: 'invalid_action' // 无效的操作
        }
      ]

      const result = ValidationUtils.validateConflictResolutions(invalidResolutions)
      expect(result.isValid).toBe(false)
      expect(result.errors.length).toBeGreaterThan(0)
    })

    it('应该检查数据完整性', () => {
      const dataWithIntegrityIssues = {
        version: '1.0',
        exportDate: '2024-08-01T10:00:00Z',
        exportType: 'all',
        bookmarks: [
          {
            id: '1',
            title: 'Test Bookmark',
            url: 'https://test.com',
            category: '不存在的分类', // 引用不存在的分类
            tags: ['不存在的标签'], // 引用不存在的标签
            description: '测试收藏',
            createdAt: '2024-01-01T00:00:00Z',
            updatedAt: '2024-01-01T00:00:00Z'
          }
        ],
        categories: [], // 空分类列表
        tags: [], // 空标签列表
        metadata: {
          totalBookmarks: 1,
          totalCategories: 0,
          totalTags: 0,
          exportedBy: 'Universe Bag v1.0',
          exportedAt: '2024-08-01T10:00:00Z'
        }
      }

      const result = ValidationUtils.checkDataIntegrity(dataWithIntegrityIssues)
      expect(result.isValid).toBe(false)
      expect(result.errors.some(e => e.code === 'CATEGORY_REFERENCE_INVALID')).toBe(true)
      expect(result.errors.some(e => e.code === 'TAG_REFERENCE_INVALID')).toBe(true)
    })
  })

  describe('批量数据处理', () => {
    it('应该支持批量验证', () => {
      const bookmarks = [
        {
          id: '1',
          title: 'Valid Bookmark',
          url: 'https://valid.com',
          category: '测试',
          tags: [],
          description: '有效收藏',
          createdAt: '2024-01-01T00:00:00Z',
          updatedAt: '2024-01-01T00:00:00Z'
        },
        {
          id: '2',
          title: 'Invalid Bookmark',
          // 缺少URL
          category: '测试',
          tags: [],
          description: '无效收藏',
          createdAt: '2024-01-01T00:00:00Z',
          updatedAt: '2024-01-01T00:00:00Z'
        }
      ]

      const result = ValidationUtils.batchValidate(
        bookmarks,
        (bookmark) => ValidationUtils.validateBookmark(bookmark)
      )

      expect(result.isValid).toBe(false)
      expect(result.errors.length).toBeGreaterThan(0)
      expect(result.errors.some(e => e.field.includes('[1]'))).toBe(true) // 第二项有错误
    })

    it('应该处理大量冲突检测', () => {
      // 创建大量测试数据
      const existingBookmarks = Array.from({ length: 100 }, (_, index) => ({
        id: `existing-${index}`,
        title: `Existing Bookmark ${index}`,
        url: `https://existing${index}.com`,
        category: '测试分类',
        tags: [`tag${index % 10}`],
        description: `现有收藏 ${index}`,
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-01T00:00:00Z'
      }))

      const importBookmarks = Array.from({ length: 50 }, (_, index) => ({
        id: `import-${index}`,
        title: `Import Bookmark ${index}`,
        url: `https://existing${index * 2}.com`, // 部分URL重复
        category: '测试分类',
        tags: [`tag${index % 5}`],
        description: `导入收藏 ${index}`,
        createdAt: '2024-01-02T00:00:00Z',
        updatedAt: '2024-01-02T00:00:00Z'
      }))

      const conflicts = conflictResolver.detectConflicts(
        existingBookmarks,
        importBookmarks,
        'bookmarks'
      )

      // 应该检测到一些冲突
      expect(conflicts.length).toBeGreaterThan(0)
      expect(conflicts.length).toBeLessThanOrEqual(50) // 不会超过导入数据量

      // 验证冲突类型
      conflicts.forEach(conflict => {
        expect(['duplicate', 'similar', 'name_conflict', 'data_mismatch']).toContain(conflict.conflictType)
        expect(conflict.existing).toBeDefined()
        expect(conflict.imported).toBeDefined()
      })
    })
  })

  describe('错误处理', () => {
    it('应该处理无效的冲突解决方案', () => {
      const conflicts: ConflictItem[] = [
        {
          id: 'conflict1',
          type: 'bookmark',
          conflictType: 'duplicate',
          existing: mockBookmarks[0],
          imported: { ...mockBookmarks[0], id: '3' },
          conflictFields: ['description'],
          suggestedResolution: 'merge'
        }
      ]

      const invalidResolutions: ConflictResolution[] = [
        {
          conflictId: 'nonexistent-conflict', // 不存在的冲突ID
          action: 'merge'
        }
      ]

      expect(() => {
        conflictResolver.resolveConflicts(conflicts, invalidResolutions)
      }).toThrow()
    })

    it('应该处理空数据集', () => {
      const conflicts = conflictResolver.detectConflicts([], [], 'bookmarks')
      expect(conflicts).toHaveLength(0)

      const resolvedData = conflictResolver.resolveConflicts([], [])
      expect(resolvedData).toHaveLength(0)
    })

    it('应该处理不支持的数据类型', () => {
      expect(() => {
        conflictResolver.detectConflicts(
          mockBookmarks,
          mockBookmarks,
          'unsupported' as any
        )
      }).toThrow()
    })
  })
})