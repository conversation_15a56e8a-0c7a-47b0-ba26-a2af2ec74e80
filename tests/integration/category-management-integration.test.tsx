// 分类管理集成测试 - 验证与现有系统的兼容性

import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest'
import CategoryManagementTab from '../../src/components/CategoryManagementTab'
import { categoryService } from '../../src/services/categoryService'
import { indexedDBService } from '../../src/utils/indexedDB'
import type { Category, Bookmark } from '../../src/types'
import type { CategoryWithStats } from '../../src/components/CategoryList'

// Mock IndexedDB服务
vi.mock('../../src/utils/indexedDB', () => ({
  indexedDBService: {
    getCategories: vi.fn(),
    getBookmarks: vi.fn(),
    saveCategory: vi.fn(),
    updateBookmark: vi.fn(),
    deleteCategory: vi.fn()
  }
}))

// Mock AI服务
vi.mock('../../src/services/aiService', () => ({
  aiService: {
    generateCategory: vi.fn().mockResolvedValue({
      category: '技术',
      confidence: 0.8
    })
  }
}))

describe('分类管理集成测试', () => {
  const mockCategories: Category[] = [
    {
      id: 'category-1',
      name: '技术',
      description: '技术相关内容',
      color: '#3B82F6',
      createdAt: new Date('2024-01-01'),
      updatedAt: new Date('2024-01-01'),
      bookmarkCount: 0
    },
    {
      id: 'category-2',
      name: '学习',
      description: '学习资料',
      color: '#10B981',
      createdAt: new Date('2024-01-02'),
      updatedAt: new Date('2024-01-02'),
      bookmarkCount: 0
    }
  ]

  const mockBookmarks: Bookmark[] = [
    {
      id: 'bookmark-1',
      title: 'React 官方文档',
      url: 'https://react.dev',
      description: 'React 官方文档',
      content: 'React 是一个用于构建用户界面的 JavaScript 库',
      category: '技术',
      tags: ['react', 'javascript'],
      createdAt: new Date('2024-01-01'),
      updatedAt: new Date('2024-01-01'),
      favicon: 'https://react.dev/favicon.ico'
    },
    {
      id: 'bookmark-2',
      title: 'JavaScript 教程',
      url: 'https://javascript.info',
      description: 'JavaScript 学习教程',
      content: '现代 JavaScript 教程',
      category: '学习',
      tags: ['javascript', 'tutorial'],
      createdAt: new Date('2024-01-02'),
      updatedAt: new Date('2024-01-02'),
      favicon: 'https://javascript.info/favicon.ico'
    },
    {
      id: 'bookmark-3',
      title: '默认分类书签',
      url: 'https://example.com',
      description: '未分类的书签',
      content: '这是一个未分类的书签',
      category: '默认分类',
      tags: [],
      createdAt: new Date('2024-01-03'),
      updatedAt: new Date('2024-01-03'),
      favicon: 'https://example.com/favicon.ico'
    }
  ]

  beforeEach(() => {
    vi.clearAllMocks()
    
    // 设置默认的mock返回值
    vi.mocked(indexedDBService.getCategories).mockResolvedValue(mockCategories)
    vi.mocked(indexedDBService.getBookmarks).mockResolvedValue(mockBookmarks)
    vi.mocked(indexedDBService.saveCategory).mockResolvedValue(undefined)
    vi.mocked(indexedDBService.updateBookmark).mockResolvedValue(undefined)
    vi.mocked(indexedDBService.deleteCategory).mockResolvedValue(undefined)
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('数据同步和兼容性', () => {
    it('应该正确加载现有的分类和书签数据', async () => {
      render(<CategoryManagementTab />)

      await waitFor(() => {
        expect(indexedDBService.getCategories).toHaveBeenCalled()
        expect(indexedDBService.getBookmarks).toHaveBeenCalled()
      })

      // 验证分类统计信息的计算
      await waitFor(() => {
        // 应该显示正确的分类数量和书签统计
        expect(screen.getByText('2')).toBeInTheDocument() // 总分类数
        expect(screen.getByText('总分类数')).toBeInTheDocument()
      })
    })

    it('应该正确计算每个分类的书签数量', async () => {
      render(<CategoryManagementTab />)

      await waitFor(() => {
        // 验证统计信息计算正确
        // 技术分类: 1个书签
        // 学习分类: 1个书签  
        // 默认分类: 1个书签（但不在categories列表中）
        expect(screen.getByText('2')).toBeInTheDocument() // 活跃分类数
        expect(screen.getByText('活跃分类')).toBeInTheDocument()
      })
    })

    it('应该处理书签分类名称与分类实体的映射', async () => {
      // 测试书签的category字段与Category实体的name字段的对应关系
      const categoriesWithStats = await categoryService.getAllCategoriesWithStats()
      
      expect(categoriesWithStats).toHaveLength(2)
      
      // 验证技术分类的书签数量
      const techCategory = categoriesWithStats.find(cat => cat.name === '技术')
      expect(techCategory?.bookmarkCount).toBe(1)
      
      // 验证学习分类的书签数量
      const learningCategory = categoriesWithStats.find(cat => cat.name === '学习')
      expect(learningCategory?.bookmarkCount).toBe(1)
    })
  })

  describe('分类创建和书签同步', () => {
    it('应该创建新分类并保持与书签系统的兼容性', async () => {
      const newCategory = {
        id: 'category-3',
        name: '工具',
        description: '实用工具',
        color: '#F59E0B',
        createdAt: new Date(),
        updatedAt: new Date(),
        bookmarkCount: 0
      }

      vi.mocked(indexedDBService.saveCategory).mockResolvedValue(undefined)
      
      // 模拟创建后的数据状态
      vi.mocked(indexedDBService.getCategories).mockResolvedValueOnce([...mockCategories, newCategory])

      render(<CategoryManagementTab />)

      await waitFor(() => {
        expect(screen.getByText('新建分类')).toBeInTheDocument()
      })

      // 打开创建模态窗口
      fireEvent.click(screen.getByText('新建分类'))

      // 这里需要更详细的表单交互测试
      // 由于我们使用了mock组件，实际的表单提交需要在真实环境中测试
    })

    it('应该验证分类名称的唯一性', async () => {
      // 测试创建重复名称的分类
      const isUnique = await categoryService.validateCategoryName('技术')
      expect(isUnique).toBe(false)

      const isUniqueNew = await categoryService.validateCategoryName('新分类')
      expect(isUniqueNew).toBe(true)
    })
  })

  describe('分类更新和书签关联', () => {
    it('应该更新分类名称并同步相关书签', async () => {
      const updatedCategory = {
        ...mockCategories[0],
        name: '前端技术',
        updatedAt: new Date()
      }

      vi.mocked(indexedDBService.saveCategory).mockResolvedValue(undefined)

      // 模拟更新分类名称
      await categoryService.updateCategory('category-1', { name: '前端技术' })

      // 验证相关书签的分类也被更新
      expect(indexedDBService.updateBookmark).toHaveBeenCalledWith('bookmark-1', {
        category: '前端技术'
      })
    })

    it('应该处理分类更新时的数据一致性', async () => {
      // 确保分类更新不会破坏现有的书签关联
      const originalBookmark = mockBookmarks[0]
      
      await categoryService.updateCategory('category-1', { 
        description: '更新的描述' 
      })

      // 验证书签的分类关联没有被意外修改
      expect(indexedDBService.updateBookmark).not.toHaveBeenCalled()
    })
  })

  describe('分类删除和书签迁移', () => {
    it('应该删除分类并将相关书签移动到默认分类', async () => {
      render(<CategoryManagementTab />)

      await waitFor(() => {
        expect(screen.getByText('2')).toBeInTheDocument()
      })

      // 模拟删除分类
      await categoryService.deleteCategory('category-1')

      // 验证相关书签被移动到默认分类
      expect(indexedDBService.updateBookmark).toHaveBeenCalledWith('bookmark-1', {
        category: '默认分类'
      })

      // 验证分类被删除
      expect(indexedDBService.deleteCategory).toHaveBeenCalledWith('category-1')
    })

    it('应该正确处理空分类的删除', async () => {
      // 创建一个没有书签的分类
      const emptyCategory = {
        ...mockCategories[0],
        id: 'empty-category'
      }

      vi.mocked(indexedDBService.getCategories).mockResolvedValue([emptyCategory])
      vi.mocked(indexedDBService.getBookmarks).mockResolvedValue([])

      await categoryService.deleteCategory('empty-category')

      // 验证没有书签需要迁移
      expect(indexedDBService.updateBookmark).not.toHaveBeenCalled()
      
      // 验证分类被删除
      expect(indexedDBService.deleteCategory).toHaveBeenCalledWith('empty-category')
    })
  })

  describe('智能分类功能集成', () => {
    it('应该与现有的智能分类功能兼容', async () => {
      const testContent = 'React hooks 使用指南'
      const suggestedCategory = await categoryService.suggestCategory(testContent)

      // 验证智能分类功能正常工作
      expect(suggestedCategory).toBe('技术')
    })

    it('应该支持从书签中同步分类', async () => {
      // 添加一个新的书签分类
      const bookmarksWithNewCategory = [
        ...mockBookmarks,
        {
          ...mockBookmarks[0],
          id: 'bookmark-4',
          category: '设计',
          title: 'Figma 设计工具'
        }
      ]

      vi.mocked(indexedDBService.getBookmarks).mockResolvedValue(bookmarksWithNewCategory)

      const syncedCategories = await categoryService.syncCategoriesFromBookmarks()

      // 验证新分类被创建
      expect(indexedDBService.saveCategory).toHaveBeenCalledWith(
        expect.objectContaining({
          name: '设计',
          description: '从书签自动提取的分类'
        })
      )
    })
  })

  describe('错误处理和恢复', () => {
    it('应该处理数据加载失败', async () => {
      vi.mocked(indexedDBService.getCategories).mockRejectedValue(new Error('数据库连接失败'))

      render(<CategoryManagementTab />)

      await waitFor(() => {
        expect(screen.getByText('加载失败')).toBeInTheDocument()
        expect(screen.getByText('数据库连接失败')).toBeInTheDocument()
      })
    })

    it('应该处理分类操作失败', async () => {
      vi.mocked(indexedDBService.saveCategory).mockRejectedValue(new Error('保存失败'))

      const alertSpy = vi.spyOn(window, 'alert').mockImplementation(() => {})

      try {
        await categoryService.createCategory({
          name: '新分类',
          color: '#3B82F6'
        })
      } catch (error) {
        expect(error).toBeInstanceOf(Error)
      }

      alertSpy.mockRestore()
    })

    it('应该在网络错误后支持重试', async () => {
      vi.mocked(indexedDBService.getCategories)
        .mockRejectedValueOnce(new Error('网络错误'))
        .mockResolvedValueOnce(mockCategories)

      render(<CategoryManagementTab />)

      await waitFor(() => {
        expect(screen.getByText('重试')).toBeInTheDocument()
      })

      fireEvent.click(screen.getByText('重试'))

      await waitFor(() => {
        expect(screen.getByText('2')).toBeInTheDocument()
      })
    })
  })

  describe('性能和优化', () => {
    it('应该避免不必要的数据重新加载', async () => {
      render(<CategoryManagementTab />)

      await waitFor(() => {
        expect(indexedDBService.getCategories).toHaveBeenCalledTimes(1)
      })

      // 多次操作不应该导致重复加载
      fireEvent.click(screen.getByText('新建分类'))
      fireEvent.click(screen.getByText('关闭'))

      // 验证没有额外的数据加载
      expect(indexedDBService.getCategories).toHaveBeenCalledTimes(1)
    })

    it('应该正确处理大量分类数据', async () => {
      // 创建大量分类数据
      const manyCategories = Array.from({ length: 100 }, (_, index) => ({
        ...mockCategories[0],
        id: `category-${index}`,
        name: `分类 ${index}`
      }))

      vi.mocked(indexedDBService.getCategories).mockResolvedValue(manyCategories)

      render(<CategoryManagementTab />)

      await waitFor(() => {
        expect(screen.getByText('100')).toBeInTheDocument()
        expect(screen.getByText('总分类数')).toBeInTheDocument()
      })
    })
  })

  describe('用户体验和反馈', () => {
    it('应该提供适当的加载状态反馈', async () => {
      // 模拟慢速加载
      vi.mocked(indexedDBService.getCategories).mockImplementation(
        () => new Promise(resolve => setTimeout(() => resolve(mockCategories), 100))
      )

      render(<CategoryManagementTab />)

      // 验证加载状态显示
      expect(screen.getByText('Loading: true')).toBeInTheDocument()

      await waitFor(() => {
        expect(screen.getByText('Loading: false')).toBeInTheDocument()
      }, { timeout: 200 })
    })

    it('应该在操作成功后提供反馈', async () => {
      // 这个测试需要在真实环境中验证用户反馈机制
      // 当前的实现使用console.log，在生产环境中应该使用更好的通知系统
      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {})

      await categoryService.createCategory({
        name: '新分类',
        color: '#3B82F6'
      })

      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('创建分类成功')
      )

      consoleSpy.mockRestore()
    })
  })
})