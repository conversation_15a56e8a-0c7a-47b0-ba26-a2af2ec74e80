import { describe, it, expect, beforeEach, vi } from 'vitest'
import { ImportExportManagerService } from '../../src/services/BookmarkImportExportService'
import { ConflictResolverService } from '../../src/services/ConflictResolverService'
import { ValidationUtils } from '../../src/utils/validation'
import type { 
  ImportData, 
  ExportAllOptions, 
  ConflictResolution,
  Bookmark,
  Category,
  Tag
} from '../../src/types'

/**
 * 导入导出功能集成测试
 * 测试完整的导入导出流程，包括冲突处理
 */
describe('ImportExportIntegration', () => {
  let importExportService: ImportExportManagerService
  let conflictResolver: ConflictResolverService

  // 模拟数据
  const mockBookmarks: Bookmark[] = [
    {
      id: '1',
      title: 'Google',
      url: 'https://www.google.com',
      category: '搜索引擎',
      tags: ['搜索', '工具'],
      description: '全球最大的搜索引擎',
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-01T00:00:00Z'
    },
    {
      id: '2',
      title: 'GitHub',
      url: 'https://github.com',
      category: '开发工具',
      tags: ['开发', '代码'],
      description: '代码托管平台',
      createdAt: '2024-01-02T00:00:00Z',
      updatedAt: '2024-01-02T00:00:00Z'
    }
  ]

  const mockCategories: Category[] = [
    {
      id: '1',
      name: '搜索引擎',
      description: '各种搜索引擎',
      parentId: null,
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-01T00:00:00Z'
    },
    {
      id: '2',
      name: '开发工具',
      description: '开发相关工具',
      parentId: null,
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-01T00:00:00Z'
    }
  ]

  const mockTags: Tag[] = [
    {
      id: '1',
      name: '搜索',
      description: '搜索相关',
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-01T00:00:00Z'
    },
    {
      id: '2',
      name: '工具',
      description: '实用工具',
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-01T00:00:00Z'
    }
  ]

  beforeEach(() => {
    // 模拟服务依赖
    const mockBookmarkService = {
      getAllBookmarks: vi.fn().mockResolvedValue(mockBookmarks),
      addBookmark: vi.fn().mockResolvedValue(true),
      updateBookmark: vi.fn().mockResolvedValue(true),
      deleteBookmark: vi.fn().mockResolvedValue(true)
    }

    const mockCategoryService = {
      getAllCategories: vi.fn().mockResolvedValue(mockCategories),
      addCategory: vi.fn().mockResolvedValue(true),
      updateCategory: vi.fn().mockResolvedValue(true),
      deleteCategory: vi.fn().mockResolvedValue(true)
    }

    const mockTagService = {
      getAllTags: vi.fn().mockResolvedValue(mockTags),
      addTag: vi.fn().mockResolvedValue(true),
      updateTag: vi.fn().mockResolvedValue(true),
      deleteTag: vi.fn().mockResolvedValue(true)
    }

    importExportService = new ImportExportManagerService(
      mockBookmarkService,
      mockCategoryService,
      mockTagService
    )
    conflictResolver = new ConflictResolverService()
  })

  describe('完整导出流程', () => {
    it('应该成功导出全部数据', async () => {
      const options: ExportAllOptions = {
        format: 'json',
        includeBookmarks: true,
        includeCategories: true,
        includeTags: true,
        includeMetadata: true
      }

      const result = await importExportService.exportAllData(options)
      const exportedData = JSON.parse(result)

      // 验证导出数据结构
      expect(exportedData).toHaveProperty('version')
      expect(exportedData).toHaveProperty('exportDate')
      expect(exportedData).toHaveProperty('exportType', 'all')
      expect(exportedData).toHaveProperty('bookmarks')
      expect(exportedData).toHaveProperty('categories')
      expect(exportedData).toHaveProperty('tags')
      expect(exportedData).toHaveProperty('metadata')

      // 验证数据内容
      expect(exportedData.bookmarks).toHaveLength(2)
      expect(exportedData.categories).toHaveLength(2)
      expect(exportedData.tags).toHaveLength(2)
      expect(exportedData.metadata.totalBookmarks).toBe(2)
      expect(exportedData.metadata.totalCategories).toBe(2)
      expect(exportedData.metadata.totalTags).toBe(2)
    })

    it('应该支持按日期范围导出', async () => {
      const options: ExportAllOptions = {
        format: 'json',
        includeBookmarks: true,
        includeCategories: true,
        includeTags: true,
        includeMetadata: true,
        dateRange: {
          start: '2024-01-01T00:00:00Z',
          end: '2024-01-01T23:59:59Z'
        }
      }

      const result = await importExportService.exportAllData(options)
      const exportedData = JSON.parse(result)

      // 应该只包含指定日期范围内的数据
      expect(exportedData.bookmarks).toHaveLength(1) // 只有Google在范围内
      expect(exportedData.bookmarks[0].title).toBe('Google')
    })
  })

  describe('完整导入流程', () => {
    it('应该成功导入无冲突的数据', async () => {
      const importData: ImportData = {
        version: '1.0',
        exportDate: '2024-08-01T10:00:00Z',
        exportType: 'all',
        bookmarks: [
          {
            id: '3',
            title: 'Stack Overflow',
            url: 'https://stackoverflow.com',
            category: '开发工具',
            tags: ['开发', '问答'],
            description: '程序员问答社区',
            createdAt: '2024-01-03T00:00:00Z',
            updatedAt: '2024-01-03T00:00:00Z'
          }
        ],
        categories: [
          {
            id: '3',
            name: '学习资源',
            description: '学习相关资源',
            parentId: null,
            createdAt: '2024-01-03T00:00:00Z',
            updatedAt: '2024-01-03T00:00:00Z'
          }
        ],
        tags: [
          {
            id: '3',
            name: '问答',
            description: '问答相关',
            createdAt: '2024-01-03T00:00:00Z',
            updatedAt: '2024-01-03T00:00:00Z'
          }
        ],
        metadata: {
          totalBookmarks: 1,
          totalCategories: 1,
          totalTags: 1,
          exportedBy: 'Universe Bag v1.0',
          exportedAt: '2024-08-01T10:00:00Z'
        }
      }

      const result = await importExportService.importData(importData, {
        conflictResolution: 'auto',
        validateData: true,
        batchSize: 100
      })

      expect(result.success).toBe(true)
      expect(result.conflicts).toHaveLength(0)
      expect(result.imported.bookmarks).toBe(1)
      expect(result.imported.categories).toBe(1)
      expect(result.imported.tags).toBe(1)
      expect(result.errors).toHaveLength(0)
    })

    it('应该检测并处理数据冲突', async () => {
      const importData: ImportData = {
        version: '1.0',
        exportDate: '2024-08-01T10:00:00Z',
        exportType: 'all',
        bookmarks: [
          {
            id: '4',
            title: 'Google Search', // 与现有数据相似
            url: 'https://www.google.com', // URL重复
            category: '搜索引擎',
            tags: ['搜索', '工具'],
            description: '谷歌搜索引擎', // 描述不同
            createdAt: '2024-01-04T00:00:00Z',
            updatedAt: '2024-01-04T00:00:00Z'
          }
        ],
        categories: [
          {
            id: '4',
            name: '搜索引擎', // 名称冲突
            description: '搜索引擎工具', // 描述不同
            parentId: null,
            createdAt: '2024-01-04T00:00:00Z',
            updatedAt: '2024-01-04T00:00:00Z'
          }
        ],
        tags: [],
        metadata: {
          totalBookmarks: 1,
          totalCategories: 1,
          totalTags: 0,
          exportedBy: 'Universe Bag v1.0',
          exportedAt: '2024-08-01T10:00:00Z'
        }
      }

      const result = await importExportService.importData(importData, {
        conflictResolution: 'prompt',
        validateData: true,
        batchSize: 100
      })

      expect(result.success).toBe(false) // 因为有冲突需要处理
      expect(result.conflicts.length).toBeGreaterThan(0)
      
      // 验证冲突类型
      const bookmarkConflict = result.conflicts.find(c => c.type === 'bookmark')
      expect(bookmarkConflict).toBeDefined()
      expect(bookmarkConflict?.conflictType).toBe('duplicate') // URL重复

      const categoryConflict = result.conflicts.find(c => c.type === 'category')
      expect(categoryConflict).toBeDefined()
      expect(categoryConflict?.conflictType).toBe('name_conflict') // 名称冲突
    })
  })

  describe('冲突解决流程', () => {
    it('应该正确解决冲突并继续导入', async () => {
      // 首先创建有冲突的导入数据
      const importData: ImportData = {
        version: '1.0',
        exportDate: '2024-08-01T10:00:00Z',
        exportType: 'bookmarks',
        bookmarks: [
          {
            id: '5',
            title: 'Google',
            url: 'https://www.google.com',
            category: '搜索引擎',
            tags: ['搜索', '工具'],
            description: '更新的描述信息',
            createdAt: '2024-01-05T00:00:00Z',
            updatedAt: '2024-01-05T00:00:00Z'
          }
        ],
        categories: [],
        tags: [],
        metadata: {
          totalBookmarks: 1,
          totalCategories: 0,
          totalTags: 0,
          exportedBy: 'Universe Bag v1.0',
          exportedAt: '2024-08-01T10:00:00Z'
        }
      }

      // 第一步：检测冲突
      const initialResult = await importExportService.importData(importData, {
        conflictResolution: 'prompt',
        validateData: true,
        batchSize: 100
      })

      expect(initialResult.conflicts).toHaveLength(1)
      const conflict = initialResult.conflicts[0]

      // 第二步：解决冲突
      const resolutions: ConflictResolution[] = [
        {
          conflictId: conflict.id,
          action: 'merge',
          mergeStrategy: 'smart'
        }
      ]

      const resolvedData = conflictResolver.resolveConflicts(
        initialResult.conflicts,
        resolutions
      )

      expect(resolvedData).toHaveLength(1)
      
      // 验证合并结果
      const mergedBookmark = resolvedData[0]
      expect(mergedBookmark.title).toBe('Google') // 保持原标题
      expect(mergedBookmark.description).toBe('更新的描述信息') // 使用新描述
      expect(mergedBookmark.url).toBe('https://www.google.com')
    })

    it('应该支持批量冲突解决', async () => {
      const importData: ImportData = {
        version: '1.0',
        exportDate: '2024-08-01T10:00:00Z',
        exportType: 'all',
        bookmarks: [
          {
            id: '6',
            title: 'Google',
            url: 'https://www.google.com',
            category: '搜索引擎',
            tags: ['搜索'],
            description: '谷歌搜索',
            createdAt: '2024-01-06T00:00:00Z',
            updatedAt: '2024-01-06T00:00:00Z'
          },
          {
            id: '7',
            title: 'GitHub',
            url: 'https://github.com',
            category: '开发工具',
            tags: ['开发'],
            description: '代码仓库',
            createdAt: '2024-01-07T00:00:00Z',
            updatedAt: '2024-01-07T00:00:00Z'
          }
        ],
        categories: [],
        tags: [],
        metadata: {
          totalBookmarks: 2,
          totalCategories: 0,
          totalTags: 0,
          exportedBy: 'Universe Bag v1.0',
          exportedAt: '2024-08-01T10:00:00Z'
        }
      }

      const result = await importExportService.importData(importData, {
        conflictResolution: 'prompt',
        validateData: true,
        batchSize: 100
      })

      expect(result.conflicts).toHaveLength(2) // 两个冲突

      // 批量解决：全部使用智能合并
      const resolutions: ConflictResolution[] = result.conflicts.map(conflict => ({
        conflictId: conflict.id,
        action: 'merge',
        mergeStrategy: 'smart'
      }))

      const resolvedData = conflictResolver.resolveConflicts(
        result.conflicts,
        resolutions
      )

      expect(resolvedData).toHaveLength(2)
      resolvedData.forEach(item => {
        expect(item).toHaveProperty('title')
        expect(item).toHaveProperty('url')
        expect(item).toHaveProperty('description')
      })
    })
  })

  describe('数据验证集成', () => {
    it('应该拒绝无效的导入数据', async () => {
      const invalidImportData = {
        // 缺少必需字段
        bookmarks: [
          {
            title: 'Invalid Bookmark',
            // 缺少URL
            category: '测试'
          }
        ]
      }

      const validationResult = ValidationUtils.validateImportData(invalidImportData)
      expect(validationResult.isValid).toBe(false)
      expect(validationResult.errors.length).toBeGreaterThan(0)

      // 导入应该失败
      await expect(
        importExportService.importData(invalidImportData as any, {
          conflictResolution: 'auto',
          validateData: true,
          batchSize: 100
        })
      ).rejects.toThrow()
    })

    it('应该验证数据完整性', async () => {
      const importData: ImportData = {
        version: '1.0',
        exportDate: '2024-08-01T10:00:00Z',
        exportType: 'all',
        bookmarks: [
          {
            id: '8',
            title: 'Test Bookmark',
            url: 'https://test.com',
            category: '不存在的分类', // 引用不存在的分类
            tags: ['不存在的标签'], // 引用不存在的标签
            description: '测试收藏',
            createdAt: '2024-01-08T00:00:00Z',
            updatedAt: '2024-01-08T00:00:00Z'
          }
        ],
        categories: [], // 空分类列表
        tags: [], // 空标签列表
        metadata: {
          totalBookmarks: 1,
          totalCategories: 0,
          totalTags: 0,
          exportedBy: 'Universe Bag v1.0',
          exportedAt: '2024-08-01T10:00:00Z'
        }
      }

      const integrityResult = ValidationUtils.checkDataIntegrity(importData)
      expect(integrityResult.isValid).toBe(false)
      expect(integrityResult.errors.some(e => e.code === 'CATEGORY_REFERENCE_INVALID')).toBe(true)
      expect(integrityResult.errors.some(e => e.code === 'TAG_REFERENCE_INVALID')).toBe(true)
    })
  })

  describe('性能和错误处理', () => {
    it('应该处理大量数据的导入', async () => {
      // 创建大量测试数据
      const largeBookmarkList = Array.from({ length: 1000 }, (_, index) => ({
        id: `large-${index}`,
        title: `Bookmark ${index}`,
        url: `https://example${index}.com`,
        category: '测试分类',
        tags: [`tag${index % 10}`],
        description: `测试收藏 ${index}`,
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-01T00:00:00Z'
      }))

      const largeImportData: ImportData = {
        version: '1.0',
        exportDate: '2024-08-01T10:00:00Z',
        exportType: 'bookmarks',
        bookmarks: largeBookmarkList,
        categories: [],
        tags: [],
        metadata: {
          totalBookmarks: 1000,
          totalCategories: 0,
          totalTags: 0,
          exportedBy: 'Universe Bag v1.0',
          exportedAt: '2024-08-01T10:00:00Z'
        }
      }

      const progressUpdates: number[] = []
      const result = await importExportService.importData(largeImportData, {
        conflictResolution: 'auto',
        validateData: true,
        batchSize: 50, // 小批次处理
        onProgress: (progress) => {
          progressUpdates.push(progress.percentage)
        }
      })

      expect(result.success).toBe(true)
      expect(result.imported.bookmarks).toBe(1000)
      expect(progressUpdates.length).toBeGreaterThan(0) // 应该有进度更新
      expect(Math.max(...progressUpdates)).toBe(100) // 最终进度应该是100%
    })

    it('应该处理网络错误和重试', async () => {
      // 模拟网络错误
      const mockBookmarkService = {
        getAllBookmarks: vi.fn().mockResolvedValue([]),
        addBookmark: vi.fn()
          .mockRejectedValueOnce(new Error('Network Error'))
          .mockRejectedValueOnce(new Error('Network Error'))
          .mockResolvedValue(true) // 第三次成功
      }

      const serviceWithRetry = new ImportExportManagerService(
        mockBookmarkService,
        { getAllCategories: vi.fn().mockResolvedValue([]) },
        { getAllTags: vi.fn().mockResolvedValue([]) }
      )

      const importData: ImportData = {
        version: '1.0',
        exportDate: '2024-08-01T10:00:00Z',
        exportType: 'bookmarks',
        bookmarks: [
          {
            id: '9',
            title: 'Test Retry',
            url: 'https://retry.com',
            category: '测试',
            tags: [],
            description: '重试测试',
            createdAt: '2024-01-09T00:00:00Z',
            updatedAt: '2024-01-09T00:00:00Z'
          }
        ],
        categories: [],
        tags: [],
        metadata: {
          totalBookmarks: 1,
          totalCategories: 0,
          totalTags: 0,
          exportedBy: 'Universe Bag v1.0',
          exportedAt: '2024-08-01T10:00:00Z'
        }
      }

      // 应该在重试后成功
      const result = await serviceWithRetry.importData(importData, {
        conflictResolution: 'auto',
        validateData: true,
        batchSize: 100
      })

      expect(result.success).toBe(true)
      expect(mockBookmarkService.addBookmark).toHaveBeenCalledTimes(3) // 重试了3次
    })
  })

  describe('端到端流程', () => {
    it('应该完成完整的导出-导入循环', async () => {
      // 第一步：导出现有数据
      const exportOptions: ExportAllOptions = {
        format: 'json',
        includeBookmarks: true,
        includeCategories: true,
        includeTags: true,
        includeMetadata: true
      }

      const exportedDataString = await importExportService.exportAllData(exportOptions)
      const exportedData = JSON.parse(exportedDataString)

      // 验证导出数据
      expect(exportedData.bookmarks).toHaveLength(2)
      expect(exportedData.categories).toHaveLength(2)
      expect(exportedData.tags).toHaveLength(2)

      // 第二步：清空现有数据（模拟）
      const emptyBookmarkService = {
        getAllBookmarks: vi.fn().mockResolvedValue([]),
        addBookmark: vi.fn().mockResolvedValue(true)
      }
      const emptyCategoryService = {
        getAllCategories: vi.fn().mockResolvedValue([]),
        addCategory: vi.fn().mockResolvedValue(true)
      }
      const emptyTagService = {
        getAllTags: vi.fn().mockResolvedValue([]),
        addTag: vi.fn().mockResolvedValue(true)
      }

      const emptyService = new ImportExportManagerService(
        emptyBookmarkService,
        emptyCategoryService,
        emptyTagService
      )

      // 第三步：导入之前导出的数据
      const importResult = await emptyService.importData(exportedData, {
        conflictResolution: 'auto',
        validateData: true,
        batchSize: 100
      })

      // 验证导入结果
      expect(importResult.success).toBe(true)
      expect(importResult.conflicts).toHaveLength(0) // 空数据库不应该有冲突
      expect(importResult.imported.bookmarks).toBe(2)
      expect(importResult.imported.categories).toBe(2)
      expect(importResult.imported.tags).toBe(2)

      // 验证服务调用
      expect(emptyBookmarkService.addBookmark).toHaveBeenCalledTimes(2)
      expect(emptyCategoryService.addCategory).toHaveBeenCalledTimes(2)
      expect(emptyTagService.addTag).toHaveBeenCalledTimes(2)
    })
  })
})