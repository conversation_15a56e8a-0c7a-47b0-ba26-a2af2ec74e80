// AI集成标签页组件测试

import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { describe, it, expect, vi, beforeEach } from 'vitest'
import AIIntegrationTab from '../src/components/AIIntegrationTab'
import { aiIntegrationService } from '../src/services/aiIntegrationService'

// Mock AI集成服务
vi.mock('../src/services/aiIntegrationService', () => ({
  aiIntegrationService: {
    getSupportedProviders: vi.fn(() => [
      {
        id: 'openai',
        name: 'OpenAI',
        description: 'OpenAI GPT系列模型',
        icon: '🤖',
        type: 'openai',
        defaultBaseUrl: 'https://api.openai.com/v1',
        requiresApiKey: true,
        supportedFeatures: ['chat', 'completion'],
        documentationUrl: 'https://platform.openai.com/docs'
      },
      {
        id: 'deepseek',
        name: 'DeepSeek',
        description: 'DeepSeek AI模型服务',
        icon: '🔍',
        type: 'deepseek',
        defaultBaseUrl: 'https://api.deepseek.com',
        requiresApiKey: true,
        supportedFeatures: ['chat', 'completion'],
        documentationUrl: 'https://platform.deepseek.com/docs'
      }
    ]),
    getConfiguredProviders: vi.fn(() => Promise.resolve([
      {
        id: 'test_1',
        name: '测试OpenAI',
        type: 'openai',
        baseUrl: 'https://api.openai.com/v1',
        apiKey: 'sk-test123',
        enabled: true,
        createdAt: new Date('2024-01-01'),
        updatedAt: new Date('2024-01-01')
      }
    ])),
    configureProvider: vi.fn(() => Promise.resolve()),
    updateProvider: vi.fn(() => Promise.resolve()),
    removeProvider: vi.fn(() => Promise.resolve()),
    testConnection: vi.fn(() => Promise.resolve({
      providerId: 'test_1',
      success: true,
      responseTime: 500,
      modelCount: 5,
      testedAt: new Date()
    })),
    testAllConnections: vi.fn(() => Promise.resolve([
      {
        providerId: 'test_1',
        success: true,
        responseTime: 500,
        modelCount: 5,
        testedAt: new Date()
      }
    ])),
    getAvailableModels: vi.fn(() => Promise.resolve([
      {
        id: 'gpt-4',
        name: 'gpt-4',
        displayName: 'GPT-4',
        description: 'OpenAI最先进的大型语言模型',
        capabilities: ['chat', 'completion'],
        providerId: 'test_1',
        isRecommended: true
      }
    ])),
    getSelectedModels: vi.fn(() => Promise.resolve({})),
    saveSelectedModel: vi.fn(() => Promise.resolve()),
    removeSelectedModel: vi.fn(() => Promise.resolve())
  }
}))

describe('AIIntegrationTab', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('应该正确渲染AI集成页面', async () => {
    render(<AIIntegrationTab />)
    
    // 等待数据加载完成
    await waitFor(() => {
      expect(screen.getByText('AI集成管理')).toBeInTheDocument()
    })
    
    // 检查页面标题和描述
    expect(screen.getByText('AI集成管理')).toBeInTheDocument()
    expect(screen.getByText(/管理和配置各种AI提供商/)).toBeInTheDocument()
  })

  it('应该显示已配置的提供商', async () => {
    render(<AIIntegrationTab />)
    
    await waitFor(() => {
      expect(screen.getByText('测试OpenAI')).toBeInTheDocument()
    })
    
    // 检查提供商信息
    expect(screen.getByText('测试OpenAI')).toBeInTheDocument()
    expect(screen.getByText('OpenAI')).toBeInTheDocument()
  })

  it('应该能够打开添加提供商对话框', async () => {
    render(<AIIntegrationTab />)
    
    await waitFor(() => {
      expect(screen.getByText('添加提供商')).toBeInTheDocument()
    })
    
    // 点击添加提供商按钮
    fireEvent.click(screen.getByText('添加提供商'))
    
    // 检查对话框是否打开
    await waitFor(() => {
      expect(screen.getByText('添加AI提供商')).toBeInTheDocument()
    })
  })

  it('应该显示使用说明', async () => {
    render(<AIIntegrationTab />)
    
    await waitFor(() => {
      expect(screen.getByText('使用说明')).toBeInTheDocument()
    })
    
    // 检查使用说明内容
    expect(screen.getByText('支持的AI提供商：')).toBeInTheDocument()
    expect(screen.getByText('配置说明：')).toBeInTheDocument()
    expect(screen.getByText('操作提示：')).toBeInTheDocument()
  })

  it('应该能够测试所有提供商连接', async () => {
    const { aiIntegrationService } = await import('../src/services/aiIntegrationService')
    
    render(<AIIntegrationTab />)
    
    await waitFor(() => {
      expect(screen.getByText('测试OpenAI')).toBeInTheDocument()
    })
    
    // 找到"测试所有连接"按钮并点击
    const testAllButton = screen.getByText('测试所有连接')
    fireEvent.click(testAllButton)
    
    // 验证测试所有连接方法被调用
    await waitFor(() => {
      expect(aiIntegrationService.testAllConnections).toHaveBeenCalled()
    })
  })

  it('应该能够选择模型', async () => {
    render(<AIIntegrationTab />)
    
    await waitFor(() => {
      expect(screen.getByText('测试OpenAI')).toBeInTheDocument()
    })
    
    // 验证模型选择相关的方法被调用
    expect(aiIntegrationService.getSelectedModels).toHaveBeenCalled()
  })

  it('应该显示已选择的模型摘要', async () => {
    // 模拟已选择的模型
    vi.mocked(aiIntegrationService.getSelectedModels).mockResolvedValue({
      'test_1': {
        modelId: 'gpt-4',
        selectedAt: new Date().toISOString()
      }
    })
    
    render(<AIIntegrationTab />)
    
    await waitFor(() => {
      expect(screen.getAllByText('测试OpenAI')).toHaveLength(2) // 一个在摘要中，一个在提供商列表中
    })
    
    // 验证已选择的模型摘要显示
    await waitFor(() => {
      expect(screen.queryByText('已选择的模型')).toBeInTheDocument()
    })
    
    // 验证模型ID显示在摘要中
    expect(screen.getAllByText('gpt-4')).toHaveLength(2) // 一个在摘要中，一个在提供商详情中
  })
})