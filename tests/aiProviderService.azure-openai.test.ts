// Azure OpenAI集成单元测试

import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest'
import { AIProviderService } from '../src/services/aiProviderService'

// Mock fetch
global.fetch = vi.fn()

describe('AIProviderService - Azure OpenAI集成', () => {
  let aiProviderService: AIProviderService
  const mockFetch = fetch as vi.MockedFunction<typeof fetch>

  beforeEach(() => {
    aiProviderService = new AIProviderService()
    vi.clearAllMocks()
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('testAzureOpenAIConnection', () => {
    const baseUrl = 'https://test-resource.openai.azure.com'
    const validApiKey = '********************************'
    const invalidApiKey = 'invalid-key'

    it('应该成功测试有效的Azure OpenAI连接', async () => {
      // 模拟成功的API响应
      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        json: async () => ({
          data: [
            {
              id: 'gpt-4-deployment',
              model: 'gpt-4',
              status: 'succeeded',
              created_at: **********,
              updated_at: **********
            },
            {
              id: 'gpt-35-turbo-deployment',
              model: 'gpt-35-turbo',
              status: 'succeeded',
              created_at: **********,
              updated_at: **********
            }
          ]
        })
      } as Response)

      const result = await aiProviderService.testAzureOpenAIConnection(baseUrl, validApiKey)

      expect(result.success).toBe(true)
      expect(result.modelCount).toBe(2)
      expect(result.error).toBeUndefined()
      expect(mockFetch).toHaveBeenCalledWith(
        `${baseUrl}/openai/deployments?api-version=2024-02-01`,
        expect.objectContaining({
          method: 'GET',
          headers: expect.objectContaining({
            'api-key': validApiKey,
            'Content-Type': 'application/json',
            'User-Agent': 'BookmarkExtension/1.0'
          })
        })
      )
    })

    it('应该处理空API密钥错误', async () => {
      const result = await aiProviderService.testAzureOpenAIConnection(baseUrl, '')

      expect(result.success).toBe(false)
      expect(result.error).toBe('API密钥不能为空')
      expect(mockFetch).not.toHaveBeenCalled()
    })

    it('应该处理无效的API密钥格式', async () => {
      const result = await aiProviderService.testAzureOpenAIConnection(baseUrl, invalidApiKey)

      expect(result.success).toBe(false)
      expect(result.error).toBe('无效的Azure OpenAI API密钥格式，应为32位十六进制字符串')
      expect(mockFetch).not.toHaveBeenCalled()
    })

    it('应该处理无效的端点URL格式', async () => {
      const invalidUrl = 'https://invalid-endpoint.com'
      const result = await aiProviderService.testAzureOpenAIConnection(invalidUrl, validApiKey)

      expect(result.success).toBe(false)
      expect(result.error).toBe('无效的Azure OpenAI端点URL，应包含".openai.azure.com"')
      expect(mockFetch).not.toHaveBeenCalled()
    })

    it('应该处理401未授权错误', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 401,
        statusText: 'Unauthorized'
      } as Response)

      const result = await aiProviderService.testAzureOpenAIConnection(baseUrl, validApiKey)

      expect(result.success).toBe(false)
      expect(result.error).toBe('API密钥无效或已过期')
    })

    it('应该处理403权限不足错误', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 403,
        statusText: 'Forbidden'
      } as Response)

      const result = await aiProviderService.testAzureOpenAIConnection(baseUrl, validApiKey)

      expect(result.success).toBe(false)
      expect(result.error).toBe('API密钥权限不足或资源访问被拒绝')
    })

    it('应该处理404端点不存在错误', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 404,
        statusText: 'Not Found'
      } as Response)

      const result = await aiProviderService.testAzureOpenAIConnection(baseUrl, validApiKey)

      expect(result.success).toBe(false)
      expect(result.error).toBe('Azure OpenAI端点不存在或配置错误')
    })

    it('应该处理429频率限制错误', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 429,
        statusText: 'Too Many Requests'
      } as Response)

      const result = await aiProviderService.testAzureOpenAIConnection(baseUrl, validApiKey)

      expect(result.success).toBe(false)
      expect(result.error).toBe('API请求频率限制，请稍后重试')
    })

    it('应该处理500服务器错误', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 500,
        statusText: 'Internal Server Error'
      } as Response)

      const result = await aiProviderService.testAzureOpenAIConnection(baseUrl, validApiKey)

      expect(result.success).toBe(false)
      expect(result.error).toBe('Azure OpenAI服务器内部错误')
    })

    it('应该处理503服务不可用错误', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 503,
        statusText: 'Service Unavailable'
      } as Response)

      const result = await aiProviderService.testAzureOpenAIConnection(baseUrl, validApiKey)

      expect(result.success).toBe(false)
      expect(result.error).toBe('Azure OpenAI服务暂时不可用')
    })

    it('应该处理网络超时错误', async () => {
      mockFetch.mockRejectedValueOnce(new DOMException('The operation was aborted', 'AbortError'))

      const result = await aiProviderService.testAzureOpenAIConnection(baseUrl, validApiKey)

      expect(result.success).toBe(false)
      expect(result.error).toBe('连接超时，请检查网络连接或API服务状态')
    })

    it('应该处理网络连接错误', async () => {
      mockFetch.mockRejectedValueOnce(new Error('fetch failed'))

      const result = await aiProviderService.testAzureOpenAIConnection(baseUrl, validApiKey)

      expect(result.success).toBe(false)
      expect(result.error).toBe('网络连接失败，请检查网络设置')
    })

    it('应该处理异常的API响应格式', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        json: async () => ({
          // 缺少data字段
          deployments: []
        })
      } as Response)

      const result = await aiProviderService.testAzureOpenAIConnection(baseUrl, validApiKey)

      expect(result.success).toBe(false)
      expect(result.error).toBe('Azure OpenAI API响应格式异常')
    })
  })

  describe('getAzureOpenAIModels', () => {
    const baseUrl = 'https://test-resource.openai.azure.com'
    const validApiKey = '********************************'

    it('应该成功获取Azure OpenAI部署列表', async () => {
      const mockDeployments = [
        {
          id: 'gpt-4-deployment',
          model: 'gpt-4',
          status: 'succeeded',
          created_at: **********,
          updated_at: **********,
          scale_settings: {
            scale_type: 'standard'
          }
        },
        {
          id: 'gpt-4-turbo-deployment',
          model: 'gpt-4-turbo',
          status: 'succeeded',
          created_at: **********,
          updated_at: **********,
          scale_settings: {
            scale_type: 'standard'
          }
        },
        {
          id: 'gpt-35-turbo-deployment',
          model: 'gpt-35-turbo',
          status: 'succeeded',
          created_at: **********,
          updated_at: **********,
          scale_settings: {
            scale_type: 'standard'
          }
        },
        {
          id: 'text-embedding-deployment',
          model: 'text-embedding-ada-002',
          status: 'succeeded',
          created_at: **********,
          updated_at: **********,
          scale_settings: {
            scale_type: 'standard'
          }
        },
        // 应该被过滤掉的失败部署
        {
          id: 'failed-deployment',
          model: 'gpt-4',
          status: 'failed',
          created_at: **********,
          updated_at: **********
        }
      ]

      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        json: async () => ({ data: mockDeployments })
      } as Response)

      const result = await aiProviderService.getAzureOpenAIModels(baseUrl, validApiKey)

      expect(result).toHaveLength(4) // 应该过滤掉1个失败的部署
      expect(result.every(model => model.providerId === 'azure-openai')).toBe(true)
      
      // 检查推荐模型
      const gpt4Deployment = result.find(m => m.id === 'gpt-4-deployment')
      expect(gpt4Deployment?.isRecommended).toBe(true)
      expect(gpt4Deployment?.displayName).toBe('GPT-4 (gpt-4-deployment)')
      expect(gpt4Deployment?.capabilities).toContain('chat')
      expect(gpt4Deployment?.capabilities).toContain('coding')
      expect(gpt4Deployment?.contextLength).toBe(8192)
      expect(gpt4Deployment?.deploymentName).toBe('gpt-4-deployment')
      expect(gpt4Deployment?.azureModel).toBe('gpt-4')

      // 检查GPT-4 Turbo
      const gpt4Turbo = result.find(m => m.id === 'gpt-4-turbo-deployment')
      expect(gpt4Turbo?.isRecommended).toBe(true)
      expect(gpt4Turbo?.displayName).toBe('GPT-4 Turbo (gpt-4-turbo-deployment)')
      expect(gpt4Turbo?.contextLength).toBe(128000)

      // 检查嵌入模型
      const embedding = result.find(m => m.id === 'text-embedding-deployment')
      expect(embedding?.capabilities).toContain('embedding')
      expect(embedding?.tags).toContain('文本嵌入')

      // 检查Azure特有属性
      result.forEach(model => {
        expect(model.deploymentName).toBeTruthy()
        expect(model.azureModel).toBeTruthy()
        expect(model.status).toBe('succeeded')
        expect(model.createdAt).toBeInstanceOf(Date)
      })
    })

    it('应该正确排序模型（推荐 > 热门 > 字母顺序）', async () => {
      const mockDeployments = [
        { id: 'text-davinci-deployment', model: 'text-davinci-003', status: 'succeeded' }, // 热门
        { id: 'gpt-35-turbo-deployment', model: 'gpt-35-turbo', status: 'succeeded' }, // 推荐且热门
        { id: 'gpt-4-deployment', model: 'gpt-4', status: 'succeeded' }, // 推荐且热门
        { id: 'embedding-deployment', model: 'text-embedding-ada-002', status: 'succeeded' } // 普通
      ]

      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        json: async () => ({ data: mockDeployments })
      } as Response)

      const result = await aiProviderService.getAzureOpenAIModels(baseUrl, validApiKey)

      // 推荐模型应该在前面
      const recommendedModels = result.filter(m => m.isRecommended)
      expect(recommendedModels.length).toBeGreaterThan(0)
      
      // 检查前两个模型都是推荐模型
      expect(result[0].isRecommended).toBe(true)
      expect(result[1].isRecommended).toBe(true)
    })

    it('应该处理API错误响应', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 401,
        statusText: 'Unauthorized'
      } as Response)

      const result = await aiProviderService.getAzureOpenAIModels(baseUrl, validApiKey)

      expect(result).toEqual([])
    })

    it('应该处理网络错误', async () => {
      mockFetch.mockRejectedValueOnce(new Error('Network error'))

      const result = await aiProviderService.getAzureOpenAIModels(baseUrl, validApiKey)

      expect(result).toEqual([])
    })

    it('应该处理空的部署列表响应', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        json: async () => ({ data: [] })
      } as Response)

      const result = await aiProviderService.getAzureOpenAIModels(baseUrl, validApiKey)

      expect(result).toEqual([])
    })

    it('应该处理缺少data字段的响应', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        json: async () => ({})
      } as Response)

      const result = await aiProviderService.getAzureOpenAIModels(baseUrl, validApiKey)

      expect(result).toEqual([])
    })

    it('应该只包含成功的部署', async () => {
      const mockDeployments = [
        { id: 'success-deployment', model: 'gpt-4', status: 'succeeded' },
        { id: 'failed-deployment', model: 'gpt-4', status: 'failed' },
        { id: 'pending-deployment', model: 'gpt-4', status: 'pending' }
      ]

      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        json: async () => ({ data: mockDeployments })
      } as Response)

      const result = await aiProviderService.getAzureOpenAIModels(baseUrl, validApiKey)

      expect(result).toHaveLength(1)
      expect(result[0].id).toBe('success-deployment')
      expect(result[0].status).toBe('succeeded')
    })
  })

  describe('Azure OpenAI模型处理辅助方法', () => {
    it('应该正确格式化模型名称', () => {
      const service = new AIProviderService()
      
      // 使用反射访问私有方法进行测试
      const formatAzureOpenAIModelName = (service as any).formatAzureOpenAIModelName.bind(service)

      // 部署名称与模型名称相同
      expect(formatAzureOpenAIModelName('gpt-4', 'gpt-4')).toBe('GPT-4')
      expect(formatAzureOpenAIModelName('gpt-35-turbo', 'gpt-35-turbo')).toBe('GPT-3.5 Turbo')

      // 部署名称与模型名称不同
      expect(formatAzureOpenAIModelName('gpt-4', 'my-gpt4-deployment')).toBe('GPT-4 (my-gpt4-deployment)')
      expect(formatAzureOpenAIModelName('gpt-35-turbo', 'turbo-deployment')).toBe('GPT-3.5 Turbo (turbo-deployment)')

      // 未知模型
      expect(formatAzureOpenAIModelName('unknown-model', 'unknown-deployment')).toBe('unknown-model (unknown-deployment)')
    })

    it('应该正确确定模型能力', () => {
      const service = new AIProviderService()
      const determineAzureOpenAICapabilities = (service as any).determineAzureOpenAICapabilities.bind(service)

      // GPT-4 模型
      const gpt4Capabilities = determineAzureOpenAICapabilities('gpt-4')
      expect(gpt4Capabilities).toContain('chat')
      expect(gpt4Capabilities).toContain('completion')
      expect(gpt4Capabilities).toContain('coding')
      expect(gpt4Capabilities).toContain('instruction-following')
      expect(gpt4Capabilities).toContain('function-calling')

      // GPT-3.5 Turbo 模型
      const gpt35Capabilities = determineAzureOpenAICapabilities('gpt-35-turbo')
      expect(gpt35Capabilities).toContain('chat')
      expect(gpt35Capabilities).toContain('instruction-following')
      expect(gpt35Capabilities).toContain('function-calling')

      // 嵌入模型
      const embeddingCapabilities = determineAzureOpenAICapabilities('text-embedding-ada-002')
      expect(embeddingCapabilities).toContain('embedding')

      // 图像生成模型
      const dalleCapabilities = determineAzureOpenAICapabilities('dall-e-3')
      expect(dalleCapabilities).toContain('image-generation')
    })

    it('应该正确获取模型上下文长度', () => {
      const service = new AIProviderService()
      const getAzureOpenAIModelContextLength = (service as any).getAzureOpenAIModelContextLength.bind(service)

      expect(getAzureOpenAIModelContextLength('gpt-4-turbo')).toBe(128000)
      expect(getAzureOpenAIModelContextLength('gpt-4-32k')).toBe(32768)
      expect(getAzureOpenAIModelContextLength('gpt-4')).toBe(8192)
      expect(getAzureOpenAIModelContextLength('gpt-35-turbo-16k')).toBe(16384)
      expect(getAzureOpenAIModelContextLength('gpt-35-turbo')).toBe(4096)
      expect(getAzureOpenAIModelContextLength('text-davinci-003')).toBe(4097)
      expect(getAzureOpenAIModelContextLength('unknown-model')).toBe(2048)
    })

    it('应该正确获取模型最大输出tokens', () => {
      const service = new AIProviderService()
      const getAzureOpenAIModelMaxTokens = (service as any).getAzureOpenAIModelMaxTokens.bind(service)

      expect(getAzureOpenAIModelMaxTokens('gpt-4-turbo')).toBe(4096)
      expect(getAzureOpenAIModelMaxTokens('gpt-4')).toBe(4096)
      expect(getAzureOpenAIModelMaxTokens('gpt-35-turbo')).toBe(4096)
      expect(getAzureOpenAIModelMaxTokens('text-davinci-003')).toBe(4097)
      expect(getAzureOpenAIModelMaxTokens('unknown-model')).toBe(2048)
    })

    it('应该正确判断推荐和热门模型', () => {
      const service = new AIProviderService()
      const isAzureOpenAIModelRecommended = (service as any).isAzureOpenAIModelRecommended.bind(service)
      const isAzureOpenAIModelPopular = (service as any).isAzureOpenAIModelPopular.bind(service)

      // 推荐模型
      expect(isAzureOpenAIModelRecommended('gpt-4-turbo')).toBe(true)
      expect(isAzureOpenAIModelRecommended('gpt-4')).toBe(true)
      expect(isAzureOpenAIModelRecommended('gpt-35-turbo')).toBe(true)
      expect(isAzureOpenAIModelRecommended('text-embedding-ada-002')).toBe(false)

      // 热门模型
      expect(isAzureOpenAIModelPopular('gpt-4-turbo')).toBe(true)
      expect(isAzureOpenAIModelPopular('gpt-4')).toBe(true)
      expect(isAzureOpenAIModelPopular('gpt-35-turbo')).toBe(true)
      expect(isAzureOpenAIModelPopular('text-davinci-003')).toBe(true)
      expect(isAzureOpenAIModelPopular('text-embedding-ada-002')).toBe(false)
    })

    it('应该正确提取模型标签', () => {
      const service = new AIProviderService()
      const extractAzureOpenAIModelTags = (service as any).extractAzureOpenAIModelTags.bind(service)

      // GPT-4模型
      const gpt4Tags = extractAzureOpenAIModelTags('gpt-4-turbo', { status: 'succeeded' })
      expect(gpt4Tags).toContain('Azure OpenAI')
      expect(gpt4Tags).toContain('GPT-4')
      expect(gpt4Tags).toContain('Turbo')
      expect(gpt4Tags).toContain('状态: succeeded')

      // GPT-3.5模型
      const gpt35Tags = extractAzureOpenAIModelTags('gpt-35-turbo-16k', { status: 'succeeded' })
      expect(gpt35Tags).toContain('GPT-3.5')
      expect(gpt35Tags).toContain('Turbo')
      expect(gpt35Tags).toContain('长上下文')
      expect(gpt35Tags).toContain('16K')

      // 嵌入模型
      const embeddingTags = extractAzureOpenAIModelTags('text-embedding-ada-002', { status: 'succeeded' })
      expect(embeddingTags).toContain('文本嵌入')

      // 图像生成模型
      const dalleTags = extractAzureOpenAIModelTags('dall-e-3', { status: 'succeeded' })
      expect(dalleTags).toContain('图像生成')
    })
  })
})