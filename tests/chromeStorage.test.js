import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { ChromeStorageService } from '../src/utils/chromeStorage.ts'

// 模拟Chrome Storage API
const mockChromeStorage = {
  sync: {
    set: vi.fn(),
    get: vi.fn(),
    remove: vi.fn(),
    clear: vi.fn(),
    getBytesInUse: vi.fn()
  },
  local: {
    set: vi.fn(),
    get: vi.fn(),
    remove: vi.fn(),
    clear: vi.fn(),
    getBytesInUse: vi.fn()
  },
  onChanged: {
    addListener: vi.fn(),
    removeListener: vi.fn()
  }
}

// 模拟全局chrome对象
global.chrome = {
  storage: mockChromeStorage
}

describe('ChromeStorageService', () => {
  beforeEach(() => {
    // 重置所有mock
    vi.clearAllMocks()
    
    // 设置默认的mock返回值
    mockChromeStorage.sync.set.mockResolvedValue(undefined)
    mockChromeStorage.sync.get.mockResolvedValue({})
    mockChromeStorage.sync.remove.mockResolvedValue(undefined)
    mockChromeStorage.sync.getBytesInUse.mockResolvedValue(1024)
    
    mockChromeStorage.local.set.mockResolvedValue(undefined)
    mockChromeStorage.local.get.mockResolvedValue({})
    mockChromeStorage.local.remove.mockResolvedValue(undefined)
    mockChromeStorage.local.getBytesInUse.mockResolvedValue(2048)
  })

  describe('同步存储操作', () => {
    it('应该保存同步设置', async () => {
      const testValue = { option1: true, option2: 'test' }
      
      await ChromeStorageService.saveSyncSetting('ai_config', testValue)
      
      expect(mockChromeStorage.sync.set).toHaveBeenCalledWith({
        'ub_sync_ai_config': testValue
      })
    })

    it('应该拒绝保存不允许的同步键', async () => {
      await expect(
        ChromeStorageService.saveSyncSetting('invalid_key', {})
      ).rejects.toThrow('键 "invalid_key" 不允许同步存储')
    })

    it('应该拒绝过大的同步数据', async () => {
      const largeData = 'x'.repeat(10000) // 超过8KB限制
      
      await expect(
        ChromeStorageService.saveSyncSetting('ai_config', { data: largeData })
      ).rejects.toThrow('数据大小超过同步存储限制')
    })

    it('应该获取同步设置', async () => {
      const testValue = { option1: true }
      mockChromeStorage.sync.get.mockResolvedValue({
        'ub_sync_ai_config': testValue
      })
      
      const result = await ChromeStorageService.getSyncSetting('ai_config')
      
      expect(mockChromeStorage.sync.get).toHaveBeenCalledWith('ub_sync_ai_config')
      expect(result).toEqual(testValue)
    })

    it('应该返回默认值当设置不存在时', async () => {
      mockChromeStorage.sync.get.mockResolvedValue({})
      
      const result = await ChromeStorageService.getSyncSetting('ai_config', { default: true })
      
      expect(result).toEqual({ default: true })
    })

    it('应该删除同步设置', async () => {
      await ChromeStorageService.removeSyncSetting('ai_config')
      
      expect(mockChromeStorage.sync.remove).toHaveBeenCalledWith('ub_sync_ai_config')
    })
  })

  describe('本地存储操作', () => {
    it('应该保存本地设置', async () => {
      const testValue = { cache: 'data' }
      
      await ChromeStorageService.saveLocalSetting('cache_data', testValue)
      
      expect(mockChromeStorage.local.set).toHaveBeenCalledWith({
        'ub_local_cache_data': testValue
      })
    })

    it('应该拒绝保存不允许的本地键', async () => {
      await expect(
        ChromeStorageService.saveLocalSetting('invalid_key', {})
      ).rejects.toThrow('键 "invalid_key" 不允许本地存储')
    })

    it('应该获取本地设置', async () => {
      const testValue = { cache: 'data' }
      mockChromeStorage.local.get.mockResolvedValue({
        'ub_local_cache_data': testValue
      })
      
      const result = await ChromeStorageService.getLocalSetting('cache_data')
      
      expect(mockChromeStorage.local.get).toHaveBeenCalledWith('ub_local_cache_data')
      expect(result).toEqual(testValue)
    })

    it('应该删除本地设置', async () => {
      await ChromeStorageService.removeLocalSetting('cache_data')
      
      expect(mockChromeStorage.local.remove).toHaveBeenCalledWith('ub_local_cache_data')
    })
  })

  describe('批量操作', () => {
    it('应该获取所有同步设置', async () => {
      mockChromeStorage.sync.get.mockResolvedValue({
        'ub_sync_ai_config': { ai: true },
        'ub_sync_ui_preferences': { theme: 'dark' },
        'other_key': 'should_be_ignored'
      })
      
      const result = await ChromeStorageService.getAllSyncSettings()
      
      expect(result).toEqual({
        ai_config: { ai: true },
        ui_preferences: { theme: 'dark' }
      })
    })

    it('应该获取所有本地设置', async () => {
      mockChromeStorage.local.get.mockResolvedValue({
        'ub_local_cache_data': { cache: true },
        'ub_local_temp_settings': { temp: true },
        'other_key': 'should_be_ignored'
      })
      
      const result = await ChromeStorageService.getAllLocalSettings()
      
      expect(result).toEqual({
        cache_data: { cache: true },
        temp_settings: { temp: true }
      })
    })

    it('应该清空所有同步设置', async () => {
      mockChromeStorage.sync.get.mockResolvedValue({
        'ub_sync_ai_config': { ai: true },
        'ub_sync_ui_preferences': { theme: 'dark' },
        'other_key': 'should_not_be_removed'
      })
      
      await ChromeStorageService.clearAllSyncSettings()
      
      expect(mockChromeStorage.sync.remove).toHaveBeenCalledWith([
        'ub_sync_ai_config',
        'ub_sync_ui_preferences'
      ])
    })

    it('应该批量保存设置', async () => {
      const syncSettings = { ai_config: { ai: true } }
      const localSettings = { cache_data: { cache: true } }
      
      await ChromeStorageService.batchSaveSettings(syncSettings, localSettings)
      
      expect(mockChromeStorage.sync.set).toHaveBeenCalledWith({
        'ub_sync_ai_config': { ai: true }
      })
      expect(mockChromeStorage.local.set).toHaveBeenCalledWith({
        'ub_local_cache_data': { cache: true }
      })
    })
  })

  describe('存储使用情况', () => {
    it('应该获取存储使用情况', async () => {
      mockChromeStorage.sync.getBytesInUse.mockResolvedValue(1024)
      mockChromeStorage.local.getBytesInUse.mockResolvedValue(2048)
      
      const usage = await ChromeStorageService.getStorageUsage()
      
      expect(usage.sync.used).toBe(1024)
      expect(usage.sync.total).toBe(102400) // 100KB
      expect(usage.sync.percentage).toBe(1) // 1024/102400 = 1%
      
      expect(usage.local.used).toBe(2048)
      expect(usage.local.total).toBe(5242880) // 5MB
      expect(usage.local.percentage).toBe(0) // 2048/5242880 ≈ 0%
    })

    it('应该检查存储配额警告', async () => {
      // 模拟接近配额限制的情况
      mockChromeStorage.sync.getBytesInUse.mockResolvedValue(90000) // 90KB，接近100KB限制
      mockChromeStorage.local.getBytesInUse.mockResolvedValue(4500000) // 4.5MB，接近5MB限制
      
      const quota = await ChromeStorageService.checkStorageQuota()
      
      expect(quota.syncNearLimit).toBe(true)
      expect(quota.localNearLimit).toBe(true)
      expect(quota.warnings).toHaveLength(2)
    })
  })

  describe('缓存清理', () => {
    it('应该清理过期的缓存数据', async () => {
      const now = Date.now()
      const oldTimestamp = now - 8 * 24 * 60 * 60 * 1000 // 8天前
      const newTimestamp = now - 1 * 24 * 60 * 60 * 1000 // 1天前
      
      const cacheData = {
        'old_item': { data: 'old', timestamp: oldTimestamp },
        'new_item': { data: 'new', timestamp: newTimestamp },
        'no_timestamp': { data: 'no_timestamp' }
      }
      
      mockChromeStorage.local.get.mockResolvedValue({
        'ub_local_cache_data': cacheData
      })
      
      await ChromeStorageService.cleanupExpiredCache(7 * 24 * 60 * 60 * 1000) // 7天
      
      expect(mockChromeStorage.local.set).toHaveBeenCalledWith({
        'ub_local_cache_data': {
          'new_item': { data: 'new', timestamp: newTimestamp },
          'no_timestamp': { data: 'no_timestamp' }
        }
      })
    })
  })

  describe('存储监听', () => {
    it('应该监听存储变化', () => {
      const callback = vi.fn()
      
      const unsubscribe = ChromeStorageService.onStorageChanged(callback)
      
      expect(mockChromeStorage.onChanged.addListener).toHaveBeenCalled()
      
      // 模拟存储变化
      const listener = mockChromeStorage.onChanged.addListener.mock.calls[0][0]
      listener({
        'ub_sync_ai_config': { newValue: { ai: true }, oldValue: null },
        'other_key': { newValue: 'ignored', oldValue: null }
      }, 'sync')
      
      expect(callback).toHaveBeenCalledWith({
        'ai_config': { newValue: { ai: true }, oldValue: null }
      }, 'sync')
      
      // 取消监听
      unsubscribe()
      expect(mockChromeStorage.onChanged.removeListener).toHaveBeenCalled()
    })
  })

  describe('设置导入导出', () => {
    it('应该导出所有设置', async () => {
      mockChromeStorage.sync.get.mockResolvedValue({
        'ub_sync_ai_config': { ai: true }
      })
      mockChromeStorage.local.get.mockResolvedValue({
        'ub_local_cache_data': { cache: true }
      })
      
      const exported = await ChromeStorageService.exportAllSettings()
      
      expect(exported.sync).toEqual({ ai_config: { ai: true } })
      expect(exported.local).toEqual({ cache_data: { cache: true } })
      expect(exported.exportDate).toBeDefined()
    })

    it('应该导入设置', async () => {
      const settingsData = {
        sync: { ai_config: { ai: true } },
        local: { cache_data: { cache: true } }
      }
      
      await ChromeStorageService.importSettings(settingsData)
      
      expect(mockChromeStorage.sync.set).toHaveBeenCalledWith({
        'ub_sync_ai_config': { ai: true }
      })
      expect(mockChromeStorage.local.set).toHaveBeenCalledWith({
        'ub_local_cache_data': { cache: true }
      })
    })
  })

  describe('默认设置', () => {
    it('应该返回默认设置', () => {
      const defaults = ChromeStorageService.getDefaultSettings()
      
      expect(defaults.sync).toBeDefined()
      expect(defaults.local).toBeDefined()
      expect(defaults.sync.ai_config).toBeDefined()
      expect(defaults.local.cache_data).toBeDefined()
    })

    it('应该初始化默认设置', async () => {
      // 模拟没有现有设置
      mockChromeStorage.sync.get.mockResolvedValue({})
      mockChromeStorage.local.get.mockResolvedValue({})
      
      await ChromeStorageService.initializeDefaultSettings()
      
      // 应该调用多次set来设置默认值
      expect(mockChromeStorage.sync.set).toHaveBeenCalled()
      expect(mockChromeStorage.local.set).toHaveBeenCalled()
    })
  })
})