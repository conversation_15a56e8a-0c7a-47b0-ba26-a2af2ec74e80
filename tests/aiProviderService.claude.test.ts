// Claude集成单元测试

import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest'
import { AIProviderService } from '../src/services/aiProviderService'

// Mock fetch
global.fetch = vi.fn()

describe('AIProviderService - Claude集成', () => {
  let aiProviderService: AIProviderService
  const mockFetch = fetch as vi.MockedFunction<typeof fetch>

  beforeEach(() => {
    aiProviderService = new AIProviderService()
    vi.clearAllMocks()
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('testClaudeConnection', () => {
    const baseUrl = 'https://api.anthropic.com/v1'
    const validApiKey = 'sk-ant-test123456789'
    const invalidApiKey = 'invalid-key'

    it('应该成功测试有效的Claude连接', async () => {
      // 模拟成功的API响应
      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        json: async () => ({
          id: 'msg_test123',
          type: 'message',
          role: 'assistant',
          content: [
            {
              type: 'text',
              text: 'Hello!'
            }
          ],
          model: 'claude-3-haiku-20240307',
          stop_reason: 'end_turn',
          stop_sequence: null,
          usage: {
            input_tokens: 8,
            output_tokens: 1
          }
        })
      } as Response)

      const result = await aiProviderService.testClaudeConnection(baseUrl, validApiKey)

      expect(result.success).toBe(true)
      expect(result.modelCount).toBe(4)
      expect(result.error).toBeUndefined()
      expect(mockFetch).toHaveBeenCalledWith(
        `${baseUrl}/messages`,
        expect.objectContaining({
          method: 'POST',
          headers: expect.objectContaining({
            'x-api-key': validApiKey,
            'Content-Type': 'application/json',
            'anthropic-version': '2023-06-01',
            'User-Agent': 'BookmarkExtension/1.0'
          }),
          body: expect.stringContaining('claude-3-haiku-20240307')
        })
      )
    })

    it('应该处理空API密钥错误', async () => {
      const result = await aiProviderService.testClaudeConnection(baseUrl, '')

      expect(result.success).toBe(false)
      expect(result.error).toBe('API密钥不能为空')
      expect(mockFetch).not.toHaveBeenCalled()
    })

    it('应该处理无效的API密钥格式', async () => {
      const result = await aiProviderService.testClaudeConnection(baseUrl, invalidApiKey)

      expect(result.success).toBe(false)
      expect(result.error).toBe('无效的Claude API密钥格式，应以"sk-ant-"开头')
      expect(mockFetch).not.toHaveBeenCalled()
    })

    it('应该处理400请求格式错误', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 400,
        statusText: 'Bad Request',
        json: async () => ({
          error: {
            type: 'invalid_request_error',
            message: 'Invalid model specified'
          }
        })
      } as Response)

      const result = await aiProviderService.testClaudeConnection(baseUrl, validApiKey)

      expect(result.success).toBe(false)
      expect(result.error).toBe('请求格式错误: Invalid model specified')
    })

    it('应该处理400通用请求错误', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 400,
        statusText: 'Bad Request',
        json: async () => ({})
      } as Response)

      const result = await aiProviderService.testClaudeConnection(baseUrl, validApiKey)

      expect(result.success).toBe(false)
      expect(result.error).toBe('请求参数无效')
    })

    it('应该处理401未授权错误', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 401,
        statusText: 'Unauthorized'
      } as Response)

      const result = await aiProviderService.testClaudeConnection(baseUrl, validApiKey)

      expect(result.success).toBe(false)
      expect(result.error).toBe('API密钥无效或已过期')
    })

    it('应该处理403权限不足错误', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 403,
        statusText: 'Forbidden'
      } as Response)

      const result = await aiProviderService.testClaudeConnection(baseUrl, validApiKey)

      expect(result.success).toBe(false)
      expect(result.error).toBe('API密钥权限不足或账户被限制')
    })

    it('应该处理429频率限制错误', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 429,
        statusText: 'Too Many Requests'
      } as Response)

      const result = await aiProviderService.testClaudeConnection(baseUrl, validApiKey)

      expect(result.success).toBe(false)
      expect(result.error).toBe('API请求频率限制，请稍后重试')
    })

    it('应该处理500服务器错误', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 500,
        statusText: 'Internal Server Error'
      } as Response)

      const result = await aiProviderService.testClaudeConnection(baseUrl, validApiKey)

      expect(result.success).toBe(false)
      expect(result.error).toBe('Claude服务器内部错误')
    })

    it('应该处理529服务过载错误', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 529,
        statusText: 'Service Overloaded'
      } as Response)

      const result = await aiProviderService.testClaudeConnection(baseUrl, validApiKey)

      expect(result.success).toBe(false)
      expect(result.error).toBe('Claude服务过载，请稍后重试')
    })

    it('应该处理网络超时错误', async () => {
      mockFetch.mockRejectedValueOnce(new DOMException('The operation was aborted', 'AbortError'))

      const result = await aiProviderService.testClaudeConnection(baseUrl, validApiKey)

      expect(result.success).toBe(false)
      expect(result.error).toBe('连接超时，请检查网络连接或API服务状态')
    })

    it('应该处理网络连接错误', async () => {
      mockFetch.mockRejectedValueOnce(new Error('fetch failed'))

      const result = await aiProviderService.testClaudeConnection(baseUrl, validApiKey)

      expect(result.success).toBe(false)
      expect(result.error).toBe('网络连接失败，请检查网络设置')
    })

    it('应该处理异常的API响应格式', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        json: async () => ({
          id: 'msg_test123',
          type: 'message',
          // 缺少content字段
        })
      } as Response)

      const result = await aiProviderService.testClaudeConnection(baseUrl, validApiKey)

      expect(result.success).toBe(false)
      expect(result.error).toBe('Claude API响应格式异常')
    })
  })

  describe('getClaudeModels', () => {
    it('应该返回预定义的Claude模型列表', async () => {
      const result = await aiProviderService.getClaudeModels()

      expect(result).toHaveLength(4)
      expect(result.every(model => model.providerId === 'claude')).toBe(true)
      
      // 检查模型排序（推荐模型在前，3.5版本在前，性能等级排序）
      expect(result[0].id).toBe('claude-3-5-sonnet-20241022')
      expect(result[0].isRecommended).toBe(true)
      expect(result[0].displayName).toBe('Claude 3.5 Sonnet')
      
      // 检查模型详细信息
      const claude35Sonnet = result.find(m => m.id === 'claude-3-5-sonnet-20241022')
      expect(claude35Sonnet?.capabilities).toContain('chat')
      expect(claude35Sonnet?.capabilities).toContain('coding')
      expect(claude35Sonnet?.contextLength).toBe(200000)
      expect(claude35Sonnet?.maxTokens).toBe(8192)
      expect(claude35Sonnet?.tags).toContain('Claude 3.5')

      const claudeOpus = result.find(m => m.id === 'claude-3-opus-20240229')
      expect(claudeOpus?.isRecommended).toBe(true)
      expect(claudeOpus?.displayName).toBe('Claude 3 Opus')
      expect(claudeOpus?.capabilities).toContain('reasoning')
      expect(claudeOpus?.tags).toContain('最强性能')

      const claudeSonnet = result.find(m => m.id === 'claude-3-sonnet-20240229')
      expect(claudeSonnet?.isRecommended).toBe(true)
      expect(claudeSonnet?.displayName).toBe('Claude 3 Sonnet')
      expect(claudeSonnet?.tags).toContain('性价比高')

      const claudeHaiku = result.find(m => m.id === 'claude-3-haiku-20240307')
      expect(claudeHaiku?.isRecommended).toBe(false)
      expect(claudeHaiku?.isPopular).toBe(true)
      expect(claudeHaiku?.displayName).toBe('Claude 3 Haiku')
      expect(claudeHaiku?.capabilities).toContain('quick-response')
      expect(claudeHaiku?.tags).toContain('快速响应')
    })

    it('应该正确排序模型（推荐 > 版本 > 性能等级）', async () => {
      const result = await aiProviderService.getClaudeModels()

      // 检查推荐模型在前
      const recommendedModels = result.filter(m => m.isRecommended)
      expect(recommendedModels.length).toBe(3)
      
      // 检查3.5版本在3.0版本之前
      const claude35Index = result.findIndex(m => m.id.includes('3-5'))
      const claude3Index = result.findIndex(m => m.id.includes('claude-3') && !m.id.includes('3-5'))
      expect(claude35Index).toBeLessThan(claude3Index)

      // 检查性能等级排序（opus > sonnet > haiku）
      const opusIndex = result.findIndex(m => m.id.includes('opus'))
      const sonnetIndex = result.findIndex(m => m.id.includes('sonnet') && !m.id.includes('3-5'))
      const haikuIndex = result.findIndex(m => m.id.includes('haiku'))
      
      expect(opusIndex).toBeLessThan(sonnetIndex)
      expect(sonnetIndex).toBeLessThan(haikuIndex)
    })

    it('应该为所有模型设置正确的基础属性', async () => {
      const result = await aiProviderService.getClaudeModels()

      result.forEach(model => {
        expect(model.id).toBeTruthy()
        expect(model.name).toBeTruthy()
        expect(model.displayName).toBeTruthy()
        expect(model.description).toBeTruthy()
        expect(model.providerId).toBe('claude')
        expect(model.capabilities).toContain('chat')
        expect(model.contextLength).toBe(200000)
        expect(model.supportedFormats).toContain('text')
        expect(model.supportedFormats).toContain('json')
        expect(typeof model.isRecommended).toBe('boolean')
        expect(typeof model.isPopular).toBe('boolean')
      })
    })
  })

  describe('validateClaudeModel', () => {
    const baseUrl = 'https://api.anthropic.com/v1'
    const validApiKey = 'sk-ant-test123456789'

    it('应该验证有效的Claude模型', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        json: async () => ({
          id: 'msg_test123',
          content: [{ type: 'text', text: 'Test' }]
        })
      } as Response)

      const result = await aiProviderService.validateClaudeModel(
        baseUrl, 
        validApiKey, 
        'claude-3-haiku-20240307'
      )

      expect(result).toBe(true)
      expect(mockFetch).toHaveBeenCalledWith(
        `${baseUrl}/messages`,
        expect.objectContaining({
          method: 'POST',
          body: expect.stringContaining('claude-3-haiku-20240307')
        })
      )
    })

    it('应该处理无效的Claude模型', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 400,
        statusText: 'Bad Request'
      } as Response)

      const result = await aiProviderService.validateClaudeModel(
        baseUrl, 
        validApiKey, 
        'invalid-model'
      )

      expect(result).toBe(false)
    })

    it('应该处理网络错误', async () => {
      mockFetch.mockRejectedValueOnce(new Error('Network error'))

      const result = await aiProviderService.validateClaudeModel(
        baseUrl, 
        validApiKey, 
        'claude-3-haiku-20240307'
      )

      expect(result).toBe(false)
    })
  })

  describe('Claude模型处理辅助方法', () => {
    it('应该正确获取模型详细信息', () => {
      const service = new AIProviderService()
      
      // 使用反射访问私有方法进行测试
      const getClaudeModelDetails = (service as any).getClaudeModelDetails.bind(service)

      // 测试Claude 3.5 Sonnet
      const claude35Details = getClaudeModelDetails('claude-3-5-sonnet-20241022')
      expect(claude35Details.displayName).toBe('Claude 3.5 Sonnet')
      expect(claude35Details.capabilities).toContain('coding')
      expect(claude35Details.contextLength).toBe(200000)
      expect(claude35Details.maxTokens).toBe(8192)

      // 测试Claude 3 Opus
      const opusDetails = getClaudeModelDetails('claude-3-opus-20240229')
      expect(opusDetails.displayName).toBe('Claude 3 Opus')
      expect(opusDetails.capabilities).toContain('reasoning')
      expect(opusDetails.capabilities).toContain('complex-tasks')

      // 测试未知模型
      const unknownDetails = getClaudeModelDetails('unknown-model')
      expect(unknownDetails.displayName).toBe('unknown-model')
      expect(unknownDetails.capabilities).toContain('chat')
      expect(unknownDetails.contextLength).toBe(200000)
    })

    it('应该正确判断推荐模型', () => {
      const service = new AIProviderService()
      const isClaudeModelRecommended = (service as any).isClaudeModelRecommended.bind(service)

      // 推荐模型
      expect(isClaudeModelRecommended('claude-3-5-sonnet-20241022')).toBe(true)
      expect(isClaudeModelRecommended('claude-3-opus-20240229')).toBe(true)
      expect(isClaudeModelRecommended('claude-3-sonnet-20240229')).toBe(true)

      // 非推荐模型
      expect(isClaudeModelRecommended('claude-3-haiku-20240307')).toBe(false)
      expect(isClaudeModelRecommended('unknown-model')).toBe(false)
    })

    it('应该正确判断热门模型', () => {
      const service = new AIProviderService()
      const isClaudeModelPopular = (service as any).isClaudeModelPopular.bind(service)

      // Claude 3系列都是热门模型
      expect(isClaudeModelPopular('claude-3-5-sonnet-20241022')).toBe(true)
      expect(isClaudeModelPopular('claude-3-opus-20240229')).toBe(true)
      expect(isClaudeModelPopular('claude-3-sonnet-20240229')).toBe(true)
      expect(isClaudeModelPopular('claude-3-haiku-20240307')).toBe(true)

      // 非Claude 3系列
      expect(isClaudeModelPopular('claude-2')).toBe(false)
      expect(isClaudeModelPopular('unknown-model')).toBe(false)
    })
  })
})