// 消息通信系统单元测试

import { describe, it, expect, beforeEach, vi } from 'vitest'
import { MessageSender, MessageReceiver, MessageValidator } from '../src/utils/messaging'

// Mock Chrome APIs
global.chrome = {
  runtime: {
    sendMessage: vi.fn(),
    onMessage: {
      addListener: vi.fn()
    }
  },
  tabs: {
    sendMessage: vi.fn(),
    query: vi.fn()
  },
  extension: {
    getViews: vi.fn()
  }
}

describe('消息通信系统测试', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('MessageValidator', () => {
    it('应该验证有效的消息格式', () => {
      const validMessage = {
        type: 'PING',
        requestId: 'req_123',
        timestamp: Date.now(),
        data: {}
      }

      const result = MessageValidator.validateMessage(validMessage)
      expect(result.isValid).toBe(true)
      expect(result.errors).toHaveLength(0)
    })

    it('应该检测无效的消息格式', () => {
      const invalidMessage = {
        // 缺少type字段
        requestId: 'req_123',
        data: {}
      }

      const result = MessageValidator.validateMessage(invalidMessage)
      expect(result.isValid).toBe(false)
      expect(result.errors).toContain('消息类型必须是字符串')
    })

    it('应该检测非对象消息', () => {
      const result = MessageValidator.validateMessage('invalid')
      expect(result.isValid).toBe(false)
      expect(result.errors).toContain('消息必须是对象')
    })

    it('应该验证有效的响应格式', () => {
      const validResponse = {
        success: true,
        data: { result: 'ok' },
        requestId: 'req_123'
      }

      const result = MessageValidator.validateResponse(validResponse)
      expect(result.isValid).toBe(true)
      expect(result.errors).toHaveLength(0)
    })

    it('应该检测无效的响应格式', () => {
      const invalidResponse = {
        // 缺少success字段
        data: { result: 'ok' }
      }

      const result = MessageValidator.validateResponse(invalidResponse)
      expect(result.isValid).toBe(false)
      expect(result.errors).toContain('success字段必须是布尔值')
    })
  })

  describe('MessageSender', () => {
    it('应该发送消息到background script', async () => {
      const mockResponse = { success: true, data: { status: 'pong' } }
      chrome.runtime.sendMessage.mockResolvedValue(mockResponse)

      const message = { type: 'PING' }
      const response = await MessageSender.sendToBackground(message)

      expect(chrome.runtime.sendMessage).toHaveBeenCalledWith(
        expect.objectContaining({
          type: 'PING',
          requestId: expect.any(String),
          timestamp: expect.any(Number)
        })
      )
      expect(response).toEqual(mockResponse)
    })

    it('应该处理发送失败的情况', async () => {
      chrome.runtime.sendMessage.mockRejectedValue(new Error('发送失败'))

      const message = { type: 'PING' }
      
      await expect(MessageSender.sendToBackground(message))
        .rejects.toThrow('消息发送失败')
    })

    it('应该处理空响应', async () => {
      chrome.runtime.sendMessage.mockResolvedValue(null)

      const message = { type: 'PING' }
      
      await expect(MessageSender.sendToBackground(message))
        .rejects.toThrow('消息发送失败: Error: 未收到响应')
    })

    it('应该处理错误响应', async () => {
      const errorResponse = { success: false, error: '操作失败' }
      chrome.runtime.sendMessage.mockResolvedValue(errorResponse)

      const message = { type: 'PING' }
      
      await expect(MessageSender.sendToBackground(message))
        .rejects.toThrow('消息发送失败: Error: 操作失败')
    })

    it('应该发送消息到content script', async () => {
      const mockResponse = { success: true, data: { title: '测试页面' } }
      chrome.tabs.sendMessage.mockResolvedValue(mockResponse)

      const tabId = 123
      const message = { type: 'GET_PAGE_INFO' }
      const response = await MessageSender.sendToContent(tabId, message)

      expect(chrome.tabs.sendMessage).toHaveBeenCalledWith(
        tabId,
        expect.objectContaining({
          type: 'GET_PAGE_INFO',
          requestId: expect.any(String),
          timestamp: expect.any(Number)
        })
      )
      expect(response).toEqual(mockResponse)
    })

    it('应该广播消息到所有标签页', async () => {
      const mockTabs = [
        { id: 1, url: 'https://example1.com' },
        { id: 2, url: 'https://example2.com' },
        { id: 3, url: 'chrome://settings' } // 这个会失败
      ]
      chrome.tabs.query.mockResolvedValue(mockTabs)
      chrome.tabs.sendMessage
        .mockResolvedValueOnce({ success: true })
        .mockResolvedValueOnce({ success: true })
        .mockRejectedValueOnce(new Error('无法发送到chrome://页面'))

      const message = { type: 'PING' }
      await MessageSender.broadcastToAllTabs(message)

      expect(chrome.tabs.query).toHaveBeenCalledWith({})
      expect(chrome.tabs.sendMessage).toHaveBeenCalledTimes(3)
    })
  })

  describe('MessageReceiver', () => {
    let receiver

    beforeEach(() => {
      receiver = new MessageReceiver()
    })

    it('应该注册和调用消息处理器', async () => {
      const mockHandler = vi.fn().mockResolvedValue({ success: true, data: 'handled' })
      receiver.registerHandler('TEST_MESSAGE', mockHandler)

      const message = { type: 'TEST_MESSAGE', data: { test: true } }
      const sender = { tab: { id: 123 } }
      
      const response = await receiver.handleMessage(message, sender)

      expect(mockHandler).toHaveBeenCalledWith(message, sender)
      expect(response).toEqual({ success: true, data: 'handled' })
    })

    it('应该处理未注册的消息类型', async () => {
      const message = { type: 'UNKNOWN_MESSAGE' }
      const sender = { tab: { id: 123 } }
      
      const response = await receiver.handleMessage(message, sender)

      expect(response.success).toBe(false)
      expect(response.error).toContain('未找到消息处理器')
    })

    it('应该处理处理器抛出的异常', async () => {
      const mockHandler = vi.fn().mockRejectedValue(new Error('处理失败'))
      receiver.registerHandler('ERROR_MESSAGE', mockHandler)

      const message = { type: 'ERROR_MESSAGE', requestId: 'req_123' }
      const sender = { tab: { id: 123 } }
      
      const response = await receiver.handleMessage(message, sender)

      expect(response.success).toBe(false)
      expect(response.error).toBe('处理失败')
      expect(response.requestId).toBe('req_123')
    })

    it('应该移除消息处理器', () => {
      const mockHandler = vi.fn()
      receiver.registerHandler('TEST_MESSAGE', mockHandler)
      receiver.unregisterHandler('TEST_MESSAGE')

      // 验证处理器已被移除
      expect(receiver.handlers.has('TEST_MESSAGE')).toBe(false)
    })

    it('应该启动消息监听', () => {
      receiver.startListening()
      expect(chrome.runtime.onMessage.addListener).toHaveBeenCalled()
    })
  })

  describe('消息ID生成', () => {
    it('应该生成唯一的请求ID', async () => {
      chrome.runtime.sendMessage.mockResolvedValue({ success: true })

      const message1 = { type: 'PING' }
      const message2 = { type: 'PING' }

      await MessageSender.sendToBackground(message1)
      await MessageSender.sendToBackground(message2)

      const calls = chrome.runtime.sendMessage.mock.calls
      const requestId1 = calls[0][0].requestId
      const requestId2 = calls[1][0].requestId

      expect(requestId1).not.toBe(requestId2)
      expect(requestId1).toMatch(/^req_\d+_\d+$/)
      expect(requestId2).toMatch(/^req_\d+_\d+$/)
    })
  })

  describe('时间戳处理', () => {
    it('应该在消息中添加时间戳', async () => {
      chrome.runtime.sendMessage.mockResolvedValue({ success: true })

      const beforeTime = Date.now()
      const message = { type: 'PING' }
      await MessageSender.sendToBackground(message)
      const afterTime = Date.now()

      const sentMessage = chrome.runtime.sendMessage.mock.calls[0][0]
      expect(sentMessage.timestamp).toBeGreaterThanOrEqual(beforeTime)
      expect(sentMessage.timestamp).toBeLessThanOrEqual(afterTime)
    })
  })
})