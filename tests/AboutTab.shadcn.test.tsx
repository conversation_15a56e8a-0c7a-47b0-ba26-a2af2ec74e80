// AboutTab shadcn重构测试

import React from 'react'
import { render, screen, waitFor } from '@testing-library/react'
import { vi, describe, it, expect, beforeEach } from 'vitest'
import AboutTab from '../src/options/components/AboutTab'

// Mock manifestReader utilities
vi.mock('../src/options/utils/manifestReader', () => ({
  getExtensionInfo: vi.fn(() => ({
    name: 'Universe Bag（乾坤袋）',
    version: '1.0.0',
    description: '智能收藏管理工具，支持AI自动分类和云端同步',
    developer: 'coffeebean'
  })),
  getBuildInfo: vi.fn(() => ({
    buildDate: '2024-01-15',
    buildVersion: '1.0.0',
    manifestVersion: '3'
  })),
  getExtensionPermissions: vi.fn(() => ['storage', 'activeTab', 'contextMenus']),
  isExtensionEnvironment: vi.fn(() => true)
}))

// Mock useResponsive hook
vi.mock('../src/options/hooks/useResponsive', () => ({
  useResponsive: () => ({
    isMobile: false,
    getResponsiveValue: (values: any, defaultValue: any) => defaultValue
  })
}))

// Mock aboutInfo data
vi.mock('../src/options/data/aboutInfo', () => ({
  defaultAboutData: {
    extensionInfo: {
      name: 'Universe Bag（乾坤袋）',
      version: '1.0.0',
      description: '智能收藏管理工具，支持AI自动分类和云端同步',
      developer: 'coffeebean'
    },
    buildInfo: {
      buildDate: '2024-01-15',
      buildVersion: '1.0.0'
    },
    developerInfo: {
      name: 'coffeebean',
      website: 'https://www.obsidian.vip',
      communityUrl: 'https://obsidian.vip/zh/documentation/community.html'
    },
    licenseInfo: {
      type: '',
      text: '',
      url: ''
    }
  }
}))

describe('AboutTab shadcn重构测试', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('应该使用shadcn Card组件显示加载状态', () => {
    // 不传入aboutData，让组件进入加载状态
    const { rerender } = render(<AboutTab />)
    
    // 由于mock的存在，组件会立即加载完成，所以我们检查最终渲染的内容
    expect(screen.getByText('关于我们')).toBeInTheDocument()
  })

  it('应该使用shadcn Card组件渲染扩展基本信息', async () => {
    render(<AboutTab />)
    
    await waitFor(() => {
      expect(screen.getByText('关于我们')).toBeInTheDocument()
      expect(screen.getByText('Universe Bag（乾坤袋）')).toBeInTheDocument()
      expect(screen.getByText('智能收藏管理工具，支持AI自动分类和云端同步')).toBeInTheDocument()
    })
  })

  it('应该使用shadcn Badge组件显示版本信息', async () => {
    render(<AboutTab />)
    
    await waitFor(() => {
      expect(screen.getByText('版本:')).toBeInTheDocument()
      expect(screen.getAllByText('1.0.0')).toHaveLength(2) // 版本和构建版本都显示1.0.0
      expect(screen.getByText('构建日期:')).toBeInTheDocument()
      expect(screen.getByText('2024-01-15')).toBeInTheDocument()
    })
  })

  it('应该使用shadcn Card组件渲染开发者信息', async () => {
    render(<AboutTab />)
    
    await waitFor(() => {
      expect(screen.getByText('开发者信息')).toBeInTheDocument()
      expect(screen.getByText('coffeebean')).toBeInTheDocument()
      expect(screen.getByText('https://www.obsidian.vip')).toBeInTheDocument()
      expect(screen.getByText('https://obsidian.vip/zh/documentation/community.html')).toBeInTheDocument()
    })
  })

  it('应该使用shadcn Badge组件显示技术信息', async () => {
    render(<AboutTab />)
    
    await waitFor(() => {
      expect(screen.getByText('技术信息')).toBeInTheDocument()
      expect(screen.getByText('运行环境:')).toBeInTheDocument()
      expect(screen.getByText('扩展环境')).toBeInTheDocument()
      expect(screen.getByText('构建版本:')).toBeInTheDocument()
      expect(screen.getByText('权限数量:')).toBeInTheDocument()
      expect(screen.getByText('3 个')).toBeInTheDocument()
    })
  })

  it('应该使用shadcn Badge组件显示权限详情', async () => {
    render(<AboutTab />)
    
    await waitFor(() => {
      expect(screen.getByText('权限详情')).toBeInTheDocument()
      expect(screen.getAllByText('storage')).toHaveLength(2) // 在技术信息和权限详情中都显示
      expect(screen.getAllByText('activeTab')).toHaveLength(2)
      expect(screen.getAllByText('contextMenus')).toHaveLength(2)
    })
  })

  it('不应该显示许可证信息', async () => {
    render(<AboutTab />)
    
    await waitFor(() => {
      expect(screen.queryByText('许可证信息')).not.toBeInTheDocument()
    })
  })

  it('应该使用shadcn Card组件显示版权信息', async () => {
    render(<AboutTab />)
    
    await waitFor(() => {
      expect(screen.getByText('© 2024 coffeebean. 保留所有权利。')).toBeInTheDocument()
      expect(screen.getByText('感谢您使用 Universe Bag，让收藏管理变得更简单！')).toBeInTheDocument()
    })
  })

  it('应该正确处理权限描述', async () => {
    render(<AboutTab />)
    
    await waitFor(() => {
      expect(screen.getByText('存储收藏数据和用户设置')).toBeInTheDocument()
      expect(screen.getByText('获取当前标签页信息')).toBeInTheDocument()
      expect(screen.getByText('添加右键菜单选项')).toBeInTheDocument()
    })
  })

  it('应该正确处理开发环境状态', async () => {
    const { getExtensionInfo, getBuildInfo, getExtensionPermissions, isExtensionEnvironment } = await import('../src/options/utils/manifestReader')
    
    vi.mocked(isExtensionEnvironment).mockReturnValue(false)
    
    render(<AboutTab />)
    
    await waitFor(() => {
      expect(screen.getByText('开发环境')).toBeInTheDocument()
    })
  })

  it('应该正确处理空权限列表', async () => {
    const { getExtensionPermissions } = await import('../src/options/utils/manifestReader')
    
    vi.mocked(getExtensionPermissions).mockReturnValue([])
    
    render(<AboutTab />)
    
    await waitFor(() => {
      expect(screen.getByText('0 个')).toBeInTheDocument()
      expect(screen.queryByText('权限详情')).not.toBeInTheDocument()
    })
  })

  it('应该正确处理大量权限的显示', async () => {
    const { getExtensionPermissions } = await import('../src/options/utils/manifestReader')
    
    const manyPermissions = ['storage', 'activeTab', 'contextMenus', 'tabs', 'scripting', 'bookmarks']
    vi.mocked(getExtensionPermissions).mockReturnValue(manyPermissions)
    
    render(<AboutTab />)
    
    await waitFor(() => {
      expect(screen.getByText('6 个')).toBeInTheDocument()
      expect(screen.getByText('+3 更多')).toBeInTheDocument()
    })
  })

  it('应该正确处理加载错误', async () => {
    const { getExtensionInfo } = await import('../src/options/utils/manifestReader')
    
    vi.mocked(getExtensionInfo).mockImplementation(() => {
      throw new Error('加载失败')
    })
    
    render(<AboutTab />)
    
    await waitFor(() => {
      // 应该显示fallback数据
      expect(screen.getByText('Universe Bag（乾坤袋）')).toBeInTheDocument()
      expect(screen.getAllByText('1.0.0')).toHaveLength(2) // 版本和构建版本都显示1.0.0
    })
  })

  it('应该正确处理外部链接', async () => {
    render(<AboutTab />)
    
    await waitFor(() => {
      const websiteLink = screen.getByRole('link', { name: 'https://www.obsidian.vip' })
      expect(websiteLink).toHaveAttribute('href', 'https://www.obsidian.vip')
      expect(websiteLink).toHaveAttribute('target', '_blank')
      expect(websiteLink).toHaveAttribute('rel', 'noopener noreferrer')
      
      const communityLink = screen.getByRole('link', { name: 'https://obsidian.vip/zh/documentation/community.html' })
      expect(communityLink).toHaveAttribute('href', 'https://obsidian.vip/zh/documentation/community.html')
      expect(communityLink).toHaveAttribute('target', '_blank')
      expect(communityLink).toHaveAttribute('rel', 'noopener noreferrer')
    })
  })

  it('应该正确处理传入的aboutData属性', async () => {
    const customAboutData = {
      extensionInfo: {
        name: 'Custom Extension',
        version: '2.0.0',
        description: 'Custom description',
        developer: 'Custom Developer'
      },
      buildInfo: {
        buildDate: '2024-02-01',
        buildVersion: '2.0.0',
        manifestVersion: '3'
      },
      developerInfo: {
        name: 'Custom Developer',
        website: 'https://custom.com',
        email: '<EMAIL>'
      },
      licenseInfo: {
        type: 'Apache-2.0',
        text: 'Apache License',
        url: 'https://apache.org/licenses/LICENSE-2.0'
      }
    }
    
    render(<AboutTab aboutData={customAboutData} />)
    
    expect(screen.getByText('Custom Extension')).toBeInTheDocument()
    expect(screen.getAllByText('2.0.0')).toHaveLength(2) // 版本和构建版本都显示2.0.0
    expect(screen.getByText('Custom description')).toBeInTheDocument()
    expect(screen.getByText('Custom Developer')).toBeInTheDocument()
  })
})