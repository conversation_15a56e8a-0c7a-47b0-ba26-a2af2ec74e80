// ImportExportManagerService 测试

import { describe, it, expect, beforeEach, vi } from 'vitest'
import { ImportExportManagerService } from '../src/services/BookmarkImportExportService'
import { ExportAllOptions, ExportCategoriesOptions, ExportTagsOptions, ImportData } from '../src/types'

// 模拟依赖服务
vi.mock('../src/services/bookmarkService', () => ({
  bookmarkService: {
    getBookmarks: vi.fn(),
    saveBookmark: vi.fn(),
    findBookmarkByUrl: vi.fn()
  }
}))

vi.mock('../src/services/categoryService', () => ({
  categoryService: {
    getCategories: vi.fn(),
    createCategory: vi.fn(),
    getCategoryByName: vi.fn()
  }
}))

vi.mock('../src/services/tagService', () => ({
  tagService: {
    getTags: vi.fn(),
    createTag: vi.fn(),
    getTagByName: vi.fn()
  }
}))

vi.mock('../src/services/ConflictResolverService', () => ({
  conflictResolverService: {
    detectConflicts: vi.fn(),
    resolveConflicts: vi.fn()
  }
}))

describe('ImportExportManagerService', () => {
  let service: ImportExportManagerService
  
  beforeEach(() => {
    service = new ImportExportManagerService()
    vi.clearAllMocks()
  })

  describe('exportAllData', () => {
    it('应该导出全部数据', async () => {
      const mockBookmarks = [
        {
          id: 'bookmark1',
          type: 'url',
          title: '测试收藏',
          url: 'https://example.com',
          category: '技术',
          tags: ['JavaScript'],
          createdAt: new Date(),
          updatedAt: new Date(),
          metadata: { aiGenerated: false }
        }
      ]

      const mockCategories = [
        {
          id: 'category1',
          name: '技术',
          description: '技术相关',
          color: '#3B82F6',
          createdAt: new Date(),
          updatedAt: new Date(),
          bookmarkCount: 1
        }
      ]

      const mockTags = [
        {
          id: 'tag1',
          name: 'JavaScript',
          color: '#F59E0B',
          usageCount: 1,
          createdAt: new Date(),
          updatedAt: new Date()
        }
      ]

      // 模拟服务调用
      const { bookmarkService } = await import('../src/services/bookmarkService')
      const { categoryService } = await import('../src/services/categoryService')
      const { tagService } = await import('../src/services/tagService')

      vi.mocked(bookmarkService.getBookmarks).mockResolvedValue(mockBookmarks as any)
      vi.mocked(categoryService.getCategories).mockResolvedValue(mockCategories as any)
      vi.mocked(tagService.getTags).mockResolvedValue(mockTags as any)

      const options: ExportAllOptions = {
        format: 'json',
        includeBookmarks: true,
        includeCategories: true,
        includeTags: true,
        includeMetadata: true
      }

      const result = await service.exportAllData(options)

      expect(result.success).toBe(true)
      expect(result.format).toBe('json')
      expect(result.itemCount).toBe(3) // 1 bookmark + 1 category + 1 tag
      
      const exportedData = JSON.parse(result.data as string)
      expect(exportedData.version).toBe('2.0')
      expect(exportedData.exportType).toBe('all')
      expect(exportedData.bookmarks).toHaveLength(1)
      expect(exportedData.categories).toHaveLength(1)
      expect(exportedData.tags).toHaveLength(1)
      expect(exportedData.metadata.totalBookmarks).toBe(1)
      expect(exportedData.metadata.totalCategories).toBe(1)
      expect(exportedData.metadata.totalTags).toBe(1)
    })

    it('应该根据选项过滤导出内容', async () => {
      const { bookmarkService } = await import('../src/services/bookmarkService')
      vi.mocked(bookmarkService.getBookmarks).mockResolvedValue([])

      const options: ExportAllOptions = {
        format: 'json',
        includeBookmarks: true,
        includeCategories: false,
        includeTags: false,
        includeMetadata: true
      }

      const result = await service.exportAllData(options)
      const exportedData = JSON.parse(result.data as string)

      expect(exportedData.bookmarks).toBeDefined()
      expect(exportedData.categories).toBeUndefined()
      expect(exportedData.tags).toBeUndefined()
    })
  })

  describe('exportCategories', () => {
    it('应该导出分类数据', async () => {
      const mockCategories = [
        {
          id: 'category1',
          name: '技术',
          description: '技术相关',
          color: '#3B82F6',
          parentId: null,
          createdAt: new Date(),
          updatedAt: new Date(),
          bookmarkCount: 5
        }
      ]

      const { categoryService } = await import('../src/services/categoryService')
      vi.mocked(categoryService.getCategories).mockResolvedValue(mockCategories as any)

      const options: ExportCategoriesOptions = {
        format: 'json',
        includeHierarchy: true,
        includeStatistics: true
      }

      const result = await service.exportCategories(options)

      expect(result.success).toBe(true)
      expect(result.itemCount).toBe(1)
      
      const exportedData = JSON.parse(result.data as string)
      expect(exportedData.exportType).toBe('categories')
      expect(exportedData.categories).toHaveLength(1)
      expect(exportedData.categories[0].bookmarkCount).toBe(5)
    })
  })

  describe('exportTags', () => {
    it('应该导出标签数据', async () => {
      const mockTags = [
        {
          id: 'tag1',
          name: 'JavaScript',
          color: '#F59E0B',
          usageCount: 10,
          createdAt: new Date(),
          updatedAt: new Date()
        }
      ]

      const { tagService } = await import('../src/services/tagService')
      vi.mocked(tagService.getTags).mockResolvedValue(mockTags as any)

      const options: ExportTagsOptions = {
        format: 'json',
        includeUsageStats: true,
        includeRelatedBookmarks: false
      }

      const result = await service.exportTags(options)

      expect(result.success).toBe(true)
      expect(result.itemCount).toBe(1)
      
      const exportedData = JSON.parse(result.data as string)
      expect(exportedData.exportType).toBe('tags')
      expect(exportedData.tags).toHaveLength(1)
      expect(exportedData.tags[0].usageCount).toBe(10)
    })

    it('应该包含相关收藏当启用选项时', async () => {
      const mockTags = [
        {
          id: 'tag1',
          name: 'JavaScript',
          color: '#F59E0B',
          usageCount: 1,
          createdAt: new Date(),
          updatedAt: new Date()
        }
      ]

      const mockBookmarks = [
        {
          id: 'bookmark1',
          type: 'url',
          title: '测试收藏',
          url: 'https://example.com',
          category: '技术',
          tags: ['JavaScript'],
          createdAt: new Date(),
          updatedAt: new Date(),
          metadata: { aiGenerated: false }
        }
      ]

      const { tagService } = await import('../src/services/tagService')
      const { bookmarkService } = await import('../src/services/bookmarkService')
      
      vi.mocked(tagService.getTags).mockResolvedValue(mockTags as any)
      vi.mocked(bookmarkService.getBookmarks).mockResolvedValue(mockBookmarks as any)

      const options: ExportTagsOptions = {
        format: 'json',
        includeUsageStats: false,
        includeRelatedBookmarks: true
      }

      const result = await service.exportTags(options)
      const exportedData = JSON.parse(result.data as string)

      expect(exportedData.bookmarks).toHaveLength(1)
      expect(exportedData.metadata.totalBookmarks).toBe(1)
    })
  })

  describe('importData', () => {
    it('应该成功导入无冲突的数据', async () => {
      const importData: ImportData = {
        version: '2.0',
        exportDate: '2024-01-01T00:00:00.000Z',
        exportType: 'all',
        metadata: {
          source: 'Test',
          totalBookmarks: 1,
          totalCategories: 1,
          totalTags: 1,
          exportOptions: {}
        },
        bookmarks: [{
          type: 'url',
          title: '测试收藏',
          url: 'https://example.com',
          category: '技术',
          tags: ['JavaScript']
        }],
        categories: [{
          name: '技术',
          description: '技术相关',
          color: '#3B82F6'
        }],
        tags: [{
          name: 'JavaScript',
          color: '#F59E0B'
        }]
      }

      // 模拟无冲突
      const { conflictResolverService } = await import('../src/services/ConflictResolverService')
      vi.mocked(conflictResolverService.detectConflicts).mockResolvedValue({
        hasConflicts: false,
        conflicts: [],
        summary: {
          bookmarkConflicts: 0,
          categoryConflicts: 0,
          tagConflicts: 0
        }
      })

      // 模拟服务调用
      const { categoryService } = await import('../src/services/categoryService')
      const { tagService } = await import('../src/services/tagService')
      const { bookmarkService } = await import('../src/services/bookmarkService')

      vi.mocked(categoryService.getCategoryByName).mockResolvedValue(null)
      vi.mocked(tagService.getTagByName).mockResolvedValue(null)
      vi.mocked(bookmarkService.findBookmarkByUrl).mockResolvedValue(null)
      vi.mocked(categoryService.createCategory).mockResolvedValue({} as any)
      vi.mocked(tagService.createTag).mockResolvedValue({} as any)
      vi.mocked(bookmarkService.saveBookmark).mockResolvedValue({} as any)

      // 模拟解析方法
      vi.spyOn(service, 'parseImportDataNew').mockResolvedValue(importData)

      const file = new File([JSON.stringify(importData)], 'test.json', { type: 'application/json' })
      const options = {
        source: 'json' as const,
        skipDuplicates: true,
        defaultCategory: '默认分类',
        validateData: true
      }

      const result = await service.importData(file, options)

      expect(result.success).toBe(true)
      expect(result.totalItems).toBe(3)
      expect(result.importedItems).toBe(3)
      expect(result.conflicts).toBeUndefined()
    })

    it('应该检测并返回冲突', async () => {
      const importData: ImportData = {
        version: '2.0',
        exportDate: '2024-01-01T00:00:00.000Z',
        exportType: 'bookmarks',
        metadata: {
          source: 'Test',
          totalBookmarks: 1,
          totalCategories: 0,
          totalTags: 0,
          exportOptions: {}
        },
        bookmarks: [{
          type: 'url',
          title: '测试收藏',
          url: 'https://example.com',
          category: '技术',
          tags: ['JavaScript']
        }]
      }

      // 模拟有冲突
      const mockConflicts = {
        hasConflicts: true,
        conflicts: [{
          id: 'conflict1',
          type: 'bookmark' as const,
          conflictType: 'duplicate' as const,
          existingData: {},
          importData: {},
          conflictFields: ['url'],
          similarity: 1.0
        }],
        summary: {
          bookmarkConflicts: 1,
          categoryConflicts: 0,
          tagConflicts: 0
        }
      }

      const { conflictResolverService } = await import('../src/services/ConflictResolverService')
      vi.mocked(conflictResolverService.detectConflicts).mockResolvedValue(mockConflicts)

      // 模拟解析方法
      vi.spyOn(service, 'parseImportDataNew').mockResolvedValue(importData)

      const file = new File([JSON.stringify(importData)], 'test.json', { type: 'application/json' })
      const options = {
        source: 'json' as const,
        skipDuplicates: false,
        validateData: true
      }

      const result = await service.importData(file, options)

      expect(result.success).toBe(false)
      expect(result.conflicts).toBeDefined()
      expect(result.conflicts!.hasConflicts).toBe(true)
      expect(result.conflicts!.conflicts).toHaveLength(1)
    })
  })

  describe('parseImportDataNew', () => {
    it('应该解析新格式的JSON数据', async () => {
      const validData: ImportData = {
        version: '2.0',
        exportDate: '2024-01-01T00:00:00.000Z',
        exportType: 'all',
        metadata: {
          source: 'Test',
          totalBookmarks: 1,
          totalCategories: 0,
          totalTags: 0,
          exportOptions: {}
        },
        bookmarks: [{
          type: 'url',
          title: '测试收藏',
          url: 'https://example.com',
          category: '技术',
          tags: ['JavaScript']
        }]
      }

      const jsonString = JSON.stringify(validData)
      const options = { source: 'json' as const }

      const result = await service.parseImportDataNew(jsonString, options)

      expect(result.version).toBe('2.0')
      expect(result.exportType).toBe('all')
      expect(result.bookmarks).toHaveLength(1)
    })

    it('应该兼容旧格式的数据', async () => {
      const legacyData = {
        bookmarks: [{
          type: 'url',
          title: '测试收藏',
          url: 'https://example.com',
          category: '技术',
          tags: ['JavaScript']
        }]
      }

      const jsonString = JSON.stringify(legacyData)
      const options = { source: 'json' as const }

      const result = await service.parseImportDataNew(jsonString, options)

      expect(result.version).toBe('1.0')
      expect(result.exportType).toBe('bookmarks')
      expect(result.bookmarks).toHaveLength(1)
      expect(result.metadata.source).toBe('Legacy Format')
    })

    it('应该处理数组格式的数据', async () => {
      const arrayData = [{
        type: 'url',
        title: '测试收藏',
        url: 'https://example.com',
        category: '技术',
        tags: ['JavaScript']
      }]

      const jsonString = JSON.stringify(arrayData)
      const options = { source: 'json' as const }

      const result = await service.parseImportDataNew(jsonString, options)

      expect(result.version).toBe('1.0')
      expect(result.exportType).toBe('bookmarks')
      expect(result.bookmarks).toHaveLength(1)
      expect(result.metadata.source).toBe('Array Format')
    })

    it('应该抛出错误当JSON无效时', async () => {
      const invalidJson = '{ invalid json }'
      const options = { source: 'json' as const }

      await expect(service.parseImportDataNew(invalidJson, options))
        .rejects.toThrow('解析JSON失败')
    })
  })
})