import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { describe, it, expect, vi, beforeEach } from 'vitest'
import PopupApp from '../src/popup/PopupApp'

// Mock chrome API
const mockChrome = {
  tabs: {
    query: vi.fn(),
    sendMessage: vi.fn(),
    create: vi.fn()
  },
  runtime: {
    sendMessage: vi.fn(),
    onMessage: {
      addListener: vi.fn(),
      removeListener: vi.fn()
    },
    getURL: vi.fn()
  },
  storage: {
    sync: {
      get: vi.fn(),
      set: vi.fn()
    }
  }
}

// 设置全局chrome对象
Object.defineProperty(global, 'chrome', {
  value: mockChrome,
  writable: true
})

// Mock window.close
Object.defineProperty(global.window, 'close', {
  value: vi.fn(),
  writable: true
})

describe('PopupApp shadcn重构测试', () => {
  beforeEach(() => {
    // 重置所有mock
    vi.clearAllMocks()
    
    // 设置默认的chrome API响应
    mockChrome.tabs.query.mockResolvedValue([{
      id: 1,
      title: '测试页面',
      url: 'https://example.com',
      favIconUrl: 'https://example.com/favicon.ico'
    }])
    
    mockChrome.runtime.sendMessage.mockResolvedValue({
      success: true,
      data: { isBookmarked: false, bookmarkId: null }
    })
    
    mockChrome.storage.sync.get.mockResolvedValue({
      appSettings: {
        autoTagging: true,
        duplicateDetection: true,
        floatingWidget: false,
        aiAssistant: true
      }
    })
    
    mockChrome.runtime.getURL.mockReturnValue('chrome-extension://test/src/options/index.html')
  })

  it('应该使用shadcn Card组件作为主容器', async () => {
    render(<PopupApp />)
    
    await waitFor(() => {
      // 检查是否使用了Card组件的类名
      const cardElement = document.querySelector('[class*="border"][class*="rounded"]')
      expect(cardElement).toBeInTheDocument()
    })
  })

  it('应该使用shadcn Button组件替换自定义按钮', async () => {
    render(<PopupApp />)
    
    await waitFor(() => {
      // 等待组件加载完成
      expect(screen.getByText('收藏当前页面')).toBeInTheDocument()
    })

    // 检查主要操作按钮是否使用了shadcn Button组件
    const bookmarkButton = screen.getByText('收藏当前页面')
    expect(bookmarkButton.closest('button')).toHaveClass('inline-flex') // shadcn Button的特征类名
    
    const detailedButton = screen.getByText('详细收藏')
    expect(detailedButton.closest('button')).toHaveClass('inline-flex')
  })

  it('应该使用shadcn Switch组件替换Toggle组件', async () => {
    render(<PopupApp />)
    
    await waitFor(() => {
      expect(screen.getByText('自动标签生成')).toBeInTheDocument()
    })

    // 检查是否使用了Switch组件
    const switches = document.querySelectorAll('[role="switch"]')
    expect(switches.length).toBeGreaterThan(0)
    
    // 检查Switch组件的特征属性
    switches.forEach(switchElement => {
      expect(switchElement).toHaveAttribute('data-state')
    })
  })

  it('应该使用shadcn颜色系统替换gray色彩类', async () => {
    render(<PopupApp />)
    
    await waitFor(() => {
      expect(screen.getByText('Universe Bag')).toBeInTheDocument()
    })

    // 检查是否使用了shadcn的颜色系统
    const textElements = screen.getAllByText(/自动标签生成|内容去重检测|页面浮窗|AI智能助手/)
    textElements.forEach(element => {
      // 应该使用text-foreground或text-muted-foreground而不是text-gray-*
      const classList = element.className
      expect(classList).not.toMatch(/text-gray-\d+/)
    })
  })

  it('应该正确处理已收藏状态的UI', async () => {
    // 模拟已收藏状态
    mockChrome.runtime.sendMessage.mockResolvedValue({
      success: true,
      data: { isBookmarked: true, bookmarkId: 'test-id' }
    })

    render(<PopupApp />)
    
    await waitFor(() => {
      expect(screen.getByText('已收藏')).toBeInTheDocument()
    })

    // 检查已收藏状态的Card组件
    const bookmarkedCard = screen.getByText('已收藏').closest('[class*="border"]')
    expect(bookmarkedCard).toBeInTheDocument()
    expect(bookmarkedCard).toHaveClass('bg-green-50')

    // 检查编辑和管理按钮
    expect(screen.getByText('编辑收藏')).toBeInTheDocument()
    expect(screen.getByText('在管理页面打开')).toBeInTheDocument()
  })

  it('应该正确处理同步状态显示', async () => {
    render(<PopupApp />)
    
    await waitFor(() => {
      expect(screen.getByTitle('手动同步')).toBeInTheDocument()
    })

    // 点击同步按钮
    const syncButton = screen.getByTitle('手动同步')
    fireEvent.click(syncButton)

    // 检查同步状态的显示（这里需要根据实际实现调整）
    expect(mockChrome.runtime.sendMessage).toHaveBeenCalledWith({ type: 'MANUAL_SYNC' })
  })

  it('应该使用shadcn Separator组件分隔内容区域', async () => {
    render(<PopupApp />)
    
    await waitFor(() => {
      expect(screen.getByText('Universe Bag')).toBeInTheDocument()
    })

    // 检查是否使用了Separator组件
    const separators = document.querySelectorAll('[data-orientation="horizontal"]')
    expect(separators.length).toBeGreaterThan(0)
  })

  it('应该正确处理设置开关的交互', async () => {
    render(<PopupApp />)
    
    await waitFor(() => {
      expect(screen.getByText('自动标签生成')).toBeInTheDocument()
    })

    // 找到自动标签生成的开关 - 使用更准确的选择器
    const switches = document.querySelectorAll('[role="switch"]')
    expect(switches.length).toBeGreaterThan(0)
    
    // 点击第一个开关（自动标签生成）
    const autoTaggingSwitch = switches[0]
    fireEvent.click(autoTaggingSwitch)
    
    // 验证设置保存调用
    await waitFor(() => {
      expect(mockChrome.storage.sync.set).toHaveBeenCalled()
    })
  })

  it('应该正确处理底部导航按钮', async () => {
    render(<PopupApp />)
    
    await waitFor(() => {
      expect(screen.getByText('管理收藏')).toBeInTheDocument()
    })

    // 检查底部导航按钮是否使用了shadcn Button组件
    const managementButton = screen.getByText('管理收藏')
    const managementButtonElement = managementButton.closest('button')
    expect(managementButtonElement).toHaveClass('rounded-md') // shadcn Button的特征类名
    
    const settingsButton = screen.getByText('设置')
    const settingsButtonElement = settingsButton.closest('button')
    expect(settingsButtonElement).toHaveClass('rounded-md') // shadcn Button的特征类名

    // 测试按钮点击
    fireEvent.click(managementButton)
    expect(mockChrome.tabs.create).toHaveBeenCalled()
  })
})