// TagsTab组件测试 - 验证与现有系统的兼容性

import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest'
import TagsTab from '../src/components/TagsTab'
import { tagService } from '../src/services/tagService'

// Mock tagService
vi.mock('../src/services/tagService', () => ({
  tagService: {
    syncTagsFromBookmarks: vi.fn(),
    getAllTagsWithStats: vi.fn(),
    createTag: vi.fn(),
    updateTag: vi.fn(),
    deleteTag: vi.fn(),
    validateTagName: vi.fn(),
    getTagUsageCount: vi.fn(),
    getBookmarksByTag: vi.fn(),
    searchTags: vi.fn()
  }
}))

// Mock Chrome API
const mockChrome = {
  runtime: {
    sendMessage: vi.fn(),
    onMessage: {
      addListener: vi.fn(),
      removeListener: vi.fn()
    }
  },
  storage: {
    local: {
      get: vi.fn(),
      set: vi.fn(),
      remove: vi.fn()
    }
  }
}

// 设置全局Chrome对象
Object.defineProperty(global, 'chrome', {
  value: mockChrome,
  writable: true
})

describe('TagsTab组件', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    
    // 默认成功的mock响应
    vi.mocked(tagService.syncTagsFromBookmarks).mockResolvedValue(undefined)
    vi.mocked(tagService.getAllTagsWithStats).mockResolvedValue([])
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  describe('初始化', () => {
    it('应该显示初始化加载状态', () => {
      render(<TagsTab />)
      
      expect(screen.getByText('正在初始化标签管理...')).toBeInTheDocument()
      expect(screen.getByRole('status')).toBeInTheDocument()
    })

    it('应该在初始化时检查Chrome API', async () => {
      render(<TagsTab />)
      
      await waitFor(() => {
        expect(screen.queryByText('正在初始化标签管理...')).not.toBeInTheDocument()
      })
    })

    it('应该在初始化成功后显示标签管理组件', async () => {
      render(<TagsTab />)
      
      await waitFor(() => {
        expect(screen.queryByText('正在初始化标签管理...')).not.toBeInTheDocument()
      })
      
      // 应该显示功能说明
      expect(screen.getByText('标签管理功能')).toBeInTheDocument()
    })
  })

  describe('错误处理', () => {
    it('应该处理Chrome API不可用的情况', async () => {
      // 临时移除Chrome API
      const originalChrome = global.chrome
      delete (global as any).chrome
      
      render(<TagsTab />)
      
      await waitFor(() => {
        expect(screen.getByText('标签管理初始化失败')).toBeInTheDocument()
        expect(screen.getByText(/Chrome扩展API不可用/)).toBeInTheDocument()
      })
      
      // 恢复Chrome API
      global.chrome = originalChrome
    })

    it('应该处理初始化失败的情况', async () => {
      // 模拟Chrome API不可用
      const originalChrome = global.chrome
      delete (global as any).chrome
      
      render(<TagsTab />)
      
      await waitFor(() => {
        expect(screen.getByText('标签管理初始化失败')).toBeInTheDocument()
        expect(screen.getByText(/Chrome扩展API不可用/)).toBeInTheDocument()
      })
      
      // 恢复Chrome API
      global.chrome = originalChrome
    })

    it('应该提供重试功能', async () => {
      // 模拟Chrome API不可用，然后恢复
      const originalChrome = global.chrome
      delete (global as any).chrome
      
      render(<TagsTab />)
      
      await waitFor(() => {
        expect(screen.getByText('标签管理初始化失败')).toBeInTheDocument()
      })
      
      // 恢复Chrome API
      global.chrome = originalChrome
      
      const retryButton = screen.getByRole('button', { name: /重试/ })
      fireEvent.click(retryButton)
      
      await waitFor(() => {
        expect(screen.queryByText('标签管理初始化失败')).not.toBeInTheDocument()
      })
    })

    it('应该限制重试次数', async () => {
      // 持续模拟Chrome API不可用
      const originalChrome = global.chrome
      delete (global as any).chrome
      
      render(<TagsTab />)
      
      await waitFor(() => {
        expect(screen.getByText('标签管理初始化失败')).toBeInTheDocument()
      })
      
      // 进行3次重试
      for (let i = 0; i < 3; i++) {
        const retryButton = screen.getByRole('button', { name: /重试/ })
        fireEvent.click(retryButton)
        
        await waitFor(() => {
          expect(screen.getByText('标签管理初始化失败')).toBeInTheDocument()
        })
      }
      
      // 第4次应该不再显示重试按钮
      await waitFor(() => {
        expect(screen.queryByRole('button', { name: /重试/ })).not.toBeInTheDocument()
        expect(screen.getByText(/已达到最大重试次数/)).toBeInTheDocument()
      })
      
      // 恢复Chrome API
      global.chrome = originalChrome
    })
  })

  describe('数据同步', () => {
    it('应该支持手动同步', async () => {
      render(<TagsTab />)
      
      await waitFor(() => {
        expect(screen.queryByText('正在初始化标签管理...')).not.toBeInTheDocument()
      })
      
      const manualSyncButton = screen.getByRole('button', { name: '手动同步' })
      fireEvent.click(manualSyncButton)
      
      await waitFor(() => {
        expect(tagService.syncTagsFromBookmarks).toHaveBeenCalledTimes(1)
      })
    })

    it('应该显示同步错误', async () => {
      vi.mocked(tagService.syncTagsFromBookmarks)
        .mockRejectedValueOnce(new Error('手动同步失败'))
      
      render(<TagsTab />)
      
      await waitFor(() => {
        expect(screen.queryByText('正在初始化标签管理...')).not.toBeInTheDocument()
      })
      
      const manualSyncButton = screen.getByRole('button', { name: '手动同步' })
      fireEvent.click(manualSyncButton)
      
      await waitFor(() => {
        expect(screen.getByText('标签同步失败')).toBeInTheDocument()
        expect(screen.getByText('手动同步失败')).toBeInTheDocument()
      })
    })
  })

  describe('用户界面', () => {
    it('应该显示功能说明', async () => {
      render(<TagsTab />)
      
      await waitFor(() => {
        expect(screen.queryByText('正在初始化标签管理...')).not.toBeInTheDocument()
      })
      
      expect(screen.getByText('标签管理功能')).toBeInTheDocument()
      expect(screen.getByText(/在这里您可以查看、创建、编辑和删除标签/)).toBeInTheDocument()
    })

    it('应该集成TagManagementTab组件', async () => {
      render(<TagsTab />)
      
      await waitFor(() => {
        expect(screen.queryByText('正在初始化标签管理...')).not.toBeInTheDocument()
      })
      
      // TagManagementTab应该被渲染（通过检查其特有的元素）
      // 这里我们检查是否有标签管理相关的内容
      expect(screen.getByText('标签管理功能')).toBeInTheDocument()
    })

    it('应该支持自定义CSS类名', () => {
      const customClass = 'custom-tags-tab'
      const { container } = render(<TagsTab className={customClass} />)
      
      expect(container.firstChild).toHaveClass(customClass)
    })
  })

  describe('与OptionsApp的兼容性', () => {
    it('应该作为独立组件正常渲染', () => {
      const { container } = render(<TagsTab />)
      
      expect(container.firstChild).toBeInTheDocument()
    })

    it('应该处理组件卸载', () => {
      const { unmount } = render(<TagsTab />)
      
      expect(() => unmount()).not.toThrow()
    })

    it('应该支持React.memo优化', () => {
      const { rerender } = render(<TagsTab />)
      
      // 相同props不应该重新渲染
      rerender(<TagsTab />)
      
      // 组件应该正常工作
      expect(screen.getByText('正在初始化标签管理...')).toBeInTheDocument()
    })
  })

  describe('无障碍性', () => {
    it('应该提供适当的ARIA标签', async () => {
      render(<TagsTab />)
      
      // 加载状态应该有适当的role
      expect(screen.getByRole('status')).toBeInTheDocument()
      
      await waitFor(() => {
        expect(screen.queryByText('正在初始化标签管理...')).not.toBeInTheDocument()
      })
    })

    it('应该支持键盘导航', async () => {
      render(<TagsTab />)
      
      await waitFor(() => {
        expect(screen.queryByText('正在初始化标签管理...')).not.toBeInTheDocument()
      })
      
      const manualSyncButton = screen.getByRole('button', { name: '手动同步' })
      expect(manualSyncButton).toBeInTheDocument()
      
      // 按钮应该可以通过键盘访问
      manualSyncButton.focus()
      expect(document.activeElement).toBe(manualSyncButton)
    })
  })
})