// AI提供商服务测试

import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest'
import { aiProviderService } from '../src/services/aiProviderService'
import { AIProviderConfig } from '../src/types/ai'

// Mock fetch
global.fetch = vi.fn()

describe('AIProviderService', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('testConnection', () => {
    it('应该成功测试Ollama连接', async () => {
      const mockResponse = {
        ok: true,
        json: () => Promise.resolve({
          models: [
            { name: 'llama2', size: 1000000 },
            { name: 'qwen', size: 2000000 }
          ]
        })
      }

      vi.mocked(fetch).mockResolvedValue(mockResponse as any)

      const config: AIProviderConfig = {
        id: 'test_ollama',
        name: 'Test Ollama',
        type: 'ollama',
        baseUrl: 'http://localhost:11434',
        enabled: true,
        createdAt: new Date(),
        updatedAt: new Date()
      }

      const result = await aiProviderService.testConnection(config)

      expect(result.success).toBe(true)
      expect(result.providerId).toBe('test_ollama')
      expect(result.modelCount).toBe(2)
      expect(result.responseTime).toBeGreaterThanOrEqual(0)
      expect(fetch).toHaveBeenCalledWith(
        'http://localhost:11434/api/tags',
        expect.objectContaining({
          method: 'GET'
        })
      )
    })

    it('应该成功测试OpenRouter连接', async () => {
      const mockResponse = {
        ok: true,
        json: () => Promise.resolve({
          data: [
            { 
              id: 'openai/gpt-3.5-turbo', 
              name: 'GPT-3.5 Turbo',
              description: 'OpenAI GPT-3.5 Turbo model',
              context_length: 4096,
              architecture: {
                modality: 'text',
                tokenizer: 'cl100k_base'
              },
              pricing: {
                prompt: '0.0015',
                completion: '0.002'
              }
            },
            { 
              id: 'openai/gpt-4', 
              name: 'GPT-4',
              description: 'OpenAI GPT-4 model',
              context_length: 8192,
              architecture: {
                modality: 'text',
                tokenizer: 'cl100k_base'
              },
              pricing: {
                prompt: '0.03',
                completion: '0.06'
              }
            }
          ]
        })
      }

      vi.mocked(fetch).mockResolvedValue(mockResponse as any)

      const config: AIProviderConfig = {
        id: 'test_openrouter',
        name: 'Test OpenRouter',
        type: 'openrouter',
        baseUrl: 'https://openrouter.ai/api/v1',
        apiKey: 'sk-or-test-key-********************',
        enabled: true,
        createdAt: new Date(),
        updatedAt: new Date()
      }

      const result = await aiProviderService.testConnection(config)

      expect(result.success).toBe(true)
      expect(result.modelCount).toBe(2)
      expect(fetch).toHaveBeenCalledWith(
        'https://openrouter.ai/api/v1/models',
        expect.objectContaining({
          method: 'GET',
          headers: expect.objectContaining({
            'Authorization': 'Bearer sk-or-test-key-********************',
            'HTTP-Referer': 'https://localhost:3000',
            'X-Title': 'Bookmark Manager Extension'
          })
        })
      )
    })

    it('网络错误应该返回失败结果', async () => {
      vi.mocked(fetch).mockRejectedValue(new Error('Network error'))

      const config: AIProviderConfig = {
        id: 'test_ollama',
        name: 'Test Ollama',
        type: 'ollama',
        baseUrl: 'http://localhost:11434',
        enabled: true,
        createdAt: new Date(),
        updatedAt: new Date()
      }

      const result = await aiProviderService.testConnection(config)

      expect(result.success).toBe(false)
      expect(result.error).toContain('Network error')
    })

    it('HTTP错误应该返回失败结果', async () => {
      const mockResponse = {
        ok: false,
        status: 404,
        statusText: 'Not Found'
      }

      vi.mocked(fetch).mockResolvedValue(mockResponse as any)

      const config: AIProviderConfig = {
        id: 'test_ollama',
        name: 'Test Ollama',
        type: 'ollama',
        baseUrl: 'http://localhost:11434',
        enabled: true,
        createdAt: new Date(),
        updatedAt: new Date()
      }

      const result = await aiProviderService.testConnection(config)

      expect(result.success).toBe(false)
      expect(result.error).toContain('HTTP 404')
    })

    it('不支持的提供商类型应该抛出错误', async () => {
      const config: AIProviderConfig = {
        id: 'test_invalid',
        name: 'Test Invalid',
        type: 'invalid' as any,
        baseUrl: 'http://example.com',
        enabled: true,
        createdAt: new Date(),
        updatedAt: new Date()
      }

      const result = await aiProviderService.testConnection(config)

      expect(result.success).toBe(false)
      expect(result.error).toContain('不支持的提供商类型')
    })
  })

  describe('getModels', () => {
    it('应该成功获取Ollama模型列表', async () => {
      const mockResponse = {
        ok: true,
        json: () => Promise.resolve({
          models: [
            {
              name: 'llama2:7b',
              size: **********,
              details: {
                format: 'gguf',
                family: 'llama',
                parameter_size: '7B',
                quantization_level: 'Q4_0'
              }
            },
            {
              name: 'qwen:14b',
              size: **********,
              details: {
                format: 'gguf',
                family: 'qwen',
                parameter_size: '14B',
                quantization_level: 'Q4_0'
              }
            },
            {
              name: 'codellama:13b-instruct',
              size: **********,
              details: {
                format: 'gguf',
                family: 'llama',
                parameter_size: '13B',
                quantization_level: 'Q4_0'
              }
            }
          ]
        })
      }

      vi.mocked(fetch).mockResolvedValue(mockResponse as any)

      const config: AIProviderConfig = {
        id: 'test_ollama',
        name: 'Test Ollama',
        type: 'ollama',
        baseUrl: 'http://localhost:11434',
        enabled: true,
        createdAt: new Date(),
        updatedAt: new Date()
      }

      const models = await aiProviderService.getModels(config)

      expect(models).toHaveLength(3)
      
      // 测试Llama 2模型
      const llama2Model = models.find(m => m.id === 'llama2:7b')
      expect(llama2Model).toBeDefined()
      expect(llama2Model?.displayName).toBe('Llama 2 (7b)')
      expect(llama2Model?.description).toContain('Meta开发的大型语言模型')
      expect(llama2Model?.size).toBe('3.54 GB')
      expect(llama2Model?.parameters).toBe('7B')
      expect(llama2Model?.capabilities).toContain('chat')
      expect(llama2Model?.capabilities).toContain('completion')
      expect(llama2Model?.tags).toContain('llama')
      expect(llama2Model?.tags).toContain('Q4_0')
      expect(llama2Model?.tags).toContain('7B')
      expect(llama2Model?.isRecommended).toBe(true)
      expect(llama2Model?.isPopular).toBe(true)

      // 测试Qwen模型
      const qwenModel = models.find(m => m.id === 'qwen:14b')
      expect(qwenModel).toBeDefined()
      expect(qwenModel?.displayName).toBe('Qwen (14b)')
      expect(qwenModel?.description).toContain('阿里巴巴开发的通义千问模型')
      expect(qwenModel?.isRecommended).toBe(true)
      expect(qwenModel?.isPopular).toBe(true)

      // 测试Code Llama模型
      const codeLlamaModel = models.find(m => m.id === 'codellama:13b-instruct')
      expect(codeLlamaModel).toBeDefined()
      expect(codeLlamaModel?.displayName).toBe('Code Llama (13b-instruct)')
      expect(codeLlamaModel?.description).toContain('基于Llama 2的代码生成模型')
      expect(codeLlamaModel?.capabilities).toContain('coding')
      expect(codeLlamaModel?.capabilities).toContain('instruction-following')
      expect(codeLlamaModel?.tags).toContain('代码生成')
      expect(codeLlamaModel?.tags).toContain('对话')
      expect(codeLlamaModel?.isRecommended).toBe(true)

      // 验证模型按推荐度排序
      expect(models.every(m => m.isRecommended)).toBe(true)
    })

    it('应该成功获取OpenRouter模型列表', async () => {
      const mockResponse = {
        ok: true,
        json: () => Promise.resolve({
          data: [
            {
              id: 'openai/gpt-3.5-turbo',
              name: 'GPT-3.5 Turbo',
              description: 'OpenAI GPT-3.5 Turbo model',
              context_length: 4096,
              architecture: {
                modality: 'text',
                tokenizer: 'cl100k_base'
              },
              pricing: {
                prompt: '0.0015',
                completion: '0.002'
              },
              top_provider: {
                context_length: 4096
              }
            }
          ]
        })
      }

      vi.mocked(fetch).mockResolvedValue(mockResponse as any)

      const config: AIProviderConfig = {
        id: 'test_openrouter',
        name: 'Test OpenRouter',
        type: 'openrouter',
        baseUrl: 'https://openrouter.ai/api/v1',
        apiKey: 'sk-or-test-key-********************',
        enabled: true,
        createdAt: new Date(),
        updatedAt: new Date()
      }

      const models = await aiProviderService.getModels(config)

      expect(models).toHaveLength(1)
      expect(models[0].id).toBe('openai/gpt-3.5-turbo')
      expect(models[0].displayName).toBe('GPT-3.5 Turbo')
      expect(models[0].description).toContain('OpenAI GPT-3.5 Turbo model')
      expect(models[0].parameters).toBe('4096 tokens')
      expect(models[0].providerId).toBe('openrouter')
      expect(models[0].isPopular).toBe(true) // gpt-3.5应该是热门模型
    })

    it('应该成功获取LM Studio模型列表', async () => {
      const mockResponse = {
        ok: true,
        json: () => Promise.resolve({
          data: [
            {
              id: 'models/llama-2-7b-chat.gguf',
              object: 'model',
              created: **********,
              owned_by: 'local'
            },
            {
              id: 'models/qwen-14b-instruct-q4.gguf',
              object: 'model',
              created: **********,
              owned_by: 'local'
            },
            {
              id: 'models/codellama-13b-python.gguf',
              object: 'model',
              created: **********,
              owned_by: 'local'
            }
          ]
        })
      }

      vi.mocked(fetch).mockResolvedValue(mockResponse as any)

      const config: AIProviderConfig = {
        id: 'test_lmstudio',
        name: 'Test LM Studio',
        type: 'lm-studio',
        baseUrl: 'http://localhost:1234/v1',
        enabled: true,
        createdAt: new Date(),
        updatedAt: new Date()
      }

      const models = await aiProviderService.getModels(config)

      expect(models).toHaveLength(3)
      
      // 测试Llama 2模型
      const llama2Model = models.find(m => m.id === 'models/llama-2-7b-chat.gguf')
      expect(llama2Model).toBeDefined()
      expect(llama2Model?.displayName).toBe('Llama 2 (llama-2-7b-chat)')
      expect(llama2Model?.description).toContain('Meta开发的大型语言模型')
      expect(llama2Model?.capabilities).toContain('chat')
      expect(llama2Model?.capabilities).toContain('instruction-following')
      expect(llama2Model?.tags).toContain('LM Studio')
      expect(llama2Model?.tags).toContain('对话')
      expect(llama2Model?.tags).toContain('7B参数')
      expect(llama2Model?.isRecommended).toBe(true)
      expect(llama2Model?.isPopular).toBe(true)

      // 测试Qwen模型
      const qwenModel = models.find(m => m.id === 'models/qwen-14b-instruct-q4.gguf')
      expect(qwenModel).toBeDefined()
      expect(qwenModel?.displayName).toBe('Qwen (qwen-14b-instruct-q4)')
      expect(qwenModel?.description).toContain('阿里巴巴开发的通义千问模型')
      expect(qwenModel?.tags).toContain('Q4量化')
      expect(qwenModel?.tags).toContain('14B参数')
      expect(qwenModel?.isRecommended).toBe(true)

      // 测试Code Llama模型
      const codeLlamaModel = models.find(m => m.id === 'models/codellama-13b-python.gguf')
      expect(codeLlamaModel).toBeDefined()
      expect(codeLlamaModel?.displayName).toBe('Code Llama (codellama-13b-python)')
      expect(codeLlamaModel?.description).toContain('基于Llama的代码生成模型')
      expect(codeLlamaModel?.capabilities).toContain('coding')
      expect(codeLlamaModel?.tags).toContain('代码生成')
      expect(codeLlamaModel?.tags).toContain('13B参数')
      expect(codeLlamaModel?.isRecommended).toBe(true)

      // 验证模型按推荐度排序
      expect(models.every(m => m.isRecommended)).toBe(true)
    })

    it('应该成功获取Xinference模型列表', async () => {
      const mockResponse = {
        ok: true,
        json: () => Promise.resolve({
          data: [
            {
              model_uid: 'llama-2-7b-chat-uid',
              model_name: 'llama-2-7b-chat',
              model_type: 'LLM',
              model_size_in_billions: 7,
              quantization: 'q4_0',
              model_description: 'Llama 2 7B Chat model',
              context_length: 4096,
              model_lang: ['en', 'zh'],
              model_ability: ['chat', 'generate']
            },
            {
              model_uid: 'qwen-14b-uid',
              model_name: 'qwen-14b',
              model_type: 'LLM',
              model_size_in_billions: 14,
              quantization: 'int4',
              model_description: 'Qwen 14B model',
              context_length: 8192,
              model_lang: ['zh', 'en'],
              model_ability: ['chat', 'generate']
            },
            {
              model_uid: 'chatglm3-6b-uid',
              model_name: 'chatglm3-6b',
              model_type: 'LLM',
              model_size_in_billions: 6,
              model_description: 'ChatGLM3 6B model',
              context_length: 32768,
              model_lang: ['zh', 'en'],
              model_ability: ['chat']
            }
          ]
        })
      }

      vi.mocked(fetch).mockResolvedValue(mockResponse as any)

      const config: AIProviderConfig = {
        id: 'test_xinference',
        name: 'Test Xinference',
        type: 'xinference',
        baseUrl: 'http://localhost:9997',
        enabled: true,
        createdAt: new Date(),
        updatedAt: new Date()
      }

      const models = await aiProviderService.getModels(config)

      expect(models).toHaveLength(3)
      
      // 测试Llama 2模型
      const llama2Model = models.find(m => m.id === 'llama-2-7b-chat-uid')
      expect(llama2Model).toBeDefined()
      expect(llama2Model?.name).toBe('llama-2-7b-chat')
      expect(llama2Model?.displayName).toBe('Llama 2 7B (q4_0)')
      expect(llama2Model?.description).toContain('Meta开发的大型语言模型')
      expect(llama2Model?.size).toBe('7B')
      expect(llama2Model?.parameters).toBe('7B')
      expect(llama2Model?.capabilities).toContain('chat')
      expect(llama2Model?.capabilities).toContain('completion')
      expect(llama2Model?.tags).toContain('Xinference')
      expect(llama2Model?.tags).toContain('LLM')
      expect(llama2Model?.tags).toContain('英文')
      expect(llama2Model?.tags).toContain('中文')
      expect(llama2Model?.tags).toContain('q4_0')
      expect(llama2Model?.tags).toContain('7B参数')
      expect(llama2Model?.isRecommended).toBe(true)
      expect(llama2Model?.isPopular).toBe(true)

      // 测试Qwen模型
      const qwenModel = models.find(m => m.id === 'qwen-14b-uid')
      expect(qwenModel).toBeDefined()
      expect(qwenModel?.displayName).toBe('Qwen 14B (int4)')
      expect(qwenModel?.description).toContain('阿里巴巴开发的通义千问模型')
      expect(qwenModel?.tags).toContain('int4')
      expect(qwenModel?.tags).toContain('14B参数')
      expect(qwenModel?.isRecommended).toBe(true)

      // 测试ChatGLM模型
      const chatglmModel = models.find(m => m.id === 'chatglm3-6b-uid')
      expect(chatglmModel).toBeDefined()
      expect(chatglmModel?.displayName).toBe('ChatGLM3 6B')
      expect(chatglmModel?.description).toContain('清华大学开发的对话语言模型')
      expect(chatglmModel?.tags).toContain('6B参数')
      expect(chatglmModel?.isRecommended).toBe(true)

      // 验证模型按推荐度排序
      expect(models.every(m => m.isRecommended)).toBe(true)
    })

    it('应该返回Claude预设模型列表', async () => {
      const config: AIProviderConfig = {
        id: 'test_claude',
        name: 'Test Claude',
        type: 'claude',
        baseUrl: 'https://api.anthropic.com/v1',
        apiKey: 'test-key',
        enabled: true,
        createdAt: new Date(),
        updatedAt: new Date()
      }

      const models = await aiProviderService.getModels(config)

      expect(models).toHaveLength(4)
      expect(models.map(m => m.id)).toContain('claude-3-opus-20240229')
      expect(models.map(m => m.id)).toContain('claude-3-sonnet-20240229')
      expect(models.map(m => m.id)).toContain('claude-3-haiku-20240307')
      
      const opusModel = models.find(m => m.id === 'claude-3-opus-20240229')
      expect(opusModel?.displayName).toBe('Claude 3 Opus')
      expect(opusModel?.isRecommended).toBe(true)
    })

    it('网络错误应该返回空数组', async () => {
      vi.mocked(fetch).mockRejectedValue(new Error('Network error'))

      const config: AIProviderConfig = {
        id: 'test_ollama',
        name: 'Test Ollama',
        type: 'ollama',
        baseUrl: 'http://localhost:11434',
        enabled: true,
        createdAt: new Date(),
        updatedAt: new Date()
      }

      const models = await aiProviderService.getModels(config)
      expect(models).toEqual([])
    })

    it('不支持的提供商类型应该返回空数组', async () => {
      const config: AIProviderConfig = {
        id: 'test_invalid',
        name: 'Test Invalid',
        type: 'invalid' as any,
        baseUrl: 'http://example.com',
        enabled: true,
        createdAt: new Date(),
        updatedAt: new Date()
      }

      const models = await aiProviderService.getModels(config)
      expect(models).toEqual([])
    })
  })

  describe('testOllamaConnection', () => {
    it('应该成功测试Ollama连接（通过version端点）', async () => {
      const mockVersionResponse = {
        ok: true,
        json: () => Promise.resolve({ version: '0.1.26' })
      }
      const mockTagsResponse = {
        ok: true,
        json: () => Promise.resolve({
          models: [
            { name: 'llama2:7b' },
            { name: 'qwen:14b' }
          ]
        })
      }

      vi.mocked(fetch)
        .mockResolvedValueOnce(mockVersionResponse as any)
        .mockResolvedValueOnce(mockTagsResponse as any)

      const result = await aiProviderService['testOllamaConnection']('http://localhost:11434')

      expect(result.success).toBe(true)
      expect(result.modelCount).toBe(2)
      expect(fetch).toHaveBeenCalledWith(
        'http://localhost:11434/api/version',
        expect.objectContaining({
          method: 'GET',
          signal: expect.any(AbortSignal)
        })
      )
      expect(fetch).toHaveBeenCalledWith(
        'http://localhost:11434/api/tags',
        expect.objectContaining({
          method: 'GET',
          signal: expect.any(AbortSignal)
        })
      )
    })

    it('应该成功测试Ollama连接（version端点失败时回退到tags端点）', async () => {
      const mockVersionResponse = {
        ok: false,
        status: 404,
        statusText: 'Not Found'
      }
      const mockTagsResponse = {
        ok: true,
        json: () => Promise.resolve({
          models: [
            { name: 'llama2:7b' }
          ]
        })
      }

      vi.mocked(fetch)
        .mockResolvedValueOnce(mockVersionResponse as any)
        .mockResolvedValueOnce(mockTagsResponse as any)

      const result = await aiProviderService['testOllamaConnection']('http://localhost:11434')

      expect(result.success).toBe(true)
      expect(result.modelCount).toBe(1)
    })

    it('连接超时应该返回友好的错误信息', async () => {
      const timeoutError = new Error('The operation was aborted')
      timeoutError.name = 'AbortError'
      
      vi.mocked(fetch).mockRejectedValue(timeoutError)

      const result = await aiProviderService['testOllamaConnection']('http://localhost:11434')

      expect(result.success).toBe(false)
      expect(result.error).toContain('连接超时')
    })

    it('连接拒绝应该返回友好的错误信息', async () => {
      const connectionError = new Error('connect ECONNREFUSED 127.0.0.1:11434')
      
      vi.mocked(fetch).mockRejectedValue(connectionError)

      const result = await aiProviderService['testOllamaConnection']('http://localhost:11434')

      expect(result.success).toBe(false)
      expect(result.error).toContain('Ollama服务未运行或端口不正确')
    })

    it('HTTP错误应该返回失败结果', async () => {
      const mockVersionResponse = {
        ok: false,
        status: 404,
        statusText: 'Not Found'
      }
      const mockTagsResponse = {
        ok: false,
        status: 500,
        statusText: 'Internal Server Error'
      }

      vi.mocked(fetch)
        .mockResolvedValueOnce(mockVersionResponse as any)
        .mockResolvedValueOnce(mockTagsResponse as any)

      const result = await aiProviderService['testOllamaConnection']('http://localhost:11434')

      expect(result.success).toBe(false)
      expect(result.error).toContain('HTTP 500')
    })
  })

  describe('validateOllamaService', () => {
    it('应该通过version端点验证Ollama服务', async () => {
      const mockResponse = {
        ok: true,
        json: () => Promise.resolve({ version: '0.1.26' })
      }

      vi.mocked(fetch).mockResolvedValue(mockResponse as any)

      const result = await aiProviderService['validateOllamaService']('http://localhost:11434')

      expect(result.isRunning).toBe(true)
      expect(result.version).toBe('0.1.26')
      expect(fetch).toHaveBeenCalledWith(
        'http://localhost:11434/api/version',
        expect.objectContaining({
          method: 'GET',
          signal: expect.any(AbortSignal)
        })
      )
    })

    it('version端点失败时应该回退到tags端点', async () => {
      const mockVersionResponse = {
        ok: false,
        status: 404,
        statusText: 'Not Found'
      }
      const mockTagsResponse = {
        ok: true,
        json: () => Promise.resolve({ models: [] })
      }

      vi.mocked(fetch)
        .mockResolvedValueOnce(mockVersionResponse as any)
        .mockResolvedValueOnce(mockTagsResponse as any)

      const result = await aiProviderService['validateOllamaService']('http://localhost:11434')

      expect(result.isRunning).toBe(true)
      expect(result.version).toBe('未知版本')
    })

    it('所有端点都失败时应该返回错误', async () => {
      const mockResponse = {
        ok: false,
        status: 500,
        statusText: 'Internal Server Error'
      }

      vi.mocked(fetch).mockResolvedValue(mockResponse as any)

      const result = await aiProviderService['validateOllamaService']('http://localhost:11434')

      expect(result.isRunning).toBe(false)
      expect(result.error).toContain('HTTP 500')
    })
  })

  describe('testLMStudioConnection', () => {
    it('应该成功测试LM Studio连接', async () => {
      const mockModelsResponse = {
        ok: true,
        json: () => Promise.resolve({
          data: [
            { id: 'llama-2-7b-chat' },
            { id: 'qwen-14b' }
          ]
        })
      }

      vi.mocked(fetch).mockResolvedValue(mockModelsResponse as any)

      const result = await aiProviderService['testLMStudioConnection']('http://localhost:1234/v1')

      expect(result.success).toBe(true)
      expect(result.modelCount).toBe(2)
      expect(fetch).toHaveBeenCalledWith(
        'http://localhost:1234/v1/models',
        expect.objectContaining({
          method: 'GET',
          headers: expect.objectContaining({
            'Content-Type': 'application/json'
          })
        })
      )
    })

    it('连接超时应该返回友好的错误信息', async () => {
      const timeoutError = new Error('The operation was aborted')
      timeoutError.name = 'AbortError'
      
      // 第一次调用validateLMStudioService时超时
      vi.mocked(fetch).mockRejectedValue(timeoutError)

      const result = await aiProviderService['testLMStudioConnection']('http://localhost:1234/v1')

      expect(result.success).toBe(false)
      expect(result.error).toContain('连接超时')
    })

    it('连接拒绝应该返回友好的错误信息', async () => {
      const connectionError = new Error('connect ECONNREFUSED 127.0.0.1:1234')
      
      // 第一次调用validateLMStudioService时连接被拒绝
      vi.mocked(fetch).mockRejectedValue(connectionError)

      const result = await aiProviderService['testLMStudioConnection']('http://localhost:1234/v1')

      expect(result.success).toBe(false)
      expect(result.error).toContain('LM Studio本地服务器未启动或端口不正确')
    })
  })

  describe('validateLMStudioService', () => {
    it('应该验证LM Studio服务状态', async () => {
      const mockResponse = {
        ok: true,
        json: () => Promise.resolve({ data: [] })
      }

      vi.mocked(fetch).mockResolvedValue(mockResponse as any)

      const result = await aiProviderService['validateLMStudioService']('http://localhost:1234/v1')

      expect(result.isRunning).toBe(true)
      expect(result.version).toBe('LM Studio Local Server')
    })

    it('服务不可用时应该返回错误', async () => {
      const mockResponse = {
        ok: false,
        status: 500,
        statusText: 'Internal Server Error'
      }

      vi.mocked(fetch).mockResolvedValue(mockResponse as any)

      const result = await aiProviderService['validateLMStudioService']('http://localhost:1234/v1')

      expect(result.isRunning).toBe(false)
      expect(result.error).toContain('HTTP 500')
    })
  })

  describe('testXinferenceConnection', () => {
    it('应该成功测试Xinference连接', async () => {
      const mockStatusResponse = {
        ok: true,
        json: () => Promise.resolve({ version: '0.9.0' })
      }
      const mockModelsResponse = {
        ok: true,
        json: () => Promise.resolve({
          data: [
            { model_uid: 'llama-2-7b', model_name: 'llama-2-7b' },
            { model_uid: 'qwen-14b', model_name: 'qwen-14b' }
          ]
        })
      }

      vi.mocked(fetch)
        .mockResolvedValueOnce(mockStatusResponse as any)
        .mockResolvedValueOnce(mockModelsResponse as any)

      const result = await aiProviderService['testXinferenceConnection']('http://localhost:9997')

      expect(result.success).toBe(true)
      expect(result.modelCount).toBe(2)
      expect(fetch).toHaveBeenCalledWith(
        'http://localhost:9997/v1/cluster/status',
        expect.objectContaining({
          method: 'GET',
          headers: expect.objectContaining({
            'Content-Type': 'application/json'
          })
        })
      )
    })

    it('连接超时应该返回友好的错误信息', async () => {
      const timeoutError = new Error('The operation was aborted')
      timeoutError.name = 'AbortError'
      
      vi.mocked(fetch).mockRejectedValue(timeoutError)

      const result = await aiProviderService['testXinferenceConnection']('http://localhost:9997')

      expect(result.success).toBe(false)
      expect(result.error).toContain('连接超时')
    })

    it('连接拒绝应该返回友好的错误信息', async () => {
      const connectionError = new Error('connect ECONNREFUSED 127.0.0.1:9997')
      
      vi.mocked(fetch).mockRejectedValue(connectionError)

      const result = await aiProviderService['testXinferenceConnection']('http://localhost:9997')

      expect(result.success).toBe(false)
      expect(result.error).toContain('Xinference服务未运行或端口不正确')
    })
  })

  describe('validateXinferenceService', () => {
    it('应该验证Xinference服务状态', async () => {
      const mockResponse = {
        ok: true,
        json: () => Promise.resolve({ version: '0.9.0' })
      }

      vi.mocked(fetch).mockResolvedValue(mockResponse as any)

      const result = await aiProviderService['validateXinferenceService']('http://localhost:9997')

      expect(result.isRunning).toBe(true)
      expect(result.version).toBe('Xinference 0.9.0')
    })

    it('集群状态端点失败时应该回退到模型列表端点', async () => {
      const mockStatusResponse = {
        ok: false,
        status: 404,
        statusText: 'Not Found'
      }
      const mockModelsResponse = {
        ok: true,
        json: () => Promise.resolve({ data: [] })
      }

      vi.mocked(fetch)
        .mockResolvedValueOnce(mockStatusResponse as any)
        .mockResolvedValueOnce(mockModelsResponse as any)

      const result = await aiProviderService['validateXinferenceService']('http://localhost:9997')

      expect(result.isRunning).toBe(true)
      expect(result.version).toBe('Xinference Server')
    })

    it('所有端点都失败时应该返回错误', async () => {
      const mockResponse = {
        ok: false,
        status: 500,
        statusText: 'Internal Server Error'
      }

      vi.mocked(fetch).mockResolvedValue(mockResponse as any)

      const result = await aiProviderService['validateXinferenceService']('http://localhost:9997')

      expect(result.isRunning).toBe(false)
      expect(result.error).toContain('HTTP 500')
    })
  })

  describe('testOpenRouterConnection', () => {
    it('应该成功测试OpenRouter连接', async () => {
      const mockResponse = {
        ok: true,
        json: () => Promise.resolve({
          data: [
            { id: 'gpt-3.5-turbo' },
            { id: 'gpt-4' }
          ]
        })
      }

      vi.mocked(fetch).mockResolvedValue(mockResponse as any)

      const result = await aiProviderService['testOpenRouterConnection']('sk-or-test-api-key-********************')

      expect(result.success).toBe(true)
      expect(result.modelCount).toBe(2)
      expect(fetch).toHaveBeenCalledWith(
        'https://openrouter.ai/api/v1/models',
        expect.objectContaining({
          method: 'GET',
          headers: expect.objectContaining({
            'Authorization': 'Bearer sk-or-test-api-key-********************',
            'Content-Type': 'application/json',
            'HTTP-Referer': 'https://localhost:3000',
            'X-Title': 'Bookmark Manager Extension'
          })
        })
      )
    })
  })

  describe('formatBytes', () => {
    it('应该正确格式化字节大小', () => {
      const formatBytes = aiProviderService['formatBytes']
      
      expect(formatBytes(0)).toBe('0 Bytes')
      expect(formatBytes(1024)).toBe('1 KB')
      expect(formatBytes(1048576)).toBe('1 MB')
      expect(formatBytes(**********)).toBe('1 GB')
      expect(formatBytes(**********)).toBe('3.54 GB')
    })
  })

  describe('Ollama模型解析方法', () => {
    describe('formatOllamaModelName', () => {
      it('应该正确格式化模型名称', () => {
        const formatName = aiProviderService['formatOllamaModelName']
        
        expect(formatName('llama2', '7b')).toBe('Llama 2 (7b)')
        expect(formatName('qwen', '14b')).toBe('Qwen (14b)')
        expect(formatName('mistral', 'latest')).toBe('Mistral (latest)')
        expect(formatName('codellama')).toBe('Code Llama')
        expect(formatName('unknown-model')).toBe('unknown-model')
      })
    })

    describe('generateOllamaModelDescription', () => {
      it('应该生成正确的模型描述', () => {
        const generateDesc = aiProviderService['generateOllamaModelDescription']
        
        const llama2Model = {
          name: 'llama2:7b',
          size: **********,
          details: {
            parameter_size: '7B',
            format: 'gguf'
          }
        }
        
        const description = generateDesc(llama2Model)
        expect(description).toContain('Meta开发的大型语言模型')
        expect(description).toContain('7B参数')
        expect(description).toContain('gguf格式')
      })
    })

    describe('determineOllamaCapabilities', () => {
      it('应该正确确定模型能力', () => {
        const determineCapabilities = aiProviderService['determineOllamaCapabilities']
        
        const codeModel = {
          name: 'codellama:7b',
          size: **********,
          details: {}
        }
        
        const capabilities = determineCapabilities(codeModel)
        expect(capabilities).toContain('chat')
        expect(capabilities).toContain('completion')
        expect(capabilities).toContain('coding')
      })
    })

    describe('extractOllamaModelTags', () => {
      it('应该正确提取模型标签', () => {
        const extractTags = aiProviderService['extractOllamaModelTags']
        
        const model = {
          name: 'llama2:7b-chat',
          size: **********,
          details: {
            family: 'llama',
            parameter_size: '7B',
            quantization_level: 'Q4_0'
          }
        }
        
        const tags = extractTags(model)
        expect(tags).toContain('llama')
        expect(tags).toContain('7B')
        expect(tags).toContain('Q4_0')
        expect(tags).toContain('对话')
        expect(tags).toContain('7b-chat')
      })
    })

    describe('isOllamaModelRecommended', () => {
      it('应该正确判断推荐模型', () => {
        const isRecommended = aiProviderService['isOllamaModelRecommended']
        
        expect(isRecommended('llama2')).toBe(true)
        expect(isRecommended('qwen')).toBe(true)
        expect(isRecommended('codellama')).toBe(true)
        expect(isRecommended('unknown-model')).toBe(false)
      })
    })

    describe('isOllamaModelPopular', () => {
      it('应该正确判断热门模型', () => {
        const isPopular = aiProviderService['isOllamaModelPopular']
        
        expect(isPopular('llama2')).toBe(true)
        expect(isPopular('mistral')).toBe(true)
        expect(isPopular('phi')).toBe(true)
        expect(isPopular('unknown-model')).toBe(false)
      })
    })
  })

  describe('LM Studio模型解析方法', () => {
    describe('formatLMStudioModelName', () => {
      it('应该正确格式化模型名称', () => {
        const formatName = aiProviderService['formatLMStudioModelName']
        
        expect(formatName('models/llama-2-7b-chat.gguf')).toBe('Llama 2 (llama-2-7b-chat)')
        expect(formatName('models/qwen-14b-instruct.gguf')).toBe('Qwen (qwen-14b-instruct)')
        expect(formatName('codellama-13b.bin')).toBe('Code Llama (codellama-13b)')
        expect(formatName('unknown-model.gguf')).toBe('unknown-model')
      })
    })

    describe('generateLMStudioModelDescription', () => {
      it('应该生成正确的模型描述', () => {
        const generateDesc = aiProviderService['generateLMStudioModelDescription']
        
        const llama2Model = {
          id: 'models/llama-2-7b-chat.gguf',
          object: 'model',
          created: **********,
          owned_by: 'local'
        }
        
        const description = generateDesc(llama2Model)
        expect(description).toContain('Meta开发的大型语言模型')
        expect(description).toContain('LM Studio本地部署')
      })
    })

    describe('determineLMStudioCapabilities', () => {
      it('应该正确确定模型能力', () => {
        const determineCapabilities = aiProviderService['determineLMStudioCapabilities']
        
        const codeModel = {
          id: 'models/codellama-7b.gguf',
          object: 'model',
          created: **********,
          owned_by: 'local'
        }
        
        const capabilities = determineCapabilities(codeModel)
        expect(capabilities).toContain('chat')
        expect(capabilities).toContain('completion')
        expect(capabilities).toContain('coding')
      })
    })

    describe('extractLMStudioModelTags', () => {
      it('应该正确提取模型标签', () => {
        const extractTags = aiProviderService['extractLMStudioModelTags']
        
        const model = {
          id: 'models/llama-2-7b-chat-q4.gguf',
          object: 'model',
          created: **********,
          owned_by: 'local'
        }
        
        const tags = extractTags(model)
        expect(tags).toContain('LM Studio')
        expect(tags).toContain('对话')
        expect(tags).toContain('Q4量化')
        expect(tags).toContain('7B参数')
      })
    })

    describe('isLMStudioModelRecommended', () => {
      it('应该正确判断推荐模型', () => {
        const isRecommended = aiProviderService['isLMStudioModelRecommended']
        
        expect(isRecommended('models/llama-2-7b.gguf')).toBe(true)
        expect(isRecommended('models/qwen-14b.gguf')).toBe(true)
        expect(isRecommended('models/codellama-13b.gguf')).toBe(true)
        expect(isRecommended('models/unknown-model.gguf')).toBe(false)
      })
    })

    describe('isLMStudioModelPopular', () => {
      it('应该正确判断热门模型', () => {
        const isPopular = aiProviderService['isLMStudioModelPopular']
        
        expect(isPopular('models/llama-2-chat.gguf')).toBe(true)
        expect(isPopular('models/mistral-instruct.gguf')).toBe(true)
        expect(isPopular('models/phi-3.gguf')).toBe(true)
        expect(isPopular('models/unknown-model.gguf')).toBe(false)
      })
    })
  })

  describe('Xinference模型解析方法', () => {
    describe('formatXinferenceModelName', () => {
      it('应该正确格式化模型名称', () => {
        const formatName = aiProviderService['formatXinferenceModelName']
        
        const llama2Model = {
          model_name: 'llama-2-7b-chat',
          model_uid: 'uid-1',
          model_type: 'LLM',
          model_size_in_billions: 7,
          quantization: 'q4_0'
        }
        
        expect(formatName(llama2Model)).toBe('Llama 2 7B (q4_0)')
        
        const qwenModel = {
          model_name: 'qwen-14b',
          model_uid: 'uid-2',
          model_type: 'LLM',
          model_size_in_billions: 14
        }
        
        expect(formatName(qwenModel)).toBe('Qwen 14B')
      })
    })

    describe('generateXinferenceModelDescription', () => {
      it('应该生成正确的模型描述', () => {
        const generateDesc = aiProviderService['generateXinferenceModelDescription']
        
        const llama2Model = {
          model_name: 'llama-2-7b-chat',
          model_uid: 'uid-1',
          model_type: 'LLM',
          model_description: 'Llama 2 7B Chat model'
        }
        
        const description = generateDesc(llama2Model)
        expect(description).toContain('Meta开发的大型语言模型')
        expect(description).toContain('Xinference分布式部署')
      })
    })

    describe('determineXinferenceCapabilities', () => {
      it('应该正确确定模型能力', () => {
        const determineCapabilities = aiProviderService['determineXinferenceCapabilities']
        
        const llmModel = {
          model_name: 'llama-2-7b-chat',
          model_uid: 'uid-1',
          model_type: 'LLM',
          model_ability: ['chat', 'generate']
        }
        
        const capabilities = determineCapabilities(llmModel)
        expect(capabilities).toContain('chat')
        expect(capabilities).toContain('completion')
        expect(capabilities).toContain('instruction-following')
      })
    })

    describe('extractXinferenceModelTags', () => {
      it('应该正确提取模型标签', () => {
        const extractTags = aiProviderService['extractXinferenceModelTags']
        
        const model = {
          model_name: 'llama-2-7b-chat',
          model_uid: 'uid-1',
          model_type: 'LLM',
          model_size_in_billions: 7,
          quantization: 'q4_0',
          model_lang: ['en', 'zh']
        }
        
        const tags = extractTags(model)
        expect(tags).toContain('Xinference')
        expect(tags).toContain('LLM')
        expect(tags).toContain('英文')
        expect(tags).toContain('中文')
        expect(tags).toContain('q4_0')
        expect(tags).toContain('7B参数')
        expect(tags).toContain('对话')
      })
    })

    describe('isXinferenceModelRecommended', () => {
      it('应该正确判断推荐模型', () => {
        const isRecommended = aiProviderService['isXinferenceModelRecommended']
        
        expect(isRecommended('llama-2-7b')).toBe(true)
        expect(isRecommended('qwen-14b')).toBe(true)
        expect(isRecommended('chatglm3-6b')).toBe(true)
        expect(isRecommended('unknown-model')).toBe(false)
      })
    })

    describe('isXinferenceModelPopular', () => {
      it('应该正确判断热门模型', () => {
        const isPopular = aiProviderService['isXinferenceModelPopular']
        
        expect(isPopular('llama-2-chat')).toBe(true)
        expect(isPopular('qwen-instruct')).toBe(true)
        expect(isPopular('chatglm3-6b')).toBe(true)
        expect(isPopular('unknown-model')).toBe(false)
      })
    })
  })

  describe('本地服务发现和适配器方法', () => {
    describe('discoverLocalServices', () => {
      it('应该发现本地AI服务', async () => {
        // Mock本地服务适配器的发现方法
        const mockServices = [
          {
            name: 'Ollama',
            baseUrl: 'http://localhost:11434',
            port: 11434,
            protocol: 'http' as const,
            healthCheckPath: '/api/version',
            modelsPath: '/api/tags',
            timeout: 10000
          }
        ]

        // 由于我们无法直接mock localAIServiceAdapter，我们测试方法是否被正确调用
        const services = await aiProviderService.discoverLocalServices([8888])
        
        expect(services).toBeInstanceOf(Array)
      })
    })

    describe('getDefaultLocalServices', () => {
      it('应该返回默认本地服务配置', () => {
        const services = aiProviderService.getDefaultLocalServices()
        
        expect(services).toBeInstanceOf(Array)
        expect(services.length).toBeGreaterThan(0)
        
        const serviceNames = services.map(s => s.name)
        expect(serviceNames).toContain('Ollama')
        expect(serviceNames).toContain('LM Studio')
        expect(serviceNames).toContain('Xinference')
      })
    })

    describe('createCustomLocalService', () => {
      it('应该创建自定义本地服务配置', () => {
        const config = aiProviderService.createCustomLocalService(
          'My Service',
          'http://localhost:9000',
          { timeout: 15000 }
        )
        
        expect(config.name).toBe('My Service')
        expect(config.baseUrl).toBe('http://localhost:9000')
        expect(config.port).toBe(9000)
        expect(config.timeout).toBe(15000)
      })
    })
  })

  describe('Together AI集成测试', () => {
    describe('testTogetherConnection', () => {
      it('应该成功测试Together AI连接', async () => {
        const mockResponse = {
          ok: true,
          json: () => Promise.resolve({
            data: [
              {
                id: 'meta-llama/Llama-2-7b-chat-hf',
                object: 'model',
                created: **********,
                owned_by: 'meta-llama',
                display_name: 'Llama 2 7B Chat',
                description: 'Llama 2 7B Chat model',
                context_length: 4096,
                pricing: {
                  input: 0.0002,
                  output: 0.0002
                }
              },
              {
                id: 'mistralai/Mistral-7B-Instruct-v0.1',
                object: 'model',
                created: **********,
                owned_by: 'mistralai',
                display_name: 'Mistral 7B Instruct',
                description: 'Mistral 7B Instruct model',
                context_length: 8192,
                pricing: {
                  input: 0.0002,
                  output: 0.0002
                }
              }
            ]
          })
        }

        vi.mocked(fetch).mockResolvedValue(mockResponse as any)

        const result = await aiProviderService['testTogetherConnection']('https://api.together.xyz/v1', 'test-api-key-********************')

        expect(result.success).toBe(true)
        expect(result.modelCount).toBe(2)
        expect(fetch).toHaveBeenCalledWith(
          'https://api.together.xyz/v1/models',
          expect.objectContaining({
            method: 'GET',
            headers: expect.objectContaining({
              'Authorization': 'Bearer test-api-key-********************'
            })
          })
        )
      })

      it('应该处理Together AI API密钥验证错误', async () => {
        const result = await aiProviderService['testTogetherConnection']('https://api.together.xyz/v1', 'short')

        expect(result.success).toBe(false)
        expect(result.error).toContain('API密钥长度不足')
      })

      it('应该处理Together AI 401错误', async () => {
        const mockResponse = {
          ok: false,
          status: 401,
          statusText: 'Unauthorized'
        }

        vi.mocked(fetch).mockResolvedValue(mockResponse as any)

        const result = await aiProviderService['testTogetherConnection']('https://api.together.xyz/v1', 'test-api-key-********************')

        expect(result.success).toBe(false)
        expect(result.error).toContain('API密钥无效或已过期')
      })

      it('应该处理Together AI连接超时', async () => {
        vi.mocked(fetch).mockRejectedValue(new DOMException('The operation was aborted.', 'AbortError'))

        const result = await aiProviderService['testTogetherConnection']('https://api.together.xyz/v1', 'test-api-key-********************')

        expect(result.success).toBe(false)
        expect(result.error).toContain('连接超时')
      })
    })

    describe('getTogetherModels', () => {
      it('应该成功获取Together AI模型列表', async () => {
        const mockResponse = {
          ok: true,
          json: () => Promise.resolve({
            data: [
              {
                id: 'meta-llama/Llama-2-7b-chat-hf',
                object: 'model',
                created: **********,
                owned_by: 'meta-llama',
                display_name: 'Llama 2 7B Chat',
                description: 'Llama 2 7B Chat model for conversational AI',
                context_length: 4096,
                pricing: {
                  input: 0.0002,
                  output: 0.0002
                },
                type: 'chat',
                license: 'custom'
              },
              {
                id: 'codellama/CodeLlama-7b-Python-hf',
                object: 'model',
                created: **********,
                owned_by: 'codellama',
                display_name: 'Code Llama 7B Python',
                description: 'Code Llama 7B specialized for Python',
                context_length: 16384,
                pricing: {
                  input: 0.0002,
                  output: 0.0002
                },
                type: 'code',
                license: 'custom'
              }
            ]
          })
        }

        vi.mocked(fetch).mockResolvedValue(mockResponse as any)

        const models = await aiProviderService['getTogetherModels']('https://api.together.xyz/v1', 'test-api-key-********************')

        expect(models).toHaveLength(2)
        
        const llamaModel = models.find(m => m.id === 'meta-llama/Llama-2-7b-chat-hf')
        expect(llamaModel).toBeDefined()
        expect(llamaModel?.displayName).toBe('Llama 2 7B Chat')
        expect(llamaModel?.description).toContain('Llama 2')
        expect(llamaModel?.size).toBe('7B')
        expect(llamaModel?.providerId).toBe('together')
        expect(llamaModel?.capabilities).toContain('chat')
        expect(llamaModel?.capabilities).toContain('instruction-following')
        expect(llamaModel?.tags).toContain('Together AI')
        expect(llamaModel?.tags).toContain('开源')
        expect(llamaModel?.tags).toContain('meta-llama')
        expect(llamaModel?.isRecommended).toBe(false) // 7B不在推荐列表中
        expect(llamaModel?.isPopular).toBe(true) // 7B在热门列表中

        const codeModel = models.find(m => m.id === 'codellama/CodeLlama-7b-Python-hf')
        expect(codeModel).toBeDefined()
        expect(codeModel?.displayName).toBe('Code Llama 7B Python')
        expect(codeModel?.capabilities).toContain('coding')
        expect(codeModel?.tags).toContain('代码生成')
        expect(codeModel?.isRecommended).toBe(true) // codellama在推荐列表中
      })

      it('应该处理空的模型列表', async () => {
        const mockResponse = {
          ok: true,
          json: () => Promise.resolve({ data: [] })
        }

        vi.mocked(fetch).mockResolvedValue(mockResponse as any)

        const models = await aiProviderService['getTogetherModels']('https://api.together.xyz/v1', 'test-api-key-********************')

        expect(models).toHaveLength(0)
      })

      it('应该处理API错误', async () => {
        const mockResponse = {
          ok: false,
          status: 500,
          statusText: 'Internal Server Error'
        }

        vi.mocked(fetch).mockResolvedValue(mockResponse as any)

        const models = await aiProviderService['getTogetherModels']('https://api.together.xyz/v1', 'test-api-key-********************')

        expect(models).toHaveLength(0)
      })
    })

    describe('Together AI模型解析方法', () => {
      describe('formatTogetherModelName', () => {
        it('应该正确格式化模型名称', () => {
          const formatName = aiProviderService['formatTogetherModelName']
          
          const modelWithDisplayName = {
            id: 'meta-llama/Llama-2-7b-chat-hf',
            object: 'model',
            created: **********,
            owned_by: 'meta-llama',
            display_name: 'Llama 2 7B Chat'
          }
          
          expect(formatName(modelWithDisplayName)).toBe('Llama 2 7B Chat')
          
          const modelWithoutDisplayName = {
            id: 'mistralai/Mistral-7B-Instruct-v0.1',
            object: 'model',
            created: **********,
            owned_by: 'mistralai'
          }
          
          expect(formatName(modelWithoutDisplayName)).toBe('Mistral 0.1')
        })
      })

      describe('generateTogetherModelDescription', () => {
        it('应该生成正确的模型描述', () => {
          const generateDesc = aiProviderService['generateTogetherModelDescription']
          
          const llama2Model = {
            id: 'meta-llama/Llama-2-7b-chat-hf',
            object: 'model',
            created: **********,
            owned_by: 'meta-llama',
            description: 'Llama 2 7B Chat model',
            context_length: 4096,
            license: 'custom'
          }
          
          const description = generateDesc(llama2Model)
          expect(description).toContain('Llama 2 7B Chat model')
          expect(description).toContain('开源许可: custom')
          expect(description).toContain('支持4,096个token的上下文')
        })
      })

      describe('determineTogetherCapabilities', () => {
        it('应该正确确定模型能力', () => {
          const determineCapabilities = aiProviderService['determineTogetherCapabilities']
          
          const codeModel = {
            id: 'codellama/CodeLlama-7b-Python-hf',
            object: 'model',
            created: **********,
            owned_by: 'codellama',
            context_length: 16384
          }
          
          const capabilities = determineCapabilities(codeModel)
          expect(capabilities).toContain('chat')
          expect(capabilities).toContain('completion')
          expect(capabilities).toContain('coding')
        })
      })

      describe('extractTogetherModelTags', () => {
        it('应该正确提取模型标签', () => {
          const extractTags = aiProviderService['extractTogetherModelTags']
          
          const model = {
            id: 'meta-llama/Llama-2-7b-chat-hf',
            object: 'model',
            created: **********,
            owned_by: 'meta-llama',
            type: 'chat',
            license: 'custom',
            context_length: 4096
          }
          
          const tags = extractTags(model)
          expect(tags).toContain('Together AI')
          expect(tags).toContain('开源')
          expect(tags).toContain('meta-llama')
          expect(tags).toContain('chat')
          expect(tags).toContain('custom')
          expect(tags).toContain('对话优化')
        })
      })

      describe('formatTogetherPricing', () => {
        it('应该正确格式化定价信息', () => {
          const formatPricing = aiProviderService['formatTogetherPricing']
          
          const modelWithPricing = {
            id: 'test-model',
            object: 'model',
            created: **********,
            owned_by: 'test',
            pricing: {
              input: 0.0002,
              output: 0.0003
            }
          }
          
          const pricing = formatPricing(modelWithPricing)
          expect(pricing).toBe('输入: $200.000/1M tokens, 输出: $300.000/1M tokens')
          
          const modelWithoutPricing = {
            id: 'test-model',
            object: 'model',
            created: **********,
            owned_by: 'test'
          }
          
          expect(formatPricing(modelWithoutPricing)).toBeNull()
        })
      })

      describe('extractModelSizeFromId', () => {
        it('应该从模型ID中提取大小', () => {
          const extractSize = aiProviderService['extractModelSizeFromId']
          
          expect(extractSize('meta-llama/Llama-2-7b-chat-hf')).toBe('7B')
          expect(extractSize('mistralai/Mistral-7B-Instruct-v0.1')).toBe('7B')
          expect(extractSize('togethercomputer/RedPajama-INCITE-7B-Chat')).toBe('7B')
          expect(extractSize('model-without-size')).toBeUndefined()
        })
      })

      describe('isTogetherModelRecommended', () => {
        it('应该正确判断推荐模型', () => {
          const isRecommended = aiProviderService['isTogetherModelRecommended']
          
          const llama3Model = {
            id: 'meta-llama/Llama-3-8b-chat-hf',
            object: 'model',
            created: **********,
            owned_by: 'meta-llama'
          }
          expect(isRecommended(llama3Model)).toBe(true)
          
          const codeModel = {
            id: 'codellama/CodeLlama-7b-Python-hf',
            object: 'model',
            created: **********,
            owned_by: 'codellama'
          }
          expect(isRecommended(codeModel)).toBe(true)
          
          const unknownModel = {
            id: 'unknown/model',
            object: 'model',
            created: **********,
            owned_by: 'unknown'
          }
          expect(isRecommended(unknownModel)).toBe(false)
        })
      })

      describe('isTogetherModelPopular', () => {
        it('应该正确判断热门模型', () => {
          const isPopular = aiProviderService['isTogetherModelPopular']
          
          const llama2_7bModel = {
            id: 'meta-llama/Llama-2-7b-chat-hf',
            object: 'model',
            created: **********,
            owned_by: 'meta-llama'
          }
          expect(isPopular(llama2_7bModel)).toBe(true)
          
          const mistral7bModel = {
            id: 'mistralai/Mistral-7B-Instruct-v0.1',
            object: 'model',
            created: **********,
            owned_by: 'mistralai'
          }
          expect(isPopular(mistral7bModel)).toBe(true)
          
          const unknownModel = {
            id: 'unknown/model',
            object: 'model',
            created: **********,
            owned_by: 'unknown'
          }
          expect(isPopular(unknownModel)).toBe(false)
        })
      })
    })
  })
})