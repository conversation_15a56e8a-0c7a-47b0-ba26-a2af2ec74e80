// 完整的收藏功能修复测试脚本
// 在浏览器控制台中运行此脚本来全面测试收藏功能

console.log('🧪 开始完整的收藏功能测试...')

// 测试数据
const testData = {
  title: document.title || '测试页面',
  url: window.location.href,
  favIconUrl: document.querySelector('link[rel="icon"]')?.href || 
              document.querySelector('link[rel="shortcut icon"]')?.href || 
              '/favicon.ico',
  timestamp: new Date().toISOString()
}

console.log('测试数据:', testData)

// 测试步骤计数器
let testStep = 1

// 延迟函数
const delay = (ms) => new Promise(resolve => setTimeout(resolve, ms))

// 测试1: 快速收藏功能
async function testQuickBookmark() {
  console.log(`\n${testStep++}. 测试快速收藏功能...`)
  
  try {
    const response = await chrome.runtime.sendMessage({
      type: 'QUICK_BOOKMARK',
      data: testData
    })
    
    console.log('快速收藏响应:', response)
    
    if (response && response.success) {
      console.log('✅ 快速收藏成功，ID:', response.data.bookmarkId)
      return response.data.bookmarkId
    } else {
      console.log('❌ 快速收藏失败:', response?.error)
      return null
    }
  } catch (error) {
    console.error('❌ 快速收藏异常:', error)
    return null
  }
}

// 测试2: 检查收藏状态
async function testBookmarkStatus() {
  console.log(`\n${testStep++}. 测试收藏状态检查...`)
  
  try {
    const response = await chrome.runtime.sendMessage({
      type: 'CHECK_BOOKMARK_STATUS',
      data: { url: testData.url }
    })
    
    console.log('状态检查响应:', response)
    
    if (response && response.success) {
      console.log('✅ 状态检查成功:', response.data)
      
      if (response.data.isBookmarked) {
        console.log('🎉 页面已正确标记为已收藏')
        return response.data.bookmarkId
      } else {
        console.log('⚠️ 页面未被标记为已收藏')
        return null
      }
    } else {
      console.log('❌ 状态检查失败:', response?.error)
      return null
    }
  } catch (error) {
    console.error('❌ 状态检查异常:', error)
    return null
  }
}

// 测试3: 获取收藏列表
async function testGetBookmarks() {
  console.log(`\n${testStep++}. 测试获取收藏列表...`)
  
  try {
    const response = await chrome.runtime.sendMessage({
      type: 'GET_BOOKMARKS',
      data: {}
    })
    
    console.log('获取收藏列表响应:', response)
    
    if (response && response.success) {
      console.log('✅ 获取收藏列表成功，数量:', response.data.length)
      
      // 查找我们刚才添加的收藏
      const ourBookmark = response.data.find(b => b.url === testData.url)
      if (ourBookmark) {
        console.log('🎉 找到了我们添加的收藏:', ourBookmark)
        return ourBookmark
      } else {
        console.log('⚠️ 在收藏列表中没有找到我们添加的收藏')
        return null
      }
    } else {
      console.log('❌ 获取收藏列表失败:', response?.error)
      return null
    }
  } catch (error) {
    console.error('❌ 获取收藏列表异常:', error)
    return null
  }
}

// 测试4: 直接访问IndexedDB
async function testDirectDBAccess() {
  console.log(`\n${testStep++}. 测试直接访问IndexedDB...`)
  
  return new Promise((resolve) => {
    try {
      const request = indexedDB.open('UniverseBagDB', 1)
      
      request.onsuccess = (event) => {
        const db = event.target.result
        const transaction = db.transaction(['bookmarks'], 'readonly')
        const store = transaction.objectStore('bookmarks')
        const getAllRequest = store.getAll()
        
        getAllRequest.onsuccess = () => {
          const bookmarks = getAllRequest.result
          console.log('✅ 直接数据库访问成功，收藏数量:', bookmarks.length)
          
          // 查找我们的收藏
          const ourBookmark = bookmarks.find(b => b.url === testData.url)
          if (ourBookmark) {
            console.log('🎉 在数据库中找到了我们的收藏:', ourBookmark)
            resolve(ourBookmark)
          } else {
            console.log('⚠️ 在数据库中没有找到我们的收藏')
            console.log('数据库中的所有收藏:', bookmarks.map(b => ({ id: b.id, title: b.title, url: b.url })))
            resolve(null)
          }
        }
        
        getAllRequest.onerror = () => {
          console.error('❌ 数据库查询失败:', getAllRequest.error)
          resolve(null)
        }
      }
      
      request.onerror = () => {
        console.error('❌ 打开数据库失败:', request.error)
        resolve(null)
      }
    } catch (error) {
      console.error('❌ 数据库访问异常:', error)
      resolve(null)
    }
  })
}

// 测试5: 检查图标状态
async function testIconStatus() {
  console.log(`\n${testStep++}. 测试图标状态...`)
  
  try {
    // 获取当前标签页信息
    const tabs = await chrome.tabs.query({ active: true, currentWindow: true })
    if (tabs.length > 0 && tabs[0].id) {
      const tabId = tabs[0].id
      
      // 检查徽章文本
      const badgeText = await chrome.action.getBadgeText({ tabId })
      console.log('当前徽章文本:', badgeText)
      
      if (badgeText === '✓') {
        console.log('✅ 图标状态正确显示为已收藏')
        return true
      } else {
        console.log('⚠️ 图标状态未正确显示')
        return false
      }
    } else {
      console.log('⚠️ 无法获取当前标签页信息')
      return false
    }
  } catch (error) {
    console.error('❌ 检查图标状态异常:', error)
    return false
  }
}

// 运行所有测试
async function runCompleteTest() {
  console.log('🚀 开始运行完整测试套件...')
  
  const results = {
    quickBookmark: null,
    statusCheck: null,
    bookmarkList: null,
    directDB: null,
    iconStatus: null
  }
  
  try {
    // 测试1: 快速收藏
    results.quickBookmark = await testQuickBookmark()
    await delay(1000)
    
    // 测试2: 状态检查
    results.statusCheck = await testBookmarkStatus()
    await delay(1000)
    
    // 测试3: 获取收藏列表
    results.bookmarkList = await testGetBookmarks()
    await delay(1000)
    
    // 测试4: 直接数据库访问
    results.directDB = await testDirectDBAccess()
    await delay(1000)
    
    // 测试5: 图标状态
    results.iconStatus = await testIconStatus()
    
    // 汇总结果
    console.log('\n📊 测试结果汇总:')
    console.log('==================')
    console.log('快速收藏:', results.quickBookmark ? '✅ 成功' : '❌ 失败')
    console.log('状态检查:', results.statusCheck ? '✅ 成功' : '❌ 失败')
    console.log('收藏列表:', results.bookmarkList ? '✅ 成功' : '❌ 失败')
    console.log('数据库访问:', results.directDB ? '✅ 成功' : '❌ 失败')
    console.log('图标状态:', results.iconStatus ? '✅ 成功' : '❌ 失败')
    
    const successCount = Object.values(results).filter(Boolean).length
    const totalCount = Object.keys(results).length
    
    console.log(`\n🎯 总体结果: ${successCount}/${totalCount} 项测试通过`)
    
    if (successCount === totalCount) {
      console.log('🎉 所有测试都通过了！收藏功能工作正常。')
    } else {
      console.log('⚠️ 部分测试失败，需要进一步调试。')
    }
    
  } catch (error) {
    console.error('❌ 测试过程中发生异常:', error)
  }
}

// 检查环境并运行测试
if (typeof chrome !== 'undefined' && chrome.runtime) {
  runCompleteTest()
} else {
  console.log('⚠️ 请在Chrome扩展环境中运行此测试')
  console.log('1. 构建扩展: npm run build')
  console.log('2. 在Chrome中加载扩展')
  console.log('3. 在任意网页的控制台中运行此脚本')
}