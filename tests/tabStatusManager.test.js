// 标签页状态管理器单元测试

import { jest } from '@jest/globals'

// Mock Chrome APIs
global.chrome = {
  tabs: {
    get: jest.fn(),
    query: jest.fn(),
    onActivated: {
      addListener: jest.fn()
    },
    onUpdated: {
      addListener: jest.fn()
    }
  },
  windows: {
    onFocusChanged: {
      addListener: jest.fn()
    },
    WINDOW_ID_NONE: -1
  },
  action: {
    setBadgeText: jest.fn(),
    setBadgeBackgroundColor: jest.fn(),
    setBadgeTextColor: jest.fn(),
    setTitle: jest.fn()
  }
}

// Mock bookmarkStatusService
const mockBookmarkStatusService = {
  checkBookmarkStatus: jest.fn(),
  updateStatusCache: jest.fn()
}

jest.mock('../src/services/bookmarkStatusService', () => ({
  bookmarkStatusService: mockBookmarkStatusService
}))

describe('TabStatusManager', () => {
  let tabStatusManager

  beforeEach(async () => {
    // 重置所有mock
    jest.clearAllMocks()
    
    // 动态导入模块
    const module = await import('../src/services/tabStatusManager.js')
    tabStatusManager = module.tabStatusManager
  })

  describe('onTabActivated', () => {
    test('应该正确处理标签页激活事件', async () => {
      // 准备测试数据
      const tabId = 123
      const mockTab = {
        id: tabId,
        url: 'https://example.com',
        title: '测试页面'
      }

      // 设置mock返回值
      chrome.tabs.get.mockResolvedValue(mockTab)
      mockBookmarkStatusService.checkBookmarkStatus.mockResolvedValue({
        isBookmarked: true,
        bookmarkId: 'bookmark-123'
      })

      // 执行测试
      await tabStatusManager.onTabActivated(tabId)

      // 验证结果
      expect(chrome.tabs.get).toHaveBeenCalledWith(tabId)
      expect(mockBookmarkStatusService.checkBookmarkStatus).toHaveBeenCalledWith(mockTab.url, true)
      expect(chrome.action.setBadgeText).toHaveBeenCalledWith({
        tabId: tabId,
        text: '✓'
      })
      expect(chrome.action.setBadgeBackgroundColor).toHaveBeenCalledWith({
        tabId: tabId,
        color: '#10b981'
      })
    })

    test('应该处理没有URL的标签页', async () => {
      // 准备测试数据
      const tabId = 123
      const mockTab = {
        id: tabId,
        url: null
      }

      // 设置mock返回值
      chrome.tabs.get.mockResolvedValue(mockTab)

      // 执行测试
      await tabStatusManager.onTabActivated(tabId)

      // 验证结果
      expect(chrome.tabs.get).toHaveBeenCalledWith(tabId)
      expect(mockBookmarkStatusService.checkBookmarkStatus).not.toHaveBeenCalled()
    })

    test('应该处理Chrome API错误', async () => {
      // 准备测试数据
      const tabId = 123
      const error = new Error('Tab not found')

      // 设置mock抛出错误
      chrome.tabs.get.mockRejectedValue(error)

      // 执行测试（不应该抛出错误）
      await expect(tabStatusManager.onTabActivated(tabId)).resolves.toBeUndefined()

      // 验证结果
      expect(chrome.tabs.get).toHaveBeenCalledWith(tabId)
      expect(mockBookmarkStatusService.checkBookmarkStatus).not.toHaveBeenCalled()
    })
  })

  describe('onTabUpdated', () => {
    test('应该只在页面加载完成时处理更新', async () => {
      // 准备测试数据
      const tabId = 123
      const changeInfo = { status: 'loading' }
      const mockTab = {
        id: tabId,
        url: 'https://example.com'
      }

      // 执行测试
      await tabStatusManager.onTabUpdated(tabId, changeInfo)

      // 验证结果 - 不应该调用任何API
      expect(chrome.tabs.get).not.toHaveBeenCalled()
      expect(mockBookmarkStatusService.checkBookmarkStatus).not.toHaveBeenCalled()
    })

    test('应该在页面加载完成时更新状态', async () => {
      // 准备测试数据
      const tabId = 123
      const changeInfo = { status: 'complete' }
      const mockTab = {
        id: tabId,
        url: 'https://example.com'
      }

      // 设置mock返回值
      chrome.tabs.get.mockResolvedValue(mockTab)
      mockBookmarkStatusService.checkBookmarkStatus.mockResolvedValue({
        isBookmarked: false
      })

      // 执行测试
      await tabStatusManager.onTabUpdated(tabId, changeInfo)

      // 等待防抖延迟
      await new Promise(resolve => setTimeout(resolve, 350))

      // 验证结果
      expect(chrome.tabs.get).toHaveBeenCalledWith(tabId)
      expect(mockBookmarkStatusService.checkBookmarkStatus).toHaveBeenCalledWith(mockTab.url, true)
      expect(chrome.action.setBadgeText).toHaveBeenCalledWith({
        tabId: tabId,
        text: ''
      })
    })
  })

  describe('checkAndUpdateIconStatus', () => {
    test('应该正确检测并更新已收藏页面的图标', async () => {
      // 准备测试数据
      const tabId = 123
      const url = 'https://example.com'

      // 设置mock返回值
      mockBookmarkStatusService.checkBookmarkStatus.mockResolvedValue({
        isBookmarked: true,
        bookmarkId: 'bookmark-123'
      })

      // 执行测试
      await tabStatusManager.checkAndUpdateIconStatus(tabId, url)

      // 验证结果
      expect(mockBookmarkStatusService.checkBookmarkStatus).toHaveBeenCalledWith(url, true)
      expect(chrome.action.setBadgeText).toHaveBeenCalledWith({
        tabId: tabId,
        text: '✓'
      })
      expect(chrome.action.setBadgeBackgroundColor).toHaveBeenCalledWith({
        tabId: tabId,
        color: '#10b981'
      })
      expect(chrome.action.setTitle).toHaveBeenCalledWith({
        tabId: tabId,
        title: 'Universe Bag - 当前页面已收藏 ✓'
      })
      expect(mockBookmarkStatusService.updateStatusCache).toHaveBeenCalledWith(
        url, 
        true, 
        'bookmark-123'
      )
    })

    test('应该正确检测并更新未收藏页面的图标', async () => {
      // 准备测试数据
      const tabId = 123
      const url = 'https://example.com'

      // 设置mock返回值
      mockBookmarkStatusService.checkBookmarkStatus.mockResolvedValue({
        isBookmarked: false
      })

      // 执行测试
      await tabStatusManager.checkAndUpdateIconStatus(tabId, url)

      // 验证结果
      expect(mockBookmarkStatusService.checkBookmarkStatus).toHaveBeenCalledWith(url, true)
      expect(chrome.action.setBadgeText).toHaveBeenCalledWith({
        tabId: tabId,
        text: ''
      })
      expect(chrome.action.setTitle).toHaveBeenCalledWith({
        tabId: tabId,
        title: 'Universe Bag - 智能收藏助手'
      })
      expect(mockBookmarkStatusService.updateStatusCache).toHaveBeenCalledWith(
        url, 
        false, 
        undefined
      )
    })

    test('应该处理检测错误并清除图标状态', async () => {
      // 准备测试数据
      const tabId = 123
      const url = 'https://example.com'
      const error = new Error('检测失败')

      // 设置mock抛出错误
      mockBookmarkStatusService.checkBookmarkStatus.mockRejectedValue(error)

      // 执行测试
      await tabStatusManager.checkAndUpdateIconStatus(tabId, url)

      // 验证结果
      expect(mockBookmarkStatusService.checkBookmarkStatus).toHaveBeenCalledWith(url, true)
      expect(chrome.action.setBadgeText).toHaveBeenCalledWith({
        tabId: tabId,
        text: ''
      })
    })
  })

  describe('handleBookmarkStatusChange', () => {
    test('应该更新所有匹配标签页的状态', async () => {
      // 准备测试数据
      const url = 'https://example.com'
      const isBookmarked = true
      const mockTabs = [
        { id: 123, url },
        { id: 456, url },
        { id: 789, url: 'https://other.com' } // 不匹配的标签页
      ]

      // 设置mock返回值
      chrome.tabs.query.mockResolvedValue(mockTabs.slice(0, 2)) // 只返回匹配的标签页

      // 执行测试
      await tabStatusManager.handleBookmarkStatusChange(url, isBookmarked)

      // 验证结果
      expect(chrome.tabs.query).toHaveBeenCalledWith({ url })
      expect(chrome.action.setBadgeText).toHaveBeenCalledTimes(2)
      expect(chrome.action.setBadgeText).toHaveBeenCalledWith({
        tabId: 123,
        text: '✓'
      })
      expect(chrome.action.setBadgeText).toHaveBeenCalledWith({
        tabId: 456,
        text: '✓'
      })
    })
  })

  describe('updateAllTabsStatus', () => {
    test('应该更新所有有效标签页的状态', async () => {
      // 准备测试数据
      const mockTabs = [
        { id: 123, url: 'https://example.com' },
        { id: 456, url: 'https://test.com' },
        { id: 789, url: 'chrome://settings' }, // 应该被过滤掉
        { id: null, url: 'https://invalid.com' } // 应该被过滤掉
      ]

      // 设置mock返回值
      chrome.tabs.query.mockResolvedValue(mockTabs)
      mockBookmarkStatusService.checkBookmarkStatus.mockResolvedValue({
        isBookmarked: false
      })

      // 执行测试
      await tabStatusManager.updateAllTabsStatus()

      // 验证结果
      expect(chrome.tabs.query).toHaveBeenCalledWith({})
      expect(mockBookmarkStatusService.checkBookmarkStatus).toHaveBeenCalledTimes(2)
      expect(mockBookmarkStatusService.checkBookmarkStatus).toHaveBeenCalledWith('https://example.com', true)
      expect(mockBookmarkStatusService.checkBookmarkStatus).toHaveBeenCalledWith('https://test.com', true)
    })
  })

  describe('getStatus', () => {
    test('应该返回管理器状态信息', () => {
      // 执行测试
      const status = tabStatusManager.getStatus()

      // 验证结果
      expect(status).toHaveProperty('pendingUpdates')
      expect(status).toHaveProperty('debounceDelay')
      expect(typeof status.pendingUpdates).toBe('number')
      expect(typeof status.debounceDelay).toBe('number')
    })
  })

  describe('cleanup', () => {
    test('应该清理所有资源', () => {
      // 执行测试
      tabStatusManager.cleanup()

      // 验证结果 - 主要是确保不抛出错误
      expect(() => tabStatusManager.cleanup()).not.toThrow()
    })
  })
})