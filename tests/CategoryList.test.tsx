// CategoryList组件测试

import React from 'react'
import { render, screen, fireEvent } from '@testing-library/react'
import { vi, describe, it, expect, beforeEach } from 'vitest'
import CategoryList from '../src/components/CategoryList'
import type { CategoryWithStats } from '../src/components/CategoryList'

// Mock CategoryCard组件
vi.mock('../src/components/CategoryCard', () => ({
  default: ({ category, bookmarkCount, onEdit, onDelete, onClick }: any) => (
    <div data-testid={`category-card-${category.id}`}>
      <div>{category.name}</div>
      <div>书签数量: {bookmarkCount}</div>
      <button onClick={onEdit}>编辑</button>
      <button onClick={onDelete}>删除</button>
      {onClick && <button onClick={onClick}>点击</button>}
    </div>
  )
}))

describe('CategoryList', () => {
  const mockCategories: CategoryWithStats[] = [
    {
      id: 'category-1',
      name: '技术',
      description: '技术相关内容',
      color: '#3B82F6',
      createdAt: new Date('2024-01-01'),
      updatedAt: new Date('2024-01-01'),
      bookmarkCount: 15
    },
    {
      id: 'category-2',
      name: '学习',
      description: '学习资料',
      color: '#10B981',
      createdAt: new Date('2024-01-02'),
      updatedAt: new Date('2024-01-02'),
      bookmarkCount: 8
    },
    {
      id: 'category-3',
      name: '工具',
      description: '实用工具',
      color: '#F59E0B',
      createdAt: new Date('2024-01-03'),
      updatedAt: new Date('2024-01-03'),
      bookmarkCount: 0
    }
  ]

  const defaultProps = {
    categories: mockCategories,
    onCategoryEdit: vi.fn(),
    onCategoryDelete: vi.fn(),
    loading: false
  }

  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('渲染测试', () => {
    it('应该渲染分类列表', () => {
      render(<CategoryList {...defaultProps} />)

      expect(screen.getByTestId('category-card-category-1')).toBeInTheDocument()
      expect(screen.getByTestId('category-card-category-2')).toBeInTheDocument()
      expect(screen.getByTestId('category-card-category-3')).toBeInTheDocument()
    })

    it('应该显示分类统计信息', () => {
      render(<CategoryList {...defaultProps} />)

      // 总分类数
      expect(screen.getByText('3')).toBeInTheDocument()
      expect(screen.getByText('总分类数')).toBeInTheDocument()

      // 活跃分类数（有书签的分类）
      expect(screen.getByText('2')).toBeInTheDocument()
      expect(screen.getByText('活跃分类')).toBeInTheDocument()

      // 空分类数
      expect(screen.getByText('1')).toBeInTheDocument()
      expect(screen.getByText('空分类')).toBeInTheDocument()

      // 总书签数
      expect(screen.getByText('23')).toBeInTheDocument()
      expect(screen.getByText('总书签数')).toBeInTheDocument()
    })

    it('应该正确传递props给CategoryCard', () => {
      render(<CategoryList {...defaultProps} />)

      expect(screen.getByText('技术')).toBeInTheDocument()
      expect(screen.getByText('书签数量: 15')).toBeInTheDocument()
      expect(screen.getByText('学习')).toBeInTheDocument()
      expect(screen.getByText('书签数量: 8')).toBeInTheDocument()
    })
  })

  describe('加载状态测试', () => {
    it('应该显示加载状态', () => {
      render(
        <CategoryList
          {...defaultProps}
          loading={true}
        />
      )

      expect(screen.getByText('加载分类数据中...')).toBeInTheDocument()
      expect(screen.queryByTestId('category-card-category-1')).not.toBeInTheDocument()
    })

    it('应该在加载时不显示统计信息', () => {
      render(
        <CategoryList
          {...defaultProps}
          loading={true}
        />
      )

      expect(screen.queryByText('总分类数')).not.toBeInTheDocument()
    })
  })

  describe('空状态测试', () => {
    it('应该显示空状态', () => {
      render(
        <CategoryList
          {...defaultProps}
          categories={[]}
        />
      )

      expect(screen.getByText('暂无分类')).toBeInTheDocument()
      expect(screen.getByText('创建您的第一个分类来更好地组织书签')).toBeInTheDocument()
    })

    it('应该在空状态时显示创建按钮', () => {
      const mockOnCreateCategory = vi.fn()
      
      render(
        <CategoryList
          {...defaultProps}
          categories={[]}
          onCreateCategory={mockOnCreateCategory}
        />
      )

      const createButton = screen.getByText('创建分类')
      expect(createButton).toBeInTheDocument()
      
      fireEvent.click(createButton)
      expect(mockOnCreateCategory).toHaveBeenCalled()
    })

    it('应该在没有onCreateCategory时不显示创建按钮', () => {
      render(
        <CategoryList
          {...defaultProps}
          categories={[]}
        />
      )

      expect(screen.queryByText('创建分类')).not.toBeInTheDocument()
    })

    it('应该在空状态时不显示统计信息', () => {
      render(
        <CategoryList
          {...defaultProps}
          categories={[]}
        />
      )

      expect(screen.queryByText('总分类数')).not.toBeInTheDocument()
    })
  })

  describe('交互测试', () => {
    it('应该处理分类编辑', () => {
      const mockOnCategoryEdit = vi.fn()
      
      render(
        <CategoryList
          {...defaultProps}
          onCategoryEdit={mockOnCategoryEdit}
        />
      )

      const editButtons = screen.getAllByText('编辑')
      fireEvent.click(editButtons[0])

      expect(mockOnCategoryEdit).toHaveBeenCalledWith(mockCategories[0])
    })

    it('应该处理分类删除', () => {
      const mockOnCategoryDelete = vi.fn()
      
      render(
        <CategoryList
          {...defaultProps}
          onCategoryDelete={mockOnCategoryDelete}
        />
      )

      const deleteButtons = screen.getAllByText('删除')
      fireEvent.click(deleteButtons[1])

      expect(mockOnCategoryDelete).toHaveBeenCalledWith(mockCategories[1])
    })

    it('应该处理分类点击', () => {
      const mockOnCategoryClick = vi.fn()
      
      render(
        <CategoryList
          {...defaultProps}
          onCategoryClick={mockOnCategoryClick}
        />
      )

      const clickButtons = screen.getAllByText('点击')
      fireEvent.click(clickButtons[0])

      expect(mockOnCategoryClick).toHaveBeenCalledWith(mockCategories[0])
    })

    it('应该在没有onCategoryClick时不显示点击按钮', () => {
      render(<CategoryList {...defaultProps} />)

      expect(screen.queryByText('点击')).not.toBeInTheDocument()
    })
  })

  describe('统计信息计算测试', () => {
    it('应该正确计算活跃分类数量', () => {
      const categoriesWithMixedCounts: CategoryWithStats[] = [
        { ...mockCategories[0], bookmarkCount: 10 },
        { ...mockCategories[1], bookmarkCount: 0 },
        { ...mockCategories[2], bookmarkCount: 5 }
      ]

      render(
        <CategoryList
          {...defaultProps}
          categories={categoriesWithMixedCounts}
        />
      )

      // 活跃分类数（bookmarkCount > 0）
      expect(screen.getByText('2')).toBeInTheDocument()
      expect(screen.getByText('活跃分类')).toBeInTheDocument()

      // 空分类数
      expect(screen.getByText('1')).toBeInTheDocument()
      expect(screen.getByText('空分类')).toBeInTheDocument()

      // 总书签数
      expect(screen.getByText('15')).toBeInTheDocument()
      expect(screen.getByText('总书签数')).toBeInTheDocument()
    })

    it('应该处理全部为空分类的情况', () => {
      const emptyCategories: CategoryWithStats[] = [
        { ...mockCategories[0], bookmarkCount: 0 },
        { ...mockCategories[1], bookmarkCount: 0 }
      ]

      render(
        <CategoryList
          {...defaultProps}
          categories={emptyCategories}
        />
      )

      // 活跃分类数应该为0
      expect(screen.getByText('活跃分类')).toBeInTheDocument()
      const activeCount = screen.getByText('活跃分类').previousElementSibling
      expect(activeCount).toHaveTextContent('0')

      // 空分类数应该为2
      expect(screen.getByText('空分类')).toBeInTheDocument()
      const emptyCount = screen.getByText('空分类').previousElementSibling
      expect(emptyCount).toHaveTextContent('2')

      // 总书签数应该为0
      expect(screen.getByText('总书签数')).toBeInTheDocument()
      const totalCount = screen.getByText('总书签数').previousElementSibling
      expect(totalCount).toHaveTextContent('0')
    })
  })

  describe('样式和布局测试', () => {
    it('应该应用自定义className', () => {
      const { container } = render(
        <CategoryList
          {...defaultProps}
          className="custom-class"
        />
      )

      expect(container.firstChild).toHaveClass('custom-class')
    })

    it('应该使用网格布局显示分类', () => {
      const { container } = render(<CategoryList {...defaultProps} />)

      // 查找分类网格容器（不是统计信息的网格）
      const gridContainers = container.querySelectorAll('.grid')
      const categoryGrid = Array.from(gridContainers).find(grid => 
        grid.classList.contains('grid-cols-1') && 
        grid.classList.contains('sm:grid-cols-2')
      )
      
      expect(categoryGrid).toBeInTheDocument()
      expect(categoryGrid).toHaveClass('grid-cols-1', 'sm:grid-cols-2', 'lg:grid-cols-3', 'xl:grid-cols-4')
    })
  })

  describe('边界情况测试', () => {
    it('应该处理单个分类', () => {
      render(
        <CategoryList
          {...defaultProps}
          categories={[mockCategories[0]]}
        />
      )

      // 检查总分类数
      expect(screen.getByText('总分类数')).toBeInTheDocument()
      const totalCount = screen.getByText('总分类数').previousElementSibling
      expect(totalCount).toHaveTextContent('1')
      
      expect(screen.getByTestId('category-card-category-1')).toBeInTheDocument()
    })

    it('应该处理大量分类', () => {
      const manyCategories = Array.from({ length: 50 }, (_, index) => ({
        ...mockCategories[0],
        id: `category-${index}`,
        name: `分类 ${index}`,
        bookmarkCount: index % 3 === 0 ? 0 : index
      }))

      render(
        <CategoryList
          {...defaultProps}
          categories={manyCategories}
        />
      )

      expect(screen.getByText('50')).toBeInTheDocument()
      expect(screen.getByText('总分类数')).toBeInTheDocument()
    })
  })
})