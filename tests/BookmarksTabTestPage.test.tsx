import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { describe, it, expect, vi, beforeEach } from 'vitest'
import BookmarksTabTestPage from '../src/components/test/BookmarksTabTestPage'

// Mock BookmarksTab组件
vi.mock('../src/components/BookmarksTab', () => ({
  default: () => <div data-testid="mocked-bookmarks-tab">Mocked BookmarksTab</div>
}))

// Mock Chrome API
const mockChrome = {
  runtime: {
    sendMessage: vi.fn()
  }
}
global.chrome = mockChrome as any

describe('BookmarksTabTestPage', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('基础渲染测试', () => {
    it('应该正确渲染测试页面标题', () => {
      render(<BookmarksTabTestPage />)
      
      expect(screen.getByText('📋 BookmarksTab组件测试页面')).toBeInTheDocument()
      expect(screen.getByText('shadcn重构验证')).toBeInTheDocument()
    })

    it('应该显示测试进度卡片', () => {
      render(<BookmarksTabTestPage />)
      
      expect(screen.getByText('📊 测试进度')).toBeInTheDocument()
      expect(screen.getByText('0/10')).toBeInTheDocument() // 初始状态
      expect(screen.getByText('0%')).toBeInTheDocument()
    })

    it('应该渲染组件预览区域', () => {
      render(<BookmarksTabTestPage />)
      
      expect(screen.getByText('组件预览区域')).toBeInTheDocument()
      expect(screen.getByTestId('mocked-bookmarks-tab')).toBeInTheDocument()
    })
  })

  describe('交互式测试功能', () => {
    it('应该能够切换测试项目状态', async () => {
      render(<BookmarksTabTestPage />)
      
      // 查找第一个测试项目的复选框
      const firstCheckbox = screen.getAllByRole('checkbox')[0]
      
      // 初始状态应该是未选中
      expect(firstCheckbox).not.toBeChecked()
      
      // 点击复选框
      fireEvent.click(firstCheckbox)
      
      // 验证状态已更新
      await waitFor(() => {
        expect(firstCheckbox).toBeChecked()
      })
      
      // 验证进度更新
      expect(screen.getByText('1/10')).toBeInTheDocument()
      expect(screen.getByText('10%')).toBeInTheDocument()
    })

    it('应该能够重置所有测试结果', async () => {
      render(<BookmarksTabTestPage />)
      
      // 选中几个测试项目
      const checkboxes = screen.getAllByRole('checkbox')
      fireEvent.click(checkboxes[0])
      fireEvent.click(checkboxes[1])
      
      await waitFor(() => {
        expect(screen.getByText('2/10')).toBeInTheDocument()
      })
      
      // 点击重置按钮
      const resetButton = screen.getByText('重置')
      fireEvent.click(resetButton)
      
      // 验证所有复选框都被重置
      await waitFor(() => {
        checkboxes.forEach(checkbox => {
          expect(checkbox).not.toBeChecked()
        })
        expect(screen.getByText('0/10')).toBeInTheDocument()
        expect(screen.getByText('0%')).toBeInTheDocument()
      })
    })

    it('应该正确计算测试进度百分比', async () => {
      render(<BookmarksTabTestPage />)
      
      const checkboxes = screen.getAllByRole('checkbox')
      const totalItems = checkboxes.length
      
      // 选中一半的测试项目
      const halfCount = Math.floor(totalItems / 2)
      for (let i = 0; i < halfCount; i++) {
        fireEvent.click(checkboxes[i])
      }
      
      await waitFor(() => {
        const expectedPercentage = Math.round((halfCount / totalItems) * 100)
        expect(screen.getByText(`${expectedPercentage}%`)).toBeInTheDocument()
      })
    })
  })

  describe('测试清单功能', () => {
    it('应该显示基础功能测试项目', () => {
      render(<BookmarksTabTestPage />)
      
      expect(screen.getByText('基础功能')).toBeInTheDocument()
      expect(screen.getByText('搜索框输入和清除')).toBeInTheDocument()
      expect(screen.getByText('分类筛选器选择')).toBeInTheDocument()
      expect(screen.getByText('添加收藏按钮点击')).toBeInTheDocument()
    })

    it('应该显示shadcn集成测试项目', () => {
      render(<BookmarksTabTestPage />)
      
      expect(screen.getByText('shadcn集成')).toBeInTheDocument()
      expect(screen.getByText('Input组件样式正确')).toBeInTheDocument()
      expect(screen.getByText('Select组件交互正常')).toBeInTheDocument()
      expect(screen.getByText('Button组件变体显示')).toBeInTheDocument()
    })

    it('应该为已完成的测试项目添加删除线样式', async () => {
      render(<BookmarksTabTestPage />)
      
      const firstCheckbox = screen.getAllByRole('checkbox')[0]
      const firstLabel = firstCheckbox.nextElementSibling as HTMLElement
      
      // 初始状态不应该有删除线
      expect(firstLabel).not.toHaveClass('line-through')
      
      // 选中测试项目
      fireEvent.click(firstCheckbox)
      
      await waitFor(() => {
        expect(firstLabel).toHaveClass('line-through', 'text-green-600')
      })
    })
  })

  describe('无障碍性测试', () => {
    it('应该为测试清单提供正确的ARIA标签', () => {
      render(<BookmarksTabTestPage />)
      
      const checklists = screen.getAllByRole('checklist')
      expect(checklists).toHaveLength(2) // 基础功能 + shadcn集成
      
      expect(checklists[0]).toHaveAttribute('aria-label', '基础功能')
      expect(checklists[1]).toHaveAttribute('aria-label', 'shadcn集成')
    })

    it('应该为复选框提供正确的标签关联', () => {
      render(<BookmarksTabTestPage />)
      
      const checkboxes = screen.getAllByRole('checkbox')
      checkboxes.forEach(checkbox => {
        const id = checkbox.getAttribute('id')
        expect(id).toBeTruthy()
        
        const label = screen.getByLabelText(checkbox.getAttribute('aria-label') || '')
        expect(label).toBeInTheDocument()
      })
    })
  })

  describe('错误处理测试', () => {
    it('应该在BookmarksTab组件出错时显示错误边界', () => {
      // 这个测试需要模拟BookmarksTab组件抛出错误
      // 由于我们已经mock了BookmarksTab，这里主要验证错误边界的存在
      render(<BookmarksTabTestPage />)
      
      // 验证组件正常渲染（没有错误）
      expect(screen.getByTestId('mocked-bookmarks-tab')).toBeInTheDocument()
    })
  })

  describe('响应式设计测试', () => {
    it('应该在不同屏幕尺寸下正确布局', () => {
      render(<BookmarksTabTestPage />)
      
      // 验证使用了响应式grid类
      const gridContainer = document.querySelector('.grid.grid-cols-1.md\\:grid-cols-2')
      expect(gridContainer).toBeInTheDocument()
    })
  })
})