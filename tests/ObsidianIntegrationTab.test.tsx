import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest'
import ObsidianIntegrationTab from '../src/components/ObsidianIntegrationTab'

// Mock Chrome API
const mockChrome = {
  runtime: {
    sendMessage: vi.fn()
  },
  storage: {
    sync: {
      get: vi.fn(),
      set: vi.fn()
    }
  }
}

// 设置全局 chrome 对象
Object.defineProperty(global, 'chrome', {
  value: mockChrome,
  writable: true
})

describe('ObsidianIntegrationTab', () => {
  beforeEach(() => {
    // 重置所有 mock
    vi.clearAllMocks()
    
    // 设置默认的 Chrome API 响应
    mockChrome.storage.sync.get.mockResolvedValue({})
    mockChrome.runtime.sendMessage.mockResolvedValue({
      success: true,
      data: []
    })
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  it('应该正确渲染组件', async () => {
    render(<ObsidianIntegrationTab />)
    
    // 检查标题是否存在
    expect(screen.getByText('Obsidian集成')).toBeInTheDocument()
    expect(screen.getByText('将您的书签数据同步到Obsidian笔记库，支持Dataview查询和Bases数据库格式')).toBeInTheDocument()
  })

  it('应该显示Obsidian库选择区域', async () => {
    render(<ObsidianIntegrationTab />)
    
    // 检查库选择相关元素
    expect(screen.getByText('Obsidian库选择')).toBeInTheDocument()
    expect(screen.getByText('选择Obsidian库')).toBeInTheDocument()
    expect(screen.getByText('刷新')).toBeInTheDocument()
    expect(screen.getByText('添加路径')).toBeInTheDocument()
  })

  it('应该显示同步设置区域', async () => {
    render(<ObsidianIntegrationTab />)
    
    // 检查同步设置相关元素
    expect(screen.getByText('同步设置')).toBeInTheDocument()
    expect(screen.getByText('目标文件夹')).toBeInTheDocument()
    expect(screen.getByText('文件名模式')).toBeInTheDocument()
    expect(screen.getByText('包含Dataview查询')).toBeInTheDocument()
    expect(screen.getByText('自动同步')).toBeInTheDocument()
  })

  it('应该显示导出格式选择', async () => {
    render(<ObsidianIntegrationTab />)
    
    // 检查导出格式选择
    expect(screen.getByText('导出格式')).toBeInTheDocument()
    expect(screen.getByText('标准Markdown')).toBeInTheDocument()
    expect(screen.getByText('Bases数据库')).toBeInTheDocument()
  })

  it('应该显示自定义模板区域', async () => {
    render(<ObsidianIntegrationTab />)
    
    // 检查自定义模板相关元素
    expect(screen.getByText('自定义模板')).toBeInTheDocument()
    expect(screen.getByText('模板内容')).toBeInTheDocument()
    expect(screen.getByText('恢复默认')).toBeInTheDocument()
    expect(screen.getByText('可用变量:')).toBeInTheDocument()
  })

  it('应该显示同步操作按钮', async () => {
    render(<ObsidianIntegrationTab />)
    
    // 检查操作按钮
    expect(screen.getByText('同步操作')).toBeInTheDocument()
    expect(screen.getByText('导出当前书签')).toBeInTheDocument()
    expect(screen.getByText('同步所有书签')).toBeInTheDocument()
  })

  it('应该显示功能说明', async () => {
    render(<ObsidianIntegrationTab />)
    
    // 检查功能说明
    expect(screen.getByText('功能说明')).toBeInTheDocument()
    expect(screen.getByText('标准Markdown格式')).toBeInTheDocument()
    expect(screen.getByText('Bases数据库格式')).toBeInTheDocument()
    expect(screen.getByText('高级功能')).toBeInTheDocument()
  })

  it('应该能够切换导出格式', async () => {
    render(<ObsidianIntegrationTab />)
    
    // 点击Bases数据库格式
    const basesCard = screen.getByText('Bases数据库').closest('div[role="button"], div[class*="cursor-pointer"]')
    if (basesCard) {
      fireEvent.click(basesCard)
      
      // 等待状态更新
      await waitFor(() => {
        // 检查是否显示了Bases数据库特定设置
        expect(screen.getByText('Bases数据库设置')).toBeInTheDocument()
        expect(screen.getByText('数据库名称')).toBeInTheDocument()
        expect(screen.getByText('创建预设视图')).toBeInTheDocument()
        expect(screen.getByText('启用评分字段')).toBeInTheDocument()
        expect(screen.getByText('启用状态字段')).toBeInTheDocument()
      })
    }
  })

  it('应该能够刷新Obsidian库列表', async () => {
    // 设置 mock 响应
    mockChrome.runtime.sendMessage.mockResolvedValue({
      success: true,
      data: [
        {
          name: '测试库',
          path: 'C:\\Test\\Vault',
          configPath: 'C:\\Test\\Vault\\.obsidian'
        }
      ]
    })

    render(<ObsidianIntegrationTab />)
    
    // 点击刷新按钮
    const refreshButton = screen.getByText('刷新')
    fireEvent.click(refreshButton)
    
    // 验证是否调用了正确的API
    await waitFor(() => {
      expect(mockChrome.runtime.sendMessage).toHaveBeenCalledWith({
        type: 'DETECT_OBSIDIAN_VAULTS'
      })
    })
  })

  it('应该能够添加自定义库路径', async () => {
    // Mock window.prompt
    const mockPrompt = vi.fn().mockReturnValue('C:\\Custom\\Vault')
    Object.defineProperty(window, 'prompt', {
      value: mockPrompt,
      writable: true
    })

    render(<ObsidianIntegrationTab />)
    
    // 点击添加路径按钮
    const addButton = screen.getByText('添加路径')
    fireEvent.click(addButton)
    
    // 验证是否调用了 prompt
    expect(mockPrompt).toHaveBeenCalledWith('请输入Obsidian库的完整路径:')
  })

  it('应该能够恢复默认模板', async () => {
    render(<ObsidianIntegrationTab />)
    
    // 点击恢复默认按钮
    const restoreButton = screen.getByText('恢复默认')
    fireEvent.click(restoreButton)
    
    // 验证模板内容是否被重置
    const templateTextarea = screen.getByRole('textbox', { name: /模板内容/i })
    expect(templateTextarea.value).toContain('# {{title}}')
  })

  it('应该在没有选择库时显示警告', async () => {
    render(<ObsidianIntegrationTab />)
    
    // 检查是否显示了选择库的提示
    expect(screen.getByText('请先选择一个Obsidian库才能进行同步操作')).toBeInTheDocument()
  })

  it('应该能够处理同步操作', async () => {
    // 设置有可用的库
    mockChrome.runtime.sendMessage.mockResolvedValue({
      success: true,
      data: [
        {
          name: '测试库',
          path: 'C:\\Test\\Vault',
          configPath: 'C:\\Test\\Vault\\.obsidian'
        }
      ]
    })

    render(<ObsidianIntegrationTab />)
    
    // 等待库列表加载
    await waitFor(() => {
      expect(screen.queryByText('请先选择一个Obsidian库才能进行同步操作')).not.toBeInTheDocument()
    })

    // 模拟同步操作的响应
    mockChrome.runtime.sendMessage.mockResolvedValue({
      success: true,
      data: {
        success: 5,
        failed: 0,
        errors: []
      }
    })

    // 点击同步所有书签按钮
    const syncButton = screen.getByText('同步所有书签')
    fireEvent.click(syncButton)
    
    // 验证是否调用了正确的API
    await waitFor(() => {
      expect(mockChrome.runtime.sendMessage).toHaveBeenCalledWith({
        type: 'SYNC_ALL_BOOKMARKS_TO_OBSIDIAN',
        data: expect.objectContaining({
          vaultPath: 'C:\\Test\\Vault',
          settings: expect.any(Object)
        })
      })
    })
  })

  it('应该能够处理同步错误', async () => {
    // 设置有可用的库
    mockChrome.runtime.sendMessage
      .mockResolvedValueOnce({
        success: true,
        data: [
          {
            name: '测试库',
            path: 'C:\\Test\\Vault',
            configPath: 'C:\\Test\\Vault\\.obsidian'
          }
        ]
      })
      .mockResolvedValueOnce({
        success: false,
        error: '同步失败'
      })

    render(<ObsidianIntegrationTab />)
    
    // 等待库列表加载
    await waitFor(() => {
      expect(screen.queryByText('请先选择一个Obsidian库才能进行同步操作')).not.toBeInTheDocument()
    })

    // 点击同步按钮
    const syncButton = screen.getByText('同步所有书签')
    fireEvent.click(syncButton)
    
    // 等待错误状态显示
    await waitFor(() => {
      expect(screen.getByText('同步失败，请检查设置后重试')).toBeInTheDocument()
    })
  })

  it('应该能够保存和加载设置', async () => {
    // 设置存储的返回值
    mockChrome.storage.sync.get.mockResolvedValue({
      obsidianSyncSettings: {
        folderPath: 'CustomBookmarks',
        includeDataview: false,
        exportType: 'bases'
      }
    })

    render(<ObsidianIntegrationTab />)
    
    // 等待设置加载
    await waitFor(() => {
      expect(mockChrome.storage.sync.get).toHaveBeenCalledWith(['obsidianSyncSettings'])
    })

    // 修改设置
    const folderInput = screen.getByDisplayValue('CustomBookmarks')
    fireEvent.change(folderInput, { target: { value: 'NewFolder' } })
    
    // 验证是否保存了设置
    await waitFor(() => {
      expect(mockChrome.storage.sync.set).toHaveBeenCalledWith({
        obsidianSyncSettings: expect.objectContaining({
          folderPath: 'NewFolder'
        })
      })
    })
  })

  it('应该显示同步结果', async () => {
    // 设置有可用的库
    mockChrome.runtime.sendMessage
      .mockResolvedValueOnce({
        success: true,
        data: [
          {
            name: '测试库',
            path: 'C:\\Test\\Vault',
            configPath: 'C:\\Test\\Vault\\.obsidian'
          }
        ]
      })
      .mockResolvedValueOnce({
        success: true,
        data: {
          success: 3,
          failed: 2,
          errors: ['错误1', '错误2']
        }
      })

    render(<ObsidianIntegrationTab />)
    
    // 等待库列表加载
    await waitFor(() => {
      expect(screen.queryByText('请先选择一个Obsidian库才能进行同步操作')).not.toBeInTheDocument()
    })

    // 点击同步按钮
    const syncButton = screen.getByText('同步所有书签')
    fireEvent.click(syncButton)
    
    // 等待同步结果显示
    await waitFor(() => {
      expect(screen.getByText('最近同步结果')).toBeInTheDocument()
      expect(screen.getByText('3')).toBeInTheDocument() // 成功数量
      expect(screen.getByText('2')).toBeInTheDocument() // 失败数量
      expect(screen.getByText('错误详情:')).toBeInTheDocument()
    })
  })
})