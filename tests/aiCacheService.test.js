// AI缓存服务单元测试

import { describe, it, expect, beforeEach, vi } from 'vitest'
import { aiCacheService } from '../src/services/aiCacheService'
import { IndexedDBService } from '../src/utils/indexedDB'

// 模拟IndexedDB服务
vi.mock('../src/utils/indexedDB', () => ({
  IndexedDBService: {
    initialize: vi.fn(),
    get: vi.fn(),
    save: vi.fn(),
    delete: vi.fn(),
    getAll: vi.fn(),
    clear: vi.fn()
  }
}))

describe('AICacheService', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    
    // 设置默认的模拟返回值
    IndexedDBService.initialize.mockResolvedValue()
    IndexedDBService.getAll.mockResolvedValue([])
    IndexedDBService.get.mockResolvedValue(null)
    IndexedDBService.save.mockResolvedValue()
    IndexedDBService.delete.mockResolvedValue()
    IndexedDBService.clear.mockResolvedValue()
  })

  describe('初始化', () => {
    it('应该成功初始化缓存服务', async () => {
      await expect(aiCacheService.initialize()).resolves.not.toThrow()
      
      expect(IndexedDBService.initialize).toHaveBeenCalled()
      expect(IndexedDBService.getAll).toHaveBeenCalled()
    })

    it('初始化失败时应该抛出错误', async () => {
      IndexedDBService.initialize.mockRejectedValue(new Error('初始化失败'))
      
      await expect(aiCacheService.initialize()).rejects.toThrow('初始化失败')
    })
  })

  describe('标签缓存', () => {
    const mockTagRequest = {
      content: '这是一个关于JavaScript的技术文章',
      title: 'JavaScript基础教程',
      url: 'https://example.com/js-tutorial',
      maxTags: 5
    }

    const mockTagResponse = {
      tags: ['JavaScript', '编程', '教程', '前端', '技术'],
      confidence: 0.9,
      reasoning: '基于内容分析生成的标签',
      processingTime: 1500
    }

    it('应该能够保存标签缓存', async () => {
      await aiCacheService.saveTagsCache(mockTagRequest, mockTagResponse)
      
      expect(IndexedDBService.save).toHaveBeenCalledWith(
        'ai_cache',
        expect.objectContaining({
          id: expect.stringMatching(/^tags_/),
          request: mockTagRequest,
          response: mockTagResponse,
          createdAt: expect.any(Date),
          expiresAt: expect.any(Date),
          hitCount: 0,
          lastAccessedAt: expect.any(Date)
        })
      )
    })

    it('应该能够获取标签缓存', async () => {
      const mockCacheItem = {
        id: 'tags_abc123',
        requestHash: 'abc123',
        request: mockTagRequest,
        response: mockTagResponse,
        createdAt: new Date(),
        expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
        hitCount: 0,
        lastAccessedAt: new Date()
      }

      IndexedDBService.get.mockResolvedValue(mockCacheItem)

      const result = await aiCacheService.getTagsCache(mockTagRequest)
      
      expect(result).toEqual(mockTagResponse)
      expect(IndexedDBService.get).toHaveBeenCalledWith('ai_cache', expect.stringMatching(/^tags_/))
      expect(IndexedDBService.save).toHaveBeenCalledWith('ai_cache', expect.objectContaining({
        hitCount: 1
      }))
    })

    it('缓存未命中时应该返回null', async () => {
      IndexedDBService.get.mockResolvedValue(null)

      const result = await aiCacheService.getTagsCache(mockTagRequest)
      
      expect(result).toBeNull()
    })

    it('缓存过期时应该删除并返回null', async () => {
      const expiredCacheItem = {
        id: 'tags_abc123',
        requestHash: 'abc123',
        request: mockTagRequest,
        response: mockTagResponse,
        createdAt: new Date(),
        expiresAt: new Date(Date.now() - 1000), // 已过期
        hitCount: 0,
        lastAccessedAt: new Date()
      }

      IndexedDBService.get.mockResolvedValue(expiredCacheItem)

      const result = await aiCacheService.getTagsCache(mockTagRequest)
      
      expect(result).toBeNull()
      expect(IndexedDBService.delete).toHaveBeenCalledWith('ai_cache', expect.stringMatching(/^tags_/))
    })
  })

  describe('分类缓存', () => {
    const mockCategoryRequest = {
      content: '这是一个关于机器学习的研究论文',
      title: '深度学习在图像识别中的应用',
      url: 'https://example.com/ml-paper',
      maxSuggestions: 3
    }

    const mockCategoryResponse = {
      category: '机器学习',
      confidence: 0.95,
      alternatives: [
        { category: '人工智能', confidence: 0.8 },
        { category: '计算机视觉', confidence: 0.7 }
      ],
      reasoning: '基于内容主题分析'
    }

    it('应该能够保存分类缓存', async () => {
      await aiCacheService.saveCategoryCache(mockCategoryRequest, mockCategoryResponse)
      
      expect(IndexedDBService.save).toHaveBeenCalledWith(
        'ai_cache',
        expect.objectContaining({
          id: expect.stringMatching(/^category_/),
          request: mockCategoryRequest,
          response: mockCategoryResponse
        })
      )
    })

    it('应该能够获取分类缓存', async () => {
      const mockCacheItem = {
        id: 'category_def456',
        requestHash: 'def456',
        request: mockCategoryRequest,
        response: mockCategoryResponse,
        createdAt: new Date(),
        expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
        hitCount: 0,
        lastAccessedAt: new Date()
      }

      IndexedDBService.get.mockResolvedValue(mockCacheItem)

      const result = await aiCacheService.getCategoryCache(mockCategoryRequest)
      
      expect(result).toEqual(mockCategoryResponse)
    })
  })

  describe('描述缓存', () => {
    const mockDescriptionRequest = {
      content: '这是一个详细介绍React Hooks的技术博客文章',
      title: 'React Hooks完全指南',
      url: 'https://example.com/react-hooks',
      maxLength: 200,
      style: 'brief'
    }

    const mockDescriptionResponse = {
      description: '这是一篇全面介绍React Hooks的技术文章，涵盖了useState、useEffect等核心概念。',
      confidence: 0.85,
      wordCount: 32
    }

    it('应该能够保存描述缓存', async () => {
      await aiCacheService.saveDescriptionCache(mockDescriptionRequest, mockDescriptionResponse)
      
      expect(IndexedDBService.save).toHaveBeenCalledWith(
        'ai_cache',
        expect.objectContaining({
          id: expect.stringMatching(/^description_/),
          request: mockDescriptionRequest,
          response: mockDescriptionResponse
        })
      )
    })

    it('应该能够获取描述缓存', async () => {
      const mockCacheItem = {
        id: 'description_ghi789',
        requestHash: 'ghi789',
        request: mockDescriptionRequest,
        response: mockDescriptionResponse,
        createdAt: new Date(),
        expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
        hitCount: 0,
        lastAccessedAt: new Date()
      }

      IndexedDBService.get.mockResolvedValue(mockCacheItem)

      const result = await aiCacheService.getDescriptionCache(mockDescriptionRequest)
      
      expect(result).toEqual(mockDescriptionResponse)
    })
  })

  describe('缓存管理', () => {
    it('应该能够清理过期缓存', async () => {
      const now = new Date()
      const expiredItem = {
        id: 'tags_expired',
        expiresAt: new Date(now.getTime() - 1000)
      }
      const validItem = {
        id: 'tags_valid',
        expiresAt: new Date(now.getTime() + 1000)
      }

      IndexedDBService.getAll.mockResolvedValue([expiredItem, validItem])

      const cleanedCount = await aiCacheService.cleanExpiredCache()
      
      expect(cleanedCount).toBe(1)
      expect(IndexedDBService.delete).toHaveBeenCalledWith('ai_cache', 'tags_expired')
      expect(IndexedDBService.delete).not.toHaveBeenCalledWith('ai_cache', 'tags_valid')
    })

    it('应该能够清理最少使用的缓存', async () => {
      const items = [
        { id: 'item1', hitCount: 5, lastAccessedAt: new Date('2024-01-01') },
        { id: 'item2', hitCount: 2, lastAccessedAt: new Date('2024-01-02') },
        { id: 'item3', hitCount: 8, lastAccessedAt: new Date('2024-01-03') }
      ]

      IndexedDBService.getAll.mockResolvedValue(items)

      const cleanedCount = await aiCacheService.cleanLeastUsedCache(2)
      
      expect(cleanedCount).toBe(2)
      expect(IndexedDBService.delete).toHaveBeenCalledWith('ai_cache', 'item2')
      expect(IndexedDBService.delete).toHaveBeenCalledWith('ai_cache', 'item1')
    })

    it('应该能够清空所有缓存', async () => {
      await aiCacheService.clearAllCache()
      
      expect(IndexedDBService.clear).toHaveBeenCalledWith('ai_cache')
    })

    it('应该能够获取缓存统计信息', async () => {
      const mockItems = [
        {
          id: 'item1',
          createdAt: new Date('2024-01-01'),
          hitCount: 5
        },
        {
          id: 'item2',
          createdAt: new Date('2024-01-02'),
          hitCount: 3
        }
      ]

      IndexedDBService.getAll.mockResolvedValue(mockItems)

      const stats = await aiCacheService.getStats()
      
      expect(stats).toEqual(expect.objectContaining({
        totalItems: 2,
        cacheSize: expect.any(Number),
        oldestItem: new Date('2024-01-01'),
        newestItem: new Date('2024-01-02')
      }))
    })

    it('应该能够获取缓存项列表', async () => {
      const mockItems = [
        { id: 'tags_item1', createdAt: new Date('2024-01-02') },
        { id: 'category_item2', createdAt: new Date('2024-01-01') },
        { id: 'tags_item3', createdAt: new Date('2024-01-03') }
      ]

      IndexedDBService.getAll.mockResolvedValue(mockItems)

      // 获取所有缓存项
      const allItems = await aiCacheService.getCacheItems()
      expect(allItems).toHaveLength(3)
      expect(allItems[0].id).toBe('tags_item3') // 按创建时间倒序

      // 获取特定类型的缓存项
      const tagItems = await aiCacheService.getCacheItems('tags')
      expect(tagItems).toHaveLength(2)
      expect(tagItems.every(item => item.id.startsWith('tags_'))).toBe(true)
    })

    it('应该能够删除特定缓存项', async () => {
      await aiCacheService.deleteCacheItem('tags_abc123')
      
      expect(IndexedDBService.delete).toHaveBeenCalledWith('ai_cache', 'tags_abc123')
    })
  })

  describe('错误处理', () => {
    it('获取缓存失败时应该返回null', async () => {
      IndexedDBService.get.mockRejectedValue(new Error('数据库错误'))

      const mockRequest = {
        content: '测试内容',
        title: '测试标题'
      }

      const result = await aiCacheService.getTagsCache(mockRequest)
      
      expect(result).toBeNull()
    })

    it('保存缓存失败时应该静默处理', async () => {
      IndexedDBService.save.mockRejectedValue(new Error('保存失败'))

      const mockRequest = {
        content: '测试内容',
        title: '测试标题'
      }

      const mockResponse = {
        tags: ['测试'],
        confidence: 0.8,
        processingTime: 1000
      }

      // 不应该抛出错误
      await expect(aiCacheService.saveTagsCache(mockRequest, mockResponse)).resolves.not.toThrow()
    })

    it('清理缓存失败时应该返回0', async () => {
      IndexedDBService.getAll.mockRejectedValue(new Error('获取失败'))

      const cleanedCount = await aiCacheService.cleanExpiredCache()
      
      expect(cleanedCount).toBe(0)
    })
  })

  describe('哈希生成', () => {
    it('相同请求应该生成相同哈希', async () => {
      const request1 = {
        content: '相同的内容',
        title: '相同的标题',
        maxTags: 5
      }

      const request2 = {
        content: '相同的内容',
        title: '相同的标题',
        maxTags: 5
      }

      const response = {
        tags: ['测试'],
        confidence: 0.8,
        processingTime: 1000
      }

      // 保存第一个请求的缓存
      await aiCacheService.saveTagsCache(request1, response)
      
      // 获取第二个请求的缓存（应该命中）
      IndexedDBService.get.mockResolvedValue({
        id: 'tags_test',
        response,
        createdAt: new Date(),
        expiresAt: new Date(Date.now() + 1000000),
        hitCount: 0,
        lastAccessedAt: new Date()
      })

      const result = await aiCacheService.getTagsCache(request2)
      
      expect(result).toEqual(response)
    })

    it('不同请求应该生成不同哈希', async () => {
      const request1 = {
        content: '内容1',
        title: '标题1'
      }

      const request2 = {
        content: '内容2',
        title: '标题2'
      }

      const response1 = { tags: ['标签1'], confidence: 0.8, processingTime: 1000 }
      const response2 = { tags: ['标签2'], confidence: 0.8, processingTime: 1000 }

      await aiCacheService.saveTagsCache(request1, response1)
      await aiCacheService.saveTagsCache(request2, response2)

      // 验证调用了两次save，说明生成了不同的哈希
      expect(IndexedDBService.save).toHaveBeenCalledTimes(2)
      
      const calls = IndexedDBService.save.mock.calls
      expect(calls[0][1].id).not.toBe(calls[1][1].id)
    })
  })
})