#!/usr/bin/env node

/**
 * Background Service Worker 单元测试
 * 测试后台服务的核心功能
 */

import { fileURLToPath } from 'url'
import path from 'path'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

console.log('🧪 开始测试 Background Service Worker...\n')

/**
 * 简单的测试框架
 */
class TestRunner {
  constructor() {
    this.tests = []
    this.passed = 0
    this.failed = 0
  }

  /**
   * 添加测试用例
   * @param {string} name - 测试名称
   * @param {Function} testFn - 测试函数
   */
  test(name, testFn) {
    this.tests.push({ name, testFn })
  }

  /**
   * 断言函数
   * @param {boolean} condition - 断言条件
   * @param {string} message - 错误消息
   */
  assert(condition, message) {
    if (!condition) {
      throw new Error(message)
    }
  }

  /**
   * 运行所有测试
   */
  async run() {
    console.log(`运行 ${this.tests.length} 个测试用例...\n`)

    for (const test of this.tests) {
      try {
        await test.testFn()
        console.log(`✅ ${test.name}`)
        this.passed++
      } catch (error) {
        console.log(`❌ ${test.name}: ${error.message}`)
        this.failed++
      }
    }

    console.log('\n' + '='.repeat(50))
    console.log(`测试结果: ${this.passed} 通过, ${this.failed} 失败`)

    if (this.failed > 0) {
      process.exit(1)
    }
  }
}

const runner = new TestRunner()

// Mock Chrome APIs for testing
const mockChrome = {
  runtime: {
    onInstalled: {
      addListener: (callback) => {
        // 模拟扩展安装事件
        callback({ reason: 'install' })
      }
    },
    onMessage: {
      addListener: (callback) => {
        // 模拟消息监听
        return true
      }
    },
    sendMessage: (message) => {
      return Promise.resolve({ status: 'mock' })
    }
  },
  contextMenus: {
    removeAll: (callback) => {
      callback && callback()
    },
    create: (options) => {
      return options.id
    },
    onClicked: {
      addListener: (callback) => {
        // 模拟右键菜单点击
        return true
      }
    }
  },
  storage: {
    local: {
      get: (keys) => {
        return Promise.resolve({})
      },
      set: (data) => {
        return Promise.resolve()
      }
    }
  },
  tabs: {
    sendMessage: (tabId, message) => {
      return Promise.resolve({ 
        title: 'Test Page',
        url: 'https://example.com'
      })
    }
  }
}

// 全局设置 chrome 对象用于测试
global.chrome = mockChrome

// 测试消息处理功能
runner.test('消息处理 - PING 消息', async () => {
  // 模拟 handleMessage 函数
  const handleMessage = async (message, sender) => {
    switch (message.type) {
      case 'PING':
        return { status: 'pong' }
      default:
        throw new Error(`未知的消息类型: ${message.type}`)
    }
  }

  const response = await handleMessage({ type: 'PING' }, {})
  runner.assert(response.status === 'pong', 'PING 消息应返回 pong 状态')
})

// 测试设置管理功能
runner.test('设置管理 - 获取默认设置', async () => {
  // 模拟 getSettings 函数
  const getSettings = async () => {
    const result = await chrome.storage.local.get(['settings'])
    return result.settings || {}
  }

  const settings = await getSettings()
  runner.assert(typeof settings === 'object', '设置应该是一个对象')
})

// 测试设置更新功能
runner.test('设置管理 - 更新设置', async () => {
  // 模拟 updateSettings 函数
  const updateSettings = async (newSettings) => {
    const result = await chrome.storage.local.get(['settings'])
    const currentSettings = result.settings || {}
    const updatedSettings = { ...currentSettings, ...newSettings }
    
    await chrome.storage.local.set({ settings: updatedSettings })
    return updatedSettings
  }

  const newSettings = { theme: 'dark', autoTagging: false }
  const result = await updateSettings(newSettings)
  
  runner.assert(result.theme === 'dark', '主题设置应该被更新')
  runner.assert(result.autoTagging === false, '自动标签设置应该被更新')
})

// 测试右键菜单创建
runner.test('右键菜单 - 创建菜单项', () => {
  // 模拟 createContextMenus 函数
  const createContextMenus = () => {
    const menuItems = []
    
    chrome.contextMenus.removeAll(() => {
      // 收藏链接菜单
      const linkMenu = chrome.contextMenus.create({
        id: 'bookmark-link',
        title: '收藏链接',
        contexts: ['link']
      })
      menuItems.push(linkMenu)
      
      // 收藏选中文字菜单
      const selectionMenu = chrome.contextMenus.create({
        id: 'bookmark-selection',
        title: '收藏摘录',
        contexts: ['selection']
      })
      menuItems.push(selectionMenu)
      
      // 收藏当前页面菜单
      const pageMenu = chrome.contextMenus.create({
        id: 'bookmark-page',
        title: '收藏当前页面',
        contexts: ['page']
      })
      menuItems.push(pageMenu)
    })
    
    return menuItems
  }

  const menuItems = createContextMenus()
  runner.assert(menuItems.length === 3, '应该创建3个右键菜单项')
  runner.assert(menuItems.includes('bookmark-link'), '应该包含收藏链接菜单')
  runner.assert(menuItems.includes('bookmark-selection'), '应该包含收藏摘录菜单')
  runner.assert(menuItems.includes('bookmark-page'), '应该包含收藏页面菜单')
})

// 测试存储初始化
runner.test('存储管理 - 初始化存储', async () => {
  // 模拟 initializeStorage 函数
  const initializeStorage = async () => {
    const result = await chrome.storage.local.get(['initialized'])
    
    if (!result.initialized) {
      const defaultData = {
        initialized: true,
        settings: {
          theme: 'light',
          language: 'zh-CN',
          autoTagging: true,
          floatingWidget: false,
          syncEnabled: false
        },
        categories: [
          {
            id: 'default',
            name: '默认分类',
            description: '未分类的收藏内容',
            color: '#3b82f6',
            createdAt: new Date().toISOString()
          }
        ],
        tags: [],
        bookmarks: []
      }
      
      await chrome.storage.local.set(defaultData)
      return defaultData
    }
    
    return result
  }

  const result = await initializeStorage()
  
  runner.assert(result.initialized === true, '存储应该被标记为已初始化')
  runner.assert(result.settings.theme === 'light', '默认主题应该是浅色')
  runner.assert(result.settings.language === 'zh-CN', '默认语言应该是中文')
  runner.assert(Array.isArray(result.categories), '分类应该是数组')
  runner.assert(result.categories.length === 1, '应该有一个默认分类')
  runner.assert(result.categories[0].name === '默认分类', '默认分类名称应该正确')
})

// 测试收藏链接处理
runner.test('收藏处理 - 处理链接收藏', async () => {
  // 模拟 handleBookmarkLink 函数
  const handleBookmarkLink = async (info, tab) => {
    if (!info.linkUrl || !tab) {
      throw new Error('缺少必要参数')
    }
    
    const response = await chrome.tabs.sendMessage(tab.id, {
      type: 'GET_LINK_INFO',
      data: { url: info.linkUrl }
    })
    
    return {
      success: true,
      linkUrl: info.linkUrl,
      pageInfo: response
    }
  }

  const mockInfo = { linkUrl: 'https://example.com' }
  const mockTab = { id: 1 }
  
  const result = await handleBookmarkLink(mockInfo, mockTab)
  
  runner.assert(result.success === true, '链接收藏应该成功')
  runner.assert(result.linkUrl === 'https://example.com', '链接URL应该正确')
  runner.assert(typeof result.pageInfo === 'object', '应该包含页面信息')
})

// 测试收藏选中文字处理
runner.test('收藏处理 - 处理文字摘录收藏', async () => {
  // 模拟 handleBookmarkSelection 函数
  const handleBookmarkSelection = async (info, tab) => {
    if (!info.selectionText || !tab) {
      throw new Error('缺少必要参数')
    }
    
    const response = await chrome.tabs.sendMessage(tab.id, {
      type: 'GET_PAGE_INFO',
      data: { selectedText: info.selectionText }
    })
    
    return {
      success: true,
      selectedText: info.selectionText,
      pageInfo: response
    }
  }

  const mockInfo = { selectionText: '这是选中的文字' }
  const mockTab = { id: 1 }
  
  const result = await handleBookmarkSelection(mockInfo, mockTab)
  
  runner.assert(result.success === true, '文字摘录收藏应该成功')
  runner.assert(result.selectedText === '这是选中的文字', '选中文字应该正确')
  runner.assert(typeof result.pageInfo === 'object', '应该包含页面信息')
})

// 测试错误处理
runner.test('错误处理 - 未知消息类型', async () => {
  // 模拟 handleMessage 函数
  const handleMessage = async (message, sender) => {
    switch (message.type) {
      case 'PING':
        return { status: 'pong' }
      default:
        throw new Error(`未知的消息类型: ${message.type}`)
    }
  }

  try {
    await handleMessage({ type: 'UNKNOWN_TYPE' }, {})
    runner.assert(false, '应该抛出错误')
  } catch (error) {
    runner.assert(error.message.includes('未知的消息类型'), '错误消息应该正确')
  }
})

// 测试参数验证
runner.test('参数验证 - 收藏链接缺少参数', async () => {
  // 模拟 handleBookmarkLink 函数
  const handleBookmarkLink = async (info, tab) => {
    if (!info.linkUrl || !tab) {
      return { success: false, error: '缺少必要参数' }
    }
    return { success: true }
  }

  // 测试缺少 linkUrl
  const result1 = await handleBookmarkLink({}, { id: 1 })
  runner.assert(result1.success === false, '缺少linkUrl时应该失败')
  
  // 测试缺少 tab
  const result2 = await handleBookmarkLink({ linkUrl: 'https://example.com' }, null)
  runner.assert(result2.success === false, '缺少tab时应该失败')
})

// 运行所有测试
runner.run().catch(error => {
  console.error('测试运行失败:', error)
  process.exit(1)
})