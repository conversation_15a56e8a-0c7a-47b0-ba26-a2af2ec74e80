/**
 * 关于我们和帮助中心页面端到端测试
 * 使用 Playwright 进行真实浏览器环境测试
 */

import { test, expect } from '@playwright/test'

test.describe('关于我们和帮助中心页面 E2E 测试', () => {
  test.beforeEach(async ({ page }) => {
    // 导航到选项页面
    await page.goto('/src/options/index.html')
    
    // 等待页面加载完成
    await page.waitForSelector('[data-testid="options-app"]', { timeout: 10000 })
  })

  test.describe('关于我们页面', () => {
    test('应该正确显示关于我们页面内容', async ({ page }) => {
      // 点击关于我们导航
      await page.click('text=关于我们')
      
      // 验证页面标题
      await expect(page.locator('h2:has-text("关于我们")')).toBeVisible()
      
      // 验证扩展基本信息
      await expect(page.locator('text=Universe Bag（乾坤袋）')).toBeVisible()
      await expect(page.locator('text=智能收藏管理工具')).toBeVisible()
      
      // 验证版本信息
      await expect(page.locator('text=版本:')).toBeVisible()
      await expect(page.locator('text=构建日期:')).toBeVisible()
      
      // 验证开发者信息
      await expect(page.locator('text=开发者信息')).toBeVisible()
      await expect(page.locator('text=coffeebean')).toBeVisible()
      
      // 验证技术信息
      await expect(page.locator('text=技术信息')).toBeVisible()
      await expect(page.locator('text=运行环境:')).toBeVisible()
      
      // 验证许可证信息
      await expect(page.locator('text=许可证信息')).toBeVisible()
      await expect(page.locator('text=MIT License')).toBeVisible()
    })

    test('应该显示权限详情', async ({ page }) => {
      await page.click('text=关于我们')
      
      // 验证权限详情卡片
      await expect(page.locator('text=权限详情')).toBeVisible()
      
      // 验证权限数量显示
      await expect(page.locator('text=权限数量:')).toBeVisible()
      
      // 验证具体权限项
      const permissionItems = page.locator('[data-testid="permission-item"]')
      if (await permissionItems.count() > 0) {
        await expect(permissionItems.first()).toBeVisible()
      }
    })

    test('应该支持外部链接点击', async ({ page }) => {
      await page.click('text=关于我们')
      
      // 查找外部链接
      const websiteLink = page.locator('a[href*="universebag.com"]')
      if (await websiteLink.count() > 0) {
        // 验证链接具有正确的属性
        await expect(websiteLink).toHaveAttribute('target', '_blank')
        await expect(websiteLink).toHaveAttribute('rel', 'noopener noreferrer')
      }
      
      const emailLink = page.locator('a[href^="mailto:"]')
      if (await emailLink.count() > 0) {
        await expect(emailLink).toBeVisible()
      }
    })
  })

  test.describe('帮助中心页面', () => {
    test('应该正确显示帮助中心页面内容', async ({ page }) => {
      // 点击帮助中心导航
      await page.click('text=帮助中心')
      
      // 等待内容加载
      await page.waitForSelector('text=帮助中心', { timeout: 5000 })
      
      // 验证页面标题
      await expect(page.locator('h2:has-text("帮助中心")')).toBeVisible()
      
      // 验证搜索框
      await expect(page.locator('input[placeholder*="搜索帮助内容"]')).toBeVisible()
      
      // 验证分类筛选按钮
      await expect(page.locator('text=全部')).toBeVisible()
      await expect(page.locator('text=使用指南')).toBeVisible()
      await expect(page.locator('text=常见问题')).toBeVisible()
      await expect(page.locator('text=故障排除')).toBeVisible()
      
      // 验证帮助内容项
      await expect(page.locator('text=快速开始')).toBeVisible()
      await expect(page.locator('text=收藏管理')).toBeVisible()
    })

    test('应该支持搜索功能', async ({ page }) => {
      await page.click('text=帮助中心')
      await page.waitForSelector('input[placeholder*="搜索帮助内容"]')
      
      // 输入搜索关键词
      await page.fill('input[placeholder*="搜索帮助内容"]', '收藏')
      
      // 等待搜索结果
      await page.waitForSelector('text=找到', { timeout: 3000 })
      
      // 验证搜索结果统计
      await expect(page.locator('text=找到')).toBeVisible()
      await expect(page.locator('text=个相关结果')).toBeVisible()
      
      // 验证清除搜索按钮
      await expect(page.locator('text=清除搜索')).toBeVisible()
    })

    test('应该支持分类筛选', async ({ page }) => {
      await page.click('text=帮助中心')
      await page.waitForSelector('text=使用指南')
      
      // 点击使用指南分类
      await page.click('text=使用指南')
      
      // 验证分类被选中
      const guideButton = page.locator('button:has-text("使用指南")')
      await expect(guideButton).toHaveClass(/bg-primary-600/)
      
      // 点击常见问题分类
      await page.click('text=常见问题')
      
      // 验证分类切换
      const faqButton = page.locator('button:has-text("常见问题")')
      await expect(faqButton).toHaveClass(/bg-primary-600/)
    })

    test('应该支持内容展开和折叠', async ({ page }) => {
      await page.click('text=帮助中心')
      await page.waitForSelector('text=快速开始')
      
      // 点击展开内容
      await page.click('text=快速开始')
      
      // 验证内容展开
      await expect(page.locator('text=欢迎使用 Universe Bag')).toBeVisible()
      
      // 再次点击折叠内容
      await page.click('text=快速开始')
      
      // 验证内容折叠
      await expect(page.locator('text=欢迎使用 Universe Bag')).not.toBeVisible()
    })

    test('应该支持展开全部和折叠全部', async ({ page }) => {
      await page.click('text=帮助中心')
      await page.waitForSelector('text=展开全部')
      
      // 点击展开全部
      await page.click('text=展开全部')
      
      // 验证多个内容被展开
      await expect(page.locator('text=欢迎使用 Universe Bag')).toBeVisible()
      
      // 点击折叠全部
      await page.click('text=折叠全部')
      
      // 验证内容被折叠
      await expect(page.locator('text=欢迎使用 Universe Bag')).not.toBeVisible()
    })

    test('应该显示联系信息', async ({ page }) => {
      await page.click('text=帮助中心')
      await page.waitForSelector('text=需要更多帮助？')
      
      // 验证联系信息区域
      await expect(page.locator('text=需要更多帮助？')).toBeVisible()
      await expect(page.locator('text=发送邮件')).toBeVisible()
      await expect(page.locator('text=访问官网')).toBeVisible()
      
      // 验证邮件链接
      const emailLink = page.locator('a[href^="mailto:"]')
      await expect(emailLink).toBeVisible()
      
      // 验证官网链接
      const websiteLink = page.locator('a[href*="universebag.com"]')
      await expect(websiteLink).toBeVisible()
      await expect(websiteLink).toHaveAttribute('target', '_blank')
    })
  })

  test.describe('导航和路由', () => {
    test('应该支持页面间导航', async ({ page }) => {
      // 测试导航到关于我们
      await page.click('text=关于我们')
      await expect(page.locator('text=开发者信息')).toBeVisible()
      
      // 测试导航到帮助中心
      await page.click('text=帮助中心')
      await page.waitForSelector('input[placeholder*="搜索帮助内容"]')
      await expect(page.locator('input[placeholder*="搜索帮助内容"]')).toBeVisible()
      
      // 测试导航回收藏管理
      await page.click('text=收藏管理')
      await expect(page.locator('text=添加收藏')).toBeVisible()
    })

    test('应该支持 URL hash 路由', async ({ page }) => {
      // 直接访问关于我们页面
      await page.goto('/src/options/index.html#about')
      await expect(page.locator('text=开发者信息')).toBeVisible()
      
      // 验证 URL
      expect(page.url()).toContain('#about')
      
      // 直接访问帮助中心页面
      await page.goto('/src/options/index.html#help')
      await page.waitForSelector('input[placeholder*="搜索帮助内容"]')
      await expect(page.locator('input[placeholder*="搜索帮助内容"]')).toBeVisible()
      
      // 验证 URL
      expect(page.url()).toContain('#help')
    })

    test('应该支持浏览器前进后退', async ({ page }) => {
      // 导航到关于我们
      await page.click('text=关于我们')
      await expect(page.locator('text=开发者信息')).toBeVisible()
      
      // 导航到帮助中心
      await page.click('text=帮助中心')
      await page.waitForSelector('input[placeholder*="搜索帮助内容"]')
      
      // 使用浏览器后退
      await page.goBack()
      await expect(page.locator('text=开发者信息')).toBeVisible()
      
      // 使用浏览器前进
      await page.goForward()
      await page.waitForSelector('input[placeholder*="搜索帮助内容"]')
      await expect(page.locator('input[placeholder*="搜索帮助内容"]')).toBeVisible()
    })
  })

  test.describe('响应式设计', () => {
    test('应该在移动设备上正确显示', async ({ page }) => {
      // 设置移动设备视口
      await page.setViewportSize({ width: 375, height: 667 })
      
      // 验证移动端布局
      await page.click('text=关于我们')
      await expect(page.locator('text=开发者信息')).toBeVisible()
      
      // 验证内容在移动端正确显示
      await expect(page.locator('text=Universe Bag（乾坤袋）')).toBeVisible()
      
      // 切换到帮助中心
      await page.click('text=帮助中心')
      await page.waitForSelector('input[placeholder*="搜索帮助内容"]')
      
      // 验证搜索框在移动端可用
      await expect(page.locator('input[placeholder*="搜索帮助内容"]')).toBeVisible()
    })

    test('应该在平板设备上正确显示', async ({ page }) => {
      // 设置平板设备视口
      await page.setViewportSize({ width: 768, height: 1024 })
      
      await page.click('text=关于我们')
      await expect(page.locator('text=开发者信息')).toBeVisible()
      
      // 验证平板端布局
      await expect(page.locator('text=技术信息')).toBeVisible()
    })
  })

  test.describe('主题切换', () => {
    test('应该支持主题切换', async ({ page }) => {
      // 查找主题切换按钮
      const themeToggle = page.locator('button[title*="主题"]').first()
      
      if (await themeToggle.count() > 0) {
        // 点击切换主题
        await themeToggle.click()
        
        // 验证深色主题应用
        await expect(page.locator('html.dark')).toBeVisible()
        
        // 再次点击切换回浅色主题
        await themeToggle.click()
        
        // 验证浅色主题应用
        await expect(page.locator('html')).not.toHaveClass('dark')
      }
    })
  })

  test.describe('键盘导航', () => {
    test('应该支持键盘快捷键', async ({ page }) => {
      // 使用 Alt+6 切换到关于我们
      await page.keyboard.press('Alt+6')
      await expect(page.locator('text=开发者信息')).toBeVisible()
      
      // 使用 Alt+7 切换到帮助中心
      await page.keyboard.press('Alt+7')
      await page.waitForSelector('input[placeholder*="搜索帮助内容"]')
      await expect(page.locator('input[placeholder*="搜索帮助内容"]')).toBeVisible()
    })

    test('应该支持 Tab 键导航', async ({ page }) => {
      // 使用 Tab 键导航
      await page.keyboard.press('Tab')
      await page.keyboard.press('Tab')
      
      // 验证焦点在导航按钮上
      const focusedElement = page.locator(':focus')
      await expect(focusedElement).toBeVisible()
    })
  })

  test.describe('性能测试', () => {
    test('页面加载性能应该良好', async ({ page }) => {
      const startTime = Date.now()
      
      await page.goto('/src/options/index.html')
      await page.waitForSelector('[data-testid="options-app"]')
      
      const loadTime = Date.now() - startTime
      
      // 页面应该在 3 秒内加载完成
      expect(loadTime).toBeLessThan(3000)
    })

    test('页面切换应该流畅', async ({ page }) => {
      const startTime = Date.now()
      
      // 快速切换多个页面
      await page.click('text=关于我们')
      await page.waitForSelector('text=开发者信息')
      
      await page.click('text=帮助中心')
      await page.waitForSelector('input[placeholder*="搜索帮助内容"]')
      
      await page.click('text=收藏管理')
      await page.waitForSelector('text=添加收藏')
      
      const switchTime = Date.now() - startTime
      
      // 页面切换应该在 2 秒内完成
      expect(switchTime).toBeLessThan(2000)
    })
  })
})