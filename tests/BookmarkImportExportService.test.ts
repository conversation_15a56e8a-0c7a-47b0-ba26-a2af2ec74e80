// 收藏导入导出服务测试

import { describe, it, expect, beforeEach, vi } from 'vitest'
import { bookmarkImportExportService } from '../src/services/BookmarkImportExportService'
import { bookmarkService } from '../src/services/bookmarkService'
import type { Bookmark, BookmarkInput } from '../src/types'

// Mock bookmarkService
vi.mock('../src/services/bookmarkService', () => ({
  bookmarkService: {
    getBookmarks: vi.fn(),
    saveBookmark: vi.fn(),
    findBookmarkByUrl: vi.fn()
  }
}))

// Mock ValidationUtils
vi.mock('../src/utils/validation', () => ({
  ValidationUtils: {
    validateBookmarkInput: vi.fn(() => ({ isValid: true, errors: [] }))
  }
}))

describe('BookmarkImportExportService 测试', () => {
  const mockBookmarks: Bookmark[] = [
    {
      id: '1',
      type: 'url',
      title: '测试收藏1',
      url: 'https://example1.com',
      description: '测试描述1',
      content: '',
      category: '技术',
      tags: ['测试', 'JavaScript'],
      favicon: '',
      createdAt: new Date('2024-01-01'),
      updatedAt: new Date('2024-01-01'),
      metadata: {
        pageTitle: '测试页面1',
        siteName: 'example1.com',
        publishDate: new Date('2024-01-01'),
        aiGenerated: false
      }
    },
    {
      id: '2',
      type: 'text',
      title: '测试收藏2',
      url: 'https://example2.com',
      description: '测试描述2',
      content: '测试内容2',
      category: '学习',
      tags: ['笔记'],
      favicon: '',
      createdAt: new Date('2024-01-02'),
      updatedAt: new Date('2024-01-02'),
      metadata: {
        pageTitle: '测试页面2',
        siteName: 'example2.com',
        publishDate: new Date('2024-01-02'),
        wordCount: 100,
        aiGenerated: false
      }
    }
  ]

  beforeEach(() => {
    vi.clearAllMocks()
    vi.mocked(bookmarkService.getBookmarks).mockResolvedValue(mockBookmarks)
  })

  describe('导出功能测试', () => {
    it('应该能够导出JSON格式', async () => {
      const options = {
        format: 'json' as const,
        includeContent: true,
        includeMetadata: true
      }

      const result = await bookmarkImportExportService.exportBookmarks(options)

      expect(result.success).toBe(true)
      expect(result.format).toBe('json')
      expect(result.itemCount).toBe(2)
      expect(result.filename).toMatch(/bookmarks_\d{8}\.json/)
      
      const exportData = JSON.parse(result.data as string)
      expect(exportData.version).toBe('1.0')
      expect(exportData.totalCount).toBe(2)
      expect(exportData.bookmarks).toHaveLength(2)
      expect(exportData.bookmarks[0].title).toBe('测试收藏1')
      expect(exportData.bookmarks[0].content).toBeDefined()
      expect(exportData.bookmarks[0].metadata).toBeDefined()
    })

    it('应该能够导出CSV格式', async () => {
      const options = {
        format: 'csv' as const,
        includeContent: false,
        includeMetadata: false
      }

      const result = await bookmarkImportExportService.exportBookmarks(options)

      expect(result.success).toBe(true)
      expect(result.format).toBe('csv')
      expect(result.itemCount).toBe(2)
      expect(result.filename).toMatch(/bookmarks_\d{8}\.csv/)
      
      const csvData = result.data as string
      const lines = csvData.split('\n')
      expect(lines[0]).toContain('ID,类型,标题,URL,描述,分类,标签,创建时间,更新时间')
      expect(lines[1]).toContain('1,url,测试收藏1,https://example1.com')
      expect(lines[2]).toContain('2,text,测试收藏2,https://example2.com')
    })

    it('应该能够导出HTML格式', async () => {
      const options = {
        format: 'html' as const,
        includeContent: true,
        includeMetadata: false
      }

      const result = await bookmarkImportExportService.exportBookmarks(options)

      expect(result.success).toBe(true)
      expect(result.format).toBe('html')
      expect(result.itemCount).toBe(2)
      expect(result.filename).toMatch(/bookmarks_\d{8}\.html/)
      
      const htmlData = result.data as string
      expect(htmlData).toContain('<!DOCTYPE html>')
      expect(htmlData).toContain('收藏导出')
      expect(htmlData).toContain('测试收藏1')
      expect(htmlData).toContain('https://example1.com')
      expect(htmlData).toContain('技术')
      expect(htmlData).toContain('学习')
    })

    it('应该能够按分类筛选导出', async () => {
      const options = {
        format: 'json' as const,
        categories: ['技术']
      }

      const result = await bookmarkImportExportService.exportBookmarks(options)

      expect(result.success).toBe(true)
      expect(result.itemCount).toBe(1)
      
      const exportData = JSON.parse(result.data as string)
      expect(exportData.bookmarks).toHaveLength(1)
      expect(exportData.bookmarks[0].category).toBe('技术')
    })

    it('应该能够按日期范围筛选导出', async () => {
      const options = {
        format: 'json' as const,
        dateRange: {
          start: new Date('2024-01-01'),
          end: new Date('2024-01-01')
        }
      }

      const result = await bookmarkImportExportService.exportBookmarks(options)

      expect(result.success).toBe(true)
      expect(result.itemCount).toBe(1)
      
      const exportData = JSON.parse(result.data as string)
      expect(exportData.bookmarks).toHaveLength(1)
      expect(exportData.bookmarks[0].title).toBe('测试收藏1')
    })

    it('应该在没有数据时抛出错误', async () => {
      vi.mocked(bookmarkService.getBookmarks).mockResolvedValue([])

      const options = {
        format: 'json' as const
      }

      await expect(bookmarkImportExportService.exportBookmarks(options))
        .rejects.toThrow('没有找到符合条件的收藏数据')
    })

    it('应该调用进度回调', async () => {
      const progressCallback = vi.fn()
      const options = {
        format: 'json' as const
      }

      await bookmarkImportExportService.exportBookmarks(options, progressCallback)

      expect(progressCallback).toHaveBeenCalledWith(0, '开始导出收藏数据...')
      expect(progressCallback).toHaveBeenCalledWith(20, '获取收藏数据...')
      expect(progressCallback).toHaveBeenCalledWith(100, '导出完成')
    })
  })

  describe('导入功能测试', () => {
    const mockImportData = [
      {
        type: 'url',
        title: '导入收藏1',
        url: 'https://import1.com',
        description: '导入描述1',
        category: '导入分类',
        tags: ['导入']
      },
      {
        type: 'url',
        title: '导入收藏2',
        url: 'https://import2.com',
        description: '导入描述2',
        category: '导入分类',
        tags: ['导入']
      }
    ]

    beforeEach(() => {
      vi.mocked(bookmarkService.saveBookmark).mockResolvedValue('new-id')
      vi.mocked(bookmarkService.findBookmarkByUrl).mockResolvedValue(null)
    })

    it('应该能够导入JSON数据', async () => {
      const jsonData = JSON.stringify({
        version: '1.0',
        bookmarks: mockImportData
      })

      const options = {
        source: 'json' as const,
        skipDuplicates: false,
        validateData: false
      }

      const result = await bookmarkImportExportService.importBookmarks(jsonData, options)

      expect(result.success).toBe(true)
      expect(result.totalItems).toBe(2)
      expect(result.importedItems).toBe(2)
      expect(result.skippedItems).toBe(0)
      expect(result.errorItems).toBe(0)
      expect(bookmarkService.saveBookmark).toHaveBeenCalledTimes(2)
    })

    it('应该能够导入CSV数据', async () => {
      const csvData = `标题,URL,描述,分类,标签
导入收藏1,https://import1.com,导入描述1,导入分类,导入
导入收藏2,https://import2.com,导入描述2,导入分类,导入`

      const options = {
        source: 'csv' as const,
        skipDuplicates: false,
        validateData: false
      }

      const result = await bookmarkImportExportService.importBookmarks(csvData, options)

      expect(result.success).toBe(true)
      expect(result.totalItems).toBe(2)
      expect(result.importedItems).toBe(2)
      expect(bookmarkService.saveBookmark).toHaveBeenCalledTimes(2)
    })

    it('应该能够导入HTML数据', async () => {
      const htmlData = `
        <html>
          <body>
            <a href="https://import1.com">导入收藏1</a>
            <a href="https://import2.com">导入收藏2</a>
          </body>
        </html>
      `

      const options = {
        source: 'html' as const,
        skipDuplicates: false,
        validateData: false
      }

      const result = await bookmarkImportExportService.importBookmarks(htmlData, options)

      expect(result.success).toBe(true)
      expect(result.totalItems).toBe(2)
      expect(result.importedItems).toBe(2)
      expect(bookmarkService.saveBookmark).toHaveBeenCalledTimes(2)
    })

    it('应该能够跳过重复项', async () => {
      // 模拟第一个URL已存在
      vi.mocked(bookmarkService.findBookmarkByUrl)
        .mockImplementation((url) => {
          if (url === 'https://import1.com') {
            return Promise.resolve(mockBookmarks[0])
          }
          return Promise.resolve(null)
        })

      const jsonData = JSON.stringify({
        bookmarks: mockImportData
      })

      const options = {
        source: 'json' as const,
        skipDuplicates: true,
        validateData: false
      }

      const result = await bookmarkImportExportService.importBookmarks(jsonData, options)

      expect(result.success).toBe(true)
      expect(result.totalItems).toBe(2)
      expect(result.importedItems).toBe(1)
      expect(result.skippedItems).toBe(1)
      expect(bookmarkService.saveBookmark).toHaveBeenCalledTimes(1)
    })

    it('应该能够设置默认分类', async () => {
      const importDataWithoutCategory = [
        {
          type: 'url',
          title: '导入收藏1',
          url: 'https://import1.com',
          category: '默认分类',
          tags: []
        }
      ]

      const jsonData = JSON.stringify({
        bookmarks: importDataWithoutCategory
      })

      const options = {
        source: 'json' as const,
        defaultCategory: '自定义分类',
        validateData: false
      }

      await bookmarkImportExportService.importBookmarks(jsonData, options)

      expect(bookmarkService.saveBookmark).toHaveBeenCalledWith(
        expect.objectContaining({
          category: '自定义分类'
        })
      )
    })

    it('应该处理导入错误', async () => {
      vi.mocked(bookmarkService.saveBookmark)
        .mockRejectedValueOnce(new Error('保存失败'))
        .mockResolvedValueOnce('success-id')

      const jsonData = JSON.stringify({
        bookmarks: mockImportData
      })

      const options = {
        source: 'json' as const,
        validateData: false
      }

      const result = await bookmarkImportExportService.importBookmarks(jsonData, options)

      expect(result.success).toBe(true)
      expect(result.totalItems).toBe(2)
      expect(result.importedItems).toBe(1)
      expect(result.errorItems).toBe(1)
      expect(result.errors).toHaveLength(1)
      expect(result.errors[0]).toContain('保存失败')
    })

    it('应该调用进度回调', async () => {
      const progressCallback = vi.fn()
      const jsonData = JSON.stringify({
        bookmarks: mockImportData
      })

      const options = {
        source: 'json' as const,
        validateData: false
      }

      await bookmarkImportExportService.importBookmarks(jsonData, options, progressCallback)

      expect(progressCallback).toHaveBeenCalledWith(0, '开始导入收藏数据...')
      expect(progressCallback).toHaveBeenCalledWith(10, '解析导入数据...')
      expect(progressCallback).toHaveBeenCalledWith(100, '导入完成')
    })

    it('应该在解析失败时抛出错误', async () => {
      const invalidJson = '{ invalid json }'
      const options = {
        source: 'json' as const
      }

      await expect(bookmarkImportExportService.importBookmarks(invalidJson, options))
        .rejects.toThrow('解析JSON失败')
    })
  })

  describe('重复检测功能测试', () => {
    it('应该能够检测重复收藏', async () => {
      const importData: BookmarkInput[] = [
        {
          type: 'url',
          title: '重复收藏',
          url: 'https://example1.com', // 与mockBookmarks[0]重复
          category: '测试',
          tags: []
        },
        {
          type: 'url',
          title: '新收藏',
          url: 'https://new.com',
          category: '测试',
          tags: []
        }
      ]

      const duplicates = await bookmarkImportExportService.detectDuplicates(importData)

      expect(duplicates).toHaveLength(1)
      expect(duplicates[0]).toBe('1')
    })

    it('应该在检测失败时返回空数组', async () => {
      vi.mocked(bookmarkService.getBookmarks).mockRejectedValue(new Error('获取失败'))

      const importData: BookmarkInput[] = [
        {
          type: 'url',
          title: '测试收藏',
          url: 'https://test.com',
          category: '测试',
          tags: []
        }
      ]

      const duplicates = await bookmarkImportExportService.detectDuplicates(importData)

      expect(duplicates).toEqual([])
    })
  })

  describe('工具方法测试', () => {
    it('应该正确转义CSV值', async () => {
      const bookmarkWithComma: Bookmark = {
        ...mockBookmarks[0],
        title: '包含,逗号的标题',
        description: '包含"引号"的描述'
      }

      vi.mocked(bookmarkService.getBookmarks).mockResolvedValue([bookmarkWithComma])

      const options = {
        format: 'csv' as const
      }

      const result = await bookmarkImportExportService.exportBookmarks(options)
      const csvData = result.data as string

      expect(csvData).toContain('"包含,逗号的标题"')
      expect(csvData).toContain('"包含""引号""的描述"')
    })

    it('应该正确转义HTML', async () => {
      const bookmarkWithHtml: Bookmark = {
        ...mockBookmarks[0],
        title: '<script>alert("xss")</script>',
        description: 'HTML & 特殊字符'
      }

      vi.mocked(bookmarkService.getBookmarks).mockResolvedValue([bookmarkWithHtml])

      const options = {
        format: 'html' as const
      }

      const result = await bookmarkImportExportService.exportBookmarks(options)
      const htmlData = result.data as string

      expect(htmlData).toContain('&lt;script&gt;alert("xss")&lt;/script&gt;')
      expect(htmlData).toContain('HTML &amp; 特殊字符')
    })

    it('应该正确格式化日期', async () => {
      const options = {
        format: 'json' as const
      }

      const result = await bookmarkImportExportService.exportBookmarks(options)

      expect(result.filename).toMatch(/bookmarks_\d{8}\.json/)
    })
  })
})