// AddBookmarkModal组件测试

import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { vi, describe, it, expect, beforeEach } from 'vitest'
import AddBookmarkModal from '../src/components/AddBookmarkModal'
import { BookmarkInput } from '../src/types'

// Mock组件的props
const mockProps = {
  isOpen: true,
  onSave: vi.fn(),
  onCancel: vi.fn(),
  loading: false
}

describe('AddBookmarkModal组件测试', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('基本渲染测试', () => {
    it('应该在isOpen为true时正确渲染', () => {
      render(<AddBookmarkModal {...mockProps} />)
      
      expect(screen.getByText('添加收藏')).toBeInTheDocument()
      expect(screen.getByText(/收藏类型/)).toBeInTheDocument()
      expect(screen.getByText('网页链接')).toBeInTheDocument()
      expect(screen.getByText('文本摘录')).toBeInTheDocument()
      expect(screen.getByPlaceholderText('请输入收藏标题')).toBeInTheDocument()
    })

    it('应该在isOpen为false时不渲染', () => {
      render(<AddBookmarkModal {...mockProps} isOpen={false} />)
      
      expect(screen.queryByText('添加收藏')).not.toBeInTheDocument()
    })

    it('应该显示正确的表单字段', () => {
      render(<AddBookmarkModal {...mockProps} />)
      
      expect(screen.getByText('标题 *')).toBeInTheDocument()
      expect(screen.getByText('网址 *')).toBeInTheDocument()
      expect(screen.getByText('描述')).toBeInTheDocument()
      expect(screen.getByText('分类 *')).toBeInTheDocument()
      expect(screen.getByText('标签')).toBeInTheDocument()
    })
  })

  describe('类型切换测试', () => {
    it('应该默认选择URL类型', () => {
      render(<AddBookmarkModal {...mockProps} />)
      
      const urlRadio = screen.getByRole('radio', { name: /网页链接/ })
      const textRadio = screen.getByRole('radio', { name: /文本摘录/ })
      
      expect(urlRadio).toBeChecked()
      expect(textRadio).not.toBeChecked()
    })

    it('应该能够切换到文本类型', () => {
      render(<AddBookmarkModal {...mockProps} />)
      
      const textRadio = screen.getByRole('radio', { name: /文本摘录/ })
      fireEvent.click(textRadio)
      
      expect(textRadio).toBeChecked()
      expect(screen.getByPlaceholderText('请输入要收藏的文本内容')).toBeInTheDocument()
    })

    it('切换到文本类型时应该显示文本内容输入框', () => {
      render(<AddBookmarkModal {...mockProps} />)
      
      const textRadio = screen.getByRole('radio', { name: /文本摘录/ })
      fireEvent.click(textRadio)
      
      expect(screen.getByText('文本内容 *')).toBeInTheDocument()
      expect(screen.getByPlaceholderText('请输入要收藏的文本内容')).toBeInTheDocument()
    })

    it('切换到URL类型时应该隐藏文本内容输入框', () => {
      render(<AddBookmarkModal {...mockProps} />)
      
      // 先切换到文本类型
      const textRadio = screen.getByRole('radio', { name: /文本摘录/ })
      fireEvent.click(textRadio)
      
      // 再切换回URL类型
      const urlRadio = screen.getByRole('radio', { name: /网页链接/ })
      fireEvent.click(urlRadio)
      
      expect(screen.queryByText('文本内容 *')).not.toBeInTheDocument()
    })
  })

  describe('表单验证测试', () => {
    it('应该验证标题为必填项', async () => {
      render(<AddBookmarkModal {...mockProps} />)
      
      const saveButton = screen.getByText('保存收藏')
      fireEvent.click(saveButton)
      
      await waitFor(() => {
        expect(screen.getByText('标题不能为空')).toBeInTheDocument()
      })
      
      expect(mockProps.onSave).not.toHaveBeenCalled()
    })

    it('应该验证URL类型时URL为必填项', async () => {
      render(<AddBookmarkModal {...mockProps} />)
      
      // 填写标题但不填写URL
      const titleInput = screen.getByPlaceholderText('请输入收藏标题')
      fireEvent.change(titleInput, { target: { value: '测试标题' } })
      
      const saveButton = screen.getByText('保存收藏')
      fireEvent.click(saveButton)
      
      await waitFor(() => {
        expect(screen.getByText('URL不能为空')).toBeInTheDocument()
      })
      
      expect(mockProps.onSave).not.toHaveBeenCalled()
    })

    it('应该验证文本类型时文本内容为必填项', async () => {
      render(<AddBookmarkModal {...mockProps} />)
      
      // 切换到文本类型
      const textRadio = screen.getByRole('radio', { name: /文本摘录/ })
      fireEvent.click(textRadio)
      
      // 填写标题但不填写文本内容
      const titleInput = screen.getByPlaceholderText('请输入收藏标题')
      fireEvent.change(titleInput, { target: { value: '测试标题' } })
      
      const saveButton = screen.getByText('保存收藏')
      fireEvent.click(saveButton)
      
      await waitFor(() => {
        expect(screen.getByText('文本内容不能为空')).toBeInTheDocument()
      })
      
      expect(mockProps.onSave).not.toHaveBeenCalled()
    })

    it('应该验证URL格式', async () => {
      render(<AddBookmarkModal {...mockProps} />)
      
      const titleInput = screen.getByPlaceholderText('请输入收藏标题')
      const urlInput = screen.getByPlaceholderText('https://example.com')
      
      fireEvent.change(titleInput, { target: { value: '测试标题' } })
      fireEvent.change(urlInput, { target: { value: '无效的URL' } })
      
      const saveButton = screen.getByText('保存收藏')
      fireEvent.click(saveButton)
      
      await waitFor(() => {
        expect(screen.getByText('请输入有效的URL地址')).toBeInTheDocument()
      })
      
      expect(mockProps.onSave).not.toHaveBeenCalled()
    })

    it('应该验证标题长度限制', async () => {
      render(<AddBookmarkModal {...mockProps} />)
      
      const titleInput = screen.getByPlaceholderText('请输入收藏标题')
      const longTitle = 'a'.repeat(201) // 超过200字符限制
      
      fireEvent.change(titleInput, { target: { value: longTitle } })
      
      const saveButton = screen.getByText('保存收藏')
      fireEvent.click(saveButton)
      
      await waitFor(() => {
        expect(screen.getByText('标题长度不能超过200个字符')).toBeInTheDocument()
      })
      
      expect(mockProps.onSave).not.toHaveBeenCalled()
    })
  })

  describe('标签管理测试', () => {
    it('应该能够添加标签', () => {
      render(<AddBookmarkModal {...mockProps} />)
      
      const tagInput = screen.getByPlaceholderText('输入标签后按回车添加')
      const addButton = screen.getByText('添加')
      
      fireEvent.change(tagInput, { target: { value: '测试标签' } })
      fireEvent.click(addButton)
      
      expect(screen.getByText('测试标签')).toBeInTheDocument()
    })

    it('应该能够通过回车键添加标签', async () => {
      render(<AddBookmarkModal {...mockProps} />)
      
      const tagInput = screen.getByPlaceholderText('输入标签后按回车添加')
      
      fireEvent.change(tagInput, { target: { value: '测试标签' } })
      fireEvent.keyDown(tagInput, { key: 'Enter', code: 'Enter' })
      
      await waitFor(() => {
        expect(screen.getByTestId('tag-测试标签')).toBeInTheDocument()
      })
    })

    it('应该能够删除标签', async () => {
      render(<AddBookmarkModal {...mockProps} />)
      
      const tagInput = screen.getByPlaceholderText('输入标签后按回车添加')
      
      // 添加标签
      fireEvent.change(tagInput, { target: { value: '测试标签' } })
      fireEvent.keyDown(tagInput, { key: 'Enter', code: 'Enter' })
      
      await waitFor(() => {
        expect(screen.getByTestId('tag-测试标签')).toBeInTheDocument()
      })
      
      // 删除标签
      const deleteButton = screen.getByLabelText('删除标签 测试标签')
      fireEvent.click(deleteButton)
      
      await waitFor(() => {
        expect(screen.queryByTestId('tag-测试标签')).not.toBeInTheDocument()
      })
    })

    it('不应该添加重复的标签', async () => {
      render(<AddBookmarkModal {...mockProps} />)
      
      const tagInput = screen.getByPlaceholderText('输入标签后按回车添加')
      
      // 添加第一个标签
      fireEvent.change(tagInput, { target: { value: '测试标签' } })
      fireEvent.keyDown(tagInput, { key: 'Enter', code: 'Enter' })
      
      await waitFor(() => {
        expect(screen.getByTestId('tag-测试标签')).toBeInTheDocument()
      })
      
      // 尝试添加相同的标签
      fireEvent.change(tagInput, { target: { value: '测试标签' } })
      fireEvent.keyDown(tagInput, { key: 'Enter', code: 'Enter' })
      
      // 应该只有一个标签
      const tagsContainer = screen.getByTestId('tags-container')
      const tags = tagsContainer.querySelectorAll('[data-testid^="tag-"]')
      expect(tags).toHaveLength(1)
    })
  })

  describe('表单提交测试', () => {
    it('应该能够提交有效的URL类型收藏', async () => {
      render(<AddBookmarkModal {...mockProps} />)
      
      const titleInput = screen.getByPlaceholderText('请输入收藏标题')
      const urlInput = screen.getByPlaceholderText('https://example.com')
      
      fireEvent.change(titleInput, { target: { value: '测试标题' } })
      fireEvent.change(urlInput, { target: { value: 'https://example.com' } })
      
      const saveButton = screen.getByText('保存收藏')
      fireEvent.click(saveButton)
      
      await waitFor(() => {
        expect(mockProps.onSave).toHaveBeenCalledWith({
          type: 'url',
          title: '测试标题',
          url: 'https://example.com',
          category: '默认分类',
          tags: [],
          metadata: {
            aiGenerated: false
          }
        })
      })
    })

    it('应该能够提交有效的文本类型收藏', async () => {
      render(<AddBookmarkModal {...mockProps} />)
      
      // 切换到文本类型
      const textRadio = screen.getByRole('radio', { name: /文本摘录/ })
      fireEvent.click(textRadio)
      
      const titleInput = screen.getByPlaceholderText('请输入收藏标题')
      const contentInput = screen.getByPlaceholderText('请输入要收藏的文本内容')
      
      fireEvent.change(titleInput, { target: { value: '测试文本' } })
      fireEvent.change(contentInput, { target: { value: '这是测试文本内容' } })
      
      const saveButton = screen.getByText('保存收藏')
      fireEvent.click(saveButton)
      
      await waitFor(() => {
        expect(mockProps.onSave).toHaveBeenCalledWith({
          type: 'text',
          title: '测试文本',
          content: '这是测试文本内容',
          category: '默认分类',
          tags: [],
          metadata: {
            aiGenerated: false
          }
        })
      })
    })

    it('应该包含所有填写的字段', async () => {
      render(<AddBookmarkModal {...mockProps} />)
      
      const titleInput = screen.getByPlaceholderText('请输入收藏标题')
      const urlInput = screen.getByPlaceholderText('https://example.com')
      const descriptionInput = screen.getByPlaceholderText('请输入收藏描述（可选）')
      const categorySelect = screen.getByDisplayValue('默认分类')
      const tagInput = screen.getByPlaceholderText('输入标签后按回车添加')
      
      fireEvent.change(titleInput, { target: { value: '完整测试' } })
      fireEvent.change(urlInput, { target: { value: 'https://test.com' } })
      fireEvent.change(descriptionInput, { target: { value: '测试描述' } })
      fireEvent.change(categorySelect, { target: { value: '工作' } })
      
      // 添加标签
      fireEvent.change(tagInput, { target: { value: '标签1' } })
      fireEvent.keyDown(tagInput, { key: 'Enter', code: 'Enter' })
      
      await waitFor(() => {
        expect(screen.getByTestId('tag-标签1')).toBeInTheDocument()
      })
      
      fireEvent.change(tagInput, { target: { value: '标签2' } })
      fireEvent.keyDown(tagInput, { key: 'Enter', code: 'Enter' })
      
      await waitFor(() => {
        expect(screen.getByTestId('tag-标签2')).toBeInTheDocument()
      })
      
      const saveButton = screen.getByText('保存收藏')
      fireEvent.click(saveButton)
      
      await waitFor(() => {
        expect(mockProps.onSave).toHaveBeenCalledWith({
          type: 'url',
          title: '完整测试',
          url: 'https://test.com',
          description: '测试描述',
          category: '工作',
          tags: ['标签1', '标签2'],
          metadata: {
            aiGenerated: false
          }
        })
      })
    })
  })

  describe('初始数据测试', () => {
    it('应该使用初始数据预填充表单', () => {
      const initialData = {
        type: 'text' as const,
        title: '初始标题',
        content: '初始内容',
        description: '初始描述',
        category: '学习',
        tags: ['初始标签']
      }
      
      render(<AddBookmarkModal {...mockProps} initialData={initialData} />)
      
      expect(screen.getByDisplayValue('初始标题')).toBeInTheDocument()
      expect(screen.getByDisplayValue('初始内容')).toBeInTheDocument()
      expect(screen.getByDisplayValue('初始描述')).toBeInTheDocument()
      expect(screen.getByDisplayValue('学习')).toBeInTheDocument()
      expect(screen.getByTestId('tag-初始标签')).toBeInTheDocument()
      
      const textRadio = screen.getByRole('radio', { name: /文本摘录/ })
      expect(textRadio).toBeChecked()
    })
  })

  describe('交互测试', () => {
    it('应该能够取消操作', () => {
      render(<AddBookmarkModal {...mockProps} />)
      
      const cancelButton = screen.getByText('取消')
      fireEvent.click(cancelButton)
      
      expect(mockProps.onCancel).toHaveBeenCalled()
    })

    it('应该能够通过遮罩层取消操作', () => {
      render(<AddBookmarkModal {...mockProps} />)
      
      // 对于shadcn Dialog，我们可以通过按ESC键来关闭
      fireEvent.keyDown(document, { key: 'Escape', code: 'Escape' })
      
      expect(mockProps.onCancel).toHaveBeenCalled()
    })

    it('应该能够通过X按钮取消操作', () => {
      render(<AddBookmarkModal {...mockProps} />)
      
      // shadcn Dialog的关闭按钮有特定的名称
      const closeButton = screen.getByRole('button', { name: 'Close' })
      fireEvent.click(closeButton)
      
      expect(mockProps.onCancel).toHaveBeenCalled()
    })
  })

  describe('加载状态测试', () => {
    it('应该在加载时禁用表单元素', () => {
      render(<AddBookmarkModal {...mockProps} loading={true} />)
      
      const titleInput = screen.getByPlaceholderText('请输入收藏标题')
      const saveButton = screen.getByText('保存中...')
      const cancelButton = screen.getByText('取消')
      
      expect(titleInput).toBeDisabled()
      expect(saveButton).toBeDisabled()
      expect(cancelButton).toBeDisabled()
    })

    it('应该在加载时显示加载指示器', () => {
      render(<AddBookmarkModal {...mockProps} loading={true} />)
      
      expect(screen.getByText('保存中...')).toBeInTheDocument()
      expect(document.querySelector('.animate-spin')).toBeInTheDocument()
    })
  })

  describe('AI建议功能测试', () => {
    it('应该显示AI建议按钮', () => {
      render(<AddBookmarkModal {...mockProps} />)
      
      expect(screen.getByText('AI建议')).toBeInTheDocument()
    })

    it('应该在没有内容时禁用AI建议按钮', () => {
      render(<AddBookmarkModal {...mockProps} />)
      
      const aiButton = screen.getByText('AI建议')
      expect(aiButton).toBeDisabled()
    })

    it('应该在有内容时启用AI建议按钮', () => {
      render(<AddBookmarkModal {...mockProps} />)
      
      const titleInput = screen.getByPlaceholderText('请输入收藏标题')
      fireEvent.change(titleInput, { target: { value: '测试标题' } })
      
      const aiButton = screen.getByText('AI建议')
      expect(aiButton).not.toBeDisabled()
    })
  })
})