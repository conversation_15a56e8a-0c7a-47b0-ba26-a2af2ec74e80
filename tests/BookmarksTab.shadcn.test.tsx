// BookmarksTab shadcn重构测试
import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { vi, describe, it, expect, beforeEach } from 'vitest'

// 模拟Chrome扩展API
const mockChrome = {
  runtime: {
    sendMessage: vi.fn()
  }
}

// @ts-ignore
global.chrome = mockChrome

// 模拟window.matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: vi.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: vi.fn(), // deprecated
    removeListener: vi.fn(), // deprecated
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn(),
  })),
})

// 模拟localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
}
Object.defineProperty(window, 'localStorage', {
  value: localStorageMock
})

// 模拟组件依赖
vi.mock('../src/components/TruncatedTitle', () => ({
  default: ({ title, className }: { title: string; className?: string }) => (
    <span className={className}>{title}</span>
  )
}))

vi.mock('../src/components/BookmarkEditModal', () => ({
  default: ({ isOpen, bookmark }: { isOpen: boolean; bookmark: any }) => 
    isOpen ? <div data-testid="edit-modal">编辑模态: {bookmark?.title}</div> : null
}))

vi.mock('../src/components/AddBookmarkModal', () => ({
  default: ({ isOpen }: { isOpen: boolean }) => 
    isOpen ? <div data-testid="add-modal">添加收藏模态</div> : null
}))

vi.mock('../src/components/DeleteConfirmModal', () => ({
  default: ({ isOpen, bookmark }: { isOpen: boolean; bookmark: any }) => 
    isOpen ? <div data-testid="delete-modal">删除确认: {bookmark?.title}</div> : null
}))

vi.mock('../src/components/ViewModeSelector', () => ({
  default: ({ currentMode, onModeChange }: { currentMode: string; onModeChange: (mode: string) => void }) => (
    <div data-testid="view-mode-selector">
      <button onClick={() => onModeChange('row')}>行视图</button>
      <button onClick={() => onModeChange('compact')}>紧凑视图</button>
      <button onClick={() => onModeChange('card')}>卡片视图</button>
      <span>当前: {currentMode}</span>
    </div>
  )
}))

vi.mock('../src/components/BookmarkRow', () => ({
  default: ({ bookmark, onEdit, onDelete }: { bookmark: any; onEdit: (b: any) => void; onDelete: (b: any) => void }) => (
    <div data-testid="bookmark-row">
      <span>{bookmark.title}</span>
      <button onClick={() => onEdit(bookmark)}>编辑</button>
      <button onClick={() => onDelete(bookmark)}>删除</button>
    </div>
  )
}))

vi.mock('../src/components/BookmarkCompact', () => ({
  default: ({ bookmark, onEdit, onDelete }: { bookmark: any; onEdit: (b: any) => void; onDelete: (b: any) => void }) => (
    <div data-testid="bookmark-compact">
      <span>{bookmark.title}</span>
      <button onClick={() => onEdit(bookmark)}>编辑</button>
      <button onClick={() => onDelete(bookmark)}>删除</button>
    </div>
  )
}))

vi.mock('../src/components/VirtualBookmarkList', () => ({
  default: ({ bookmarks }: { bookmarks: any[] }) => (
    <div data-testid="virtual-list">虚拟列表: {bookmarks.length} 项</div>
  )
}))

vi.mock('../src/hooks/useViewMode', () => ({
  useViewMode: () => ({
    viewMode: 'card',
    setViewMode: vi.fn(),
    isLoading: false
  })
}))

vi.mock('../src/utils/layoutStability', () => ({
  useViewSwitchStability: () => ({
    containerRef: { current: null },
    displayView: 'card',
    isTransitioning: false
  }),
  useScrollPositionLock: () => ({
    lockScrollPosition: vi.fn()
  })
}))

vi.mock('../src/hooks/useAdvancedSearch', () => ({
  useAdvancedSearch: () => ({
    query: '',
    setQuery: vi.fn(),
    results: [
      { item: { id: '1', title: '测试收藏1', url: 'https://example.com', category: '技术', tags: ['测试'], createdAt: new Date().toISOString() } },
      { item: { id: '2', title: '测试收藏2', url: 'https://example2.com', category: '学习', tags: ['学习'], createdAt: new Date().toISOString() } }
    ],
    suggestions: [],
    isSearching: false,
    hasResults: true,
    totalResults: 2,
    searchTime: 10,
    addFilter: vi.fn(),
    clearFilters: vi.fn()
  })
}))

vi.mock('../src/options/hooks/useTheme', () => ({
  useTheme: () => ({
    theme: 'light',
    actualTheme: 'light',
    setTheme: vi.fn()
  })
}))

vi.mock('../src/options/hooks/useResponsive', () => ({
  useResponsive: () => ({
    isMobile: false,
    isTablet: false,
    breakpoint: 'lg',
    getResponsiveValue: vi.fn((values, defaultValue) => defaultValue)
  })
}))

// 导入要测试的组件
import OptionsApp from '../src/options/OptionsApp'

describe('BookmarksTab shadcn重构测试', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    
    // 模拟成功的API响应
    mockChrome.runtime.sendMessage.mockResolvedValue({
      success: true,
      data: [
        {
          id: '1',
          title: '测试收藏1',
          url: 'https://example.com',
          description: '这是一个测试收藏',
          category: '技术',
          tags: ['测试', 'shadcn'],
          createdAt: new Date().toISOString()
        },
        {
          id: '2',
          title: '测试收藏2',
          url: 'https://example2.com',
          description: '另一个测试收藏',
          category: '学习',
          tags: ['学习'],
          createdAt: new Date().toISOString()
        }
      ]
    })
  })

  it('应该使用shadcn Card组件渲染主容器', async () => {
    render(<OptionsApp />)
    
    // 等待数据加载
    await waitFor(() => {
      expect(screen.queryByText('加载收藏数据中...')).not.toBeInTheDocument()
    })
    
    // 验证Card组件的存在（通过类名）
    const cardElement = document.querySelector('[class*="border"][class*="rounded"]')
    expect(cardElement).toBeInTheDocument()
  })

  it('应该使用shadcn Button组件渲染操作按钮', async () => {
    render(<OptionsApp />)
    
    await waitFor(() => {
      expect(screen.queryByText('加载收藏数据中...')).not.toBeInTheDocument()
    })
    
    // 验证添加收藏按钮
    const addButton = screen.getByText('添加收藏')
    expect(addButton).toBeInTheDocument()
    expect(addButton.closest('button')).toHaveClass('flex', 'items-center', 'gap-2')
    
    // 验证刷新按钮
    const refreshButton = screen.getByText('刷新')
    expect(refreshButton).toBeInTheDocument()
  })

  it('应该使用shadcn Input组件渲染搜索框', async () => {
    render(<OptionsApp />)
    
    await waitFor(() => {
      expect(screen.queryByText('加载收藏数据中...')).not.toBeInTheDocument()
    })
    
    // 验证搜索输入框
    const searchInput = screen.getByPlaceholderText('搜索收藏...')
    expect(searchInput).toBeInTheDocument()
    expect(searchInput).toHaveClass('pl-10') // shadcn Input的特定样式
    
    // 验证搜索图标
    const searchIcon = document.querySelector('[class*="absolute"][class*="left-3"]')
    expect(searchIcon).toBeInTheDocument()
  })

  it('应该使用shadcn Select组件渲染分类选择器', async () => {
    render(<OptionsApp />)
    
    await waitFor(() => {
      expect(screen.queryByText('加载收藏数据中...')).not.toBeInTheDocument()
    })
    
    // 验证Select组件的触发器
    const selectTrigger = document.querySelector('[role="combobox"]')
    expect(selectTrigger).toBeInTheDocument()
  })

  it('应该使用shadcn CardHeader和CardTitle渲染标题区域', async () => {
    render(<OptionsApp />)
    
    await waitFor(() => {
      expect(screen.queryByText('加载收藏数据中...')).not.toBeInTheDocument()
    })
    
    // 验证标题
    const title = screen.getByText('收藏管理')
    expect(title).toBeInTheDocument()
    
    // 验证描述
    const description = screen.getByText('管理您的收藏内容，支持搜索、分类和多种视图模式')
    expect(description).toBeInTheDocument()
  })

  it('应该在卡片视图中使用shadcn Card组件渲染收藏项', async () => {
    render(<OptionsApp />)
    
    await waitFor(() => {
      expect(screen.queryByText('加载收藏数据中...')).not.toBeInTheDocument()
    })
    
    // 等待收藏数据加载
    await waitFor(() => {
      expect(screen.getByText('测试收藏1')).toBeInTheDocument()
    })
    
    // 验证收藏项使用Card组件
    const bookmarkCards = document.querySelectorAll('[class*="border"][class*="rounded"][class*="cursor-pointer"]')
    expect(bookmarkCards.length).toBeGreaterThan(0)
  })

  it('应该使用shadcn Button组件渲染收藏项操作按钮', async () => {
    render(<OptionsApp />)
    
    await waitFor(() => {
      expect(screen.queryByText('加载收藏数据中...')).not.toBeInTheDocument()
    })
    
    await waitFor(() => {
      expect(screen.getByText('测试收藏1')).toBeInTheDocument()
    })
    
    // 验证编辑和删除按钮使用shadcn Button组件
    const actionButtons = document.querySelectorAll('button[title*="编辑"], button[title*="删除"]')
    expect(actionButtons.length).toBeGreaterThan(0)
    
    // 验证按钮具有shadcn Button的样式类
    actionButtons.forEach(button => {
      expect(button).toHaveClass('inline-flex') // shadcn Button的基础类
    })
  })

  it('应该正确处理搜索功能', async () => {
    render(<OptionsApp />)
    
    await waitFor(() => {
      expect(screen.queryByText('加载收藏数据中...')).not.toBeInTheDocument()
    })
    
    const searchInput = screen.getByPlaceholderText('搜索收藏...')
    
    // 测试搜索输入
    fireEvent.change(searchInput, { target: { value: '测试' } })
    expect(searchInput).toHaveValue('测试')
  })

  it('应该正确处理分类筛选', async () => {
    render(<OptionsApp />)
    
    await waitFor(() => {
      expect(screen.queryByText('加载收藏数据中...')).not.toBeInTheDocument()
    })
    
    // 验证分类选择器存在
    const selectTrigger = document.querySelector('[role="combobox"]')
    expect(selectTrigger).toBeInTheDocument()
  })

  it('应该正确处理添加收藏按钮点击', async () => {
    render(<OptionsApp />)
    
    await waitFor(() => {
      expect(screen.queryByText('加载收藏数据中...')).not.toBeInTheDocument()
    })
    
    const addButton = screen.getByText('添加收藏')
    fireEvent.click(addButton)
    
    // 验证添加模态窗口打开
    await waitFor(() => {
      expect(screen.getByTestId('add-modal')).toBeInTheDocument()
    })
  })

  it('应该使用shadcn样式系统的颜色和间距', async () => {
    render(<OptionsApp />)
    
    await waitFor(() => {
      expect(screen.queryByText('加载收藏数据中...')).not.toBeInTheDocument()
    })
    
    // 验证使用了shadcn的间距系统
    const cardContent = document.querySelector('[class*="p-4"]')
    expect(cardContent).toBeInTheDocument()
    
    // 验证使用了shadcn的颜色系统
    const primaryButton = screen.getByText('添加收藏').closest('button')
    expect(primaryButton).toHaveClass('bg-primary')
  })

  it('应该保持响应式设计', async () => {
    render(<OptionsApp />)
    
    await waitFor(() => {
      expect(screen.queryByText('加载收藏数据中...')).not.toBeInTheDocument()
    })
    
    // 验证响应式类的存在
    const responsiveElements = document.querySelectorAll('[class*="sm:"], [class*="md:"], [class*="lg:"]')
    expect(responsiveElements.length).toBeGreaterThan(0)
  })
})