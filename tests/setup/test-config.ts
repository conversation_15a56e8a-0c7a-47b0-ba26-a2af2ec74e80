/**
 * 测试环境配置常量
 * 集中管理测试环境中使用的配置值
 */

export const TEST_CONFIG = {
  // 扩展相关配置
  EXTENSION_ID: 'test',
  EXTENSION_NAME: 'Universe Bag（乾坤袋）',
  EXTENSION_VERSION: '1.0.0',
  EXTENSION_DESCRIPTION: '智能收藏管理工具，支持AI自动分类和云端同步',
  
  // 动画和性能配置
  ANIMATION_FRAME_DELAY: 16, // 60fps
  
  // 视口配置
  DEFAULT_VIEWPORT: {
    width: 1920,
    height: 1080
  },
  
  // 测试数据配置
  DEFAULT_TEST_DATA: {
    bookmarks: [],
    categories: [],
    tags: [],
    appSettings: {
      autoTagging: true,
      syncEnabled: false,
      theme: 'system' as const,
      language: 'zh-CN' as const
    }
  },
  
  // 测试页面路径
  TEST_PAGES: {
    options: '/options.html',
    popup: '/popup.html',
    background: '/background.html'
  },
  
  // Chrome 扩展权限
  EXTENSION_PERMISSIONS: [
    'storage',
    'activeTab', 
    'contextMenus',
    'tabs',
    'scripting'
  ] as const,
  
  // 测试超时配置
  TIMEOUTS: {
    default: 5000,
    animation: 1000,
    api_call: 3000
  }
} as const

// 类型导出
export type TestConfig = typeof TEST_CONFIG
export type ThemeType = typeof TEST_CONFIG.DEFAULT_TEST_DATA.appSettings.theme
export type LanguageType = typeof TEST_CONFIG.DEFAULT_TEST_DATA.appSettings.language