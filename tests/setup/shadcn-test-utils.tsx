/**
 * shadcn/ui 组件测试工具函数
 * 用于处理shadcn组件的样式验证和测试
 */

import { vi } from 'vitest'
import { screen, within } from '@testing-library/react'

/**
 * shadcn 样式类映射
 * 将语义化的样式名称映射到实际的 Tailwind CSS 类
 */
export const shadcnStyleMap = {
  // 按钮样式
  'button-primary': 'bg-primary text-primary-foreground hover:bg-primary/90',
  'button-secondary': 'bg-secondary text-secondary-foreground hover:bg-secondary/80',
  'button-outline': 'border border-input bg-background hover:bg-accent hover:text-accent-foreground',
  'button-ghost': 'hover:bg-accent hover:text-accent-foreground',
  'button-destructive': 'bg-destructive text-destructive-foreground hover:bg-destructive/90',
  
  // 文本颜色
  'text-primary': 'text-foreground',
  'text-secondary': 'text-muted-foreground',
  'text-muted': 'text-muted-foreground',
  'text-accent': 'text-accent-foreground',
  
  // 背景颜色
  'bg-primary': 'bg-primary',
  'bg-secondary': 'bg-secondary',
  'bg-accent': 'bg-accent',
  'bg-muted': 'bg-muted',
  'bg-card': 'bg-card',
  'bg-background': 'bg-background',
  
  // 边框颜色
  'border-primary': 'border-primary',
  'border-secondary': 'border-border',
  'border-accent': 'border-accent',
  'border-muted': 'border-border',
  
  // 焦点样式
  'focus-ring': 'focus:ring-2 focus:ring-ring focus:ring-offset-2',
  'focus-outline': 'focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2'
}

/**
 * 检查元素是否包含shadcn样式类
 * @param element - DOM元素
 * @param styleKey - 样式键名
 * @returns boolean
 */
export const hasShadcnStyle = (element: Element, styleKey: string): boolean => {
  const expectedClasses = shadcnStyleMap[styleKey as keyof typeof shadcnStyleMap]
  if (!expectedClasses) {
    console.warn(`未找到样式键: ${styleKey}`)
    return false
  }
  
  const classList = element.className
  const classArray = expectedClasses.split(' ')
  
  // 检查是否包含所有必需的类
  return classArray.some(cls => classList.includes(cls))
}

/**
 * 验证shadcn按钮样式
 * @param element - 按钮元素
 * @param variant - 按钮变体
 */
export const expectShadcnButton = (element: Element, variant: 'primary' | 'secondary' | 'outline' | 'ghost' | 'destructive' = 'primary') => {
  const styleKey = `button-${variant}`
  expect(hasShadcnStyle(element, styleKey)).toBe(true)
}

/**
 * 验证shadcn文本颜色
 * @param element - 文本元素
 * @param colorType - 颜色类型
 */
export const expectShadcnTextColor = (element: Element, colorType: 'primary' | 'secondary' | 'muted' | 'accent' = 'primary') => {
  const styleKey = `text-${colorType}`
  expect(hasShadcnStyle(element, styleKey)).toBe(true)
}

/**
 * 验证shadcn背景颜色
 * @param element - 元素
 * @param bgType - 背景类型
 */
export const expectShadcnBackground = (element: Element, bgType: 'primary' | 'secondary' | 'accent' | 'muted' | 'card' | 'background' = 'background') => {
  const styleKey = `bg-${bgType}`
  expect(hasShadcnStyle(element, styleKey)).toBe(true)
}

/**
 * 验证shadcn边框样式
 * @param element - 元素
 * @param borderType - 边框类型
 */
export const expectShadcnBorder = (element: Element, borderType: 'primary' | 'secondary' | 'accent' | 'muted' = 'secondary') => {
  const styleKey = `border-${borderType}`
  expect(hasShadcnStyle(element, styleKey)).toBe(true)
}

/**
 * 验证shadcn焦点样式
 * @param element - 元素
 * @param focusType - 焦点类型
 */
export const expectShadcnFocus = (element: Element, focusType: 'ring' | 'outline' = 'ring') => {
  const styleKey = `focus-${focusType}`
  expect(hasShadcnStyle(element, styleKey)).toBe(true)
}

/**
 * 查找shadcn按钮
 * @param name - 按钮名称或正则表达式
 * @param options - 查找选项
 */
export const findShadcnButton = (name: string | RegExp, options?: { container?: Element }) => {
  const container = options?.container || document.body
  
  // 尝试通过role="button"查找
  try {
    return within(container).getByRole('button', { name })
  } catch {
    // 如果找不到，尝试通过role="tab"查找（对于标签页按钮）
    try {
      return within(container).getByRole('tab', { name })
    } catch {
      // 最后尝试通过文本内容查找
      return within(container).getByText(name)
    }
  }
}

/**
 * 查找所有shadcn按钮
 * @param container - 容器元素
 */
export const findAllShadcnButtons = (container?: Element) => {
  const searchContainer = container || document.body
  
  // 查找所有按钮和标签页
  const buttons = within(searchContainer).queryAllByRole('button')
  const tabs = within(searchContainer).queryAllByRole('tab')
  
  return [...buttons, ...tabs]
}

/**
 * 模拟shadcn主题切换
 * @param theme - 主题名称
 */
export const mockShadcnTheme = (theme: 'light' | 'dark' | 'system' = 'light') => {
  // 移除现有主题类
  document.documentElement.classList.remove('light', 'dark')
  
  // 添加新主题类
  if (theme !== 'system') {
    document.documentElement.classList.add(theme)
  }
  
  // 模拟系统主题
  if (theme === 'system') {
    const mockMatchMedia = vi.fn().mockImplementation(query => ({
      matches: query === '(prefers-color-scheme: dark)' ? false : true,
      media: query,
      onchange: null,
      addListener: vi.fn(),
      removeListener: vi.fn(),
      addEventListener: vi.fn(),
      removeEventListener: vi.fn(),
      dispatchEvent: vi.fn(),
    }))
    
    Object.defineProperty(window, 'matchMedia', {
      writable: true,
      value: mockMatchMedia,
    })
  }
}

/**
 * 验证shadcn组件的可访问性
 * @param element - 元素
 */
export const expectShadcnAccessibility = (element: Element) => {
  // 检查是否有适当的ARIA属性
  const hasAriaLabel = element.hasAttribute('aria-label')
  const hasAriaLabelledBy = element.hasAttribute('aria-labelledby')
  const hasTitle = element.hasAttribute('title')
  const hasRole = element.hasAttribute('role')
  
  // 至少应该有一种可访问性标识
  expect(hasAriaLabel || hasAriaLabelledBy || hasTitle || hasRole).toBe(true)
}

/**
 * 等待shadcn动画完成
 * @param duration - 等待时间（毫秒）
 */
export const waitForShadcnAnimation = async (duration: number = 200) => {
  return new Promise(resolve => setTimeout(resolve, duration))
}

/**
 * 模拟shadcn组件的hover状态
 * @param element - 元素
 */
export const mockShadcnHover = (element: Element) => {
  // 添加hover相关的类
  element.classList.add('hover')
  
  // 触发hover事件
  const hoverEvent = new MouseEvent('mouseenter', {
    bubbles: true,
    cancelable: true,
  })
  element.dispatchEvent(hoverEvent)
}

/**
 * 模拟shadcn组件的focus状态
 * @param element - 元素
 */
export const mockShadcnFocus = (element: Element) => {
  // 添加focus相关的类
  element.classList.add('focus', 'focus-visible')
  
  // 触发focus事件
  const focusEvent = new FocusEvent('focus', {
    bubbles: true,
    cancelable: true,
  })
  element.dispatchEvent(focusEvent)
}

/**
 * 创建shadcn测试数据
 */
export const createShadcnTestData = () => ({
  buttons: [
    { text: '主要按钮', variant: 'primary' as const },
    { text: '次要按钮', variant: 'secondary' as const },
    { text: '轮廓按钮', variant: 'outline' as const },
    { text: '幽灵按钮', variant: 'ghost' as const },
    { text: '危险按钮', variant: 'destructive' as const }
  ],
  colors: [
    { name: '主要文本', type: 'primary' as const },
    { name: '次要文本', type: 'secondary' as const },
    { name: '静音文本', type: 'muted' as const },
    { name: '强调文本', type: 'accent' as const }
  ],
  themes: ['light', 'dark', 'system'] as const
})

// 导出常用的测试断言
export const shadcnAssertions = {
  expectButton: expectShadcnButton,
  expectText: expectShadcnTextColor,
  expectBackground: expectShadcnBackground,
  expectBorder: expectShadcnBorder,
  expectFocus: expectShadcnFocus,
  expectAccessibility: expectShadcnAccessibility
}