/**
 * 样式测试修复工具
 * 用于修复测试中硬编码样式类检查的问题
 */

import { vi } from 'vitest'

/**
 * 创建灵活的样式类检查函数
 * 替代硬编码的 toHaveClass 检查
 */
export const createFlexibleStyleMatcher = () => {
  return {
    /**
     * 检查元素是否包含shadcn相关的样式类
     * @param element - DOM元素
     * @param expectedStyles - 期望的样式类型
     */
    toHaveShadcnStyle: (element: Element, expectedStyles: string[]) => {
      const classList = Array.from(element.classList)
      
      // 检查是否包含任何期望的样式类
      const hasExpectedStyle = expectedStyles.some(style => 
        classList.some(cls => cls.includes(style) || cls.startsWith(style))
      )
      
      return {
        pass: hasExpectedStyle,
        message: () => hasExpectedStyle 
          ? `Expected element not to have shadcn styles: ${expectedStyles.join(', ')}`
          : `Expected element to have shadcn styles: ${expectedStyles.join(', ')}, but got: ${classList.join(', ')}`
      }
    },
    
    /**
     * 检查元素是否包含主题相关的样式类
     * @param element - DOM元素
     * @param themeType - 主题类型
     */
    toHaveThemeStyle: (element: Element, themeType: 'primary' | 'secondary' | 'accent' | 'muted') => {
      const classList = Array.from(element.classList)
      
      // 定义主题相关的样式模式
      const themePatterns = {
        primary: ['primary', 'bg-primary', 'text-primary', 'border-primary'],
        secondary: ['secondary', 'bg-secondary', 'text-secondary', 'border-secondary'],
        accent: ['accent', 'bg-accent', 'text-accent', 'border-accent'],
        muted: ['muted', 'bg-muted', 'text-muted', 'border-muted']
      }
      
      const patterns = themePatterns[themeType]
      const hasThemeStyle = patterns.some(pattern => 
        classList.some(cls => cls.includes(pattern))
      )
      
      return {
        pass: hasThemeStyle,
        message: () => hasThemeStyle
          ? `Expected element not to have ${themeType} theme styles`
          : `Expected element to have ${themeType} theme styles, but got: ${classList.join(', ')}`
      }
    },
    
    /**
     * 检查元素是否包含响应式样式类
     * @param element - DOM元素
     * @param breakpoint - 断点类型
     */
    toHaveResponsiveStyle: (element: Element, breakpoint: 'sm' | 'md' | 'lg' | 'xl') => {
      const classList = Array.from(element.classList)
      
      const hasResponsiveStyle = classList.some(cls => cls.startsWith(`${breakpoint}:`))
      
      return {
        pass: hasResponsiveStyle,
        message: () => hasResponsiveStyle
          ? `Expected element not to have ${breakpoint} responsive styles`
          : `Expected element to have ${breakpoint} responsive styles, but got: ${classList.join(', ')}`
      }
    }
  }
}

/**
 * 样式类映射表
 * 将旧的硬编码样式类映射到新的shadcn样式类
 */
export const styleClassMap = {
  // 按钮样式映射
  'bg-primary-50': ['bg-primary/5', 'bg-primary/10', 'bg-accent/5'],
  'bg-primary-600': ['bg-primary', 'bg-primary/90'],
  'text-primary-600': ['text-primary', 'text-primary-foreground'],
  'text-gray-600': ['text-muted-foreground', 'text-secondary-foreground'],
  'text-gray-700': ['text-foreground', 'text-card-foreground'],
  'text-gray-900': ['text-foreground'],
  'bg-gray-50': ['bg-accent/5', 'bg-muted/5'],
  'bg-gray-100': ['bg-secondary', 'bg-muted'],
  'bg-gray-200': ['bg-secondary/80', 'bg-muted/80'],
  'border-gray-200': ['border-border', 'border-muted'],
  'border-gray-300': ['border-border', 'border-input'],
  'bg-white': ['bg-background', 'bg-card'],
  'bg-green-50': ['bg-green-50', 'bg-success/5'],
  
  // 焦点样式映射
  'focus:ring-primary-500': ['focus:ring-ring', 'focus:ring-primary'],
  'focus:border-primary-500': ['focus:border-primary', 'focus:border-ring'],
  'focus:outline-none': ['focus-visible:outline-none'],
  'focus:ring-2': ['focus-visible:ring-2', 'focus:ring-2'],
  
  // Hover样式映射
  'hover:bg-gray-50': ['hover:bg-accent/5', 'hover:bg-secondary/5'],
  'hover:bg-gray-200': ['hover:bg-secondary/80', 'hover:bg-accent/80'],
  'hover:text-gray-900': ['hover:text-foreground', 'hover:text-accent-foreground'],
  'hover:border-gray-300': ['hover:border-border', 'hover:border-accent'],
  
  // 文本颜色映射
  'text-yellow-400': ['text-yellow-400', 'text-warning'],
  'text-orange-500': ['text-orange-500', 'text-warning'],
  'text-muted-foreground': ['text-muted-foreground'],
  'text-foreground': ['text-foreground', 'text-card-foreground'],
  
  // 边框样式映射
  'border-primary': ['border-primary'],
  'border-border': ['border-border', 'border-input'],
  'border-t': ['border-t'],
  'border-2': ['border-2', 'border'],
  
  // 背景样式映射
  'bg-primary': ['bg-primary'],
  'bg-secondary': ['bg-secondary'],
  'bg-accent': ['bg-accent'],
  'bg-background': ['bg-background'],
  'bg-card': ['bg-card'],
  'bg-primary/5': ['bg-primary/5', 'bg-accent/5']
}

/**
 * 创建样式类检查函数
 * @param element - DOM元素
 * @param expectedClass - 期望的样式类
 * @returns boolean
 */
export const hasFlexibleClass = (element: Element, expectedClass: string): boolean => {
  const classList = Array.from(element.classList)
  
  // 直接匹配
  if (classList.includes(expectedClass)) {
    return true
  }
  
  // 使用映射表匹配
  const mappedClasses = styleClassMap[expectedClass as keyof typeof styleClassMap]
  if (mappedClasses) {
    return mappedClasses.some(mappedClass => 
      classList.some(cls => cls.includes(mappedClass) || cls === mappedClass)
    )
  }
  
  // 模糊匹配（用于处理动态生成的类名）
  const baseClass = expectedClass.split('-')[0]
  return classList.some(cls => cls.startsWith(baseClass))
}

/**
 * 扩展expect匹配器
 */
export const extendExpectMatchers = () => {
  const matchers = createFlexibleStyleMatcher()
  
  // 扩展vitest的expect
  expect.extend({
    toHaveShadcnStyle: matchers.toHaveShadcnStyle,
    toHaveThemeStyle: matchers.toHaveThemeStyle,
    toHaveResponsiveStyle: matchers.toHaveResponsiveStyle,
    
    // 灵活的class检查
    toHaveFlexibleClass: (element: Element, expectedClass: string) => {
      const hasClass = hasFlexibleClass(element, expectedClass)
      
      return {
        pass: hasClass,
        message: () => hasClass
          ? `Expected element not to have class: ${expectedClass}`
          : `Expected element to have class: ${expectedClass}, but got: ${Array.from(element.classList).join(', ')}`
      }
    }
  })
}

/**
 * 样式测试辅助函数
 */
export const styleTestHelpers = {
  /**
   * 检查按钮是否有正确的shadcn样式
   */
  expectShadcnButton: (element: Element, variant: 'primary' | 'secondary' | 'outline' | 'ghost' = 'primary') => {
    const variantClasses = {
      primary: ['bg-primary', 'text-primary-foreground', 'hover:bg-primary/90'],
      secondary: ['bg-secondary', 'text-secondary-foreground', 'hover:bg-secondary/80'],
      outline: ['border', 'border-input', 'bg-background', 'hover:bg-accent'],
      ghost: ['hover:bg-accent', 'hover:text-accent-foreground']
    }
    
    const expectedClasses = variantClasses[variant]
    const classList = Array.from(element.classList)
    
    // 至少应该包含一个期望的类
    const hasVariantStyle = expectedClasses.some(expectedClass =>
      classList.some(cls => cls.includes(expectedClass.split(' ')[0]))
    )
    
    expect(hasVariantStyle).toBe(true)
  },
  
  /**
   * 检查文本是否有正确的shadcn颜色
   */
  expectShadcnTextColor: (element: Element, colorType: 'foreground' | 'muted' | 'accent' = 'foreground') => {
    const colorClasses = {
      foreground: ['text-foreground', 'text-card-foreground'],
      muted: ['text-muted-foreground'],
      accent: ['text-accent-foreground', 'text-primary']
    }
    
    const expectedClasses = colorClasses[colorType]
    const classList = Array.from(element.classList)
    
    const hasColorStyle = expectedClasses.some(expectedClass =>
      classList.some(cls => cls.includes(expectedClass))
    )
    
    expect(hasColorStyle).toBe(true)
  },
  
  /**
   * 检查元素是否有正确的shadcn背景色
   */
  expectShadcnBackground: (element: Element, bgType: 'primary' | 'secondary' | 'accent' | 'background' | 'card' = 'background') => {
    const bgClasses = {
      primary: ['bg-primary'],
      secondary: ['bg-secondary'],
      accent: ['bg-accent'],
      background: ['bg-background'],
      card: ['bg-card']
    }
    
    const expectedClasses = bgClasses[bgType]
    const classList = Array.from(element.classList)
    
    const hasBgStyle = expectedClasses.some(expectedClass =>
      classList.some(cls => cls.includes(expectedClass))
    )
    
    expect(hasBgStyle).toBe(true)
  }
}

// 声明全局类型扩展
declare global {
  namespace Vi {
    interface AsymmetricMatchersContaining {
      toHaveShadcnStyle(expectedStyles: string[]): any
      toHaveThemeStyle(themeType: 'primary' | 'secondary' | 'accent' | 'muted'): any
      toHaveResponsiveStyle(breakpoint: 'sm' | 'md' | 'lg' | 'xl'): any
      toHaveFlexibleClass(expectedClass: string): any
    }
  }
}