// Grok集成单元测试

import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest'
import { AIProviderService } from '../src/services/aiProviderService'

// Mock fetch
global.fetch = vi.fn()

describe('AIProviderService - Grok集成', () => {
  let aiProviderService: AIProviderService
  const mockFetch = fetch as vi.MockedFunction<typeof fetch>

  beforeEach(() => {
    aiProviderService = new AIProviderService()
    vi.clearAllMocks()
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('testGrokConnection', () => {
    const baseUrl = 'https://api.x.ai/v1'
    const validApiKey = 'xai-test123456789'
    const invalidApiKey = 'invalid-key'

    it('应该成功测试有效的Grok连接', async () => {
      // 模拟成功的API响应
      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        json: async () => ({
          data: [
            {
              id: 'grok-beta',
              object: 'model',
              created: **********,
              owned_by: 'xai'
            },
            {
              id: 'grok-1',
              object: 'model',
              created: **********,
              owned_by: 'xai'
            }
          ]
        })
      } as Response)

      const result = await aiProviderService.testGrokConnection(baseUrl, validApiKey)

      expect(result.success).toBe(true)
      expect(result.modelCount).toBe(2)
      expect(result.error).toBeUndefined()
      expect(mockFetch).toHaveBeenCalledWith(
        `${baseUrl}/models`,
        expect.objectContaining({
          method: 'GET',
          headers: expect.objectContaining({
            'Authorization': `Bearer ${validApiKey}`,
            'Content-Type': 'application/json',
            'User-Agent': 'BookmarkExtension/1.0'
          })
        })
      )
    })

    it('应该处理空API密钥错误', async () => {
      const result = await aiProviderService.testGrokConnection(baseUrl, '')

      expect(result.success).toBe(false)
      expect(result.error).toBe('API密钥不能为空')
      expect(mockFetch).not.toHaveBeenCalled()
    })

    it('应该处理无效的API密钥格式', async () => {
      const result = await aiProviderService.testGrokConnection(baseUrl, invalidApiKey)

      expect(result.success).toBe(false)
      expect(result.error).toBe('无效的Grok API密钥格式，应以"xai-"开头')
      expect(mockFetch).not.toHaveBeenCalled()
    })

    it('应该处理400请求参数错误', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 400,
        statusText: 'Bad Request'
      } as Response)

      const result = await aiProviderService.testGrokConnection(baseUrl, validApiKey)

      expect(result.success).toBe(false)
      expect(result.error).toBe('请求参数无效')
    })

    it('应该处理401未授权错误', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 401,
        statusText: 'Unauthorized'
      } as Response)

      const result = await aiProviderService.testGrokConnection(baseUrl, validApiKey)

      expect(result.success).toBe(false)
      expect(result.error).toBe('API密钥无效或已过期')
    })

    it('应该处理403权限不足错误', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 403,
        statusText: 'Forbidden'
      } as Response)

      const result = await aiProviderService.testGrokConnection(baseUrl, validApiKey)

      expect(result.success).toBe(false)
      expect(result.error).toBe('API密钥权限不足或服务被禁用')
    })

    it('应该处理429频率限制错误', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 429,
        statusText: 'Too Many Requests'
      } as Response)

      const result = await aiProviderService.testGrokConnection(baseUrl, validApiKey)

      expect(result.success).toBe(false)
      expect(result.error).toBe('API请求频率限制，请稍后重试')
    })

    it('应该处理500服务器错误', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 500,
        statusText: 'Internal Server Error'
      } as Response)

      const result = await aiProviderService.testGrokConnection(baseUrl, validApiKey)

      expect(result.success).toBe(false)
      expect(result.error).toBe('Grok服务器内部错误')
    })

    it('应该处理503服务不可用错误', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 503,
        statusText: 'Service Unavailable'
      } as Response)

      const result = await aiProviderService.testGrokConnection(baseUrl, validApiKey)

      expect(result.success).toBe(false)
      expect(result.error).toBe('Grok服务暂时不可用')
    })

    it('应该处理网络超时错误', async () => {
      mockFetch.mockRejectedValueOnce(new DOMException('The operation was aborted', 'AbortError'))

      const result = await aiProviderService.testGrokConnection(baseUrl, validApiKey)

      expect(result.success).toBe(false)
      expect(result.error).toBe('连接超时，请检查网络连接或API服务状态')
    })

    it('应该处理网络连接错误', async () => {
      mockFetch.mockRejectedValueOnce(new Error('fetch failed'))

      const result = await aiProviderService.testGrokConnection(baseUrl, validApiKey)

      expect(result.success).toBe(false)
      expect(result.error).toBe('网络连接失败，请检查网络设置')
    })

    it('应该处理异常的API响应格式', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        json: async () => ({
          // 缺少data字段
          models: []
        })
      } as Response)

      const result = await aiProviderService.testGrokConnection(baseUrl, validApiKey)

      expect(result.success).toBe(false)
      expect(result.error).toBe('Grok API响应格式异常')
    })
  })

  describe('getGrokModels', () => {
    it('应该返回预定义的Grok模型列表', async () => {
      const result = await aiProviderService.getGrokModels()

      expect(result).toHaveLength(3)
      expect(result.every(model => model.providerId === 'grok')).toBe(true)
      
      // 检查模型排序（按版本号：1.5 > 1 > beta）
      expect(result[0].id).toBe('grok-1.5')
      expect(result[1].id).toBe('grok-1')
      expect(result[2].id).toBe('grok-beta')
      
      // 检查Grok-1.5模型详细信息
      const grok15 = result.find(m => m.id === 'grok-1.5')
      expect(grok15?.displayName).toBe('Grok-1.5')
      expect(grok15?.isRecommended).toBe(true)
      expect(grok15?.isPopular).toBe(true)
      expect(grok15?.capabilities).toContain('chat')
      expect(grok15?.capabilities).toContain('reasoning')
      expect(grok15?.capabilities).toContain('real-time-info')
      expect(grok15?.capabilities).toContain('improved-logic')
      expect(grok15?.contextLength).toBe(16384)
      expect(grok15?.maxTokens).toBe(4096)
      expect(grok15?.tags).toContain('xAI')
      expect(grok15?.tags).toContain('Grok')
      expect(grok15?.tags).toContain('改进版本')

      // 检查Grok-1模型
      const grok1 = result.find(m => m.id === 'grok-1')
      expect(grok1?.displayName).toBe('Grok-1')
      expect(grok1?.isRecommended).toBe(true)
      expect(grok1?.isPopular).toBe(true)
      expect(grok1?.capabilities).toContain('reasoning')
      expect(grok1?.tags).toContain('第一代')
      expect(grok1?.tags).toContain('真实性')

      // 检查Grok Beta模型
      const grokBeta = result.find(m => m.id === 'grok-beta')
      expect(grokBeta?.displayName).toBe('Grok Beta')
      expect(grokBeta?.isRecommended).toBe(true)
      expect(grokBeta?.capabilities).toContain('real-time-info')
      expect(grokBeta?.tags).toContain('Beta版本')
      expect(grokBeta?.tags).toContain('实时信息')
    })

    it('应该正确排序模型（版本号降序）', async () => {
      const result = await aiProviderService.getGrokModels()

      // 检查版本排序：1.5 > 1 > beta
      expect(result[0].id).toBe('grok-1.5')
      expect(result[1].id).toBe('grok-1')
      expect(result[2].id).toBe('grok-beta')
    })

    it('应该为所有模型设置正确的基础属性', async () => {
      const result = await aiProviderService.getGrokModels()

      result.forEach(model => {
        expect(model.id).toBeTruthy()
        expect(model.name).toBeTruthy()
        expect(model.displayName).toBeTruthy()
        expect(model.description).toBeTruthy()
        expect(model.providerId).toBe('grok')
        expect(model.capabilities).toContain('chat')
        expect(model.capabilities).toContain('reasoning')
        expect(model.capabilities).toContain('real-time-info')
        expect(model.supportedFormats).toContain('text')
        expect(model.supportedFormats).toContain('json')
        expect(model.isRecommended).toBe(true) // 所有Grok模型都是推荐的
        expect(typeof model.isPopular).toBe('boolean')
        expect(typeof model.maxTokens).toBe('number')
        expect(typeof model.contextLength).toBe('number')
      })
    })
  })

  describe('validateGrokModel', () => {
    const baseUrl = 'https://api.x.ai/v1'
    const validApiKey = 'xai-test123456789'

    it('应该验证有效的Grok模型', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        json: async () => ({
          id: 'chatcmpl-test123',
          object: 'chat.completion',
          created: **********,
          model: 'grok-1',
          choices: [
            {
              index: 0,
              message: {
                role: 'assistant',
                content: 'Hello!'
              },
              finish_reason: 'stop'
            }
          ]
        })
      } as Response)

      const result = await aiProviderService.validateGrokModel(
        baseUrl, 
        validApiKey, 
        'grok-1'
      )

      expect(result).toBe(true)
      expect(mockFetch).toHaveBeenCalledWith(
        `${baseUrl}/chat/completions`,
        expect.objectContaining({
          method: 'POST',
          headers: expect.objectContaining({
            'Authorization': `Bearer ${validApiKey}`,
            'Content-Type': 'application/json'
          }),
          body: expect.stringContaining('grok-1')
        })
      )
    })

    it('应该处理无效的Grok模型', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 400,
        statusText: 'Bad Request'
      } as Response)

      const result = await aiProviderService.validateGrokModel(
        baseUrl, 
        validApiKey, 
        'invalid-model'
      )

      expect(result).toBe(false)
    })

    it('应该处理网络错误', async () => {
      mockFetch.mockRejectedValueOnce(new Error('Network error'))

      const result = await aiProviderService.validateGrokModel(
        baseUrl, 
        validApiKey, 
        'grok-1'
      )

      expect(result).toBe(false)
    })
  })

  describe('Grok模型处理辅助方法', () => {
    it('应该正确获取模型详细信息', () => {
      const service = new AIProviderService()
      
      // 使用反射访问私有方法进行测试
      const getGrokModelDetails = (service as any).getGrokModelDetails.bind(service)

      // 测试Grok-1.5
      const grok15Details = getGrokModelDetails('grok-1.5')
      expect(grok15Details.displayName).toBe('Grok-1.5')
      expect(grok15Details.capabilities).toContain('improved-logic')
      expect(grok15Details.contextLength).toBe(16384)
      expect(grok15Details.maxTokens).toBe(4096)

      // 测试Grok-1
      const grok1Details = getGrokModelDetails('grok-1')
      expect(grok1Details.displayName).toBe('Grok-1')
      expect(grok1Details.capabilities).toContain('reasoning')
      expect(grok1Details.tags).toContain('真实性')

      // 测试Grok Beta
      const grokBetaDetails = getGrokModelDetails('grok-beta')
      expect(grokBetaDetails.displayName).toBe('Grok Beta')
      expect(grokBetaDetails.tags).toContain('Beta版本')

      // 测试未知模型
      const unknownDetails = getGrokModelDetails('unknown-model')
      expect(unknownDetails.displayName).toBe('unknown-model')
      expect(unknownDetails.capabilities).toContain('chat')
      expect(unknownDetails.contextLength).toBe(8192)
    })

    it('应该正确判断推荐模型', () => {
      const service = new AIProviderService()
      const isGrokModelRecommended = (service as any).isGrokModelRecommended.bind(service)

      // 所有Grok模型都是推荐的
      expect(isGrokModelRecommended('grok-1.5')).toBe(true)
      expect(isGrokModelRecommended('grok-1')).toBe(true)
      expect(isGrokModelRecommended('grok-beta')).toBe(true)
      expect(isGrokModelRecommended('unknown-model')).toBe(true)
    })

    it('应该正确判断热门模型', () => {
      const service = new AIProviderService()
      const isGrokModelPopular = (service as any).isGrokModelPopular.bind(service)

      // 正式版本是热门模型
      expect(isGrokModelPopular('grok-1.5')).toBe(true)
      expect(isGrokModelPopular('grok-1')).toBe(true)

      // Beta版本不是热门模型
      expect(isGrokModelPopular('grok-beta')).toBe(false)
      expect(isGrokModelPopular('unknown-model')).toBe(false)
    })
  })
})