import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { vi } from 'vitest'
import OptionsApp from '../src/options/OptionsApp'

// Mock Chrome API
const mockChrome = {
  runtime: {
    sendMessage: vi.fn()
  }
}

// @ts-ignore
global.chrome = mockChrome

describe('收藏管理页面头部布局稳定性测试', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    mockChrome.runtime.sendMessage.mockResolvedValue({
      success: true,
      data: []
    })
  })

  test('头部容器应该有固定的最小高度', async () => {
    render(<OptionsApp />)
    
    // 等待组件加载完成
    await waitFor(() => {
      expect(screen.getByRole('heading', { level: 2, name: '收藏管理' })).toBeInTheDocument()
    })
    
    // 查找头部容器 - 使用h2标题来定位
    const titleElement = screen.getByRole('heading', { level: 2, name: '收藏管理' })
    const headerElement = titleElement.closest('.bookmark-header')
    expect(headerElement).toBeInTheDocument()
    
    // 检查CSS类是否正确应用
    expect(headerElement).toHaveClass('bookmark-header')
  })

  test('标题区域应该有固定宽度防止被压缩', async () => {
    render(<OptionsApp />)
    
    await waitFor(() => {
      expect(screen.getByRole('heading', { level: 2, name: '收藏管理' })).toBeInTheDocument()
    })
    
    // 查找标题容器 - 使用h2标题来定位
    const titleElement = screen.getByRole('heading', { level: 2, name: '收藏管理' })
    const titleContainer = titleElement.closest('.bookmark-header-title')
    expect(titleContainer).toBeInTheDocument()
    expect(titleContainer).toHaveClass('bookmark-header-title')
  })

  test('控制按钮区域应该有正确的布局类', async () => {
    render(<OptionsApp />)
    
    await waitFor(() => {
      expect(screen.getByText('添加收藏')).toBeInTheDocument()
    })
    
    // 查找控制按钮容器
    const controlsContainer = screen.getByText('添加收藏').closest('.bookmark-header-controls')
    expect(controlsContainer).toBeInTheDocument()
    expect(controlsContainer).toHaveClass('bookmark-header-controls')
  })

  test('ViewModeSelector加载时应该显示占位符', async () => {
    render(<OptionsApp />)
    
    // 在ViewModeSelector加载期间，应该显示占位符
    const placeholder = document.querySelector('.view-mode-placeholder')
    
    // 由于ViewModeSelector可能很快加载完成，我们检查占位符的存在或ViewModeSelector的存在
    await waitFor(() => {
      const hasPlaceholder = document.querySelector('.view-mode-placeholder')
      const hasViewModeSelector = screen.queryByRole('group') // ViewModeSelector通常是一个按钮组
      expect(hasPlaceholder || hasViewModeSelector).toBeTruthy()
    })
  })

  test('所有控制按钮应该正确渲染', async () => {
    render(<OptionsApp />)
    
    await waitFor(() => {
      expect(screen.getByText('添加收藏')).toBeInTheDocument()
      expect(screen.getByText('刷新')).toBeInTheDocument()
      expect(screen.getByPlaceholderText('搜索收藏...')).toBeInTheDocument()
      expect(screen.getByDisplayValue('所有分类')).toBeInTheDocument()
    })
  })

  test('搜索框应该有正确的宽度设置', async () => {
    render(<OptionsApp />)
    
    await waitFor(() => {
      const searchInput = screen.getByPlaceholderText('搜索收藏...')
      expect(searchInput).toBeInTheDocument()
      expect(searchInput).toHaveClass('w-64')
    })
  })

  test('头部布局在窗口大小变化时应该保持稳定', async () => {
    render(<OptionsApp />)
    
    await waitFor(() => {
      expect(screen.getByRole('heading', { level: 2, name: '收藏管理' })).toBeInTheDocument()
    })
    
    // 模拟窗口大小变化
    Object.defineProperty(window, 'innerWidth', {
      writable: true,
      configurable: true,
      value: 768,
    })
    
    fireEvent(window, new Event('resize'))
    
    // 验证头部元素仍然存在且布局正确
    const titleElement = screen.getByRole('heading', { level: 2, name: '收藏管理' })
    const headerElement = titleElement.closest('.bookmark-header')
    expect(headerElement).toBeInTheDocument()
    expect(headerElement).toHaveClass('bookmark-header')
  })

  test('控制按钮区域的元素应该有正确的间距', async () => {
    render(<OptionsApp />)
    
    await waitFor(() => {
      expect(screen.getByText('添加收藏')).toBeInTheDocument()
    })
    
    // 检查控制按钮容器的CSS类
    const controlsContainer = screen.getByText('添加收藏').closest('.bookmark-header-controls')
    expect(controlsContainer).toHaveClass('bookmark-header-controls')
  })
})