// ConflictResolverService 单元测试

import { describe, it, expect, beforeEach, vi } from 'vitest'
import { ConflictResolverService } from '../src/services/ConflictResolverService'
import { BookmarkInput, Bookmark, CategoryInput, Category, TagInput, Tag, ImportData } from '../src/types'

// 模拟服务依赖
vi.mock('../src/services/bookmarkService', () => ({
  bookmarkService: {
    getBookmarks: vi.fn()
  }
}))

vi.mock('../src/services/categoryService', () => ({
  categoryService: {
    getCategories: vi.fn()
  }
}))

vi.mock('../src/services/tagService', () => ({
  tagService: {
    getTags: vi.fn()
  }
}))

describe('ConflictResolverService', () => {
  let conflictResolverService: ConflictResolverService
  
  beforeEach(() => {
    conflictResolverService = new ConflictResolverService()
    vi.clearAllMocks()
  })

  describe('detectBookmarkConflicts', () => {
    it('应该检测到URL重复冲突', async () => {
      const importBookmarks: BookmarkInput[] = [
        {
          type: 'url',
          title: '测试收藏',
          url: 'https://example.com',
          category: '测试分类',
          tags: ['测试']
        }
      ]

      const existingBookmarks: Bookmark[] = [
        {
          id: 'bookmark1',
          type: 'url',
          title: '现有收藏',
          url: 'https://example.com',
          category: '现有分类',
          tags: ['现有'],
          createdAt: new Date(),
          updatedAt: new Date(),
          metadata: { aiGenerated: false }
        }
      ]

      const conflicts = await conflictResolverService.detectBookmarkConflicts(
        importBookmarks,
        existingBookmarks
      )

      expect(conflicts).toHaveLength(1)
      expect(conflicts[0].conflictType).toBe('duplicate')
      expect(conflicts[0].conflictFields).toContain('url')
      expect(conflicts[0].similarity).toBe(1.0)
    })

    it('应该检测到内容相似度冲突', async () => {
      const importBookmarks: BookmarkInput[] = [
        {
          type: 'url',
          title: '这是一个测试标题',
          url: 'https://different.com',
          content: '这是一个测试内容，包含很多相同的词汇和信息',
          category: '测试分类',
          tags: ['测试']
        }
      ]

      const existingBookmarks: Bookmark[] = [
        {
          id: 'bookmark1',
          type: 'url',
          title: '这是一个测试标题',
          url: 'https://another.com',
          content: '这是一个测试内容，包含很多相同的词汇和信息',
          category: '现有分类',
          tags: ['现有'],
          createdAt: new Date(),
          updatedAt: new Date(),
          metadata: { aiGenerated: false }
        }
      ]

      // 先测试相似度计算
      const similarity = conflictResolverService['calculateBookmarkSimilarity'](
        importBookmarks[0], 
        existingBookmarks[0]
      )
      console.log('计算的相似度:', similarity)

      const conflicts = await conflictResolverService.detectBookmarkConflicts(
        importBookmarks,
        existingBookmarks
      )

      console.log('检测到的冲突数量:', conflicts.length)
      if (conflicts.length > 0) {
        console.log('冲突详情:', conflicts[0])
      }

      expect(conflicts).toHaveLength(1)
      expect(conflicts[0].conflictType).toBe('data_mismatch')
      expect(conflicts[0].similarity).toBeGreaterThan(0.8)
    })

    it('应该不检测到冲突当相似度低时', async () => {
      const importBookmarks: BookmarkInput[] = [
        {
          type: 'url',
          title: '完全不同的标题',
          url: 'https://different.com',
          content: '完全不同的内容',
          category: '测试分类',
          tags: ['测试']
        }
      ]

      const existingBookmarks: Bookmark[] = [
        {
          id: 'bookmark1',
          type: 'url',
          title: '另一个标题',
          url: 'https://another.com',
          content: '另一个内容',
          category: '现有分类',
          tags: ['现有'],
          createdAt: new Date(),
          updatedAt: new Date(),
          metadata: { aiGenerated: false }
        }
      ]

      const conflicts = await conflictResolverService.detectBookmarkConflicts(
        importBookmarks,
        existingBookmarks
      )

      expect(conflicts).toHaveLength(0)
    })
  })

  describe('detectCategoryConflicts', () => {
    it('应该检测到分类名称冲突', async () => {
      const importCategories: CategoryInput[] = [
        {
          name: '技术',
          description: '技术相关内容',
          color: '#3B82F6'
        }
      ]

      const existingCategories: Category[] = [
        {
          id: 'category1',
          name: '技术',
          description: '现有技术分类',
          color: '#10B981',
          createdAt: new Date(),
          updatedAt: new Date(),
          bookmarkCount: 5
        }
      ]

      const conflicts = await conflictResolverService.detectCategoryConflicts(
        importCategories,
        existingCategories
      )

      expect(conflicts).toHaveLength(1)
      expect(conflicts[0].conflictType).toBe('data_mismatch')
      expect(conflicts[0].conflictFields).toContain('name')
    })
  })

  describe('detectTagConflicts', () => {
    it('应该检测到标签名称冲突', async () => {
      const importTags: TagInput[] = [
        {
          name: 'JavaScript',
          color: '#F59E0B'
        }
      ]

      const existingTags: Tag[] = [
        {
          id: 'tag1',
          name: 'JavaScript',
          color: '#EF4444',
          usageCount: 10,
          createdAt: new Date(),
          updatedAt: new Date()
        }
      ]

      const conflicts = await conflictResolverService.detectTagConflicts(
        importTags,
        existingTags
      )

      expect(conflicts).toHaveLength(1)
      expect(conflicts[0].conflictType).toBe('data_mismatch')
      expect(conflicts[0].conflictFields).toContain('name')
    })
  })

  describe('mergeBookmarkData', () => {
    it('应该正确合并收藏夹数据', () => {
      const existing: Bookmark = {
        id: 'bookmark1',
        type: 'url',
        title: '现有标题',
        url: 'https://example.com',
        description: '短描述',
        content: '短内容',
        category: '现有分类',
        tags: ['tag1', 'tag2'],
        createdAt: new Date(),
        updatedAt: new Date(),
        metadata: { aiGenerated: false, wordCount: 100 }
      }

      const imported: BookmarkInput = {
        type: 'url',
        title: '导入标题',
        url: 'https://example.com',
        description: '这是一个更长的描述内容',
        content: '这是一个更长的内容信息',
        category: '导入分类',
        tags: ['tag2', 'tag3'],
        metadata: { aiGenerated: true, pageTitle: '页面标题' }
      }

      const merged = conflictResolverService['mergeBookmarkData'](existing, imported)

      expect(merged.tags).toEqual(['tag1', 'tag2', 'tag3']) // 合并并去重
      expect(merged.description).toBe('这是一个更长的描述内容') // 选择更长的描述
      expect(merged.content).toBe('这是一个更长的内容信息') // 选择更长的内容
      expect(merged.metadata?.aiGenerated).toBe(true) // 保持AI生成标记
      expect(merged.metadata?.wordCount).toBe(100) // 保留现有元数据
      expect(merged.metadata?.pageTitle).toBe('页面标题') // 添加新元数据
    })
  })

  describe('calculateTextSimilarity', () => {
    it('应该正确计算文本相似度', () => {
      const similarity1 = conflictResolverService['calculateTextSimilarity']('相同文本', '相同文本')
      expect(similarity1).toBe(1)

      const similarity2 = conflictResolverService['calculateTextSimilarity']('完全不同', '另一个文本')
      expect(similarity2).toBeLessThan(0.5)

      const similarity3 = conflictResolverService['calculateTextSimilarity']('', '')
      expect(similarity3).toBe(1)

      const similarity4 = conflictResolverService['calculateTextSimilarity']('测试', '')
      expect(similarity4).toBe(0)
    })
  })

  describe('calculateLevenshteinDistance', () => {
    it('应该正确计算编辑距离', () => {
      const distance1 = conflictResolverService['calculateLevenshteinDistance']('kitten', 'sitting')
      expect(distance1).toBe(3)

      const distance2 = conflictResolverService['calculateLevenshteinDistance']('same', 'same')
      expect(distance2).toBe(0)

      const distance3 = conflictResolverService['calculateLevenshteinDistance']('', 'test')
      expect(distance3).toBe(4)
    })
  })

  describe('generateConflictId', () => {
    it('应该生成唯一的冲突ID', () => {
      const id1 = conflictResolverService['generateConflictId']()
      const id2 = conflictResolverService['generateConflictId']()

      expect(id1).toMatch(/^conflict_\d+_[a-z0-9]+$/)
      expect(id2).toMatch(/^conflict_\d+_[a-z0-9]+$/)
      expect(id1).not.toBe(id2)
    })
  })
})