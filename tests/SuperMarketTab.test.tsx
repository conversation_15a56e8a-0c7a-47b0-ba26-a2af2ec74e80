import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { describe, it, expect, vi } from 'vitest'
import SuperMarketTab from '../src/components/SuperMarketTab'

describe('SuperMarketTab', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('应该正确渲染超级市场页面', () => {
    render(<SuperMarketTab />)
    
    // 检查标题
    expect(screen.getByText('超级市场')).toBeInTheDocument()
    expect(screen.getByText('发现优质资源，探索无限可能。通过AI智能搜索找到最适合您的工具和资源')).toBeInTheDocument()
  })

  it('应该显示搜索框和AI搜索开关', () => {
    render(<SuperMarketTab />)
    
    // 检查搜索框
    const searchInput = screen.getByPlaceholderText(/使用自然语言搜索资源/)
    expect(searchInput).toBeInTheDocument()
    
    // 检查搜索按钮
    const searchButton = screen.getByRole('button', { name: /搜索/i })
    expect(searchButton).toBeInTheDocument()
    
    // 检查AI搜索开关
    expect(screen.getByText('AI智能搜索')).toBeInTheDocument()
  })

  it('应该显示分类按钮', () => {
    render(<SuperMarketTab />)
    
    // 检查分类按钮
    expect(screen.getByText('全部')).toBeInTheDocument()
    expect(screen.getByText('开发工具')).toBeInTheDocument()
    expect(screen.getByText('设计资源')).toBeInTheDocument()
    expect(screen.getByText('效率工具')).toBeInTheDocument()
    expect(screen.getByText('学习资源')).toBeInTheDocument()
    expect(screen.getByText('娱乐休闲')).toBeInTheDocument()
  })

  it('应该显示资源卡片', () => {
    render(<SuperMarketTab />)
    
    // 检查一些预期的资源
    expect(screen.getByText('GitHub Copilot')).toBeInTheDocument()
    expect(screen.getByText('Figma')).toBeInTheDocument()
    expect(screen.getByText('Notion')).toBeInTheDocument()
    expect(screen.getByText('VS Code')).toBeInTheDocument()
  })

  it('应该处理搜索功能', async () => {
    render(<SuperMarketTab />)
    
    const searchInput = screen.getByPlaceholderText(/使用自然语言搜索资源/)
    const searchButton = screen.getByRole('button', { name: /搜索/i })
    
    // 输入搜索关键词
    fireEvent.change(searchInput, { target: { value: 'GitHub' } })
    
    // 点击搜索按钮
    fireEvent.click(searchButton)
    
    // 应该显示搜索中状态
    expect(screen.getByText('搜索中...')).toBeInTheDocument()
    
    // 等待搜索完成
    await waitFor(() => {
      expect(screen.queryByText('搜索中...')).not.toBeInTheDocument()
    }, { timeout: 2000 })
  })

  it('应该支持分类筛选', () => {
    render(<SuperMarketTab />)
    
    // 点击开发工具分类
    const devToolsButton = screen.getByText('开发工具')
    fireEvent.click(devToolsButton)
    
    // 应该高亮选中的分类
    expect(devToolsButton.closest('button')).toHaveClass('bg-primary')
  })

  it('应该支持视图模式切换', () => {
    render(<SuperMarketTab />)
    
    // 查找视图切换按钮（通过图标查找）
    const gridButtons = screen.getAllByRole('button')
    const gridButton = gridButtons.find(button => 
      button.querySelector('svg') && 
      button.querySelector('svg')?.getAttribute('class')?.includes('w-4')
    )
    
    expect(gridButton).toBeInTheDocument()
  })

  it('应该显示统计信息', () => {
    render(<SuperMarketTab />)
    
    // 检查统计信息
    expect(screen.getByText('平台统计')).toBeInTheDocument()
    expect(screen.getByText('总资源数')).toBeInTheDocument()
    expect(screen.getByText('分类数量')).toBeInTheDocument()
    expect(screen.getByText('总浏览量')).toBeInTheDocument()
    expect(screen.getByText('总点赞数')).toBeInTheDocument()
  })

  it('应该支持资源点赞功能', () => {
    render(<SuperMarketTab />)
    
    // 查找点赞按钮（心形图标）
    const likeButtons = screen.getAllByRole('button')
    const likeButton = likeButtons.find(button => 
      button.querySelector('svg') && 
      button.textContent === ''
    )
    
    if (likeButton) {
      fireEvent.click(likeButton)
      // 点赞功能应该正常工作（这里只是测试点击不会报错）
    }
  })

  it('应该支持排序功能', () => {
    render(<SuperMarketTab />)
    
    // 查找排序选择器
    const sortSelectors = screen.getAllByRole('combobox')
    expect(sortSelectors.length).toBeGreaterThan(0)
  })
})