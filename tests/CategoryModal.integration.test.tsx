// CategoryModal集成测试

import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { vi, describe, it, expect, beforeEach } from 'vitest'
import CategoryModal from '../src/components/CategoryModal'
import { categoryService } from '../src/services/categoryService'
import type { Category } from '../src/types'

// Mock categoryService
vi.mock('../src/services/categoryService', () => ({
  categoryService: {
    validateCategoryName: vi.fn(),
    createCategory: vi.fn(),
    updateCategory: vi.fn(),
    deleteCategory: vi.fn()
  }
}))

describe('CategoryModal集成测试', () => {
  const mockCategory: Category = {
    id: 'test-category-1',
    name: '测试分类',
    description: '测试分类描述',
    color: '#3B82F6',
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-01'),
    bookmarkCount: 5
  }

  beforeEach(() => {
    vi.clearAllMocks()
    // 设置默认的mock返回值
    vi.mocked(categoryService.validateCategoryName).mockResolvedValue(true)
    vi.mocked(categoryService.createCategory).mockResolvedValue(mockCategory)
    vi.mocked(categoryService.updateCategory).mockResolvedValue(mockCategory)
    vi.mocked(categoryService.deleteCategory).mockResolvedValue(undefined)
  })

  describe('创建分类集成测试', () => {
    it('应该完整地处理分类创建流程', async () => {
      const mockOnSave = vi.fn().mockResolvedValue(undefined)
      const mockOnClose = vi.fn()

      render(
        <CategoryModal
          isOpen={true}
          type="create"
          onSave={mockOnSave}
          onClose={mockOnClose}
        />
      )

      // 验证模态窗口打开
      expect(screen.getByText('创建新分类')).toBeInTheDocument()

      // 填写表单
      const nameInput = screen.getByLabelText(/分类名称/)
      const descriptionInput = screen.getByLabelText(/分类描述/)

      fireEvent.change(nameInput, { target: { value: '新分类' } })
      fireEvent.change(descriptionInput, { target: { value: '新分类描述' } })

      // 提交表单
      const submitButton = screen.getByText('创建分类')
      fireEvent.click(submitButton)

      // 等待验证完成
      await waitFor(() => {
        expect(categoryService.validateCategoryName).toHaveBeenCalledWith('新分类', undefined)
      })

      // 等待提交完成
      await waitFor(() => {
        expect(mockOnSave).toHaveBeenCalledWith({
          name: '新分类',
          description: '新分类描述',
          color: '#3B82F6',
          parentId: undefined
        })
      })
    })

    it('应该处理名称重复的验证错误', async () => {
      // 设置名称重复的情况
      vi.mocked(categoryService.validateCategoryName).mockResolvedValue(false)

      const mockOnSave = vi.fn()
      const mockOnClose = vi.fn()

      render(
        <CategoryModal
          isOpen={true}
          type="create"
          onSave={mockOnSave}
          onClose={mockOnClose}
        />
      )

      // 填写重复的名称
      const nameInput = screen.getByLabelText(/分类名称/)
      fireEvent.change(nameInput, { target: { value: '重复名称' } })

      // 提交表单
      const submitButton = screen.getByText('创建分类')
      fireEvent.click(submitButton)

      // 等待验证错误显示
      await waitFor(() => {
        expect(screen.getByText('分类名称已存在，请使用其他名称')).toBeInTheDocument()
      })

      // 验证没有调用保存
      expect(mockOnSave).not.toHaveBeenCalled()
    })
  })

  describe('编辑分类集成测试', () => {
    it('应该完整地处理分类编辑流程', async () => {
      const mockOnSave = vi.fn().mockResolvedValue(undefined)
      const mockOnClose = vi.fn()

      render(
        <CategoryModal
          isOpen={true}
          type="edit"
          category={mockCategory}
          onSave={mockOnSave}
          onClose={mockOnClose}
        />
      )

      // 验证模态窗口打开并预填充数据
      expect(screen.getByText('编辑分类')).toBeInTheDocument()
      expect(screen.getByDisplayValue('测试分类')).toBeInTheDocument()
      expect(screen.getByDisplayValue('测试分类描述')).toBeInTheDocument()

      // 修改名称
      const nameInput = screen.getByLabelText(/分类名称/)
      fireEvent.change(nameInput, { target: { value: '修改后的分类' } })

      // 提交表单
      const submitButton = screen.getByText('保存修改')
      fireEvent.click(submitButton)

      // 等待验证完成（编辑时应该排除当前分类ID）
      await waitFor(() => {
        expect(categoryService.validateCategoryName).toHaveBeenCalledWith('修改后的分类', 'test-category-1')
      })

      // 等待提交完成
      await waitFor(() => {
        expect(mockOnSave).toHaveBeenCalledWith({
          name: '修改后的分类',
          description: '测试分类描述',
          color: '#3B82F6',
          parentId: undefined
        })
      })
    })
  })

  describe('删除分类集成测试', () => {
    it('应该完整地处理分类删除流程', async () => {
      const mockOnDelete = vi.fn().mockResolvedValue(undefined)
      const mockOnClose = vi.fn()

      render(
        <CategoryModal
          isOpen={true}
          type="delete"
          category={mockCategory}
          bookmarkCount={5}
          onDelete={mockOnDelete}
          onClose={mockOnClose}
        />
      )

      // 验证删除确认界面
      expect(screen.getByText('确认删除分类')).toBeInTheDocument()
      expect(screen.getByText('测试分类')).toBeInTheDocument()
      expect(screen.getByText(/将 5 个书签移动到"默认分类"/)).toBeInTheDocument()

      // 点击确认删除
      const deleteButton = screen.getByText('确认删除')
      fireEvent.click(deleteButton)

      // 等待删除完成
      await waitFor(() => {
        expect(mockOnDelete).toHaveBeenCalled()
      })
    })

    it('应该显示空分类的删除提示', () => {
      const mockOnDelete = vi.fn()
      const mockOnClose = vi.fn()

      render(
        <CategoryModal
          isOpen={true}
          type="delete"
          category={mockCategory}
          bookmarkCount={0}
          onDelete={mockOnDelete}
          onClose={mockOnClose}
        />
      )

      // 验证空分类的提示
      expect(screen.getByText(/不会影响任何书签（此分类为空）/)).toBeInTheDocument()
    })
  })

  describe('加载状态集成测试', () => {
    it('应该在加载时禁用所有交互', async () => {
      const mockOnSave = vi.fn()
      const mockOnClose = vi.fn()

      render(
        <CategoryModal
          isOpen={true}
          type="create"
          onSave={mockOnSave}
          onClose={mockOnClose}
          loading={true}
        />
      )

      // 验证关闭按钮被禁用
      const closeButton = screen.getByLabelText('关闭')
      expect(closeButton).toBeDisabled()

      // 验证表单元素被禁用
      const nameInput = screen.getByLabelText(/分类名称/)
      const submitButton = screen.getByRole('button', { name: /保存中.../ })
      
      expect(nameInput).toBeDisabled()
      expect(submitButton).toBeDisabled()
    })

    it('应该在删除加载时显示正确的状态', () => {
      const mockOnDelete = vi.fn()
      const mockOnClose = vi.fn()

      render(
        <CategoryModal
          isOpen={true}
          type="delete"
          category={mockCategory}
          onDelete={mockOnDelete}
          onClose={mockOnClose}
          loading={true}
        />
      )

      // 验证删除按钮显示加载状态
      expect(screen.getByText('删除中...')).toBeInTheDocument()
      
      // 验证取消按钮被禁用
      const cancelButton = screen.getByText('取消')
      expect(cancelButton).toBeDisabled()
    })
  })

  describe('错误处理集成测试', () => {
    it('应该处理表单验证服务错误', async () => {
      // 模拟验证服务错误
      vi.mocked(categoryService.validateCategoryName).mockRejectedValue(new Error('服务错误'))

      const mockOnSave = vi.fn()
      const mockOnClose = vi.fn()

      render(
        <CategoryModal
          isOpen={true}
          type="create"
          onSave={mockOnSave}
          onClose={mockOnClose}
        />
      )

      // 填写表单
      const nameInput = screen.getByLabelText(/分类名称/)
      fireEvent.change(nameInput, { target: { value: '测试名称' } })

      // 提交表单
      const submitButton = screen.getByText('创建分类')
      fireEvent.click(submitButton)

      // 等待验证完成（服务错误时应该允许提交）
      await waitFor(() => {
        expect(mockOnSave).toHaveBeenCalled()
      })
    })
  })

  describe('键盘交互集成测试', () => {
    it('应该支持ESC键关闭模态窗口', () => {
      const mockOnClose = vi.fn()

      render(
        <CategoryModal
          isOpen={true}
          type="create"
          onSave={vi.fn()}
          onClose={mockOnClose}
        />
      )

      // 按ESC键
      fireEvent.keyDown(document, { key: 'Escape' })

      expect(mockOnClose).toHaveBeenCalled()
    })

    it('应该在加载时忽略ESC键', () => {
      const mockOnClose = vi.fn()

      render(
        <CategoryModal
          isOpen={true}
          type="create"
          onSave={vi.fn()}
          onClose={mockOnClose}
          loading={true}
        />
      )

      // 按ESC键
      fireEvent.keyDown(document, { key: 'Escape' })

      expect(mockOnClose).not.toHaveBeenCalled()
    })
  })
})