import { describe, it, expect } from 'vitest'
import { SerializationUtils } from '../src/utils/serialization.ts'

describe('SerializationUtils', () => {
  const mockBookmark = {
    id: 'test-id',
    type: 'url',
    title: '测试书签',
    url: 'https://example.com',
    content: undefined,
    description: '测试描述',
    tags: ['测试', '示例'],
    category: '工作',
    favicon: 'https://example.com/favicon.ico',
    thumbnail: undefined,
    createdAt: new Date('2023-01-01T00:00:00.000Z'),
    updatedAt: new Date('2023-01-02T00:00:00.000Z'),
    metadata: {
      pageTitle: '示例页面',
      siteName: 'example.com',
      author: '作者',
      publishDate: new Date('2023-01-01T12:00:00.000Z'),
      wordCount: 100,
      language: 'zh-CN',
      aiGenerated: false
    }
  }

  const mockCategory = {
    id: 'cat-id',
    name: '工作',
    description: '工作相关',
    color: '#FF5733',
    parentId: undefined,
    createdAt: new Date('2023-01-01T00:00:00.000Z'),
    updatedAt: new Date('2023-01-02T00:00:00.000Z'),
    bookmarkCount: 5
  }

  const mockTag = {
    id: 'tag-id',
    name: '重要',
    color: '#FF5733',
    usageCount: 10,
    createdAt: new Date('2023-01-01T00:00:00.000Z'),
    updatedAt: new Date('2023-01-02T00:00:00.000Z')
  }

  describe('书签序列化和反序列化', () => {
    it('应该正确序列化书签对象', () => {
      const json = SerializationUtils.serializeBookmark(mockBookmark)
      const parsed = JSON.parse(json)

      expect(parsed.id).toBe(mockBookmark.id)
      expect(parsed.title).toBe(mockBookmark.title)
      expect(parsed.createdAt).toBe('2023-01-01T00:00:00.000Z')
      expect(parsed.updatedAt).toBe('2023-01-02T00:00:00.000Z')
      expect(parsed.metadata.publishDate).toBe('2023-01-01T12:00:00.000Z')
    })

    it('应该正确反序列化书签对象', () => {
      const json = SerializationUtils.serializeBookmark(mockBookmark)
      const deserialized = SerializationUtils.deserializeBookmark(json)

      expect(deserialized.id).toBe(mockBookmark.id)
      expect(deserialized.title).toBe(mockBookmark.title)
      expect(deserialized.createdAt).toEqual(mockBookmark.createdAt)
      expect(deserialized.updatedAt).toEqual(mockBookmark.updatedAt)
      expect(deserialized.metadata.publishDate).toEqual(mockBookmark.metadata.publishDate)
    })

    it('应该正确序列化书签数组', () => {
      const bookmarks = [mockBookmark, { ...mockBookmark, id: 'test-id-2' }]
      const json = SerializationUtils.serializeBookmarks(bookmarks)
      const parsed = JSON.parse(json)

      expect(parsed).toHaveLength(2)
      expect(parsed[0].id).toBe('test-id')
      expect(parsed[1].id).toBe('test-id-2')
    })

    it('应该正确反序列化书签数组', () => {
      const bookmarks = [mockBookmark, { ...mockBookmark, id: 'test-id-2' }]
      const json = SerializationUtils.serializeBookmarks(bookmarks)
      const deserialized = SerializationUtils.deserializeBookmarks(json)

      expect(deserialized).toHaveLength(2)
      expect(deserialized[0].id).toBe('test-id')
      expect(deserialized[1].id).toBe('test-id-2')
      expect(deserialized[0].createdAt).toEqual(mockBookmark.createdAt)
    })
  })

  describe('分类序列化和反序列化', () => {
    it('应该正确序列化分类对象', () => {
      const json = SerializationUtils.serializeCategory(mockCategory)
      const parsed = JSON.parse(json)

      expect(parsed.id).toBe(mockCategory.id)
      expect(parsed.name).toBe(mockCategory.name)
      expect(parsed.createdAt).toBe('2023-01-01T00:00:00.000Z')
      expect(parsed.updatedAt).toBe('2023-01-02T00:00:00.000Z')
    })

    it('应该正确反序列化分类对象', () => {
      const json = SerializationUtils.serializeCategory(mockCategory)
      const deserialized = SerializationUtils.deserializeCategory(json)

      expect(deserialized.id).toBe(mockCategory.id)
      expect(deserialized.name).toBe(mockCategory.name)
      expect(deserialized.createdAt).toEqual(mockCategory.createdAt)
      expect(deserialized.updatedAt).toEqual(mockCategory.updatedAt)
    })
  })

  describe('标签序列化和反序列化', () => {
    it('应该正确序列化标签对象', () => {
      const json = SerializationUtils.serializeTag(mockTag)
      const parsed = JSON.parse(json)

      expect(parsed.id).toBe(mockTag.id)
      expect(parsed.name).toBe(mockTag.name)
      expect(parsed.createdAt).toBe('2023-01-01T00:00:00.000Z')
      expect(parsed.updatedAt).toBe('2023-01-02T00:00:00.000Z')
    })

    it('应该正确反序列化标签对象', () => {
      const json = SerializationUtils.serializeTag(mockTag)
      const deserialized = SerializationUtils.deserializeTag(json)

      expect(deserialized.id).toBe(mockTag.id)
      expect(deserialized.name).toBe(mockTag.name)
      expect(deserialized.createdAt).toEqual(mockTag.createdAt)
      expect(deserialized.updatedAt).toEqual(mockTag.updatedAt)
    })
  })

  describe('对象创建', () => {
    it('应该从输入创建完整的书签对象', () => {
      const input = {
        type: 'url',
        title: '测试书签',
        url: 'https://example.com',
        description: '测试描述',
        tags: ['测试'],
        category: '工作'
      }

      const bookmark = SerializationUtils.createBookmarkFromInput(input)

      expect(bookmark.id).toBeDefined()
      expect(bookmark.type).toBe(input.type)
      expect(bookmark.title).toBe(input.title)
      expect(bookmark.url).toBe(input.url)
      expect(bookmark.description).toBe(input.description)
      expect(bookmark.tags).toEqual(input.tags)
      expect(bookmark.category).toBe(input.category)
      expect(bookmark.createdAt).toBeInstanceOf(Date)
      expect(bookmark.updatedAt).toBeInstanceOf(Date)
      expect(bookmark.metadata).toBeDefined()
    })

    it('应该从输入创建完整的分类对象', () => {
      const input = {
        name: '工作',
        description: '工作相关',
        color: '#FF5733'
      }

      const category = SerializationUtils.createCategoryFromInput(input)

      expect(category.id).toBeDefined()
      expect(category.name).toBe(input.name)
      expect(category.description).toBe(input.description)
      expect(category.color).toBe(input.color)
      expect(category.createdAt).toBeInstanceOf(Date)
      expect(category.updatedAt).toBeInstanceOf(Date)
      expect(category.bookmarkCount).toBe(0)
    })

    it('应该从输入创建完整的标签对象', () => {
      const input = {
        name: '重要',
        color: '#FF5733'
      }

      const tag = SerializationUtils.createTagFromInput(input)

      expect(tag.id).toBeDefined()
      expect(tag.name).toBe(input.name)
      expect(tag.color).toBe(input.color)
      expect(tag.createdAt).toBeInstanceOf(Date)
      expect(tag.updatedAt).toBeInstanceOf(Date)
      expect(tag.usageCount).toBe(0)
    })
  })

  describe('工具函数', () => {
    it('应该生成唯一ID', () => {
      const id1 = SerializationUtils.generateId()
      const id2 = SerializationUtils.generateId()

      expect(id1).toBeDefined()
      expect(id2).toBeDefined()
      expect(id1).not.toBe(id2)
      expect(typeof id1).toBe('string')
      expect(typeof id2).toBe('string')
    })

    it('应该深拷贝对象', () => {
      const original = {
        name: '测试',
        date: new Date('2023-01-01'),
        nested: {
          value: 42,
          array: [1, 2, 3]
        }
      }

      const cloned = SerializationUtils.deepClone(original)

      expect(cloned).toEqual(original)
      expect(cloned).not.toBe(original)
      expect(cloned.nested).not.toBe(original.nested)
      expect(cloned.nested.array).not.toBe(original.nested.array)
      expect(cloned.date).toEqual(original.date)
      expect(cloned.date).not.toBe(original.date)
    })

    it('应该正确比较对象相等性', () => {
      const obj1 = {
        name: '测试',
        date: new Date('2023-01-01'),
        nested: { value: 42 },
        array: [1, 2, 3]
      }

      const obj2 = {
        name: '测试',
        date: new Date('2023-01-01'),
        nested: { value: 42 },
        array: [1, 2, 3]
      }

      const obj3 = {
        name: '测试',
        date: new Date('2023-01-02'),
        nested: { value: 42 },
        array: [1, 2, 3]
      }

      expect(SerializationUtils.deepEqual(obj1, obj2)).toBe(true)
      expect(SerializationUtils.deepEqual(obj1, obj3)).toBe(false)
      expect(SerializationUtils.deepEqual(null, null)).toBe(true)
      expect(SerializationUtils.deepEqual(null, obj1)).toBe(false)
    })
  })
})