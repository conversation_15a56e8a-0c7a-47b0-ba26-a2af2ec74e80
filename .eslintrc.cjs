module.exports = {
  root: true,
  env: { 
    browser: true, 
    es2020: true,
    webextensions: true
  },
  extends: [
    'eslint:recommended',
    '@typescript-eslint/recommended',
    'plugin:react-hooks/recommended',
  ],
  ignorePatterns: ['dist', '.eslintrc.cjs'],
  parser: '@typescript-eslint/parser',
  plugins: ['react-refresh'],
  rules: {
    'react-refresh/only-export-components': [
      'warn',
      { allowConstantExport: true },
    ],
    // 允许console.log用于调试
    'no-console': 'off',
    // 允许any类型（开发阶段）
    '@typescript-eslint/no-explicit-any': 'warn',
    // 允许未使用的变量（以下划线开头）
    '@typescript-eslint/no-unused-vars': [
      'warn',
      { argsIgnorePattern: '^_', varsIgnorePattern: '^_' }
    ],
    // 允许空函数
    '@typescript-eslint/no-empty-function': 'warn'
  },
  globals: {
    chrome: 'readonly'
  }
}