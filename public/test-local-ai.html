<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>本地AI服务集成测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            background: white;
            border-radius: 8px;
            padding: 24px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        h1, h2 {
            color: #333;
            margin-top: 0;
        }
        
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            background: #fafafa;
        }
        
        button {
            background: #007acc;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        
        button:hover {
            background: #005a9e;
        }
        
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        
        .success {
            color: #28a745;
            font-weight: bold;
        }
        
        .error {
            color: #dc3545;
            font-weight: bold;
        }
        
        .info {
            color: #17a2b8;
        }
        
        .result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 4px;
            background: #f8f9fa;
            border-left: 4px solid #007acc;
            white-space: pre-wrap;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .service-card {
            border: 1px solid #ddd;
            border-radius: 6px;
            padding: 15px;
            margin: 10px 0;
            background: white;
        }
        
        .service-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        
        .service-name {
            font-weight: bold;
            font-size: 16px;
        }
        
        .service-status {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        
        .status-online {
            background: #d4edda;
            color: #155724;
        }
        
        .status-offline {
            background: #f8d7da;
            color: #721c24;
        }
        
        .status-unknown {
            background: #fff3cd;
            color: #856404;
        }
        
        .service-details {
            font-size: 14px;
            color: #666;
        }
        
        .model-list {
            margin-top: 10px;
        }
        
        .model-item {
            padding: 8px;
            margin: 5px 0;
            background: #f8f9fa;
            border-radius: 4px;
            border-left: 3px solid #007acc;
        }
        
        .model-name {
            font-weight: bold;
            color: #333;
        }
        
        .model-details {
            font-size: 12px;
            color: #666;
            margin-top: 4px;
        }
        
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #007acc;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .input-group {
            margin: 10px 0;
        }
        
        .input-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        
        .input-group input {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .flex {
            display: flex;
            gap: 10px;
            align-items: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🤖 本地AI服务集成测试</h1>
        <p>这个页面用于测试本地AI服务的发现、连接和模型获取功能。</p>
    </div>

    <div class="container">
        <div class="section">
            <h2>🔍 服务发现测试</h2>
            <p>自动扫描本地AI服务，包括Ollama、LM Studio、Xinference等。</p>
            
            <div class="input-group">
                <label>自定义端口 (用逗号分隔):</label>
                <input type="text" id="customPorts" placeholder="例如: 8888,9999,7777" />
            </div>
            
            <div class="flex">
                <button onclick="discoverServices()">🔍 发现本地服务</button>
                <button onclick="getDefaultServices()">📋 获取默认配置</button>
                <button onclick="clearResults()">🗑️ 清空结果</button>
            </div>
            
            <div id="discoveryResult" class="result" style="display: none;"></div>
        </div>
    </div>

    <div class="container">
        <div class="section">
            <h2>🔧 自定义服务测试</h2>
            <p>测试自定义本地AI服务的连接和配置。</p>
            
            <div class="input-group">
                <label>服务名称:</label>
                <input type="text" id="serviceName" placeholder="例如: My AI Service" />
            </div>
            
            <div class="input-group">
                <label>服务地址:</label>
                <input type="text" id="serviceUrl" placeholder="例如: http://localhost:8080" />
            </div>
            
            <div class="input-group">
                <label>API路径 (可选):</label>
                <input type="text" id="apiPath" placeholder="例如: /v1" />
            </div>
            
            <div class="input-group">
                <label>超时时间 (毫秒):</label>
                <input type="number" id="timeout" placeholder="10000" value="10000" />
            </div>
            
            <div class="flex">
                <button onclick="testCustomService()">🧪 测试自定义服务</button>
                <button onclick="getCustomModels()">📦 获取模型列表</button>
            </div>
            
            <div id="customResult" class="result" style="display: none;"></div>
        </div>
    </div>

    <div class="container">
        <div class="section">
            <h2>📊 服务状态面板</h2>
            <p>显示发现的服务状态和模型信息。</p>
            
            <div id="servicesPanel">
                <p class="info">点击"发现本地服务"开始扫描...</p>
            </div>
        </div>
    </div>

    <script type="module">
        // 模拟AI服务类 - 在实际环境中这些会从模块导入
        class MockLocalAIServiceAdapter {
            constructor() {
                this.defaultServices = [
                    {
                        name: 'Ollama',
                        baseUrl: 'http://localhost:11434',
                        port: 11434,
                        protocol: 'http',
                        apiPath: '/api',
                        healthCheckPath: '/api/version',
                        modelsPath: '/api/tags',
                        timeout: 10000
                    },
                    {
                        name: 'LM Studio',
                        baseUrl: 'http://localhost:1234',
                        port: 1234,
                        protocol: 'http',
                        apiPath: '/v1',
                        healthCheckPath: '/v1/models',
                        modelsPath: '/v1/models',
                        timeout: 10000
                    },
                    {
                        name: 'Xinference',
                        baseUrl: 'http://localhost:9997',
                        port: 9997,
                        protocol: 'http',
                        apiPath: '/v1',
                        healthCheckPath: '/v1/cluster/status',
                        modelsPath: '/v1/models',
                        timeout: 10000
                    }
                ];
            }

            async discoverLocalServices(customPorts = []) {
                const services = [];
                const errors = [];
                const commonPorts = [1234, 5000, 7860, 8000, 8080, 9997, 11434];
                const portsToScan = [...new Set([...commonPorts, ...customPorts])];

                console.log(`开始扫描 ${portsToScan.length} 个端口...`);

                for (const port of portsToScan) {
                    try {
                        const service = await this.scanPort(port);
                        if (service) {
                            services.push(service);
                        }
                    } catch (error) {
                        errors.push(`端口 ${port}: ${error.message}`);
                    }
                }

                return { services, errors };
            }

            async scanPort(port) {
                const baseUrl = `http://localhost:${port}`;
                
                try {
                    const response = await fetch(`${baseUrl}/api/version`, {
                        method: 'GET',
                        signal: AbortSignal.timeout(3000)
                    });

                    if (response.ok) {
                        return {
                            name: port === 11434 ? 'Ollama' : `Service on ${port}`,
                            baseUrl,
                            port,
                            protocol: 'http',
                            healthCheckPath: '/api/version',
                            modelsPath: '/api/tags',
                            timeout: 10000,
                            status: 'online'
                        };
                    }
                } catch (error) {
                    // 尝试其他端点
                    try {
                        const response = await fetch(`${baseUrl}/v1/models`, {
                            method: 'GET',
                            signal: AbortSignal.timeout(3000)
                        });

                        if (response.ok) {
                            return {
                                name: port === 1234 ? 'LM Studio' : 
                                      port === 9997 ? 'Xinference' : 
                                      `Service on ${port}`,
                                baseUrl,
                                port,
                                protocol: 'http',
                                healthCheckPath: '/v1/models',
                                modelsPath: '/v1/models',
                                timeout: 10000,
                                status: 'online'
                            };
                        }
                    } catch (innerError) {
                        // 服务不可用
                    }
                }

                throw new Error(`无法连接到端口 ${port}`);
            }

            async testLocalServiceConnection(config) {
                const startTime = Date.now();
                
                try {
                    const healthCheckUrl = `${config.baseUrl}${config.healthCheckPath || '/models'}`;
                    
                    const response = await fetch(healthCheckUrl, {
                        method: 'GET',
                        headers: { 'Content-Type': 'application/json' },
                        signal: AbortSignal.timeout(config.timeout || 10000)
                    });

                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }

                    const responseTime = Date.now() - startTime;

                    return {
                        providerId: config.name.toLowerCase().replace(/\s+/g, '-'),
                        success: true,
                        responseTime,
                        modelCount: Math.floor(Math.random() * 5) + 1, // 模拟模型数量
                        testedAt: new Date()
                    };
                } catch (error) {
                    const responseTime = Date.now() - startTime;
                    
                    return {
                        providerId: config.name.toLowerCase().replace(/\s+/g, '-'),
                        success: false,
                        responseTime,
                        error: error.message,
                        testedAt: new Date()
                    };
                }
            }

            async getLocalServiceModels(config) {
                try {
                    const modelsUrl = `${config.baseUrl}${config.modelsPath || '/models'}`;
                    
                    const response = await fetch(modelsUrl, {
                        method: 'GET',
                        headers: { 'Content-Type': 'application/json' },
                        signal: AbortSignal.timeout(config.timeout || 15000)
                    });

                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }

                    // 模拟不同服务的响应格式
                    const mockModels = this.generateMockModels(config.name);
                    return mockModels;
                } catch (error) {
                    console.error(`获取${config.name}模型列表失败:`, error);
                    return [];
                }
            }

            generateMockModels(serviceName) {
                const models = [];
                
                if (serviceName === 'Ollama') {
                    models.push(
                        {
                            id: 'llama2:7b',
                            name: 'llama2:7b',
                            displayName: 'Llama 2 7B',
                            description: 'Meta开发的大型语言模型 (Ollama本地部署)',
                            size: '3.8 GB',
                            parameters: '7B',
                            capabilities: ['chat', 'completion'],
                            tags: ['Ollama', 'llama', 'Q4_0', '7B参数'],
                            providerId: 'ollama',
                            isRecommended: true,
                            isPopular: true
                        },
                        {
                            id: 'qwen:14b',
                            name: 'qwen:14b',
                            displayName: 'Qwen 14B',
                            description: '阿里巴巴开发的通义千问模型 (Ollama本地部署)',
                            size: '8.2 GB',
                            parameters: '14B',
                            capabilities: ['chat', 'completion'],
                            tags: ['Ollama', 'qwen', 'Q4_0', '14B参数'],
                            providerId: 'ollama',
                            isRecommended: true,
                            isPopular: true
                        }
                    );
                } else if (serviceName === 'LM Studio') {
                    models.push(
                        {
                            id: 'models/llama-2-7b-chat.gguf',
                            name: 'llama-2-7b-chat.gguf',
                            displayName: 'Llama 2 7B Chat',
                            description: 'Meta开发的大型语言模型 (LM Studio本地部署)',
                            capabilities: ['chat', 'completion', 'instruction-following'],
                            tags: ['LM Studio', '对话', '7B参数'],
                            providerId: 'lm-studio',
                            isRecommended: true,
                            isPopular: true
                        }
                    );
                } else if (serviceName === 'Xinference') {
                    models.push(
                        {
                            id: 'chatglm3-6b-uid',
                            name: 'chatglm3-6b',
                            displayName: 'ChatGLM3 6B',
                            description: '清华大学开发的对话语言模型 (Xinference分布式部署)',
                            size: '6B',
                            parameters: '6B',
                            capabilities: ['chat', 'instruction-following'],
                            tags: ['Xinference', 'LLM', '中文', '英文', '6B参数', '对话'],
                            providerId: 'xinference',
                            isRecommended: true,
                            isPopular: true
                        }
                    );
                }
                
                return models;
            }

            createCustomServiceConfig(name, baseUrl, options = {}) {
                const url = new URL(baseUrl);
                
                return {
                    name,
                    baseUrl,
                    port: parseInt(url.port) || (url.protocol === 'https:' ? 443 : 80),
                    protocol: url.protocol.replace(':', ''),
                    apiPath: options.apiPath || '/v1',
                    healthCheckPath: options.healthCheckPath || '/models',
                    modelsPath: options.modelsPath || '/models',
                    timeout: options.timeout || 10000,
                    headers: options.headers || {}
                };
            }

            getDefaultServices() {
                return [...this.defaultServices];
            }
        }

        // 全局实例
        window.localAIAdapter = new MockLocalAIServiceAdapter();
        window.discoveredServices = [];

        // 全局函数
        window.discoverServices = async function() {
            const customPortsInput = document.getElementById('customPorts').value;
            const customPorts = customPortsInput ? 
                customPortsInput.split(',').map(p => parseInt(p.trim())).filter(p => !isNaN(p)) : 
                [];

            const resultDiv = document.getElementById('discoveryResult');
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = '<div class="loading"></div> 正在扫描本地AI服务...';

            try {
                const result = await window.localAIAdapter.discoverLocalServices(customPorts);
                window.discoveredServices = result.services;

                let output = `发现 ${result.services.length} 个本地AI服务:\n\n`;
                
                result.services.forEach((service, index) => {
                    output += `${index + 1}. ${service.name}\n`;
                    output += `   地址: ${service.baseUrl}\n`;
                    output += `   端口: ${service.port}\n`;
                    output += `   状态: ${service.status || '未知'}\n\n`;
                });

                if (result.errors.length > 0) {
                    output += `\n扫描错误 (${result.errors.length} 个):\n`;
                    result.errors.forEach(error => {
                        output += `- ${error}\n`;
                    });
                }

                resultDiv.innerHTML = output;
                updateServicesPanel(result.services);
            } catch (error) {
                resultDiv.innerHTML = `❌ 发现服务失败: ${error.message}`;
            }
        };

        window.getDefaultServices = function() {
            const services = window.localAIAdapter.getDefaultServices();
            const resultDiv = document.getElementById('discoveryResult');
            resultDiv.style.display = 'block';

            let output = `默认支持的本地AI服务 (${services.length} 个):\n\n`;
            
            services.forEach((service, index) => {
                output += `${index + 1}. ${service.name}\n`;
                output += `   地址: ${service.baseUrl}\n`;
                output += `   端口: ${service.port}\n`;
                output += `   健康检查: ${service.healthCheckPath}\n`;
                output += `   模型端点: ${service.modelsPath}\n\n`;
            });

            resultDiv.innerHTML = output;
        };

        window.testCustomService = async function() {
            const name = document.getElementById('serviceName').value;
            const url = document.getElementById('serviceUrl').value;
            const apiPath = document.getElementById('apiPath').value;
            const timeout = parseInt(document.getElementById('timeout').value) || 10000;

            if (!name || !url) {
                alert('请填写服务名称和地址');
                return;
            }

            const resultDiv = document.getElementById('customResult');
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = '<div class="loading"></div> 正在测试自定义服务...';

            try {
                const config = window.localAIAdapter.createCustomServiceConfig(name, url, {
                    apiPath,
                    timeout
                });

                const result = await window.localAIAdapter.testLocalServiceConnection(config);

                let output = `自定义服务测试结果:\n\n`;
                output += `服务名称: ${name}\n`;
                output += `服务地址: ${url}\n`;
                output += `连接状态: ${result.success ? '✅ 成功' : '❌ 失败'}\n`;
                output += `响应时间: ${result.responseTime}ms\n`;
                
                if (result.success) {
                    output += `模型数量: ${result.modelCount || 0}\n`;
                } else {
                    output += `错误信息: ${result.error}\n`;
                }
                
                output += `测试时间: ${result.testedAt.toLocaleString()}\n`;

                resultDiv.innerHTML = output;
            } catch (error) {
                resultDiv.innerHTML = `❌ 测试失败: ${error.message}`;
            }
        };

        window.getCustomModels = async function() {
            const name = document.getElementById('serviceName').value;
            const url = document.getElementById('serviceUrl').value;
            const apiPath = document.getElementById('apiPath').value;
            const timeout = parseInt(document.getElementById('timeout').value) || 10000;

            if (!name || !url) {
                alert('请填写服务名称和地址');
                return;
            }

            const resultDiv = document.getElementById('customResult');
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = '<div class="loading"></div> 正在获取模型列表...';

            try {
                const config = window.localAIAdapter.createCustomServiceConfig(name, url, {
                    apiPath,
                    timeout
                });

                const models = await window.localAIAdapter.getLocalServiceModels(config);

                let output = `模型列表 (${models.length} 个):\n\n`;
                
                models.forEach((model, index) => {
                    output += `${index + 1}. ${model.displayName}\n`;
                    output += `   ID: ${model.id}\n`;
                    output += `   描述: ${model.description}\n`;
                    if (model.size) output += `   大小: ${model.size}\n`;
                    if (model.parameters) output += `   参数: ${model.parameters}\n`;
                    output += `   能力: ${model.capabilities.join(', ')}\n`;
                    output += `   标签: ${model.tags.join(', ')}\n`;
                    output += `   推荐: ${model.isRecommended ? '是' : '否'}\n\n`;
                });

                if (models.length === 0) {
                    output = '未找到任何模型';
                }

                resultDiv.innerHTML = output;
            } catch (error) {
                resultDiv.innerHTML = `❌ 获取模型失败: ${error.message}`;
            }
        };

        window.clearResults = function() {
            document.getElementById('discoveryResult').style.display = 'none';
            document.getElementById('customResult').style.display = 'none';
            document.getElementById('servicesPanel').innerHTML = '<p class="info">点击"发现本地服务"开始扫描...</p>';
        };

        async function updateServicesPanel(services) {
            const panel = document.getElementById('servicesPanel');
            
            if (services.length === 0) {
                panel.innerHTML = '<p class="error">未发现任何本地AI服务</p>';
                return;
            }

            let html = '';
            
            for (const service of services) {
                // 测试连接状态
                const connectionResult = await window.localAIAdapter.testLocalServiceConnection(service);
                
                // 获取模型列表
                const models = connectionResult.success ? 
                    await window.localAIAdapter.getLocalServiceModels(service) : [];

                html += `
                    <div class="service-card">
                        <div class="service-header">
                            <div class="service-name">${service.name}</div>
                            <div class="service-status ${connectionResult.success ? 'status-online' : 'status-offline'}">
                                ${connectionResult.success ? '在线' : '离线'}
                            </div>
                        </div>
                        <div class="service-details">
                            <div>地址: ${service.baseUrl}</div>
                            <div>端口: ${service.port}</div>
                            <div>响应时间: ${connectionResult.responseTime}ms</div>
                            ${connectionResult.success ? 
                                `<div>模型数量: ${models.length}</div>` : 
                                `<div class="error">错误: ${connectionResult.error}</div>`
                            }
                        </div>
                        ${models.length > 0 ? `
                            <div class="model-list">
                                <strong>可用模型:</strong>
                                ${models.map(model => `
                                    <div class="model-item">
                                        <div class="model-name">${model.displayName}</div>
                                        <div class="model-details">
                                            ${model.description}
                                            ${model.size ? ` | 大小: ${model.size}` : ''}
                                            ${model.parameters ? ` | 参数: ${model.parameters}` : ''}
                                        </div>
                                    </div>
                                `).join('')}
                            </div>
                        ` : ''}
                    </div>
                `;
            }
            
            panel.innerHTML = html;
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('本地AI服务测试页面已加载');
            
            // 设置默认值
            document.getElementById('serviceName').value = 'Test Service';
            document.getElementById('serviceUrl').value = 'http://localhost:8080';
            document.getElementById('apiPath').value = '/v1';
        });
    </script>
</body>
</html>