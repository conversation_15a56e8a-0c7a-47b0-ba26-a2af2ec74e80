/**
 * VirtualBookmarkList shadcn重构验证脚本
 * 验证组件是否正确使用了shadcn组件和颜色系统
 */

const fs = require('fs')
const path = require('path')

// 颜色输出函数
const colors = {
  green: (text) => `\x1b[32m${text}\x1b[0m`,
  red: (text) => `\x1b[31m${text}\x1b[0m`,
  yellow: (text) => `\x1b[33m${text}\x1b[0m`,
  blue: (text) => `\x1b[34m${text}\x1b[0m`,
  cyan: (text) => `\x1b[36m${text}\x1b[0m`
}

console.log(colors.cyan('🔍 VirtualBookmarkList shadcn重构验证'))
console.log('=' .repeat(50))

// 检查文件是否存在
function checkFileExists(filePath) {
  const fullPath = path.resolve(filePath)
  const exists = fs.existsSync(fullPath)
  console.log(`${exists ? colors.green('✓') : colors.red('✗')} ${filePath}`)
  return exists
}

// 检查文件内容
function checkFileContent(filePath, checks) {
  try {
    const content = fs.readFileSync(path.resolve(filePath), 'utf8')
    let allPassed = true
    
    console.log(colors.blue(`\n📄 检查 ${filePath}:`))
    
    checks.forEach(({ name, pattern, required = true }) => {
      const found = pattern.test(content)
      const status = found ? colors.green('✓') : (required ? colors.red('✗') : colors.yellow('⚠'))
      console.log(`  ${status} ${name}`)
      
      if (required && !found) {
        allPassed = false
      }
    })
    
    return allPassed
  } catch (error) {
    console.log(colors.red(`✗ 无法读取文件: ${filePath}`))
    return false
  }
}

let allTestsPassed = true

// 1. 检查主要文件是否存在
console.log(colors.yellow('\n1. 检查文件存在性:'))
const requiredFiles = [
  'src/components/VirtualBookmarkList.tsx',
  'tests/VirtualBookmarkList.shadcn.test.tsx',
  'src/components/examples/VirtualBookmarkListDemo.tsx'
]

requiredFiles.forEach(file => {
  if (!checkFileExists(file)) {
    allTestsPassed = false
  }
})

// 2. 检查VirtualBookmarkList组件的shadcn集成
console.log(colors.yellow('\n2. 检查VirtualBookmarkList组件shadcn集成:'))
const componentChecks = [
  {
    name: '导入Button组件',
    pattern: /import\s+{\s*Button\s*}\s+from\s+['"]\.\/ui\/button['"]/
  },
  {
    name: '导入Badge组件',
    pattern: /import\s+{\s*Badge\s*}\s+from\s+['"]\.\/ui\/badge['"]/
  },
  {
    name: '导入cn工具函数',
    pattern: /import\s+{\s*cn\s*}\s+from\s+['"]@\/lib\/utils['"]/
  },
  {
    name: '使用Button组件替换自定义按钮',
    pattern: /<Button[\s\S]*?variant=["']ghost["'][\s\S]*?>/
  },
  {
    name: '使用Badge组件显示标签',
    pattern: /<Badge[\s\S]*?variant=["']secondary["'][\s\S]*?>/
  },
  {
    name: '使用shadcn颜色系统 - text-foreground',
    pattern: /text-foreground/
  },
  {
    name: '使用shadcn颜色系统 - text-muted-foreground',
    pattern: /text-muted-foreground/
  },
  {
    name: '使用shadcn颜色系统 - text-primary',
    pattern: /text-primary/
  },
  {
    name: '使用shadcn颜色系统 - border-border',
    pattern: /border-border/
  },
  {
    name: '使用shadcn颜色系统 - border-primary',
    pattern: /border-primary/
  },
  {
    name: '使用cn函数合并类名',
    pattern: /cn\(/
  },
  {
    name: '使用bg-muted背景色',
    pattern: /bg-muted/
  },
  {
    name: '使用text-destructive颜色',
    pattern: /text-destructive/
  }
]

if (!checkFileContent('src/components/VirtualBookmarkList.tsx', componentChecks)) {
  allTestsPassed = false
}

// 3. 检查测试文件
console.log(colors.yellow('\n3. 检查测试文件:'))
const testChecks = [
  {
    name: '测试shadcn Button组件使用',
    pattern: /应该在card视图中使用shadcn Button组件/
  },
  {
    name: '测试shadcn Badge组件使用',
    pattern: /应该使用shadcn Badge组件显示标签/
  },
  {
    name: '测试shadcn颜色系统',
    pattern: /应该使用shadcn颜色系统/
  },
  {
    name: '测试cn工具函数',
    pattern: /应该使用cn工具函数合并类名/
  },
  {
    name: '测试高亮状态样式',
    pattern: /应该为高亮项目使用shadcn主题颜色/
  },
  {
    name: '测试主题一致性',
    pattern: /shadcn主题一致性/
  },
  {
    name: '测试响应式布局',
    pattern: /响应式布局/
  }
]

if (!checkFileContent('tests/VirtualBookmarkList.shadcn.test.tsx', testChecks)) {
  allTestsPassed = false
}

// 4. 检查示例组件
console.log(colors.yellow('\n4. 检查示例组件:'))
const demoChecks = [
  {
    name: '导入VirtualBookmarkList组件',
    pattern: /import VirtualBookmarkList from ['"]\.\.\/VirtualBookmarkList['"]/
  },
  {
    name: '导入shadcn Button组件',
    pattern: /import\s+{\s*Button\s*}\s+from\s+['"]\.\.\/ui\/button['"]/
  },
  {
    name: '导入shadcn Badge组件',
    pattern: /import\s+{\s*Badge\s*}\s+from\s+['"]\.\.\/ui\/badge['"]/
  },
  {
    name: '使用VirtualBookmarkList组件',
    pattern: /<VirtualBookmarkList[\s\S]*?\/>/
  },
  {
    name: '展示shadcn组件特性',
    pattern: /shadcn组件特性/
  },
  {
    name: '包含控制面板',
    pattern: /控制面板/
  },
  {
    name: '使用shadcn颜色类',
    pattern: /text-foreground|text-muted-foreground|border-border/
  }
]

if (!checkFileContent('src/components/examples/VirtualBookmarkListDemo.tsx', demoChecks)) {
  allTestsPassed = false
}

// 5. 检查特定的shadcn集成模式
console.log(colors.yellow('\n5. 检查shadcn集成模式:'))

// 检查Button组件的正确使用
const buttonPatterns = [
  {
    name: 'Button使用ghost变体',
    pattern: /variant=["']ghost["']/,
    file: 'src/components/VirtualBookmarkList.tsx'
  },
  {
    name: 'Button使用icon尺寸',
    pattern: /size=["']icon["']/,
    file: 'src/components/VirtualBookmarkList.tsx'
  },
  {
    name: 'Button包含正确的类名',
    pattern: /className=["'][^"']*h-8 w-8[^"']*["']/,
    file: 'src/components/VirtualBookmarkList.tsx'
  }
]

buttonPatterns.forEach(({ name, pattern, file }) => {
  try {
    const content = fs.readFileSync(path.resolve(file), 'utf8')
    const found = pattern.test(content)
    console.log(`  ${found ? colors.green('✓') : colors.red('✗')} ${name}`)
    if (!found) allTestsPassed = false
  } catch (error) {
    console.log(`  ${colors.red('✗')} ${name} (文件读取失败)`)
    allTestsPassed = false
  }
})

// 6. 检查颜色系统的一致性
console.log(colors.yellow('\n6. 检查颜色系统一致性:'))

const colorSystemChecks = [
  {
    name: '移除了自定义颜色类 (gray-400)',
    pattern: /text-gray-400/,
    shouldNotExist: true,
    file: 'src/components/VirtualBookmarkList.tsx'
  },
  {
    name: '移除了自定义颜色类 (gray-600)',
    pattern: /text-gray-600/,
    shouldNotExist: true,
    file: 'src/components/VirtualBookmarkList.tsx'
  },
  {
    name: '移除了自定义颜色类 (primary-600)',
    pattern: /text-primary-600/,
    shouldNotExist: true,
    file: 'src/components/VirtualBookmarkList.tsx'
  },
  {
    name: '移除了自定义边框类 (border-gray-200)',
    pattern: /border-gray-200/,
    shouldNotExist: true,
    file: 'src/components/VirtualBookmarkList.tsx'
  },
  {
    name: '移除了自定义背景类 (bg-primary-50)',
    pattern: /bg-primary-50/,
    shouldNotExist: true,
    file: 'src/components/VirtualBookmarkList.tsx'
  }
]

colorSystemChecks.forEach(({ name, pattern, shouldNotExist, file }) => {
  try {
    const content = fs.readFileSync(path.resolve(file), 'utf8')
    const found = pattern.test(content)
    const passed = shouldNotExist ? !found : found
    console.log(`  ${passed ? colors.green('✓') : colors.red('✗')} ${name}`)
    if (!passed) allTestsPassed = false
  } catch (error) {
    console.log(`  ${colors.red('✗')} ${name} (文件读取失败)`)
    allTestsPassed = false
  }
})

// 7. 总结
console.log('\n' + '='.repeat(50))
if (allTestsPassed) {
  console.log(colors.green('🎉 所有检查通过！VirtualBookmarkList组件已成功重构为使用shadcn组件'))
  console.log(colors.green('\n✅ 重构完成的功能:'))
  console.log(colors.green('  • 使用shadcn Button组件替换自定义按钮'))
  console.log(colors.green('  • 使用shadcn Badge组件替换标签显示'))
  console.log(colors.green('  • 使用shadcn颜色系统替换自定义颜色'))
  console.log(colors.green('  • 确保与shadcn主题系统的一致性'))
  console.log(colors.green('  • 使用cn工具函数合并类名'))
  console.log(colors.green('  • 创建了完整的测试覆盖'))
  console.log(colors.green('  • 提供了示例组件展示'))
} else {
  console.log(colors.red('❌ 部分检查未通过，请检查上述问题'))
  console.log(colors.yellow('\n🔧 建议的修复步骤:'))
  console.log(colors.yellow('  1. 确保所有必需的文件都已创建'))
  console.log(colors.yellow('  2. 检查shadcn组件的导入和使用'))
  console.log(colors.yellow('  3. 验证颜色系统的正确应用'))
  console.log(colors.yellow('  4. 运行测试确保功能正常'))
}

console.log(colors.cyan('\n📋 下一步:'))
console.log(colors.cyan('  • 运行测试: npm test VirtualBookmarkList.shadcn.test.tsx'))
console.log(colors.cyan('  • 查看示例: 打开 src/components/examples/VirtualBookmarkListDemo.tsx'))
console.log(colors.cyan('  • 验证集成: 在应用中测试VirtualBookmarkList组件'))

process.exit(allTestsPassed ? 0 : 1)