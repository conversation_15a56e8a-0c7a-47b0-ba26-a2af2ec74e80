// AddBookmarkModal重构验证脚本

const fs = require('fs')
const path = require('path')

console.log('🔍 开始验证AddBookmarkModal shadcn重构...\n')

// 检查组件文件是否存在
const componentPath = path.join(__dirname, '../src/components/AddBookmarkModal.tsx')
if (!fs.existsSync(componentPath)) {
  console.error('❌ AddBookmarkModal.tsx 文件不存在')
  process.exit(1)
}

// 读取组件文件内容
const componentContent = fs.readFileSync(componentPath, 'utf8')

// 验证shadcn组件导入
const requiredImports = [
  'Dialog',
  'DialogContent',
  'DialogDescription',
  'DialogFooter',
  'DialogHeader',
  'DialogTitle',
  'Form',
  'FormControl',
  'FormField',
  'FormItem',
  'FormLabel',
  'FormMessage',
  'Input',
  'Textarea',
  'Button',
  'Select',
  'SelectContent',
  'SelectItem',
  'SelectTrigger',
  'SelectValue',
  'Badge'
]

console.log('📦 检查shadcn组件导入:')
let importChecksPassed = 0
requiredImports.forEach(importName => {
  if (componentContent.includes(importName)) {
    console.log(`  ✅ ${importName} - 已导入`)
    importChecksPassed++
  } else {
    console.log(`  ❌ ${importName} - 未找到`)
  }
})

console.log(`\n📊 导入检查结果: ${importChecksPassed}/${requiredImports.length} 通过\n`)

// 验证react-hook-form集成
console.log('🎯 检查react-hook-form集成:')
const hookFormChecks = [
  { name: 'useForm导入', pattern: 'useForm' },
  { name: 'form.control使用', pattern: 'form.control' },
  { name: 'form.handleSubmit使用', pattern: 'form.handleSubmit' },
  { name: 'watch函数使用', pattern: 'watch' },
  { name: 'setValue函数使用', pattern: 'setValue' },
  { name: 'getValues函数使用', pattern: 'getValues' }
]

let hookFormChecksPassed = 0
hookFormChecks.forEach(check => {
  if (componentContent.includes(check.pattern)) {
    console.log(`  ✅ ${check.name} - 已实现`)
    hookFormChecksPassed++
  } else {
    console.log(`  ❌ ${check.name} - 未找到`)
  }
})

console.log(`\n📊 react-hook-form检查结果: ${hookFormChecksPassed}/${hookFormChecks.length} 通过\n`)

// 验证功能保持完整性
console.log('🔧 检查功能完整性:')
const functionalityChecks = [
  { name: '类型切换功能', pattern: 'handleTypeChange' },
  { name: '标签管理功能', pattern: 'handleAddTag' },
  { name: '标签删除功能', pattern: 'handleRemoveTag' },
  { name: 'AI建议功能', pattern: 'handleAIGenerate' },
  { name: '表单验证功能', pattern: 'validate' },
  { name: '重复检查功能', pattern: 'checkDuplicate' },
  { name: '初始数据处理', pattern: 'initialData' },
  { name: '加载状态处理', pattern: 'loading' }
]

let functionalityChecksPassed = 0
functionalityChecks.forEach(check => {
  if (componentContent.includes(check.pattern)) {
    console.log(`  ✅ ${check.name} - 已保留`)
    functionalityChecksPassed++
  } else {
    console.log(`  ❌ ${check.name} - 未找到`)
  }
})

console.log(`\n📊 功能完整性检查结果: ${functionalityChecksPassed}/${functionalityChecks.length} 通过\n`)

// 验证自定义CSS移除
console.log('🎨 检查自定义CSS移除:')
const customCSSPatterns = [
  'className="fixed inset-0 z-50 overflow-y-auto"',
  'className="fixed inset-0 bg-black bg-opacity-50"',
  'className="flex min-h-full items-center justify-center"',
  'className="relative bg-white rounded-lg shadow-xl"'
]

let customCSSRemoved = 0
customCSSPatterns.forEach(pattern => {
  if (!componentContent.includes(pattern)) {
    console.log(`  ✅ 自定义模态样式 - 已移除`)
    customCSSRemoved++
  } else {
    console.log(`  ❌ 自定义模态样式 - 仍存在`)
  }
})

console.log(`\n📊 自定义CSS移除检查结果: ${customCSSRemoved}/${customCSSPatterns.length} 通过\n`)

// 检查测试文件
console.log('🧪 检查测试文件:')
const testFiles = [
  '../tests/AddBookmarkModal.test.tsx',
  '../tests/AddBookmarkModal.shadcn.test.tsx'
]

let testFilesPassed = 0
testFiles.forEach(testFile => {
  const testPath = path.join(__dirname, testFile)
  if (fs.existsSync(testPath)) {
    console.log(`  ✅ ${path.basename(testFile)} - 存在`)
    testFilesPassed++
  } else {
    console.log(`  ❌ ${path.basename(testFile)} - 不存在`)
  }
})

console.log(`\n📊 测试文件检查结果: ${testFilesPassed}/${testFiles.length} 通过\n`)

// 总结
const totalChecks = requiredImports.length + hookFormChecks.length + functionalityChecks.length + customCSSPatterns.length + testFiles.length
const totalPassed = importChecksPassed + hookFormChecksPassed + functionalityChecksPassed + customCSSRemoved + testFilesPassed

console.log('=' .repeat(60))
console.log('📋 AddBookmarkModal shadcn重构验证总结:')
console.log('=' .repeat(60))
console.log(`📦 shadcn组件导入: ${importChecksPassed}/${requiredImports.length} 通过`)
console.log(`🎯 react-hook-form集成: ${hookFormChecksPassed}/${hookFormChecks.length} 通过`)
console.log(`🔧 功能完整性: ${functionalityChecksPassed}/${functionalityChecks.length} 通过`)
console.log(`🎨 自定义CSS移除: ${customCSSRemoved}/${customCSSPatterns.length} 通过`)
console.log(`🧪 测试文件: ${testFilesPassed}/${testFiles.length} 通过`)
console.log('-'.repeat(60))
console.log(`🎯 总体通过率: ${totalPassed}/${totalChecks} (${Math.round(totalPassed/totalChecks*100)}%)`)

if (totalPassed === totalChecks) {
  console.log('\n🎉 AddBookmarkModal shadcn重构验证完全通过！')
  console.log('✨ 组件已成功重构为使用shadcn/ui组件')
} else {
  console.log('\n⚠️  AddBookmarkModal shadcn重构验证部分通过')
  console.log('🔧 请检查未通过的项目并进行修复')
}

console.log('\n📋 测试建议:')
console.log('1. 运行单元测试: npm test -- tests/AddBookmarkModal.test.tsx --run')
console.log('2. 运行shadcn验证测试: npm test -- tests/AddBookmarkModal.shadcn.test.tsx --run')
console.log('3. 构建项目: npm run build')
console.log('4. 在Chrome扩展中测试实际功能')
console.log('5. 验证所有交互和样式是否正常')

console.log('\n🚀 如需查看实际效果，请:')
console.log('1. 加载构建后的扩展到Chrome')
console.log('2. 打开扩展的选项页面')
console.log('3. 点击"添加收藏"按钮查看新的shadcn风格模态窗口')