const fs = require('fs')
const path = require('path')

/**
 * 验证PopupApp组件shadcn重构的完整性
 */
function verifyPopupAppShadcnRefactor() {
  console.log('🔍 开始验证PopupApp组件shadcn重构...\n')
  
  const results = {
    passed: 0,
    failed: 0,
    details: []
  }

  // 检查PopupApp组件文件
  const popupAppPath = path.join(__dirname, '../src/popup/PopupApp.tsx')
  
  if (!fs.existsSync(popupAppPath)) {
    results.failed++
    results.details.push('❌ PopupApp.tsx文件不存在')
    return results
  }

  const popupAppContent = fs.readFileSync(popupAppPath, 'utf-8')

  // 检查shadcn组件导入
  const requiredImports = [
    'Button',
    'Card',
    'CardContent', 
    'CardHeader',
    'Switch',
    'Separator'
  ]

  console.log('📦 检查shadcn组件导入...')
  requiredImports.forEach(component => {
    if (popupAppContent.includes(`import { ${component}`) || popupAppContent.includes(`, ${component}`)) {
      results.passed++
      results.details.push(`✅ 正确导入${component}组件`)
    } else {
      results.failed++
      results.details.push(`❌ 缺少${component}组件导入`)
    }
  })

  // 检查是否移除了Toggle组件导入
  console.log('\n🗑️  检查旧组件移除...')
  if (!popupAppContent.includes("import Toggle from './components/Toggle'")) {
    results.passed++
    results.details.push('✅ 已移除Toggle组件导入')
  } else {
    results.failed++
    results.details.push('❌ 仍然导入Toggle组件')
  }

  // 检查shadcn组件使用
  console.log('\n🎨 检查shadcn组件使用...')
  const componentUsageChecks = [
    { component: '<Card', description: 'Card组件使用' },
    { component: '<CardContent', description: 'CardContent组件使用' },
    { component: '<CardHeader', description: 'CardHeader组件使用' },
    { component: '<Button', description: 'Button组件使用' },
    { component: '<Switch', description: 'Switch组件使用' },
    { component: '<Separator', description: 'Separator组件使用' }
  ]

  componentUsageChecks.forEach(check => {
    if (popupAppContent.includes(check.component)) {
      results.passed++
      results.details.push(`✅ 正确使用${check.description}`)
    } else {
      results.failed++
      results.details.push(`❌ 未使用${check.description}`)
    }
  })

  // 检查是否使用shadcn颜色系统
  console.log('\n🎨 检查颜色系统...')
  const colorSystemChecks = [
    { pattern: 'text-foreground', description: '主要文本颜色' },
    { pattern: 'text-muted-foreground', description: '次要文本颜色' },
    { pattern: 'text-primary-foreground', description: '主色文本颜色' }
  ]

  colorSystemChecks.forEach(check => {
    if (popupAppContent.includes(check.pattern)) {
      results.passed++
      results.details.push(`✅ 使用shadcn ${check.description}`)
    } else {
      results.failed++
      results.details.push(`❌ 未使用shadcn ${check.description}`)
    }
  })

  // 检查是否移除了自定义CSS类
  console.log('\n🧹 检查自定义样式移除...')
  const customStyleChecks = [
    { pattern: 'text-gray-', description: 'gray颜色类' },
    { pattern: 'bg-gray-', description: 'gray背景色类' },
    { pattern: 'border-gray-', description: 'gray边框色类' }
  ]

  let customStylesFound = false
  customStyleChecks.forEach(check => {
    if (popupAppContent.includes(check.pattern)) {
      customStylesFound = true
      results.failed++
      results.details.push(`❌ 仍使用自定义${check.description}`)
    }
  })

  if (!customStylesFound) {
    results.passed++
    results.details.push('✅ 已移除大部分自定义gray颜色类')
  }

  // 检查测试文件
  console.log('\n🧪 检查测试文件...')
  const testPath = path.join(__dirname, '../tests/PopupApp.shadcn.test.tsx')
  if (fs.existsSync(testPath)) {
    results.passed++
    results.details.push('✅ shadcn重构测试文件存在')
  } else {
    results.failed++
    results.details.push('❌ 缺少shadcn重构测试文件')
  }

  // 检查演示文件
  console.log('\n📋 检查演示文件...')
  const demoPath = path.join(__dirname, '../src/components/examples/PopupAppDemo.tsx')
  if (fs.existsSync(demoPath)) {
    results.passed++
    results.details.push('✅ PopupApp演示文件存在')
  } else {
    results.failed++
    results.details.push('❌ 缺少PopupApp演示文件')
  }

  return results
}

// 运行验证
const results = verifyPopupAppShadcnRefactor()

console.log('\n' + '='.repeat(50))
console.log('📊 验证结果汇总')
console.log('='.repeat(50))

results.details.forEach(detail => {
  console.log(detail)
})

console.log('\n' + '='.repeat(50))
console.log(`✅ 通过: ${results.passed}`)
console.log(`❌ 失败: ${results.failed}`)
console.log(`📈 成功率: ${((results.passed / (results.passed + results.failed)) * 100).toFixed(1)}%`)

if (results.failed === 0) {
  console.log('\n🎉 PopupApp组件shadcn重构验证完全通过！')
  process.exit(0)
} else {
  console.log('\n⚠️  PopupApp组件shadcn重构存在问题，请检查上述失败项')
  process.exit(1)
}