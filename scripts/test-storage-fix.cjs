/**
 * 测试存储修复脚本
 * 验证AI提供商存储功能是否正常工作
 */

// 模拟Chrome存储API
const mockChromeStorage = {
  sync: {
    data: {},
    set: function(items) {
      return new Promise((resolve) => {
        Object.assign(this.data, items);
        console.log('✅ 同步存储设置成功:', Object.keys(items));
        resolve();
      });
    },
    get: function(keys) {
      return new Promise((resolve) => {
        if (keys === null) {
          resolve(this.data);
        } else if (typeof keys === 'string') {
          resolve({ [keys]: this.data[keys] });
        } else if (Array.isArray(keys)) {
          const result = {};
          keys.forEach(key => {
            if (this.data[key] !== undefined) {
              result[key] = this.data[key];
            }
          });
          resolve(result);
        } else {
          resolve(this.data);
        }
      });
    },
    remove: function(keys) {
      return new Promise((resolve) => {
        if (Array.isArray(keys)) {
          keys.forEach(key => delete this.data[key]);
        } else {
          delete this.data[keys];
        }
        console.log('✅ 同步存储删除成功:', keys);
        resolve();
      });
    },
    getBytesInUse: function() {
      return Promise.resolve(JSON.stringify(this.data).length);
    }
  },
  local: {
    data: {},
    set: function(items) {
      return new Promise((resolve) => {
        Object.assign(this.data, items);
        console.log('✅ 本地存储设置成功:', Object.keys(items));
        resolve();
      });
    },
    get: function(keys) {
      return new Promise((resolve) => {
        if (keys === null) {
          resolve(this.data);
        } else if (typeof keys === 'string') {
          resolve({ [keys]: this.data[keys] });
        } else if (Array.isArray(keys)) {
          const result = {};
          keys.forEach(key => {
            if (this.data[key] !== undefined) {
              result[key] = this.data[key];
            }
          });
          resolve(result);
        } else {
          resolve(this.data);
        }
      });
    },
    remove: function(keys) {
      return new Promise((resolve) => {
        if (Array.isArray(keys)) {
          keys.forEach(key => delete this.data[key]);
        } else {
          delete this.data[keys];
        }
        console.log('✅ 本地存储删除成功:', keys);
        resolve();
      });
    },
    getBytesInUse: function() {
      return Promise.resolve(JSON.stringify(this.data).length);
    }
  },
  onChanged: {
    addListener: function(callback) {
      console.log('✅ 存储变化监听器已添加');
    },
    removeListener: function(callback) {
      console.log('✅ 存储变化监听器已移除');
    }
  }
};

// 设置全局Chrome对象
global.chrome = {
  storage: mockChromeStorage
};

// 模拟crypto.randomUUID
const { randomUUID } = require('crypto');
global.crypto = global.crypto || {};
global.crypto.randomUUID = randomUUID;

// 导入所需模块
const fs = require('fs');
const path = require('path');

// 简化的ChromeStorageService实现用于测试
class ChromeStorageService {
  static SYNC_KEYS = [
    'ai_config',
    'ai_providers',
    'ui_preferences',
    'sync_config',
    'privacy_settings',
    'feature_flags'
  ];

  static LOCAL_KEYS = [
    'cache_data',
    'temp_settings',
    'device_specific_config',
    'performance_data',
    'debug_logs'
  ];

  static SYNC_PREFIX = 'ub_sync_';
  static LOCAL_PREFIX = 'ub_local_';
  static SYNC_ITEM_MAX_SIZE = 8192;
  static SYNC_QUOTA_BYTES = 102400;
  static LOCAL_QUOTA_BYTES = 5242880;

  static async saveSyncSetting(key, value) {
    if (!this.SYNC_KEYS.includes(key)) {
      throw new Error(`键 "${key}" 不允许同步存储`);
    }

    const storageKey = this.SYNC_PREFIX + key;
    const serializedValue = JSON.stringify(value);
    
    if (serializedValue.length > this.SYNC_ITEM_MAX_SIZE) {
      throw new Error(`数据大小超过同步存储限制 (${serializedValue.length} > ${this.SYNC_ITEM_MAX_SIZE} 字节)`);
    }

    try {
      await chrome.storage.sync.set({ [storageKey]: value });
      console.log(`同步设置已保存: ${key}`);
    } catch (error) {
      console.error(`保存同步设置失败: ${key}`, error);
      throw new Error(`保存同步设置失败: ${error.message}`);
    }
  }

  static async getSyncSetting(key, defaultValue = null) {
    if (!this.SYNC_KEYS.includes(key)) {
      throw new Error(`键 "${key}" 不允许同步存储`);
    }

    const storageKey = this.SYNC_PREFIX + key;

    try {
      const result = await chrome.storage.sync.get(storageKey);
      return result[storageKey] !== undefined ? result[storageKey] : defaultValue;
    } catch (error) {
      console.error(`获取同步设置失败: ${key}`, error);
      return defaultValue;
    }
  }

  static async saveLocalSetting(key, value) {
    if (!this.LOCAL_KEYS.includes(key)) {
      throw new Error(`键 "${key}" 不允许本地存储`);
    }

    const storageKey = this.LOCAL_PREFIX + key;

    try {
      await chrome.storage.local.set({ [storageKey]: value });
      console.log(`本地设置已保存: ${key}`);
    } catch (error) {
      console.error(`保存本地设置失败: ${key}`, error);
      throw new Error(`保存本地设置失败: ${error.message}`);
    }
  }

  static async getLocalSetting(key, defaultValue = null) {
    if (!this.LOCAL_KEYS.includes(key)) {
      throw new Error(`键 "${key}" 不允许本地存储`);
    }

    const storageKey = this.LOCAL_PREFIX + key;

    try {
      const result = await chrome.storage.local.get(storageKey);
      return result[storageKey] !== undefined ? result[storageKey] : defaultValue;
    } catch (error) {
      console.error(`获取本地设置失败: ${key}`, error);
      return defaultValue;
    }
  }

  static async getAllSyncSettings() {
    try {
      const result = await chrome.storage.sync.get(null);
      const syncSettings = {};

      for (const [storageKey, value] of Object.entries(result)) {
        if (storageKey.startsWith(this.SYNC_PREFIX)) {
          const key = storageKey.replace(this.SYNC_PREFIX, '');
          syncSettings[key] = value;
        }
      }

      return syncSettings;
    } catch (error) {
      console.error('获取所有同步设置失败:', error);
      return {};
    }
  }

  static async getAllLocalSettings() {
    try {
      const result = await chrome.storage.local.get(null);
      const localSettings = {};

      for (const [storageKey, value] of Object.entries(result)) {
        if (storageKey.startsWith(this.LOCAL_PREFIX)) {
          const key = storageKey.replace(this.LOCAL_PREFIX, '');
          localSettings[key] = value;
        }
      }

      return localSettings;
    } catch (error) {
      console.error('获取所有本地设置失败:', error);
      return {};
    }
  }

  static async getStorageUsage() {
    try {
      const [syncUsage, localUsage] = await Promise.all([
        chrome.storage.sync.getBytesInUse(),
        chrome.storage.local.getBytesInUse()
      ]);

      return {
        sync: {
          used: syncUsage,
          total: this.SYNC_QUOTA_BYTES,
          percentage: Math.round((syncUsage / this.SYNC_QUOTA_BYTES) * 100)
        },
        local: {
          used: localUsage,
          total: this.LOCAL_QUOTA_BYTES,
          percentage: Math.round((localUsage / this.LOCAL_QUOTA_BYTES) * 100)
        }
      };
    } catch (error) {
      console.error('获取存储使用情况失败:', error);
      return {
        sync: { used: 0, total: this.SYNC_QUOTA_BYTES, percentage: 0 },
        local: { used: 0, total: this.LOCAL_QUOTA_BYTES, percentage: 0 }
      };
    }
  }

  static async exportAllSettings() {
    const [syncSettings, localSettings] = await Promise.all([
      this.getAllSyncSettings(),
      this.getAllLocalSettings()
    ]);

    return {
      sync: syncSettings,
      local: localSettings,
      exportDate: new Date().toISOString()
    };
  }

  static async batchSaveSettings(syncSettings = {}, localSettings = {}) {
    const promises = [];

    for (const [key, value] of Object.entries(syncSettings)) {
      if (this.SYNC_KEYS.includes(key)) {
        promises.push(this.saveSyncSetting(key, value));
      }
    }

    for (const [key, value] of Object.entries(localSettings)) {
      if (this.LOCAL_KEYS.includes(key)) {
        promises.push(this.saveLocalSetting(key, value));
      }
    }

    try {
      await Promise.all(promises);
      console.log('批量保存设置完成');
    } catch (error) {
      console.error('批量保存设置失败:', error);
      throw error;
    }
  }

  static getDefaultSettings() {
    return {
      sync: {
        ai_config: {
          provider: 'openai',
          model: 'gpt-3.5-turbo',
          autoTagging: true,
          autoCategories: true
        },
        ai_providers: [],
        ui_preferences: {
          theme: 'light',
          language: 'zh-CN',
          viewMode: 'card',
          itemsPerPage: 20
        },
        sync_config: {
          enabled: false,
          provider: 'notion',
          syncFrequency: 3600000
        },
        privacy_settings: {
          enableAnalytics: false,
          shareUsageData: false,
          encryptSensitiveData: true
        },
        feature_flags: {
          enableFloatingWidget: true,
          enableAIAssistant: true,
          enableCloudSync: false
        }
      },
      local: {
        cache_data: {},
        temp_settings: {},
        device_specific_config: {
          deviceId: crypto.randomUUID(),
          installDate: new Date().toISOString()
        },
        performance_data: {
          lastCleanup: new Date().toISOString(),
          cacheHitRate: 0
        },
        debug_logs: []
      }
    };
  }

  static async initializeDefaultSettings() {
    const defaults = this.getDefaultSettings();
    
    const promises = [];

    for (const [key, value] of Object.entries(defaults.sync)) {
      promises.push(
        this.getSyncSetting(key).then(existing => {
          if (existing === null) {
            return this.saveSyncSetting(key, value);
          }
        })
      );
    }

    for (const [key, value] of Object.entries(defaults.local)) {
      promises.push(
        this.getLocalSetting(key).then(existing => {
          if (existing === null) {
            return this.saveLocalSetting(key, value);
          }
        })
      );
    }

    await Promise.all(promises);
    console.log('默认设置初始化完成');
  }
}

// 测试函数
async function testStorageFix() {
  console.log('🧪 开始测试存储修复...\n');

  try {
    // 测试1: 初始化默认设置
    console.log('📋 测试1: 初始化默认设置');
    await ChromeStorageService.initializeDefaultSettings();
    console.log('✅ 默认设置初始化成功\n');

    // 测试2: 保存AI提供商配置
    console.log('📋 测试2: 保存AI提供商配置');
    const testProvider = {
      id: 'test_openai_001',
      name: 'Test OpenAI',
      type: 'openai',
      baseUrl: 'https://api.openai.com/v1',
      apiKey: 'sk-test123',
      enabled: true,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    const providers = [testProvider];
    await ChromeStorageService.saveSyncSetting('ai_providers', providers);
    console.log('✅ AI提供商配置保存成功\n');

    // 测试3: 读取AI提供商配置
    console.log('📋 测试3: 读取AI提供商配置');
    const savedProviders = await ChromeStorageService.getSyncSetting('ai_providers', []);
    console.log('✅ 读取到的提供商数量:', savedProviders.length);
    console.log('✅ 第一个提供商名称:', savedProviders[0]?.name);
    console.log('✅ AI提供商配置读取成功\n');

    // 测试4: 更新AI提供商配置
    console.log('📋 测试4: 更新AI提供商配置');
    const updatedProvider = {
      ...testProvider,
      name: 'Updated OpenAI',
      updatedAt: new Date()
    };
    const updatedProviders = [updatedProvider];
    await ChromeStorageService.saveSyncSetting('ai_providers', updatedProviders);
    console.log('✅ AI提供商配置更新成功\n');

    // 测试5: 验证更新结果
    console.log('📋 测试5: 验证更新结果');
    const finalProviders = await ChromeStorageService.getSyncSetting('ai_providers', []);
    console.log('✅ 更新后的提供商名称:', finalProviders[0]?.name);
    console.log('✅ 更新验证成功\n');

    // 测试6: 获取存储使用情况
    console.log('📋 测试6: 获取存储使用情况');
    const usage = await ChromeStorageService.getStorageUsage();
    console.log('✅ 同步存储使用:', usage.sync.used, '字节');
    console.log('✅ 本地存储使用:', usage.local.used, '字节');
    console.log('✅ 存储使用情况获取成功\n');

    // 测试7: 导出所有设置
    console.log('📋 测试7: 导出所有设置');
    const exportedSettings = await ChromeStorageService.exportAllSettings();
    console.log('✅ 导出的同步设置键:', Object.keys(exportedSettings.sync));
    console.log('✅ 导出的本地设置键:', Object.keys(exportedSettings.local));
    console.log('✅ 设置导出成功\n');

    // 测试8: 批量保存设置
    console.log('📋 测试8: 批量保存设置');
    await ChromeStorageService.batchSaveSettings(
      {
        ai_config: {
          provider: 'openai',
          model: 'gpt-4',
          autoTagging: true
        }
      },
      {
        cache_data: {
          test_cache: {
            value: 'test',
            timestamp: Date.now()
          }
        }
      }
    );
    console.log('✅ 批量设置保存成功\n');

    // 测试9: 测试错误处理
    console.log('📋 测试9: 测试错误处理');
    try {
      await ChromeStorageService.saveSyncSetting('invalid_key', 'test');
      console.log('❌ 应该抛出错误但没有');
    } catch (error) {
      console.log('✅ 正确捕获到错误:', error.message);
    }
    console.log('✅ 错误处理测试成功\n');

    console.log('🎉 所有存储测试通过！');
    console.log('✅ AI提供商存储功能已修复');
    console.log('✅ 现在可以正常保存和读取AI提供商配置');
    console.log('✅ 存储键 "ai_providers" 已添加到允许的同步存储列表');

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    console.error('❌ 错误详情:', error);
  }
}

// 运行测试
testStorageFix().then(() => {
  console.log('\n📊 测试完成');
  console.log('\n🔧 修复说明:');
  console.log('1. 在 ChromeStorageService.SYNC_KEYS 中添加了 "ai_providers"');
  console.log('2. 在默认设置中添加了空的 ai_providers 数组');
  console.log('3. 现在可以正常保存和读取AI提供商配置');
  console.log('\n📝 使用方法:');
  console.log('- 重新构建项目: npm run build');
  console.log('- 重新加载Chrome扩展');
  console.log('- 在测试页面中添加AI提供商配置');
}).catch(error => {
  console.error('\n❌ 测试异常:', error);
});