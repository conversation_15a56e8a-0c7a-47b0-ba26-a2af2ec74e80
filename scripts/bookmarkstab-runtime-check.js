/**
 * BookmarksTab组件运行时检测脚本
 * 在浏览器控制台中运行，验证组件的实际运行状态
 * 
 * 使用方法：
 * 1. 在Chrome中加载扩展
 * 2. 打开扩展选项页面
 * 3. 打开开发者工具控制台
 * 4. 复制并运行此脚本
 */

(function() {
  console.log('🔍 开始BookmarksTab组件运行时检测...\n');

  const checks = [
    {
      name: '验证BookmarksTab组件已渲染',
      check: () => {
        const bookmarksTab = document.querySelector('[role="tabpanel"]');
        return bookmarksTab !== null;
      }
    },
    {
      name: '验证shadcn Card组件样式',
      check: () => {
        const cards = document.querySelectorAll('.rounded-lg.border.bg-card');
        return cards.length > 0;
      }
    },
    {
      name: '验证shadcn Input组件存在',
      check: () => {
        const searchInput = document.querySelector('input[placeholder*="搜索"]');
        return searchInput !== null && searchInput.classList.contains('flex');
      }
    },
    {
      name: '验证shadcn Select组件存在',
      check: () => {
        const selectTrigger = document.querySelector('[role="combobox"]');
        return selectTrigger !== null;
      }
    },
    {
      name: '验证shadcn Button组件存在',
      check: () => {
        const buttons = document.querySelectorAll('button');
        let shadcnButtons = 0;
        buttons.forEach(btn => {
          if (btn.classList.contains('inline-flex') || 
              btn.classList.contains('justify-center')) {
            shadcnButtons++;
          }
        });
        return shadcnButtons > 0;
      }
    },
    {
      name: '验证收藏管理标题显示',
      check: () => {
        const title = document.querySelector('h1, h2, h3, div');
        const titleElements = Array.from(document.querySelectorAll('*')).filter(el => 
          el.textContent && el.textContent.includes('收藏管理')
        );
        return titleElements.length > 0;
      }
    },
    {
      name: '验证搜索框可交互',
      check: () => {
        const searchInput = document.querySelector('input[placeholder*="搜索"]');
        if (!searchInput) return false;
        
        // 测试输入
        searchInput.focus();
        searchInput.value = 'test';
        const event = new Event('input', { bubbles: true });
        searchInput.dispatchEvent(event);
        
        return searchInput.value === 'test';
      }
    },
    {
      name: '验证按钮可点击',
      check: () => {
        const buttons = document.querySelectorAll('button');
        let clickableButtons = 0;
        buttons.forEach(btn => {
          if (!btn.disabled && btn.offsetParent !== null) {
            clickableButtons++;
          }
        });
        return clickableButtons > 0;
      }
    },
    {
      name: '验证主题颜色变量应用',
      check: () => {
        const computedStyle = getComputedStyle(document.documentElement);
        const hasThemeVars = computedStyle.getPropertyValue('--background') !== '' ||
                           computedStyle.getPropertyValue('--foreground') !== '' ||
                           computedStyle.getPropertyValue('--primary') !== '';
        return hasThemeVars;
      }
    },
    {
      name: '验证响应式布局',
      check: () => {
        const containers = document.querySelectorAll('.flex, .grid');
        return containers.length > 0;
      }
    }
  ];

  // 执行检查
  let passedChecks = 0;
  let totalChecks = checks.length;
  const results = [];

  checks.forEach((check, index) => {
    try {
      const result = check.check();
      const status = result ? '✅ 通过' : '❌ 失败';
      const message = `${index + 1}/${totalChecks}: ${check.name} - ${status}`;
      
      console.log(message);
      results.push({ name: check.name, passed: result });
      
      if (result) {
        passedChecks++;
      }
    } catch (error) {
      const message = `${index + 1}/${totalChecks}: ${check.name} - ❌ 错误: ${error.message}`;
      console.log(message);
      results.push({ name: check.name, passed: false, error: error.message });
    }
  });

  // 输出结果
  console.log('\n' + '='.repeat(50));
  console.log(`📊 BookmarksTab运行时检测完成: ${passedChecks}/${totalChecks} 项通过`);

  if (passedChecks === totalChecks) {
    console.log('🎉 所有运行时检查都通过了！BookmarksTab组件运行正常。');
    
    console.log('\n📋 建议进行的手动测试:');
    console.log('1. 在搜索框中输入文字，观察搜索功能');
    console.log('2. 点击分类选择器，测试下拉选项');
    console.log('3. 点击"添加收藏"按钮，测试模态窗口');
    console.log('4. 点击"刷新"按钮，测试数据重新加载');
    console.log('5. 切换浏览器窗口大小，测试响应式布局');
    console.log('6. 切换主题模式，测试颜色变化');
  } else {
    console.log(`❌ 有 ${totalChecks - passedChecks} 项检查失败。`);
    
    console.log('\n🔧 失败的检查项:');
    results.forEach(result => {
      if (!result.passed) {
        console.log(`- ${result.name}${result.error ? ` (${result.error})` : ''}`);
      }
    });
  }

  // 返回检测结果供进一步分析
  window.bookmarksTabCheckResults = {
    passed: passedChecks,
    total: totalChecks,
    success: passedChecks === totalChecks,
    results: results
  };

  console.log('\n💡 检测结果已保存到 window.bookmarksTabCheckResults');
  console.log('');
})();