#!/usr/bin/env node

/**
 * 快速测试脚本
 * 
 * 功能说明:
 * - 只运行核心的、已实现的测试文件
 * - 跳过依赖不存在模块的测试
 * - 提供快速的测试反馈
 */

import { spawn } from 'child_process'
import path from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)
const rootDir = path.resolve(__dirname, '..')

console.log('🚀 运行核心测试...\n')

// 核心测试文件列表（已确认可以运行的）
const coreTests = [
  'tests/bookmarkService.test.js',
  'tests/background-service-worker.test.js', 
  'tests/popup-components.test.js',
  'scripts/test-build.test.js'
]

/**
 * 运行单个测试
 */
function runTest(testFile) {
  return new Promise((resolve) => {
    const fileName = path.basename(testFile)
    console.log(`🔄 运行: ${fileName}`)

    const child = spawn('node', [testFile], {
      stdio: 'pipe',
      cwd: rootDir
    })

    let stdout = ''
    let stderr = ''

    child.stdout.on('data', (data) => {
      stdout += data.toString()
    })

    child.stderr.on('data', (data) => {
      stderr += data.toString()
    })

    child.on('close', (code) => {
      const success = code === 0
      
      if (success) {
        console.log(`✅ ${fileName} - 通过`)
        
        // 解析测试统计
        const statsMatch = stdout.match(/测试结果:\s*(\d+)\s*通过,\s*(\d+)\s*失败/)
        if (statsMatch) {
          const passed = parseInt(statsMatch[1])
          const failed = parseInt(statsMatch[2])
          console.log(`   📊 ${passed} 通过, ${failed} 失败`)
        }
      } else {
        console.log(`❌ ${fileName} - 失败`)
        if (stderr) {
          console.log(`   错误: ${stderr.split('\n')[0]}`)
        }
      }
      
      console.log() // 空行
      resolve({ success, file: fileName, stdout, stderr })
    })
  })
}

/**
 * 主函数
 */
async function main() {
  const results = []
  let totalPassed = 0
  let totalFailed = 0
  
  for (const testFile of coreTests) {
    const result = await runTest(path.join(rootDir, testFile))
    results.push(result)
    
    // 解析统计信息
    const statsMatch = result.stdout.match(/测试结果:\s*(\d+)\s*通过,\s*(\d+)\s*失败/)
    if (statsMatch) {
      totalPassed += parseInt(statsMatch[1])
      totalFailed += parseInt(statsMatch[2])
    } else if (result.success) {
      totalPassed += 1 // 默认算作1个通过
    }
  }
  
  // 输出总结
  console.log('='.repeat(50))
  console.log('📊 测试总结')
  console.log('='.repeat(50))
  
  const successCount = results.filter(r => r.success).length
  const failCount = results.filter(r => !r.success).length
  
  console.log(`测试文件: ${coreTests.length} 个`)
  console.log(`成功: ${successCount} 个`)
  console.log(`失败: ${failCount} 个`)
  console.log(`测试用例: ${totalPassed + totalFailed} 个`)
  console.log(`通过: ${totalPassed} 个`)
  console.log(`失败: ${totalFailed} 个`)
  
  if (failCount === 0) {
    console.log('\n🎉 所有核心测试通过!')
    process.exit(0)
  } else {
    console.log('\n💥 有测试失败!')
    process.exit(1)
  }
}

main().catch(error => {
  console.error('❌ 测试运行失败:', error.message)
  process.exit(1)
})