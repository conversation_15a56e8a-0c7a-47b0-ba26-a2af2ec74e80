// 任务16运行时检查脚本 - 验证其他页面组件shadcn重构结果

/**
 * 任务16运行时检查脚本
 * 验证CategoryManagementTab、TagsTab、ImportExportTab、AboutTab、HelpCenterTab组件的shadcn重构结果
 */

class Task16RuntimeChecker {
    constructor() {
        this.results = {
            categoryManagement: { passed: false, issues: [], shadcnComponents: [] },
            tagsTab: { passed: false, issues: [], shadcnComponents: [] },
            importExportTab: { passed: false, issues: [], shadcnComponents: [] },
            aboutTab: { passed: false, issues: [], shadcnComponents: [] },
            helpCenterTab: { passed: false, issues: [], shadcnComponents: [] },
            overall: { passed: false, totalIssues: 0 }
        };
        
        this.expectedShadcnComponents = {
            categoryManagement: ['Card', 'Button', 'CardTitle', 'CardDescription'],
            tagsTab: ['Card', 'Alert', 'Button', 'CardTitle', 'CardDescription'],
            importExportTab: ['Card', 'Button', 'Input', 'Select', 'Checkbox', 'Alert', 'Progress', 'Label'],
            aboutTab: ['Card', 'Badge', 'CardTitle', 'CardDescription'],
            helpCenterTab: ['Card', 'Button', 'Badge', 'Alert', 'CardTitle', 'CardDescription']
        };
    }

    /**
     * 运行所有检查
     */
    async runAllChecks() {
        console.log('🚀 开始任务16运行时检查...');
        console.log('📋 检查范围：CategoryManagementTab、TagsTab、ImportExportTab、AboutTab、HelpCenterTab');
        
        try {
            // 检查各个组件
            await this.checkCategoryManagementTab();
            await this.checkTagsTab();
            await this.checkImportExportTab();
            await this.checkAboutTab();
            await this.checkHelpCenterTab();
            
            // 生成总体结果
            this.generateOverallResults();
            
            // 输出结果
            this.printResults();
            
            return this.results;
        } catch (error) {
            console.error('❌ 运行时检查过程中发生错误:', error);
            return { error: error.message };
        }
    }

    /**
     * 检查CategoryManagementTab组件
     */
    async checkCategoryManagementTab() {
        console.log('\n📁 检查CategoryManagementTab组件...');
        const result = this.results.categoryManagement;
        
        try {
            // 检查组件文件是否存在
            const componentExists = await this.checkFileExists('src/components/CategoryManagementTab.tsx');
            if (!componentExists) {
                result.issues.push('组件文件不存在');
                return;
            }

            // 检查shadcn组件导入
            const imports = await this.checkShadcnImports('src/components/CategoryManagementTab.tsx', this.expectedShadcnComponents.categoryManagement);
            result.shadcnComponents = imports.found;
            result.issues.push(...imports.missing.map(comp => `缺少shadcn组件导入: ${comp}`));

            // 检查组件使用
            const usage = await this.checkShadcnUsage('src/components/CategoryManagementTab.tsx', this.expectedShadcnComponents.categoryManagement);
            result.issues.push(...usage.issues);

            // 检查特定重构内容
            const specificChecks = await this.checkCategoryManagementSpecifics();
            result.issues.push(...specificChecks);

            result.passed = result.issues.length === 0;
            console.log(result.passed ? '✅ CategoryManagementTab检查通过' : `❌ CategoryManagementTab检查失败 (${result.issues.length}个问题)`);
            
        } catch (error) {
            result.issues.push(`检查过程出错: ${error.message}`);
            console.error('❌ CategoryManagementTab检查出错:', error);
        }
    }

    /**
     * 检查TagsTab组件
     */
    async checkTagsTab() {
        console.log('\n🏷️ 检查TagsTab组件...');
        const result = this.results.tagsTab;
        
        try {
            const componentExists = await this.checkFileExists('src/components/TagsTab.tsx');
            if (!componentExists) {
                result.issues.push('组件文件不存在');
                return;
            }

            const imports = await this.checkShadcnImports('src/components/TagsTab.tsx', this.expectedShadcnComponents.tagsTab);
            result.shadcnComponents = imports.found;
            result.issues.push(...imports.missing.map(comp => `缺少shadcn组件导入: ${comp}`));

            const usage = await this.checkShadcnUsage('src/components/TagsTab.tsx', this.expectedShadcnComponents.tagsTab);
            result.issues.push(...usage.issues);

            const specificChecks = await this.checkTagsTabSpecifics();
            result.issues.push(...specificChecks);

            result.passed = result.issues.length === 0;
            console.log(result.passed ? '✅ TagsTab检查通过' : `❌ TagsTab检查失败 (${result.issues.length}个问题)`);
            
        } catch (error) {
            result.issues.push(`检查过程出错: ${error.message}`);
            console.error('❌ TagsTab检查出错:', error);
        }
    }

    /**
     * 检查ImportExportTab组件
     */
    async checkImportExportTab() {
        console.log('\n📤 检查ImportExportTab组件...');
        const result = this.results.importExportTab;
        
        try {
            const componentExists = await this.checkFileExists('src/components/ImportExportTab.tsx');
            if (!componentExists) {
                result.issues.push('组件文件不存在');
                return;
            }

            const imports = await this.checkShadcnImports('src/components/ImportExportTab.tsx', this.expectedShadcnComponents.importExportTab);
            result.shadcnComponents = imports.found;
            result.issues.push(...imports.missing.map(comp => `缺少shadcn组件导入: ${comp}`));

            const usage = await this.checkShadcnUsage('src/components/ImportExportTab.tsx', this.expectedShadcnComponents.importExportTab);
            result.issues.push(...usage.issues);

            const specificChecks = await this.checkImportExportTabSpecifics();
            result.issues.push(...specificChecks);

            result.passed = result.issues.length === 0;
            console.log(result.passed ? '✅ ImportExportTab检查通过' : `❌ ImportExportTab检查失败 (${result.issues.length}个问题)`);
            
        } catch (error) {
            result.issues.push(`检查过程出错: ${error.message}`);
            console.error('❌ ImportExportTab检查出错:', error);
        }
    }

    /**
     * 检查AboutTab组件
     */
    async checkAboutTab() {
        console.log('\nℹ️ 检查AboutTab组件...');
        const result = this.results.aboutTab;
        
        try {
            const componentExists = await this.checkFileExists('src/options/components/AboutTab.tsx');
            if (!componentExists) {
                result.issues.push('组件文件不存在');
                return;
            }

            const imports = await this.checkShadcnImports('src/options/components/AboutTab.tsx', this.expectedShadcnComponents.aboutTab);
            result.shadcnComponents = imports.found;
            result.issues.push(...imports.missing.map(comp => `缺少shadcn组件导入: ${comp}`));

            const usage = await this.checkShadcnUsage('src/options/components/AboutTab.tsx', this.expectedShadcnComponents.aboutTab);
            result.issues.push(...usage.issues);

            const specificChecks = await this.checkAboutTabSpecifics();
            result.issues.push(...specificChecks);

            result.passed = result.issues.length === 0;
            console.log(result.passed ? '✅ AboutTab检查通过' : `❌ AboutTab检查失败 (${result.issues.length}个问题)`);
            
        } catch (error) {
            result.issues.push(`检查过程出错: ${error.message}`);
            console.error('❌ AboutTab检查出错:', error);
        }
    }

    /**
     * 检查HelpCenterTab组件
     */
    async checkHelpCenterTab() {
        console.log('\n❓ 检查HelpCenterTab组件...');
        const result = this.results.helpCenterTab;
        
        try {
            const componentExists = await this.checkFileExists('src/options/components/HelpCenterTab.tsx');
            if (!componentExists) {
                result.issues.push('组件文件不存在');
                return;
            }

            const imports = await this.checkShadcnImports('src/options/components/HelpCenterTab.tsx', this.expectedShadcnComponents.helpCenterTab);
            result.shadcnComponents = imports.found;
            result.issues.push(...imports.missing.map(comp => `缺少shadcn组件导入: ${comp}`));

            const usage = await this.checkShadcnUsage('src/options/components/HelpCenterTab.tsx', this.expectedShadcnComponents.helpCenterTab);
            result.issues.push(...usage.issues);

            const specificChecks = await this.checkHelpCenterTabSpecifics();
            result.issues.push(...specificChecks);

            result.passed = result.issues.length === 0;
            console.log(result.passed ? '✅ HelpCenterTab检查通过' : `❌ HelpCenterTab检查失败 (${result.issues.length}个问题)`);
            
        } catch (error) {
            result.issues.push(`检查过程出错: ${error.message}`);
            console.error('❌ HelpCenterTab检查出错:', error);
        }
    }

    /**
     * 检查CategoryManagementTab特定内容
     */
    async checkCategoryManagementSpecifics() {
        const issues = [];
        const content = await this.readFile('src/components/CategoryManagementTab.tsx');
        
        if (!content) return ['无法读取组件文件'];

        // 检查是否使用Card组件替换头部区域
        if (!content.includes('<Card') || !content.includes('CardHeader')) {
            issues.push('未使用Card组件替换头部区域');
        }

        // 检查是否使用CardTitle和CardDescription
        if (!content.includes('CardTitle') || !content.includes('CardDescription')) {
            issues.push('未使用CardTitle和CardDescription组件');
        }

        // 检查是否使用Button组件
        if (!content.includes('<Button')) {
            issues.push('未使用shadcn Button组件');
        }

        // 检查错误状态是否使用Card
        if (content.includes('renderErrorState') && !content.includes('CardContent')) {
            issues.push('错误状态未使用Card组件');
        }

        return issues;
    }

    /**
     * 检查TagsTab特定内容
     */
    async checkTagsTabSpecifics() {
        const issues = [];
        const content = await this.readFile('src/components/TagsTab.tsx');
        
        if (!content) return ['无法读取组件文件'];

        // 检查是否使用Alert组件
        if (!content.includes('<Alert') || !content.includes('AlertDescription')) {
            issues.push('未使用Alert组件显示状态信息');
        }

        // 检查是否使用Card组件显示加载状态
        if (!content.includes('CardContent')) {
            issues.push('未使用Card组件显示加载状态');
        }

        // 检查是否使用Button组件
        if (!content.includes('<Button')) {
            issues.push('未使用shadcn Button组件');
        }

        return issues;
    }

    /**
     * 检查ImportExportTab特定内容
     */
    async checkImportExportTabSpecifics() {
        const issues = [];
        const content = await this.readFile('src/components/ImportExportTab.tsx');
        
        if (!content) return ['无法读取组件文件'];

        // 检查是否使用表单组件
        const formComponents = ['Input', 'Select', 'Checkbox', 'Label'];
        formComponents.forEach(comp => {
            if (!content.includes(`<${comp}`)) {
                issues.push(`未使用shadcn ${comp}组件`);
            }
        });

        // 检查是否使用Progress组件
        if (!content.includes('<Progress')) {
            issues.push('未使用Progress组件显示进度');
        }

        // 检查是否使用Alert组件显示状态
        if (!content.includes('<Alert')) {
            issues.push('未使用Alert组件显示状态信息');
        }

        return issues;
    }

    /**
     * 检查AboutTab特定内容
     */
    async checkAboutTabSpecifics() {
        const issues = [];
        const content = await this.readFile('src/options/components/AboutTab.tsx');
        
        if (!content) return ['无法读取组件文件'];

        // 检查是否使用Badge组件
        if (!content.includes('<Badge')) {
            issues.push('未使用Badge组件显示版本和权限信息');
        }

        // 检查是否使用Card组件
        if (!content.includes('<Card') || !content.includes('CardContent')) {
            issues.push('未使用Card组件重构信息卡片');
        }

        // 检查是否保持响应式设计
        if (!content.includes('useResponsive')) {
            issues.push('可能丢失了响应式设计支持');
        }

        return issues;
    }

    /**
     * 检查HelpCenterTab特定内容
     */
    async checkHelpCenterTabSpecifics() {
        const issues = [];
        const content = await this.readFile('src/options/components/HelpCenterTab.tsx');
        
        if (!content) return ['无法读取组件文件'];

        // 检查是否使用Badge组件显示搜索结果
        if (!content.includes('<Badge')) {
            issues.push('未使用Badge组件显示搜索结果统计');
        }

        // 检查是否使用Alert组件显示联系信息
        if (!content.includes('<Alert')) {
            issues.push('未使用Alert组件显示联系信息');
        }

        // 检查是否使用Card组件重构内容展示
        if (!content.includes('CardContent') || !content.includes('CardHeader')) {
            issues.push('未使用Card组件重构帮助内容展示');
        }

        return issues;
    }

    /**
     * 检查文件是否存在
     */
    async checkFileExists(filePath) {
        try {
            const fs = await import('fs');
            await fs.promises.access(filePath);
            return true;
        } catch {
            return false;
        }
    }

    /**
     * 读取文件内容
     */
    async readFile(filePath) {
        try {
            const fs = await import('fs');
            return await fs.promises.readFile(filePath, 'utf8');
        } catch (error) {
            console.warn(`⚠️ 无法读取文件 ${filePath}:`, error.message);
            return null;
        }
    }

    /**
     * 检查shadcn组件导入
     */
    async checkShadcnImports(filePath, expectedComponents) {
        const content = await this.readFile(filePath);
        if (!content) return { found: [], missing: expectedComponents };

        const found = [];
        const missing = [];

        expectedComponents.forEach(component => {
            // 检查是否有对应的导入语句
            const importRegex = new RegExp(`import.*{[^}]*\\b${component}\\b[^}]*}.*from.*['"].*ui/${component.toLowerCase()}['"]`, 'i');
            const uiImportRegex = new RegExp(`import.*{[^}]*\\b${component}\\b[^}]*}.*from.*['"].*ui/.*['"]`, 'i');
            
            if (importRegex.test(content) || uiImportRegex.test(content)) {
                found.push(component);
            } else {
                missing.push(component);
            }
        });

        return { found, missing };
    }

    /**
     * 检查shadcn组件使用
     */
    async checkShadcnUsage(filePath, expectedComponents) {
        const content = await this.readFile(filePath);
        const issues = [];

        if (!content) return { issues: ['无法读取文件内容'] };

        expectedComponents.forEach(component => {
            // 检查是否在JSX中使用了组件
            const usageRegex = new RegExp(`<${component}[\\s>]`, 'g');
            const matches = content.match(usageRegex);
            
            if (!matches || matches.length === 0) {
                issues.push(`${component}组件已导入但未使用`);
            }
        });

        return { issues };
    }

    /**
     * 生成总体结果
     */
    generateOverallResults() {
        const allResults = [
            this.results.categoryManagement,
            this.results.tagsTab,
            this.results.importExportTab,
            this.results.aboutTab,
            this.results.helpCenterTab
        ];

        const totalIssues = allResults.reduce((sum, result) => sum + result.issues.length, 0);
        const passedComponents = allResults.filter(result => result.passed).length;

        this.results.overall = {
            passed: totalIssues === 0 && passedComponents === allResults.length,
            totalIssues,
            passedComponents,
            totalComponents: allResults.length,
            passRate: Math.round((passedComponents / allResults.length) * 100)
        };
    }

    /**
     * 打印检查结果
     */
    printResults() {
        console.log('\n' + '='.repeat(80));
        console.log('📊 任务16运行时检查结果汇总');
        console.log('='.repeat(80));

        // 总体结果
        const overall = this.results.overall;
        console.log(`\n🎯 总体结果: ${overall.passed ? '✅ 通过' : '❌ 失败'}`);
        console.log(`📈 通过率: ${overall.passRate}% (${overall.passedComponents}/${overall.totalComponents})`);
        console.log(`🐛 总问题数: ${overall.totalIssues}`);

        // 各组件详细结果
        console.log('\n📋 各组件检查结果:');
        
        const components = [
            { name: 'CategoryManagementTab', key: 'categoryManagement', icon: '📁' },
            { name: 'TagsTab', key: 'tagsTab', icon: '🏷️' },
            { name: 'ImportExportTab', key: 'importExportTab', icon: '📤' },
            { name: 'AboutTab', key: 'aboutTab', icon: 'ℹ️' },
            { name: 'HelpCenterTab', key: 'helpCenterTab', icon: '❓' }
        ];

        components.forEach(({ name, key, icon }) => {
            const result = this.results[key];
            console.log(`\n${icon} ${name}:`);
            console.log(`   状态: ${result.passed ? '✅ 通过' : '❌ 失败'}`);
            console.log(`   shadcn组件: ${result.shadcnComponents.join(', ') || '无'}`);
            console.log(`   问题数: ${result.issues.length}`);
            
            if (result.issues.length > 0) {
                result.issues.forEach(issue => {
                    console.log(`   ⚠️  ${issue}`);
                });
            }
        });

        // 建议
        console.log('\n💡 建议:');
        if (overall.passed) {
            console.log('   🎉 所有组件都已成功完成shadcn重构！');
            console.log('   📝 建议进行最终的用户测试和性能验证');
        } else {
            console.log('   🔧 请根据上述问题列表修复相关组件');
            console.log('   📖 参考shadcn文档确保组件使用正确');
            console.log('   🧪 修复后重新运行此检查脚本');
        }

        console.log('\n' + '='.repeat(80));
    }

    /**
     * 生成JSON报告
     */
    generateJsonReport() {
        const report = {
            timestamp: new Date().toISOString(),
            task: 'Task 16 - Other Components shadcn Refactor',
            summary: this.results.overall,
            components: {
                categoryManagement: this.results.categoryManagement,
                tagsTab: this.results.tagsTab,
                importExportTab: this.results.importExportTab,
                aboutTab: this.results.aboutTab,
                helpCenterTab: this.results.helpCenterTab
            },
            expectedShadcnComponents: this.expectedShadcnComponents
        };

        return JSON.stringify(report, null, 2);
    }
}

// 常量定义
const REPORT_PATH = 'docs/task-16-runtime-check-report.json';
const EXIT_CODE_SUCCESS = 0;
const EXIT_CODE_FAILURE = 1;

/**
 * 运行所有检查
 * @returns {Promise<Object>} 检查结果
 */
async function runChecks() {
    const checker = new Task16RuntimeChecker();
    return await checker.runAllChecks();
}

/**
 * 保存检查报告到文件
 * @param {Object} results - 检查结果
 */
async function saveReport(results) {
    try {
        const checker = new Task16RuntimeChecker();
        const jsonReport = checker.generateJsonReport();
        
        // 动态导入 fs 模块
        const { writeFileSync } = await import('fs');
        const { resolve } = await import('path');
        
        // 验证路径安全性
        const resolvedPath = resolve(REPORT_PATH);
        const expectedDir = resolve('docs');
        
        if (!resolvedPath.startsWith(expectedDir)) {
            throw new Error('不安全的文件路径');
        }
        
        writeFileSync(REPORT_PATH, jsonReport);
        console.log(`\n📄 详细报告已保存到: ${REPORT_PATH}`);
    } catch (error) {
        console.error('⚠️ 保存报告失败:', error.message);
        // 不抛出错误，允许程序继续执行
    }
}

/**
 * 处理程序退出
 * @param {Object} results - 检查结果
 */
function handleExit(results) {
    const exitCode = results.overall?.passed ? EXIT_CODE_SUCCESS : EXIT_CODE_FAILURE;
    process.exit(exitCode);
}

/**
 * 处理错误
 * @param {Error} error - 错误对象
 */
function handleError(error) {
    console.error('❌ 检查脚本执行失败:', error.message);
    console.error('堆栈信息:', error.stack);
    process.exit(EXIT_CODE_FAILURE);
}

/**
 * 主执行函数
 */
async function main() {
    try {
        console.log('🚀 开始执行 Task16 运行时检查...\n');
        
        const results = await runChecks();
        await saveReport(results);
        handleExit(results);
    } catch (error) {
        handleError(error);
    }
}

/**
 * 检测是否为主模块
 * @returns {boolean} 是否为主模块
 */
function isMainModule() {
    try {
        // 尝试使用标准的 ES 模块检测方式
        const { fileURLToPath } = require('url');
        const __filename = fileURLToPath(import.meta.url);
        return process.argv[1] === __filename;
    } catch (error) {
        // 降级到多种检测方式
        return import.meta.url === `file://${process.argv[1]}` || 
               import.meta.url.endsWith(process.argv[1]) ||
               process.argv[1].endsWith('task16-runtime-check.js');
    }
}

// 如果直接运行此脚本
if (isMainModule()) {
    main().catch(error => {
        console.error('❌ 脚本执行失败:', error);
        process.exit(EXIT_CODE_FAILURE);
    });
}

// 导出类供其他模块使用
export default Task16RuntimeChecker;