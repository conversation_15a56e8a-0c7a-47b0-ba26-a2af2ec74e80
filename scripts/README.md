# 脚本文档

本目录包含项目的各种实用脚本，用于项目验证、构建和测试。

## 脚本列表

### test-build.js

**描述**: 构建测试脚本，用于验证项目配置是否正确，无需实际安装依赖即可检查项目结构和配置文件的完整性。

**用途**: 
- 在开发环境设置前验证项目配置
- CI/CD流程中的预检查
- 新开发者快速验证项目完整性

**运行方式**:
```bash
node scripts/test-build.js
```

#### 主要功能模块

##### 1. checkRequiredFiles()
**功能**: 检查项目必要文件是否存在
**返回值**: `boolean` - 所有必要文件都存在时返回true

**检查的文件列表**:
- `package.json` - 项目配置文件
- `manifest.json` - Chrome扩展清单文件
- `vite.config.ts` - Vite构建配置
- `tsconfig.json` - TypeScript配置
- `tailwind.config.js` - Tailwind CSS配置
- `postcss.config.js` - PostCSS配置
- `src/background/index.ts` - Background Service Worker
- `src/content/index.ts` - Content Script
- `src/popup/index.html` - 弹出窗口HTML
- `src/popup/index.tsx` - 弹出窗口入口
- `src/options/index.html` - 选项页面HTML
- `src/options/index.tsx` - 选项页面入口
- `src/types/index.ts` - TypeScript类型定义
- `src/styles/globals.css` - 全局样式

**使用示例**:
```javascript
const filesExist = checkRequiredFiles();
if (filesExist) {
  console.log('所有必要文件都存在');
} else {
  console.log('缺少必要文件');
}
```

##### 2. validatePackageJson()
**功能**: 验证package.json配置的完整性
**返回值**: `boolean` - 配置有效时返回true

**验证项目**:
- **必要字段**: name, version, scripts, dependencies, devDependencies
- **关键脚本**: dev, build
- **核心依赖**: react, react-dom, lucide-react
- **开发依赖**: vite, @vitejs/plugin-react, typescript, tailwindcss

**使用示例**:
```javascript
const isValid = validatePackageJson();
if (isValid) {
  console.log('package.json配置正确');
}
```

##### 3. validateManifest()
**功能**: 验证Chrome扩展manifest.json配置
**返回值**: `boolean` - 配置符合Manifest V3规范时返回true

**验证项目**:
- **必要字段**: manifest_version, name, version, description, permissions, background, content_scripts, action
- **Manifest V3特性**: service_worker配置
- **权限配置**: 检查必要权限是否声明

**使用示例**:
```javascript
const manifestValid = validateManifest();
if (manifestValid) {
  console.log('Chrome扩展配置正确');
}
```

##### 4. validateTypeScript()
**功能**: 验证TypeScript配置的正确性
**返回值**: `boolean` - 配置适合Chrome扩展开发时返回true

**验证项目**:
- **编译选项**: target, module, jsx, strict
- **路径映射**: @/* 别名配置
- **类型定义**: Chrome扩展类型支持

**使用示例**:
```javascript
const tsValid = validateTypeScript();
if (tsValid) {
  console.log('TypeScript配置正确');
}
```

##### 5. checkSourceStructure()
**功能**: 检查源代码目录结构的完整性
**返回值**: `boolean` - 所有预期文件和目录都存在时返回true

**检查的目录结构**:
```
src/
├── background/
│   └── index.ts
├── content/
│   ├── index.ts
│   └── style.css
├── popup/
│   ├── index.html
│   ├── index.tsx
│   └── PopupApp.tsx
├── options/
│   ├── index.html
│   ├── index.tsx
│   └── OptionsApp.tsx
├── types/
│   └── index.ts
└── styles/
    └── globals.css
```

**使用示例**:
```javascript
const structureValid = checkSourceStructure();
if (structureValid) {
  console.log('源代码结构完整');
}
```

##### 6. main()
**功能**: 主测试函数，协调执行所有验证测试
**返回值**: `void` - 通过console输出结果，失败时退出进程

**执行流程**:
1. 依次执行所有验证函数
2. 收集测试结果
3. 输出详细的验证报告
4. 提供下一步操作指导

**测试项目**:
- 必要文件检查
- package.json验证
- manifest.json验证
- TypeScript配置验证
- 源代码结构检查

#### 输出示例

**成功情况**:
```
🧪 测试项目配置...

📁 检查必要文件:
  ✅ package.json
  ✅ manifest.json
  ✅ vite.config.ts
  ...

📦 验证package.json:
  ✅ name: 已定义
  ✅ version: 已定义
  ...

🎉 项目配置验证通过！

✅ 项目结构正确
✅ 配置文件完整
✅ Chrome扩展配置正确

📋 下一步:
1. 安装Node.js (https://nodejs.org/)
2. 运行: npm install
3. 运行: npm run dev
4. 在Chrome中加载扩展进行测试
```

**失败情况**:
```
❌ 项目配置验证失败

请检查并修复上述问题后重试
```

#### 技术特性

- **模块化设计**: 每个验证功能独立封装，便于维护和扩展
- **详细报告**: 提供具体的错误信息和修复建议
- **跨平台兼容**: 使用Node.js标准API，支持Windows/macOS/Linux
- **零依赖**: 仅使用Node.js内置模块，无需额外安装依赖
- **ES模块**: 使用现代JavaScript模块语法

#### 错误处理

脚本包含完善的错误处理机制：
- JSON解析错误捕获
- 文件系统访问异常处理
- 进程退出码设置（失败时退出码为1）

#### 扩展性

可以轻松添加新的验证功能：
```javascript
// 添加新的验证函数
function validateNewFeature() {
  // 验证逻辑
  return true; // 或 false
}

// 在main函数的tests数组中添加
const tests = [
  // 现有测试...
  { name: '新功能验证', fn: validateNewFeature }
];
```

### test-build.test.js

**描述**: test-build.js脚本的单元测试文件，用于验证各个验证函数的正确性。采用自定义轻量级测试框架，提供完整的测试覆盖和详细的错误报告。

**运行方式**:
```bash
node scripts/test-build.test.js
```

#### 主要功能模块

##### 1. TestRunner 测试框架类
**功能**: 提供完整的测试执行和结果报告功能
**方法**:
- `constructor()`: 初始化测试运行器
- `test(name, testFn)`: 添加测试用例
- `assert(condition, message)`: 断言验证
- `run()`: 执行所有测试并输出结果

**使用示例**:
```javascript
const runner = new TestRunner()
runner.test('测试名称', () => {
  runner.assert(condition, '错误消息')
})
await runner.run()
```

##### 2. 测试用例覆盖
**验证内容**:
- ✅ **必要文件存在性检查**: 验证核心配置文件是否存在
- ✅ **package.json结构验证**: 检查Node.js项目配置完整性
- ✅ **manifest.json配置验证**: 验证Chrome扩展清单符合Manifest V3规范
- ✅ **TypeScript配置验证**: 检查编译配置的正确性
- ✅ **源代码结构验证**: 验证src目录结构完整性
- ✅ **脚本自身结构验证**: 检查被测试脚本的函数完整性

##### 3. 测试框架特性
**核心特性**:
- **零依赖**: 仅使用Node.js内置模块
- **异步支持**: 支持同步和异步测试函数
- **详细报告**: 提供具体的错误定位和描述
- **统计信息**: 显示通过/失败测试数量
- **进程控制**: 失败时正确设置退出码
- **易于扩展**: 简单的API设计便于添加新测试

##### 4. 输出示例

**成功情况**:
```
🧪 开始测试 test-build.js 脚本...

运行 6 个测试用例...

✅ 检查必要文件是否存在
✅ 验证package.json结构
✅ 验证manifest.json结构
✅ 验证TypeScript配置
✅ 验证源代码结构
✅ 验证test-build.js脚本结构

==================================================
测试结果: 6 通过, 0 失败
```

**失败情况**:
```
❌ 验证package.json结构: package.json缺少必要字段: scripts
❌ 验证TypeScript配置: tsconfig.json缺少重要选项: jsx
==================================================
测试结果: 4 通过, 2 失败
```

##### 5. 扩展测试用例
**添加新测试**:
```javascript
runner.test('新测试用例', () => {
  const result = someValidationFunction()
  runner.assert(result.isValid, '验证应该通过')
})

// 异步测试
runner.test('异步验证', async () => {
  const result = await asyncFunction()
  runner.assert(result.success, '异步操作应该成功')
})

// 复杂验证测试
runner.test('复杂配置验证', () => {
  const config = loadConfiguration()
  
  // 多重断言
  runner.assert(config !== null, '配置不应为空')
  runner.assert(typeof config === 'object', '配置应为对象')
  runner.assert(Array.isArray(config.features), '功能列表应为数组')
})
```

##### 6. 核心导出内容

**主要类和函数**:
```javascript
// TestRunner 测试框架类
class TestRunner {
  constructor()                                    // 初始化测试运行器
  test(name: string, testFn: Function): void     // 添加测试用例
  assert(condition: boolean, message: string): void // 断言验证
  async run(): Promise<void>                      // 执行所有测试
}

// 全局常量
const __filename: string    // 当前文件绝对路径
const __dirname: string     // 当前目录绝对路径
const runner: TestRunner    // 测试运行器实例
```

**测试用例函数**:
- `testRequiredFiles()` - 必要文件存在性检查
- `testPackageJsonStructure()` - package.json结构验证
- `testManifestStructure()` - manifest.json配置验证
- `testTypeScriptConfig()` - TypeScript配置验证
- `testSourceStructure()` - 源代码结构验证
- `testScriptStructure()` - 脚本自身结构验证

## API 文档

详细的API文档请参考：
- [test-build.test.js API 文档](../docs/test-build-test-api.md) - 测试框架完整API参考
- [API.md](API.md) - 通用脚本API文档

## 技术规范

### 代码质量标准
- **ES模块**: 使用现代JavaScript模块语法
- **零依赖**: 仅使用Node.js内置模块
- **类型安全**: 完整的参数和返回值验证
- **错误处理**: 全面的异常捕获和报告
- **文档覆盖**: 100%函数和类的文档覆盖

### 性能指标
- **执行时间**: 测试套件 < 100ms
- **内存占用**: 运行时 < 10MB
- **文件大小**: 源码 < 50KB
- **启动时间**: 冷启动 < 50ms

### 兼容性要求
- **Node.js**: 16.0.0+
- **操作系统**: Windows 10+, macOS 10.15+, Ubuntu 18.04+
- **文件系统**: 支持UTF-8编码
- **权限**: 需要读取项目文件的权限

## 快速开始

```bash
# 验证项目配置
npm run test:config

# 运行脚本测试
npm run test:scripts

# 直接运行脚本
node scripts/test-build.js
node scripts/test-build.test.js
node scripts/extract-performance-api.js
```

### extract-performance-api.js

**描述**: 性能监控工具API提取脚本，从 performance.ts 文件中提取类和函数签名，生成完整的API文档。

**用途**:
- 自动提取性能监控相关的API信息
- 生成完整的API参考文档
- 验证文档与代码的一致性
- 支持中文注释和文档生成

**运行方式**:
```bash
node scripts/extract-performance-api.js
```

#### 主要功能模块

##### 1. PerformanceAPIExtractor 类
**功能**: 负责从TypeScript文件中提取性能监控相关的API信息

**主要方法**:
- `extractFromFile(filePath)` - 从指定文件提取API信息
- `extractClasses(content)` - 提取类定义和方法
- `extractFunctions(content)` - 提取导出的函数
- `generateDocumentation()` - 生成完整的API文档

**提取内容**:
- ✅ **类信息**: PerformanceMonitor, MemoryMonitor类的完整定义
- ✅ **方法签名**: 所有公有方法的参数和返回类型
- ✅ **函数信息**: debounce, throttle工具函数
- ✅ **注释解析**: 提取中文注释和参数说明
- ✅ **导出内容**: 所有导出的类、函数和实例

**输出文件**: `docs/performance-utils-api.md`

**使用示例**:
```javascript
const extractor = new PerformanceAPIExtractor()
extractor.extractFromFile('src/utils/performance.ts')
const documentation = extractor.generateDocumentation()
```

##### 2. 核心提取功能

**extractClasses(content)**:
- 提取类定义和类注释
- 解析类的静态方法和实例方法
- 识别方法的访问修饰符（public/private/static）
- 提取方法参数和返回类型

**extractFunctions(content)**:
- 提取导出的工具函数
- 解析函数的泛型参数
- 提取函数参数和返回类型
- 解析函数注释和文档

**parseComment(commentText)**:
- 解析JSDoc格式的注释
- 提取函数描述、参数说明和返回值说明
- 支持中文注释内容
- 生成结构化的注释数据

##### 3. 文档生成功能

**generateDocumentation()**:
- 生成完整的Markdown格式API文档
- 按类别组织方法和函数
- 包含使用示例和最佳实践
- 提供详细的参数和返回值说明

**generateMethodDoc(method)**:
- 为每个方法生成详细文档
- 包含方法签名、参数说明、返回值
- 添加使用示例和注意事项

**generateFunctionDoc(func)**:
- 为工具函数生成文档
- 包含泛型参数说明
- 提供实际使用场景示例

#### 输出示例

**成功情况**:
```
🔍 提取性能监控工具API信息...

📄 分析文件: performance.ts

📝 文档已生成: docs/performance-utils-api.md

📊 提取统计:
- 类数量: 2 个
- 函数数量: 2 个
- 导出内容: 4 个
- 总方法数: 12 个
- 公有方法: 10 个

✅ 性能监控工具API提取完成!
```

#### 技术特性

- **TypeScript解析**: 完整的TypeScript语法解析支持
- **注释提取**: 支持JSDoc格式的中文注释
- **类型识别**: 准确识别复杂的TypeScript类型
- **文档生成**: 自动生成结构化的Markdown文档
- **统计分析**: 提供详细的代码统计信息

#### 扩展性

可以轻松扩展以支持其他文件的API提取：
```javascript
// 扩展支持其他工具文件
const files = [
  'src/utils/performance.ts',
  'src/utils/validation.ts',
  'src/utils/modelFactory.ts'
]

files.forEach(file => {
  extractor.extractFromFile(file)
})
```