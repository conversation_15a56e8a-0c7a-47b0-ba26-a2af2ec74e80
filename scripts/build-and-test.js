// 构建和测试脚本
// 用于构建扩展并提供测试指导

const { execSync } = require('child_process')
const fs = require('fs')
const path = require('path')

console.log('🔧 Universe Bag 扩展构建和测试脚本')
console.log('=====================================')

// 检查必要的文件
function checkRequiredFiles() {
  console.log('\n1. 检查必要文件...')
  
  const requiredFiles = [
    'package.json',
    'vite.config.ts',
    'manifest.json',
    'src/background/index.ts',
    'src/services/bookmarkService.ts',
    'src/utils/indexedDB.ts'
  ]
  
  const missingFiles = requiredFiles.filter(file => !fs.existsSync(file))
  
  if (missingFiles.length > 0) {
    console.log('❌ 缺少必要文件:', missingFiles)
    return false
  }
  
  console.log('✅ 所有必要文件都存在')
  return true
}

// 安装依赖
function installDependencies() {
  console.log('\n2. 安装依赖...')
  
  try {
    execSync('npm install', { stdio: 'inherit' })
    console.log('✅ 依赖安装完成')
    return true
  } catch (error) {
    console.log('❌ 依赖安装失败:', error.message)
    return false
  }
}

// 构建扩展
function buildExtension() {
  console.log('\n3. 构建扩展...')
  
  try {
    execSync('npm run build', { stdio: 'inherit' })
    console.log('✅ 扩展构建完成')
    
    // 检查构建输出
    if (fs.existsSync('dist')) {
      console.log('✅ 构建输出目录存在')
      return true
    } else {
      console.log('❌ 构建输出目录不存在')
      return false
    }
  } catch (error) {
    console.log('❌ 扩展构建失败:', error.message)
    return false
  }
}

// 提供测试指导
function provideTestingGuidance() {
  console.log('\n4. 测试指导')
  console.log('============')
  
  console.log('\n📋 手动测试步骤:')
  console.log('1. 打开Chrome浏览器')
  console.log('2. 进入扩展管理页面 (chrome://extensions/)')
  console.log('3. 开启"开发者模式"')
  console.log('4. 点击"加载已解压的扩展程序"')
  console.log('5. 选择项目的 "dist" 文件夹')
  console.log('6. 扩展安装完成后，访问任意网页')
  console.log('7. 在浏览器控制台中运行测试脚本')
  
  console.log('\n🧪 可用的测试脚本:')
  console.log('- test-complete-bookmark-fix.js (完整功能测试)')
  console.log('- test-bookmark-fix.js (基础功能测试)')
  console.log('- debug-bookmark-fix.js (调试测试)')
  
  console.log('\n📝 测试脚本使用方法:')
  console.log('1. 在任意网页打开开发者工具 (F12)')
  console.log('2. 切换到 Console 标签页')
  console.log('3. 复制并粘贴测试脚本内容')
  console.log('4. 按 Enter 运行测试')
  
  console.log('\n🔍 预期测试结果:')
  console.log('- 快速收藏: ✅ 成功')
  console.log('- 状态检查: ✅ 成功')
  console.log('- 收藏列表: ✅ 成功')
  console.log('- 数据库访问: ✅ 成功')
  console.log('- 图标状态: ✅ 成功')
  
  console.log('\n🚨 如果测试失败:')
  console.log('1. 检查浏览器控制台是否有错误信息')
  console.log('2. 确认扩展已正确加载')
  console.log('3. 尝试重新加载扩展')
  console.log('4. 检查扩展的background script是否正常运行')
}

// 主函数
function main() {
  console.log('开始构建和测试流程...\n')
  
  // 检查文件
  if (!checkRequiredFiles()) {
    console.log('\n❌ 构建失败: 缺少必要文件')
    return
  }
  
  // 安装依赖
  if (!installDependencies()) {
    console.log('\n❌ 构建失败: 依赖安装失败')
    return
  }
  
  // 构建扩展
  if (!buildExtension()) {
    console.log('\n❌ 构建失败: 扩展构建失败')
    return
  }
  
  // 提供测试指导
  provideTestingGuidance()
  
  console.log('\n🎉 构建完成！请按照上述指导进行测试。')
}

// 运行主函数
main()