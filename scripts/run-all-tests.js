#!/usr/bin/env node

/**
 * 运行所有测试脚本
 * 
 * 功能说明:
 * - 自动发现和运行所有测试文件
 * - 提供统一的测试入口
 * - 生成测试报告和统计信息
 * - 支持并行和串行执行模式
 */

import fs from 'fs'
import path from 'path'
import { spawn } from 'child_process'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)
const rootDir = path.resolve(__dirname, '..')

console.log('🧪 运行所有测试...\n')

/**
 * 测试运行器类
 */
class TestRunner {
  constructor() {
    this.testFiles = []
    this.results = []
    this.totalTests = 0
    this.passedTests = 0
    this.failedTests = 0
  }

  /**
   * 发现测试文件
   */
  discoverTests() {
    const testDirs = [
      path.join(rootDir, 'tests'),
      path.join(rootDir, 'scripts')
    ]

    testDirs.forEach(dir => {
      if (fs.existsSync(dir)) {
        const files = fs.readdirSync(dir)
        files.forEach(file => {
          if (file.endsWith('.test.js')) {
            this.testFiles.push(path.join(dir, file))
          }
        })
      }
    })

    console.log(`📁 发现 ${this.testFiles.length} 个测试文件:`)
    this.testFiles.forEach(file => {
      console.log(`  - ${path.relative(rootDir, file)}`)
    })
    console.log()
  }

  /**
   * 运行单个测试文件
   * 
   * @param {string} testFile - 测试文件路径
   * @returns {Promise<object>} 测试结果
   */
  runTest(testFile) {
    return new Promise((resolve) => {
      const fileName = path.basename(testFile)
      console.log(`🔄 运行测试: ${fileName}`)

      const child = spawn('node', [testFile], {
        stdio: 'pipe',
        cwd: rootDir
      })

      let stdout = ''
      let stderr = ''

      child.stdout.on('data', (data) => {
        stdout += data.toString()
      })

      child.stderr.on('data', (data) => {
        stderr += data.toString()
      })

      child.on('close', (code) => {
        const result = {
          file: fileName,
          path: testFile,
          exitCode: code,
          stdout: stdout,
          stderr: stderr,
          success: code === 0,
          duration: Date.now() - startTime
        }

        // 解析测试结果
        this.parseTestOutput(result)

        if (result.success) {
          console.log(`✅ ${fileName} - 通过`)
        } else {
          console.log(`❌ ${fileName} - 失败 (退出码: ${code})`)
          if (stderr) {
            console.log(`   错误: ${stderr.trim()}`)
          }
        }

        resolve(result)
      })

      const startTime = Date.now()
    })
  }

  /**
   * 解析测试输出
   * 
   * @param {object} result - 测试结果对象
   */
  parseTestOutput(result) {
    const output = result.stdout

    // 解析测试统计信息
    const statsMatch = output.match(/测试结果:\s*(\d+)\s*通过,\s*(\d+)\s*失败/)
    if (statsMatch) {
      result.passed = parseInt(statsMatch[1])
      result.failed = parseInt(statsMatch[2])
      result.total = result.passed + result.failed
    } else {
      // 如果没有找到统计信息，尝试其他格式
      const passedMatch = output.match(/✅/g)
      const failedMatch = output.match(/❌/g)
      
      result.passed = passedMatch ? passedMatch.length : 0
      result.failed = failedMatch ? failedMatch.length : 0
      result.total = result.passed + result.failed
    }

    // 如果测试成功但没有解析到数字，设置默认值
    if (result.success && result.total === 0) {
      result.passed = 1
      result.total = 1
    }
  }

  /**
   * 运行所有测试
   */
  async runAllTests() {
    console.log(`🚀 开始运行 ${this.testFiles.length} 个测试文件...\n`)

    for (const testFile of this.testFiles) {
      const result = await this.runTest(testFile)
      this.results.push(result)

      this.totalTests += result.total || 0
      this.passedTests += result.passed || 0
      this.failedTests += result.failed || 0

      console.log() // 空行分隔
    }
  }

  /**
   * 生成测试报告
   */
  generateReport() {
    console.log('='.repeat(60))
    console.log('📊 测试报告')
    console.log('='.repeat(60))

    // 总体统计
    console.log(`\n📈 总体统计:`)
    console.log(`  测试文件: ${this.testFiles.length} 个`)
    console.log(`  测试用例: ${this.totalTests} 个`)
    console.log(`  通过: ${this.passedTests} 个`)
    console.log(`  失败: ${this.failedTests} 个`)
    console.log(`  成功率: ${this.totalTests > 0 ? ((this.passedTests / this.totalTests) * 100).toFixed(1) : 0}%`)

    // 详细结果
    console.log(`\n📋 详细结果:`)
    this.results.forEach(result => {
      const status = result.success ? '✅' : '❌'
      const duration = result.duration ? `${result.duration}ms` : 'N/A'
      const stats = result.total > 0 ? `${result.passed}/${result.total}` : 'N/A'
      
      console.log(`  ${status} ${result.file.padEnd(30)} ${stats.padEnd(10)} ${duration}`)
    })

    // 失败的测试详情
    const failedTests = this.results.filter(r => !r.success)
    if (failedTests.length > 0) {
      console.log(`\n❌ 失败的测试:`)
      failedTests.forEach(result => {
        console.log(`\n  📁 ${result.file}:`)
        if (result.stderr) {
          console.log(`     错误: ${result.stderr.trim()}`)
        }
        if (result.stdout) {
          // 只显示错误相关的输出
          const errorLines = result.stdout.split('\n').filter(line => 
            line.includes('❌') || line.includes('Error') || line.includes('失败')
          )
          if (errorLines.length > 0) {
            errorLines.forEach(line => {
              console.log(`     ${line.trim()}`)
            })
          }
        }
      })
    }

    // 性能统计
    if (this.results.length > 0) {
      const totalDuration = this.results.reduce((sum, r) => sum + (r.duration || 0), 0)
      const avgDuration = totalDuration / this.results.length
      
      console.log(`\n⏱️  性能统计:`)
      console.log(`  总耗时: ${totalDuration}ms`)
      console.log(`  平均耗时: ${avgDuration.toFixed(1)}ms`)
      console.log(`  最快测试: ${Math.min(...this.results.map(r => r.duration || 0))}ms`)
      console.log(`  最慢测试: ${Math.max(...this.results.map(r => r.duration || 0))}ms`)
    }

    console.log('\n' + '='.repeat(60))

    // 返回总体结果
    return {
      success: this.failedTests === 0,
      totalFiles: this.testFiles.length,
      totalTests: this.totalTests,
      passed: this.passedTests,
      failed: this.failedTests
    }
  }
}

/**
 * 主函数
 */
async function main() {
  const runner = new TestRunner()
  
  try {
    // 发现测试文件
    runner.discoverTests()
    
    if (runner.testFiles.length === 0) {
      console.log('⚠️  没有找到测试文件')
      return
    }
    
    // 运行所有测试
    await runner.runAllTests()
    
    // 生成报告
    const report = runner.generateReport()
    
    // 根据结果设置退出码
    if (report.success) {
      console.log('🎉 所有测试通过!')
      process.exit(0)
    } else {
      console.log('💥 有测试失败!')
      process.exit(1)
    }
    
  } catch (error) {
    console.error('❌ 测试运行失败:', error.message)
    process.exit(1)
  }
}

// 运行主函数
main()