#!/usr/bin/env node

/**
 * BookmarksTab shadcn重构验证脚本
 * 验证BookmarksTab组件是否正确使用了shadcn/ui组件
 */

const fs = require('fs')
const path = require('path')

console.log('🔍 开始验证BookmarksTab shadcn重构...\n')

// 验证文件路径
const optionsAppPath = path.join(__dirname, '../src/options/OptionsApp.tsx')

// 检查文件是否存在
if (!fs.existsSync(optionsAppPath)) {
  console.error('❌ OptionsApp.tsx文件不存在')
  process.exit(1)
}

// 读取文件内容
const fileContent = fs.readFileSync(optionsAppPath, 'utf8')

// 验证项目
const checks = [
  {
    name: '导入shadcn Button组件',
    test: () => fileContent.includes("import { Button } from '../components/ui/button'"),
    description: '检查是否导入了shadcn Button组件'
  },
  {
    name: '导入shadcn Input组件',
    test: () => fileContent.includes("import { Input } from '../components/ui/input'"),
    description: '检查是否导入了shadcn Input组件'
  },
  {
    name: '导入shadcn Select组件',
    test: () => fileContent.includes("import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../components/ui/select'"),
    description: '检查是否导入了shadcn Select组件'
  },
  {
    name: '导入shadcn Card组件',
    test: () => fileContent.includes("import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../components/ui/card'"),
    description: '检查是否导入了shadcn Card组件'
  },
  {
    name: '使用Card作为主容器',
    test: () => fileContent.includes('<Card className="m-6">'),
    description: '检查是否使用Card组件作为主容器'
  },
  {
    name: '使用CardHeader组织标题',
    test: () => fileContent.includes('<CardHeader>'),
    description: '检查是否使用CardHeader组织标题区域'
  },
  {
    name: '使用CardTitle显示标题',
    test: () => fileContent.includes('<CardTitle className="text-2xl">收藏管理</CardTitle>'),
    description: '检查是否使用CardTitle显示标题'
  },
  {
    name: '使用CardDescription显示描述',
    test: () => fileContent.includes('<CardDescription>管理您的收藏内容，支持搜索、分类和多种视图模式</CardDescription>'),
    description: '检查是否使用CardDescription显示描述'
  },
  {
    name: '使用Button组件替换添加按钮',
    test: () => fileContent.includes('<Button onClick={handleAddBookmark} className="flex items-center gap-2">'),
    description: '检查是否使用Button组件替换添加收藏按钮'
  },
  {
    name: '使用Button组件替换刷新按钮',
    test: () => fileContent.includes('<Button variant="outline" onClick={loadBookmarks}>'),
    description: '检查是否使用Button组件替换刷新按钮'
  },
  {
    name: '使用Input组件替换搜索框',
    test: () => fileContent.includes('<Input') && fileContent.includes('placeholder="搜索收藏..."') && fileContent.includes('type="text"'),
    description: '检查是否使用Input组件替换搜索输入框'
  },
  {
    name: '添加搜索图标',
    test: () => fileContent.includes('<Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />'),
    description: '检查是否添加了搜索图标'
  },
  {
    name: '使用Select组件替换分类选择器',
    test: () => fileContent.includes('<Select value={selectedCategory} onValueChange={setSelectedCategory}>'),
    description: '检查是否使用Select组件替换分类选择器'
  },
  {
    name: '使用SelectTrigger',
    test: () => fileContent.includes('<SelectTrigger className="w-[180px]">'),
    description: '检查是否使用SelectTrigger'
  },
  {
    name: '使用SelectValue',
    test: () => fileContent.includes('<SelectValue placeholder="选择分类" />'),
    description: '检查是否使用SelectValue'
  },
  {
    name: '使用SelectContent和SelectItem',
    test: () => fileContent.includes('<SelectContent>') && fileContent.includes('<SelectItem value="all">所有分类</SelectItem>'),
    description: '检查是否使用SelectContent和SelectItem'
  },
  {
    name: '使用CardContent包装内容',
    test: () => fileContent.includes('<CardContent>'),
    description: '检查是否使用CardContent包装内容'
  },
  {
    name: '在卡片视图中使用Card组件',
    test: () => fileContent.includes('// 默认卡片视图 - 使用shadcn Card组件') && fileContent.includes('<Card'),
    description: '检查是否在卡片视图中使用Card组件'
  },
  {
    name: '使用shadcn颜色系统',
    test: () => fileContent.includes('text-muted-foreground') && fileContent.includes('bg-muted'),
    description: '检查是否使用shadcn的颜色系统'
  },
  {
    name: '使用现代间距系统',
    test: () => fileContent.includes('gap-2') && fileContent.includes('gap-4'),
    description: '检查是否使用现代的gap间距系统'
  },
  {
    name: '保持搜索建议功能',
    test: () => fileContent.includes('搜索建议下拉框') && fileContent.includes('<Card className="absolute top-full left-0 right-0 mt-1 z-10'),
    description: '检查是否保持搜索建议功能并使用Card组件'
  }
]

// 执行验证
let passedChecks = 0
let totalChecks = checks.length

console.log('📋 执行验证检查:\n')

checks.forEach((check, index) => {
  const passed = check.test()
  const status = passed ? '✅' : '❌'
  const number = (index + 1).toString().padStart(2, '0')
  
  console.log(`${status} ${number}. ${check.name}`)
  if (!passed) {
    console.log(`   📝 ${check.description}`)
  }
  
  if (passed) passedChecks++
})

console.log('\n' + '='.repeat(60))
console.log(`📊 验证结果: ${passedChecks}/${totalChecks} 项检查通过`)

if (passedChecks === totalChecks) {
  console.log('🎉 所有检查都通过了！BookmarksTab shadcn重构成功完成。')
  console.log('\n✨ 重构亮点:')
  console.log('   • 完全使用shadcn/ui组件替换自定义组件')
  console.log('   • 统一的设计语言和交互体验')
  console.log('   • 更好的可访问性和主题支持')
  console.log('   • 减少自定义CSS，提高可维护性')
  console.log('   • 保持所有原有功能完整性')
} else {
  console.log('⚠️  部分检查未通过，请检查重构实现。')
  process.exit(1)
}

console.log('\n🔧 下一步建议:')
console.log('   1. 运行构建测试: npm run build')
console.log('   2. 测试功能完整性: 在浏览器中验证所有功能')
console.log('   3. 继续下一个组件的shadcn重构')