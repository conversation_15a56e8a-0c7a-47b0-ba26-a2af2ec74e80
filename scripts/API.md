# Scripts API 文档

## test-build.js

### 概述
构建测试脚本，提供项目配置验证功能，确保Chrome扩展项目结构和配置的完整性。

### 导出函数

#### checkRequiredFiles()
```javascript
function checkRequiredFiles(): boolean
```

**功能**: 检查项目必要文件是否存在  
**返回值**: `boolean` - 所有必要文件都存在时返回true  
**副作用**: 在控制台输出检查结果  

**检查的文件**:
- 配置文件: `package.json`, `manifest.json`, `vite.config.ts`, `tsconfig.json`, `tailwind.config.js`, `postcss.config.js`
- 源代码文件: `src/background/index.ts`, `src/content/index.ts`, `src/popup/index.html`, `src/popup/index.tsx`, `src/options/index.html`, `src/options/index.tsx`, `src/types/index.ts`, `src/styles/globals.css`

---

#### validatePackageJson()
```javascript
function validatePackageJson(): boolean
```

**功能**: 验证package.json配置的完整性  
**返回值**: `boolean` - 配置有效时返回true  
**副作用**: 在控制台输出验证结果  

**验证项目**:
- 必要字段: `name`, `version`, `scripts`, `dependencies`, `devDependencies`
- 关键脚本: `dev`, `build`
- 核心依赖: `react`, `react-dom`, `lucide-react`
- 开发依赖: `vite`, `@vitejs/plugin-react`, `typescript`, `tailwindcss`

---

#### validateManifest()
```javascript
function validateManifest(): boolean
```

**功能**: 验证Chrome扩展manifest.json配置  
**返回值**: `boolean` - 配置符合Manifest V3规范时返回true  
**副作用**: 在控制台输出验证结果  

**验证项目**:
- 必要字段: `manifest_version`, `name`, `version`, `description`, `permissions`, `background`, `content_scripts`, `action`
- Manifest V3特性: `service_worker`配置
- 版本检查: 确保使用Manifest V3

---

#### validateTypeScript()
```javascript
function validateTypeScript(): boolean
```

**功能**: 验证TypeScript配置的正确性  
**返回值**: `boolean` - 配置适合Chrome扩展开发时返回true  
**副作用**: 在控制台输出验证结果  

**验证项目**:
- 编译选项: `target`, `module`, `jsx`, `strict`
- 路径映射: `@/*` 别名配置
- 类型定义: Chrome扩展类型支持

---

#### checkSourceStructure()
```javascript
function checkSourceStructure(): boolean
```

**功能**: 检查源代码目录结构的完整性  
**返回值**: `boolean` - 所有预期文件和目录都存在时返回true  
**副作用**: 在控制台输出检查结果  

**检查的目录结构**:
```
src/
├── background/index.ts
├── content/index.ts, style.css
├── popup/index.html, index.tsx, PopupApp.tsx
├── options/index.html, index.tsx, OptionsApp.tsx
├── types/index.ts
└── styles/globals.css
```

---

#### main()
```javascript
function main(): void
```

**功能**: 主测试函数，协调执行所有验证测试  
**返回值**: `void` - 通过console输出结果，失败时退出进程  
**副作用**: 
- 在控制台输出详细的验证报告
- 失败时设置进程退出码为1
- 成功时提供下一步操作指导

**执行流程**:
1. 依次执行所有验证函数
2. 收集测试结果
3. 输出汇总报告
4. 提供操作建议

### 使用示例

#### 基本使用
```bash
# 运行完整的项目配置验证
node scripts/test-build.js

# 通过npm脚本运行
npm run test:config
```

#### 编程方式使用
```javascript
import { checkRequiredFiles, validatePackageJson } from './scripts/test-build.js'

// 检查单个功能
const filesExist = checkRequiredFiles()
const packageValid = validatePackageJson()

if (filesExist && packageValid) {
  console.log('基础配置正确')
}
```

### 错误处理

所有函数都包含完善的错误处理：
- JSON解析错误自动捕获
- 文件系统访问异常处理
- 详细的错误信息输出
- 优雅的降级处理

### 扩展性

脚本设计为模块化结构，可以轻松添加新的验证功能：

```javascript
// 添加新的验证函数
function validateNewFeature() {
  console.log('\n🔍 验证新功能:')
  // 验证逻辑
  return true
}

// 在main函数中注册
const tests = [
  // 现有测试...
  { name: '新功能验证', fn: validateNewFeature }
]
```

### 依赖关系

- **Node.js**: >= 14.0.0 (支持ES模块)
- **内置模块**: `fs`, `path`
- **外部依赖**: 无

### 性能特征

- **执行时间**: 通常 < 1秒
- **内存使用**: 最小化，仅读取必要文件
- **I/O操作**: 只读操作，不修改任何文件
- **并发安全**: 可以并发运行多个实例

---

## extract-api.js

### 概述
API 提取工具，用于从 TypeScript 文件中自动提取函数签名、接口定义和类型定义，生成详细的 API 文档。

### 导出类

#### APIExtractor
```javascript
class APIExtractor
```

**功能**: API 信息提取和文档生成的核心类  
**用途**: 分析 TypeScript 源代码，提取 API 信息并生成文档

**主要方法**:

##### extractFromFile()
```javascript
extractFromFile(filePath: string): void
```

**功能**: 从指定文件中提取 API 信息  
**参数**: 
- `filePath`: 要分析的 TypeScript 文件路径

**副作用**: 更新内部的函数、接口和类型列表

##### extractFunctions()
```javascript
extractFunctions(content: string, fileName: string): void
```

**功能**: 从文件内容中提取函数定义  
**参数**:
- `content`: 文件内容字符串
- `fileName`: 文件名（用于标识）

**提取的函数类型**:
- 普通函数声明: `function functionName()`
- 异步函数: `async function functionName()`
- 箭头函数: `const functionName = () =>`
- 异步箭头函数: `const functionName = async () =>`

##### extractInterfaces()
```javascript
extractInterfaces(content: string, fileName: string): void
```

**功能**: 从文件内容中提取接口定义  
**参数**:
- `content`: 文件内容字符串
- `fileName`: 文件名（用于标识）

**提取模式**: `interface InterfaceName { ... }`

##### extractTypes()
```javascript
extractTypes(content: string, fileName: string): void
```

**功能**: 从文件内容中提取类型定义  
**参数**:
- `content`: 文件内容字符串
- `fileName`: 文件名（用于标识）

**提取模式**: `type TypeName = ...`

##### extractComment()
```javascript
extractComment(beforeCode: string): string
```

**功能**: 提取代码前的注释内容  
**参数**: 
- `beforeCode`: 代码前的内容

**返回值**: 清理后的注释文本

**支持的注释格式**:
- 单行注释: `// 注释内容`
- 多行注释: `/* 注释内容 */`
- JSDoc 注释: `/** 注释内容 */`

##### generateDocumentation()
```javascript
generateDocumentation(): string
```

**功能**: 生成完整的 API 文档  
**返回值**: Markdown 格式的文档字符串

**文档结构**:
- 函数列表（包含签名、说明、文件来源）
- 接口列表（包含完整定义和说明）
- 类型定义列表（包含定义和说明）

##### generateStats()
```javascript
generateStats(): object
```

**功能**: 生成提取统计信息  
**返回值**: 包含各类型数量的统计对象

**返回格式**:
```javascript
{
  functions: number,    // 函数数量
  interfaces: number,   // 接口数量
  types: number,        // 类型数量
  total: number         // 总数量
}
```

### 使用示例

#### 基本使用
```bash
# 运行 API 提取工具
node scripts/extract-api.js
```

#### 编程方式使用
```javascript
import { APIExtractor } from './scripts/extract-api.js'

const extractor = new APIExtractor()

// 分析单个文件
extractor.extractFromFile('src/background/index.ts')

// 生成文档
const documentation = extractor.generateDocumentation()
console.log(documentation)

// 获取统计信息
const stats = extractor.generateStats()
console.log(`提取了 ${stats.total} 个 API 项目`)
```

### 输出示例

**控制台输出**:
```
🔍 提取 API 信息...
📄 分析文件: index.ts
📄 分析文件: index.ts
📄 分析文件: index.ts

📊 提取统计:
  函数: 12 个
  接口: 10 个
  类型: 1 个
  总计: 23 个

📝 文档已生成: docs/api-extraction.md
✅ API 提取完成！
```

**生成的文档格式**:
```markdown
# API 提取报告

## 函数列表

### functionName
**文件**: index.ts
**类型**: function
**签名**: `function functionName(param: type): returnType`
**说明**: 函数功能描述

## 接口列表

### InterfaceName
**文件**: index.ts
**说明**: 接口功能描述

```typescript
interface InterfaceName {
  property: type
}
```

## 类型定义

### TypeName
**文件**: index.ts
**说明**: 类型功能描述

```typescript
type TypeName = 'value1' | 'value2'
```
```

### 配置选项

可以通过修改 `main()` 函数中的 `filesToAnalyze` 数组来配置要分析的文件：

```javascript
const filesToAnalyze = [
  'src/background/index.ts',
  'src/content/index.ts',
  'src/types/index.ts',
  'src/popup/PopupApp.tsx',  // 添加新文件
  'src/options/OptionsApp.tsx'
]
```

### 扩展性

#### 添加新的提取规则
```javascript
// 在 APIExtractor 类中添加新方法
extractClasses(content, fileName) {
  const classRegex = /class\s+(\w+)(?:\s+extends\s+\w+)?\s*{/g
  let match
  
  while ((match = classRegex.exec(content)) !== null) {
    const className = match[1]
    // 处理类定义...
  }
}
```

#### 自定义输出格式
```javascript
// 添加 JSON 格式输出
generateJSON() {
  return JSON.stringify({
    functions: this.functions,
    interfaces: this.interfaces,
    types: this.types
  }, null, 2)
}
```

### 错误处理

脚本包含完善的错误处理：
- 文件不存在时的警告提示
- 正则表达式匹配失败的处理
- 文件读取权限错误的处理

### 性能特征

- **执行时间**: 通常 < 2秒（取决于文件大小和数量）
- **内存使用**: 轻量级，仅在内存中处理文本
- **I/O操作**: 只读源文件，写入一个文档文件
- **扩展性**: 可以轻松添加新的文件类型支持