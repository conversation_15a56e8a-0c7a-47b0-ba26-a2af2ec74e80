#!/usr/bin/env node

/**
 * 快速测试本地AI服务的脚本
 * 
 * 使用方法:
 * node scripts/quick-test-local-ai.js
 */

import { execSync } from 'child_process';
import { existsSync } from 'fs';
import { join } from 'path';

const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function colorLog(color, message) {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function success(message) {
  colorLog('green', `✅ ${message}`);
}

function error(message) {
  colorLog('red', `❌ ${message}`);
}

function info(message) {
  colorLog('blue', `ℹ️  ${message}`);
}

function warning(message) {
  colorLog('yellow', `⚠️  ${message}`);
}

function header(message) {
  console.log();
  colorLog('cyan', `${'='.repeat(60)}`);
  colorLog('cyan', `🤖 ${message}`);
  colorLog('cyan', `${'='.repeat(60)}`);
}

async function checkPrerequisites() {
  header('检查前置条件');
  
  // 检查Node.js版本
  try {
    const nodeVersion = process.version;
    info(`Node.js版本: ${nodeVersion}`);
    
    const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]);
    if (majorVersion < 16) {
      error('需要Node.js 16或更高版本');
      return false;
    }
    success('Node.js版本检查通过');
  } catch (err) {
    error('无法检查Node.js版本');
    return false;
  }
  
  // 检查项目文件
  const requiredFiles = [
    'package.json',
    'src/services/localAIServiceAdapter.ts',
    'src/services/aiProviderService.ts',
    'src/components/test/LocalAIServiceTestPage.tsx'
  ];
  
  for (const file of requiredFiles) {
    if (existsSync(file)) {
      success(`找到文件: ${file}`);
    } else {
      error(`缺少文件: ${file}`);
      return false;
    }
  }
  
  return true;
}

async function runTests() {
  header('运行单元测试');
  
  try {
    info('运行本地AI服务适配器测试...');
    execSync('npm test tests/localAIServiceAdapter.test.ts', { stdio: 'inherit' });
    success('本地AI服务适配器测试通过');
    
    info('运行AI提供商服务测试...');
    execSync('npm test tests/aiProviderService.test.ts', { stdio: 'inherit' });
    success('AI提供商服务测试通过');
    
    return true;
  } catch (err) {
    error('测试失败');
    return false;
  }
}

async function buildExtension() {
  header('构建Chrome扩展');
  
  try {
    info('开始构建...');
    execSync('npm run build', { stdio: 'inherit' });
    success('扩展构建成功');
    
    // 检查构建产物
    if (existsSync('dist')) {
      success('构建产物已生成');
      
      const distFiles = [
        'dist/manifest.json',
        'dist/src/options/index.html',
        'dist/assets'
      ];
      
      for (const file of distFiles) {
        if (existsSync(file)) {
          success(`构建文件存在: ${file}`);
        } else {
          warning(`构建文件缺失: ${file}`);
        }
      }
    } else {
      error('构建产物目录不存在');
      return false;
    }
    
    return true;
  } catch (err) {
    error('构建失败');
    return false;
  }
}

function printInstructions() {
  header('安装和测试说明');
  
  console.log();
  colorLog('bright', '📋 Chrome扩展安装步骤:');
  console.log('1. 打开Chrome浏览器');
  console.log('2. 访问 chrome://extensions/');
  console.log('3. 开启右上角的"开发者模式"');
  console.log('4. 点击"加载已解压的扩展程序"');
  console.log('5. 选择项目的 dist 文件夹');
  
  console.log();
  colorLog('bright', '🧪 测试页面访问步骤:');
  console.log('1. 点击扩展图标，选择"选项"');
  console.log('2. 在左侧导航栏找到"本地AI服务测试"');
  console.log('3. 点击进入测试页面');
  
  console.log();
  colorLog('bright', '🔍 测试功能:');
  console.log('• 服务发现 - 自动扫描本地AI服务');
  console.log('• 连接测试 - 测试服务可用性');
  console.log('• 模型获取 - 查看可用模型列表');
  console.log('• 自定义配置 - 测试特定服务');
  
  console.log();
  colorLog('bright', '🎯 支持的服务:');
  console.log('• Ollama (端口11434)');
  console.log('• LM Studio (端口1234)');
  console.log('• Xinference (端口9997)');
  console.log('• Text Generation WebUI (端口5000)');
  console.log('• LocalAI (端口8080)');
  console.log('• 其他OpenAI兼容服务');
  
  console.log();
  colorLog('bright', '📚 详细文档:');
  console.log('• 查看 docs/local-ai-service-test-guide.md');
  console.log('• 查看 docs/ai-integration-task2-completion-summary.md');
  
  console.log();
  colorLog('magenta', '🚀 准备就绪！现在可以在Chrome扩展中测试本地AI服务功能了！');
}

async function main() {
  console.log();
  colorLog('magenta', '🤖 本地AI服务集成快速测试工具');
  console.log();
  
  try {
    // 1. 检查前置条件
    const prereqsOk = await checkPrerequisites();
    if (!prereqsOk) {
      error('前置条件检查失败，请解决问题后重试');
      process.exit(1);
    }
    
    // 2. 运行单元测试
    const testsOk = await runTests();
    if (!testsOk) {
      error('单元测试失败，请检查代码');
      process.exit(1);
    }
    
    // 3. 构建扩展
    const buildOk = await buildExtension();
    if (!buildOk) {
      error('扩展构建失败，请检查构建配置');
      process.exit(1);
    }
    
    // 4. 显示使用说明
    printInstructions();
    
    success('快速测试准备完成！');
    
  } catch (err) {
    error(`测试过程中发生错误: ${err.message}`);
    process.exit(1);
  }
}

// 运行主函数
main().catch(err => {
  error(`脚本执行失败: ${err.message}`);
  process.exit(1);
});