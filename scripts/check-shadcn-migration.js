#!/usr/bin/env node

/**
 * 简化版 shadcn/ui 迁移检查工具
 */

import fs from 'fs'
import path from 'path'

// 颜色映射规则
const OLD_COLORS = [
  'bg-gray-100', 'bg-gray-200', 'bg-gray-800', 'bg-gray-900',
  'text-gray-700', 'text-gray-300', 'text-gray-600',
  'border-gray-300', 'border-gray-200',
  'focus:ring-blue-500', 'focus:ring-primary-500'
]

const DARK_MODE_CLASSES = [
  'dark:bg-gray-800', 'dark:bg-gray-900', 'dark:text-gray-300',
  'dark:text-white', 'dark:border-gray-600'
]

function findTSXFiles(dir) {
  const files = []
  
  function scan(currentDir) {
    try {
      const items = fs.readdirSync(currentDir)
      
      items.forEach(item => {
        const fullPath = path.join(currentDir, item)
        const stat = fs.statSync(fullPath)
        
        if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
          scan(fullPath)
        } else if (stat.isFile() && (item.endsWith('.tsx') || item.endsWith('.ts')) && !item.includes('.test.')) {
          files.push(fullPath)
        }
      })
    } catch (error) {
      console.log(`跳过目录: ${currentDir} (${error.message})`)
    }
  }
  
  scan(dir)
  return files
}

function checkFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8')
    const issues = []
    
    // 检查旧颜色类
    OLD_COLORS.forEach(oldColor => {
      if (content.includes(oldColor)) {
        issues.push({
          type: '旧颜色类',
          class: oldColor,
          suggestion: '替换为shadcn颜色变量'
        })
      }
    })
    
    // 检查深色模式类
    DARK_MODE_CLASSES.forEach(darkClass => {
      if (content.includes(darkClass)) {
        issues.push({
          type: '深色模式类',
          class: darkClass,
          suggestion: '移除，使用shadcn自动处理'
        })
      }
    })
    
    return issues
  } catch (error) {
    console.log(`无法读取文件: ${filePath} (${error.message})`)
    return []
  }
}

function main() {
  console.log('🔍 开始检查shadcn迁移质量...\n')
  
  const files = findTSXFiles('src')
  console.log(`📁 找到 ${files.length} 个文件需要检查\n`)
  
  let totalIssues = 0
  let migratedFiles = 0
  
  files.forEach(file => {
    const issues = checkFile(file)
    
    if (issues.length > 0) {
      console.log(`⚠️  ${file}`)
      issues.forEach(issue => {
        console.log(`   - ${issue.type}: ${issue.class}`)
        console.log(`     💡 ${issue.suggestion}`)
      })
      console.log('')
      totalIssues += issues.length
    } else {
      migratedFiles++
    }
  })
  
  // 统计报告
  console.log('📊 检查结果统计')
  console.log('=' .repeat(30))
  console.log(`📁 检查文件数: ${files.length}`)
  console.log(`✅ 已迁移文件: ${migratedFiles}`)
  console.log(`⚠️  发现问题数: ${totalIssues}`)
  console.log(`📈 迁移进度: ${Math.round((migratedFiles / files.length) * 100)}%`)
  
  if (totalIssues === 0) {
    console.log('\n🎉 恭喜！所有文件都已正确迁移到shadcn!')
  } else {
    console.log(`\n📋 建议使用迁移助手工具: npm run shadcn:migrate`)
  }
}

main()