<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图标生成器</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .icon-preview {
            display: inline-block;
            margin: 10px;
            text-align: center;
            border: 1px solid #ddd;
            padding: 10px;
        }
        canvas {
            border: 1px solid #ccc;
        }
        button {
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 20px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #005a87;
        }
    </style>
</head>
<body>
    <h1>Universe Bag 图标生成器</h1>
    <p>这个工具将帮助你从logo.png生成不同尺寸的图标文件。</p>
    
    <input type="file" id="logoInput" accept="image/*">
    <button onclick="generateIcons()">生成图标</button>
    <button onclick="downloadAll()">下载所有图标</button>
    
    <div id="iconContainer">
        <div class="icon-preview">
            <h3>16x16</h3>
            <canvas id="canvas16" width="16" height="16"></canvas>
            <br><button onclick="downloadIcon(16)">下载</button>
        </div>
        <div class="icon-preview">
            <h3>32x32</h3>
            <canvas id="canvas32" width="32" height="32"></canvas>
            <br><button onclick="downloadIcon(32)">下载</button>
        </div>
        <div class="icon-preview">
            <h3>48x48</h3>
            <canvas id="canvas48" width="48" height="48"></canvas>
            <br><button onclick="downloadIcon(48)">下载</button>
        </div>
        <div class="icon-preview">
            <h3>128x128</h3>
            <canvas id="canvas128" width="128" height="128"></canvas>
            <br><button onclick="downloadIcon(128)">下载</button>
        </div>
    </div>

    <script>
        let originalImage = null;
        const sizes = [16, 32, 48, 128];

        // 自动加载logo.png
        window.onload = function() {
            loadLogoFromPath();
        };

        function loadLogoFromPath() {
            const img = new Image();
            img.onload = function() {
                originalImage = img;
                generateIcons();
            };
            img.onerror = function() {
                console.log('无法自动加载logo.png，请手动选择文件');
            };
            img.src = '../logo.png';
        }

        document.getElementById('logoInput').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    const img = new Image();
                    img.onload = function() {
                        originalImage = img;
                        generateIcons();
                    };
                    img.src = e.target.result;
                };
                reader.readAsDataURL(file);
            }
        });

        function generateIcons() {
            if (!originalImage) return;

            sizes.forEach(size => {
                const canvas = document.getElementById(`canvas${size}`);
                const ctx = canvas.getContext('2d');
                
                // 清除画布
                ctx.clearRect(0, 0, size, size);
                
                // 绘制调整尺寸后的图像
                ctx.drawImage(originalImage, 0, 0, size, size);
            });
        }

        function downloadIcon(size) {
            const canvas = document.getElementById(`canvas${size}`);
            const link = document.createElement('a');
            link.download = `icon-${size}.png`;
            link.href = canvas.toDataURL();
            link.click();
        }

        function downloadAll() {
            sizes.forEach(size => {
                setTimeout(() => downloadIcon(size), size * 10);
            });
        }
    </script>
</body>
</html>