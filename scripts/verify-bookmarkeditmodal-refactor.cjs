// BookmarkEditModal shadcn重构验证脚本

const fs = require('fs')
const path = require('path')

console.log('🔍 开始验证BookmarkEditModal shadcn重构...\n')

// 验证文件存在性
const filesToCheck = [
  'src/components/BookmarkEditModal.tsx',
  'tests/BookmarkEditModal.shadcn.test.tsx',
  'src/components/examples/BookmarkEditModalDemo.tsx',
  'src/components/test/BookmarkEditModalTest.tsx',
  'docs/task-8-bookmarkeditmodal-shadcn-refactor.md'
]

console.log('📋 检查: 验证重构相关文件存在')
let allFilesExist = true
filesToCheck.forEach(file => {
  if (fs.existsSync(file)) {
    console.log(`✅ ${file} - 存在`)
  } else {
    console.log(`❌ ${file} - 不存在`)
    allFilesExist = false
  }
})

if (allFilesExist) {
  console.log('✅ 验证重构相关文件存在 - 通过\n')
} else {
  console.log('❌ 验证重构相关文件存在 - 失败\n')
  process.exit(1)
}

// 验证BookmarkEditModal组件内容
console.log('📋 检查: 验证BookmarkEditModal组件shadcn集成')
const bookmarkEditModalContent = fs.readFileSync('src/components/BookmarkEditModal.tsx', 'utf8')

const requiredImports = [
  'from "@/components/ui/dialog"',
  'from "@/components/ui/form"',
  'from "@/components/ui/input"',
  'from "@/components/ui/textarea"',
  'from "@/components/ui/button"',
  'from "@/components/ui/select"',
  'from "@/components/ui/badge"',
  'from "react-hook-form"'
]

const requiredComponents = [
  'Dialog',
  'DialogContent',
  'DialogHeader',
  'DialogTitle',
  'DialogDescription',
  'DialogFooter',
  'Form',
  'FormField',
  'FormItem',
  'FormLabel',
  'FormControl',
  'FormMessage',
  'Input',
  'Textarea',
  'Button',
  'Select',
  'Badge',
  'useForm'
]

let importsValid = true
requiredImports.forEach(importStr => {
  if (bookmarkEditModalContent.includes(importStr)) {
    console.log(`✅ 导入检查: ${importStr}`)
  } else {
    console.log(`❌ 导入检查: ${importStr} - 缺失`)
    importsValid = false
  }
})

let componentsValid = true
requiredComponents.forEach(component => {
  if (bookmarkEditModalContent.includes(component)) {
    console.log(`✅ 组件使用: ${component}`)
  } else {
    console.log(`❌ 组件使用: ${component} - 未使用`)
    componentsValid = false
  }
})

// 检查是否移除了自定义样式
const customStylePatterns = [
  'className="fixed inset-0',
  'className="bg-black bg-opacity',
  'className="flex min-h-full',
  'className="relative bg-white rounded-lg shadow-xl'
]

let customStylesRemoved = true
customStylePatterns.forEach(pattern => {
  if (bookmarkEditModalContent.includes(pattern)) {
    console.log(`❌ 自定义样式检查: 发现自定义模态样式 - ${pattern}`)
    customStylesRemoved = false
  } else {
    console.log(`✅ 自定义样式检查: 已移除自定义模态样式`)
  }
})

if (importsValid && componentsValid && customStylesRemoved) {
  console.log('✅ 验证BookmarkEditModal组件shadcn集成 - 通过\n')
} else {
  console.log('❌ 验证BookmarkEditModal组件shadcn集成 - 失败\n')
}

// 验证测试文件
console.log('📋 检查: 验证测试文件内容')
const testContent = fs.readFileSync('tests/BookmarkEditModal.shadcn.test.tsx', 'utf8')

const testPatterns = [
  'BookmarkEditModal shadcn组件测试',
  '应该正确渲染shadcn Dialog组件',
  '应该正确渲染shadcn Form组件',
  '应该正确渲染shadcn Input组件',
  '应该正确渲染shadcn Button组件',
  '应该正确渲染shadcn Select组件'
]

let testsValid = true
testPatterns.forEach(pattern => {
  if (testContent.includes(pattern)) {
    console.log(`✅ 测试内容: ${pattern}`)
  } else {
    console.log(`❌ 测试内容: ${pattern} - 缺失`)
    testsValid = false
  }
})

if (testsValid) {
  console.log('✅ 验证测试文件内容 - 通过\n')
} else {
  console.log('❌ 验证测试文件内容 - 失败\n')
}

// 验证演示组件
console.log('📋 检查: 验证演示组件')
const demoContent = fs.readFileSync('src/components/examples/BookmarkEditModalDemo.tsx', 'utf8')

if (demoContent.includes('BookmarkEditModal') && demoContent.includes('shadcn重构演示')) {
  console.log('✅ 验证演示组件 - 通过\n')
} else {
  console.log('❌ 验证演示组件 - 失败\n')
}

// 验证测试页面
console.log('📋 检查: 验证测试页面')
const testPageContent = fs.readFileSync('src/components/test/BookmarkEditModalTest.tsx', 'utf8')

if (testPageContent.includes('BookmarkEditModal shadcn重构测试') && testPageContent.includes('testBookmarks')) {
  console.log('✅ 验证测试页面 - 通过\n')
} else {
  console.log('❌ 验证测试页面 - 失败\n')
}

// 验证文档
console.log('📋 检查: 验证文档完整性')
const docContent = fs.readFileSync('docs/task-8-bookmarkeditmodal-shadcn-refactor.md', 'utf8')

const docSections = [
  '# 任务8：BookmarkEditModal shadcn重构完成报告',
  '## 重构内容',
  '## 技术实现',
  '## 测试覆盖',
  '## 演示组件'
]

let docValid = true
docSections.forEach(section => {
  if (docContent.includes(section)) {
    console.log(`✅ 文档章节: ${section}`)
  } else {
    console.log(`❌ 文档章节: ${section} - 缺失`)
    docValid = false
  }
})

if (docValid) {
  console.log('✅ 验证文档完整性 - 通过\n')
} else {
  console.log('❌ 验证文档完整性 - 失败\n')
}

// 统计代码行数
console.log('📊 代码统计:')
const componentLines = bookmarkEditModalContent.split('\n').length
const testLines = testContent.split('\n').length
const demoLines = demoContent.split('\n').length
const testPageLines = testPageContent.split('\n').length

console.log(`   BookmarkEditModal组件: ${componentLines} 行`)
console.log(`   测试文件: ${testLines} 行`)
console.log(`   演示组件: ${demoLines} 行`)
console.log(`   测试页面: ${testPageLines} 行`)
console.log(`   总计: ${componentLines + testLines + demoLines + testPageLines} 行\n`)

// 最终结果
const allChecksPass = allFilesExist && importsValid && componentsValid && customStylesRemoved && testsValid && docValid

console.log('==================================================')
if (allChecksPass) {
  console.log('🎉 BookmarkEditModal shadcn重构验证完成: 所有检查通过!')
  console.log('✅ 组件已成功重构为使用shadcn/ui组件')
  console.log('✅ 测试覆盖完整')
  console.log('✅ 演示和文档齐全')
  console.log('\n🚀 可以继续进行下一个组件的重构工作!')
} else {
  console.log('❌ BookmarkEditModal shadcn重构验证失败')
  console.log('请检查上述失败项并修复后重新验证')
  process.exit(1)
}
console.log('==================================================')