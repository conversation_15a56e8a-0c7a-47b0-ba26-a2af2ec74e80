/**
 * VirtualBookmarkList组件替换验证脚本
 * 验证重构后的组件在实际应用中的工作状态
 */

const fs = require('fs')
const path = require('path')

// 颜色输出函数
const colors = {
  green: (text) => `\x1b[32m${text}\x1b[0m`,
  red: (text) => `\x1b[31m${text}\x1b[0m`,
  yellow: (text) => `\x1b[33m${text}\x1b[0m`,
  blue: (text) => `\x1b[34m${text}\x1b[0m`,
  cyan: (text) => `\x1b[36m${text}\x1b[0m`,
  magenta: (text) => `\x1b[35m${text}\x1b[0m`
}

console.log(colors.cyan('🔄 VirtualBookmarkList组件替换验证'))
console.log('=' .repeat(50))

// 检查文件内容
function checkFileContent(filePath, checks) {
  try {
    const content = fs.readFileSync(path.resolve(filePath), 'utf8')
    let allPassed = true
    
    console.log(colors.blue(`\n📄 检查 ${filePath}:`))
    
    checks.forEach(({ name, pattern, required = true }) => {
      const found = pattern.test(content)
      const status = found ? colors.green('✓') : (required ? colors.red('✗') : colors.yellow('⚠'))
      console.log(`  ${status} ${name}`)
      
      if (required && !found) {
        allPassed = false
      }
    })
    
    return allPassed
  } catch (error) {
    console.log(colors.red(`✗ 无法读取文件: ${filePath}`))
    return false
  }
}

let allTestsPassed = true

// 1. 验证VirtualBookmarkList组件的shadcn重构
console.log(colors.yellow('\n1. 验证VirtualBookmarkList组件shadcn重构:'))
const componentChecks = [
  {
    name: '使用shadcn Button组件',
    pattern: /import.*Button.*from ['"]\.\/ui\/button['"]/
  },
  {
    name: '使用shadcn Badge组件',
    pattern: /import.*Badge.*from ['"]\.\/ui\/badge['"]/
  },
  {
    name: '使用cn工具函数',
    pattern: /import.*cn.*from ['"]@\/lib\/utils['"]/
  },
  {
    name: '使用shadcn颜色系统 - text-foreground',
    pattern: /text-foreground/
  },
  {
    name: '使用shadcn颜色系统 - text-muted-foreground',
    pattern: /text-muted-foreground/
  },
  {
    name: '使用shadcn颜色系统 - border-border',
    pattern: /border-border/
  },
  {
    name: '使用shadcn颜色系统 - border-primary',
    pattern: /border-primary/
  },
  {
    name: '移除了自定义颜色类',
    pattern: /text-gray-400|text-gray-600|border-gray-200/,
    required: false // 这些应该不存在
  }
]

if (!checkFileContent('src/components/VirtualBookmarkList.tsx', componentChecks)) {
  allTestsPassed = false
}

// 2. 验证OptionsApp中的正确使用
console.log(colors.yellow('\n2. 验证OptionsApp中的VirtualBookmarkList使用:'))
const optionsAppChecks = [
  {
    name: '正确导入VirtualBookmarkList',
    pattern: /import VirtualBookmarkList from ['"]\.\.\/components\/VirtualBookmarkList['"]/
  },
  {
    name: '在useVirtualScroll条件下使用',
    pattern: /useVirtualScroll \? \(\s*<VirtualBookmarkList/
  },
  {
    name: '传递bookmarks属性',
    pattern: /bookmarks=\{filteredBookmarks\}/
  },
  {
    name: '传递viewMode属性',
    pattern: /viewMode=\{displayViewMode\}/
  },
  {
    name: '传递containerHeight属性',
    pattern: /containerHeight=\{600\}/
  },
  {
    name: '传递highlightBookmarkId属性',
    pattern: /highlightBookmarkId=\{highlightBookmarkId\}/
  },
  {
    name: '传递onEdit回调',
    pattern: /onEdit=\{handleEditClick\}/
  },
  {
    name: '传递onDelete回调',
    pattern: /onDelete=\{handleDeleteClick\}/
  },
  {
    name: '传递onClick回调',
    pattern: /onClick=\{handleBookmarkClick\}/
  }
]

if (!checkFileContent('src/options/OptionsApp.tsx', optionsAppChecks)) {
  allTestsPassed = false
}

// 3. 验证构建产物
console.log(colors.yellow('\n3. 验证构建产物:'))
const buildFiles = [
  'dist/src/options/index.html',
  'dist/manifest.json'
]

let buildValid = true
buildFiles.forEach(file => {
  if (fs.existsSync(file)) {
    console.log(colors.green(`✓ ${file}`))
  } else {
    console.log(colors.red(`✗ ${file}`))
    buildValid = false
  }
})

if (!buildValid) {
  allTestsPassed = false
}

// 4. 验证shadcn组件依赖
console.log(colors.yellow('\n4. 验证shadcn组件依赖:'))
const shadcnComponents = [
  'src/components/ui/button.tsx',
  'src/components/ui/badge.tsx',
  'src/lib/utils.ts'
]

shadcnComponents.forEach(component => {
  if (fs.existsSync(component)) {
    console.log(colors.green(`✓ ${component}`))
  } else {
    console.log(colors.red(`✗ ${component}`))
    allTestsPassed = false
  }
})

// 5. 检查接口兼容性
console.log(colors.yellow('\n5. 检查接口兼容性:'))
try {
  const componentContent = fs.readFileSync('src/components/VirtualBookmarkList.tsx', 'utf8')
  
  const interfaceChecks = [
    {
      name: 'VirtualBookmarkListProps接口存在',
      pattern: /interface VirtualBookmarkListProps/
    },
    {
      name: 'bookmarks属性定义',
      pattern: /bookmarks:\s*any\[\]/
    },
    {
      name: 'viewMode属性定义',
      pattern: /viewMode:\s*ViewMode/
    },
    {
      name: 'containerHeight属性定义',
      pattern: /containerHeight:\s*number/
    },
    {
      name: 'highlightBookmarkId属性定义',
      pattern: /highlightBookmarkId\?:\s*string\s*\|\s*null/
    },
    {
      name: 'onEdit回调定义',
      pattern: /onEdit:\s*\(bookmark:\s*any\)\s*=>\s*void/
    },
    {
      name: 'onDelete回调定义',
      pattern: /onDelete:\s*\(bookmark:\s*any\)\s*=>\s*void/
    },
    {
      name: 'onClick回调定义',
      pattern: /onClick:\s*\(bookmark:\s*any\)\s*=>\s*void/
    }
  ]
  
  console.log(colors.blue('\n📄 检查接口兼容性:'))
  interfaceChecks.forEach(({ name, pattern }) => {
    const found = pattern.test(componentContent)
    const status = found ? colors.green('✓') : colors.red('✗')
    console.log(`  ${status} ${name}`)
    if (!found) allTestsPassed = false
  })
  
} catch (error) {
  console.log(colors.red('✗ 无法检查接口兼容性'))
  allTestsPassed = false
}

// 6. 总结
console.log('\n' + '='.repeat(50))
if (allTestsPassed) {
  console.log(colors.green('🎉 VirtualBookmarkList组件替换验证通过！'))
  console.log(colors.green('\n✅ 验证通过的项目:'))
  console.log(colors.green('  • shadcn组件正确集成'))
  console.log(colors.green('  • OptionsApp中正确使用'))
  console.log(colors.green('  • 构建产物完整'))
  console.log(colors.green('  • shadcn依赖完整'))
  console.log(colors.green('  • 接口完全兼容'))
  
  console.log(colors.cyan('\n🚀 组件已成功替换，可以进行实际测试:'))
  console.log(colors.cyan('  1. 在Chrome中加载扩展'))
  console.log(colors.cyan('  2. 打开扩展选项页面'))
  console.log(colors.cyan('  3. 测试收藏管理功能'))
  console.log(colors.cyan('  4. 验证虚拟滚动性能'))
  console.log(colors.cyan('  5. 检查shadcn样式效果'))
  
  console.log(colors.magenta('\n📋 重点测试项目:'))
  console.log(colors.magenta('  • 视图模式切换（行视图、紧凑视图、卡片视图）'))
  console.log(colors.magenta('  • 大量数据下的虚拟滚动性能'))
  console.log(colors.magenta('  • 编辑和删除按钮的shadcn样式'))
  console.log(colors.magenta('  • 标签的Badge组件显示'))
  console.log(colors.magenta('  • 高亮功能的shadcn颜色'))
  console.log(colors.magenta('  • 整体视觉一致性'))
  
} else {
  console.log(colors.red('❌ VirtualBookmarkList组件替换验证失败'))
  console.log(colors.yellow('\n🔧 请检查上述失败项目并修复'))
}

console.log(colors.blue('\n📖 相关文档:'))
console.log(colors.blue('  • 重构文档: docs/task-11-virtualbookmarklist-shadcn-refactor.md'))
console.log(colors.blue('  • 测试指南: docs/virtualbookmarklist-test-guide.md'))
console.log(colors.blue('  • 快速测试: node scripts/quick-test-virtualbookmarklist.cjs'))

process.exit(allTestsPassed ? 0 : 1)