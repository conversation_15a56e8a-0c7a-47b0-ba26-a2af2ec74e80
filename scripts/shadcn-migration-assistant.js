#!/usr/bin/env node

/**
 * shadcn/ui 迁移助手
 * 自动化辅助shadcn迁移过程
 */

const fs = require('fs')
const path = require('path')
const readline = require('readline')

// 颜色映射规则
const COLOR_MAPPINGS = {
  // 背景色
  'bg-gray-50': 'bg-secondary/50',
  'bg-gray-100': 'bg-secondary',
  'bg-gray-200': 'bg-secondary/80',
  'bg-gray-300': 'bg-muted',
  'bg-gray-800': 'bg-secondary',
  'bg-gray-900': 'bg-background',
  'bg-white': 'bg-background',
  'bg-blue-500': 'bg-primary',
  'bg-blue-600': 'bg-primary/90',
  'bg-green-500': 'bg-primary',
  'bg-red-500': 'bg-destructive',
  
  // 文本色
  'text-gray-400': 'text-muted-foreground',
  'text-gray-500': 'text-muted-foreground',
  'text-gray-600': 'text-muted-foreground',
  'text-gray-700': 'text-secondary-foreground',
  'text-gray-800': 'text-foreground',
  'text-gray-900': 'text-foreground',
  'text-black': 'text-foreground',
  'text-white': 'text-primary-foreground',
  'text-blue-500': 'text-primary',
  'text-blue-600': 'text-primary',
  'text-green-500': 'text-primary',
  'text-red-500': 'text-destructive',
  
  // 边框色
  'border-gray-200': 'border-border',
  'border-gray-300': 'border-border',
  'border-gray-400': 'border-border',
  'border-blue-500': 'border-primary',
  'border-red-500': 'border-destructive',
  
  // 焦点环
  'focus:ring-blue-500': 'focus:ring-ring',
  'focus:ring-primary-500': 'focus:ring-ring',
  'focus:ring-gray-500': 'focus:ring-ring',
  'focus:ring-green-500': 'focus:ring-ring',
  'focus:ring-red-500': 'focus:ring-ring'
}

// 需要移除的深色模式类
const DARK_MODE_CLASSES_TO_REMOVE = [
  'dark:bg-gray-50',
  'dark:bg-gray-100',
  'dark:bg-gray-200',
  'dark:bg-gray-300',
  'dark:bg-gray-800',
  'dark:bg-gray-900',
  'dark:text-gray-100',
  'dark:text-gray-200',
  'dark:text-gray-300',
  'dark:text-gray-400',
  'dark:text-white',
  'dark:border-gray-600',
  'dark:border-gray-700',
  'dark:border-gray-800'
]

class ShadcnMigrationAssistant {
  constructor() {
    this.rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    })
  }

  /**
   * 主入口函数
   */
  async run() {
    console.log('🚀 shadcn/ui 迁移助手')
    console.log('=' .repeat(40))
    
    try {
      const action = await this.selectAction()
      
      switch (action) {
        case '1':
          await this.migrateFile()
          break
        case '2':
          await this.migrateDirectory()
          break
        case '3':
          await this.generateTestTemplate()
          break
        case '4':
          await this.validateMigration()
          break
        case '5':
          await this.showMigrationGuide()
          break
        default:
          console.log('无效选择')
      }
    } catch (error) {
      console.error('❌ 错误:', error.message)
    } finally {
      this.rl.close()
    }
  }

  /**
   * 选择操作
   */
  selectAction() {
    return new Promise((resolve) => {
      console.log('\n请选择操作:')
      console.log('1. 迁移单个文件')
      console.log('2. 迁移整个目录')
      console.log('3. 生成测试模板')
      console.log('4. 验证迁移质量')
      console.log('5. 显示迁移指南')
      
      this.rl.question('\n请输入选择 (1-5): ', (answer) => {
        resolve(answer.trim())
      })
    })
  }

  /**
   * 迁移单个文件
   */
  async migrateFile() {
    const filePath = await this.askQuestion('请输入文件路径: ')
    
    if (!fs.existsSync(filePath)) {
      console.log('❌ 文件不存在:', filePath)
      return
    }

    console.log(`\n🔄 开始迁移文件: ${filePath}`)
    
    const originalContent = fs.readFileSync(filePath, 'utf8')
    const migratedContent = this.performMigration(originalContent)
    
    if (originalContent === migratedContent) {
      console.log('✅ 文件已经是最新的shadcn格式')
      return
    }

    // 显示变更预览
    console.log('\n📋 变更预览:')
    this.showChanges(originalContent, migratedContent)
    
    const confirm = await this.askQuestion('\n是否应用这些变更? (y/n): ')
    
    if (confirm.toLowerCase() === 'y') {
      // 备份原文件
      fs.writeFileSync(`${filePath}.backup`, originalContent)
      
      // 写入迁移后的内容
      fs.writeFileSync(filePath, migratedContent)
      
      console.log('✅ 迁移完成!')
      console.log(`📁 原文件已备份为: ${filePath}.backup`)
      
      // 询问是否生成测试文件
      const generateTest = await this.askQuestion('是否生成对应的测试文件? (y/n): ')
      if (generateTest.toLowerCase() === 'y') {
        await this.generateTestForFile(filePath)
      }
    } else {
      console.log('❌ 迁移已取消')
    }
  }

  /**
   * 迁移整个目录
   */
  async migrateDirectory() {
    const dirPath = await this.askQuestion('请输入目录路径: ')
    
    if (!fs.existsSync(dirPath)) {
      console.log('❌ 目录不存在:', dirPath)
      return
    }

    const files = this.findTSXFiles(dirPath)
    console.log(`\n📁 找到 ${files.length} 个文件需要检查`)
    
    let migratedCount = 0
    
    for (const file of files) {
      console.log(`\n🔄 检查文件: ${file}`)
      
      const originalContent = fs.readFileSync(file, 'utf8')
      const migratedContent = this.performMigration(originalContent)
      
      if (originalContent !== migratedContent) {
        console.log('📋 发现需要迁移的内容')
        
        const confirm = await this.askQuestion(`迁移 ${path.basename(file)}? (y/n/a=全部): `)
        
        if (confirm.toLowerCase() === 'y' || confirm.toLowerCase() === 'a') {
          // 备份并迁移
          fs.writeFileSync(`${file}.backup`, originalContent)
          fs.writeFileSync(file, migratedContent)
          migratedCount++
          console.log('✅ 已迁移')
          
          if (confirm.toLowerCase() === 'a') {
            // 自动迁移剩余文件
            for (let i = files.indexOf(file) + 1; i < files.length; i++) {
              const remainingFile = files[i]
              const remainingOriginal = fs.readFileSync(remainingFile, 'utf8')
              const remainingMigrated = this.performMigration(remainingOriginal)
              
              if (remainingOriginal !== remainingMigrated) {
                fs.writeFileSync(`${remainingFile}.backup`, remainingOriginal)
                fs.writeFileSync(remainingFile, remainingMigrated)
                migratedCount++
                console.log(`✅ 已迁移: ${path.basename(remainingFile)}`)
              }
            }
            break
          }
        }
      } else {
        console.log('✅ 已经是最新格式')
      }
    }
    
    console.log(`\n🎉 迁移完成! 共迁移了 ${migratedCount} 个文件`)
  }

  /**
   * 执行迁移转换
   */
  performMigration(content) {
    let migratedContent = content

    // 1. 替换颜色类
    Object.entries(COLOR_MAPPINGS).forEach(([oldClass, newClass]) => {
      const regex = new RegExp(`\\b${oldClass.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}\\b`, 'g')
      migratedContent = migratedContent.replace(regex, newClass)
    })

    // 2. 移除深色模式类
    DARK_MODE_CLASSES_TO_REMOVE.forEach(darkClass => {
      const regex = new RegExp(`\\s*${darkClass.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}\\b`, 'g')
      migratedContent = migratedContent.replace(regex, '')
    })

    // 3. 清理多余的空格
    migratedContent = migratedContent.replace(/\s+/g, ' ')
    migratedContent = migratedContent.replace(/className="\s+/g, 'className="')
    migratedContent = migratedContent.replace(/\s+"/g, '"')

    // 4. 添加性能优化提示
    if (content.includes('.find(') || content.includes('.filter(') || content.includes('.map(')) {
      if (!content.includes('useMemo')) {
        migratedContent = this.addPerformanceOptimizationComment(migratedContent)
      }
    }

    return migratedContent
  }

  /**
   * 添加性能优化注释
   */
  addPerformanceOptimizationComment(content) {
    if (!content.includes('import React')) {
      return content
    }

    return content.replace(
      /import React(.*?) from 'react'/,
      `import React$1 from 'react'\n// TODO: 考虑添加 useMemo 优化性能`
    )
  }

  /**
   * 显示变更内容
   */
  showChanges(original, migrated) {
    const originalLines = original.split('\n')
    const migratedLines = migrated.split('\n')
    
    let changeCount = 0
    
    for (let i = 0; i < Math.max(originalLines.length, migratedLines.length); i++) {
      const originalLine = originalLines[i] || ''
      const migratedLine = migratedLines[i] || ''
      
      if (originalLine !== migratedLine) {
        changeCount++
        if (changeCount <= 10) { // 只显示前10个变更
          console.log(`第${i + 1}行:`)
          console.log(`  - ${originalLine}`)
          console.log(`  + ${migratedLine}`)
        }
      }
    }
    
    if (changeCount > 10) {
      console.log(`... 还有 ${changeCount - 10} 个变更`)
    }
    
    console.log(`\n📊 总共 ${changeCount} 处变更`)
  }

  /**
   * 生成测试模板
   */
  async generateTestTemplate() {
    const componentPath = await this.askQuestion('请输入组件文件路径: ')
    
    if (!fs.existsSync(componentPath)) {
      console.log('❌ 组件文件不存在:', componentPath)
      return
    }

    await this.generateTestForFile(componentPath)
  }

  /**
   * 为文件生成测试
   */
  async generateTestForFile(componentPath) {
    const componentName = path.basename(componentPath, path.extname(componentPath))
    const testPath = componentPath.replace(/\.(tsx|ts)$/, '.shadcn.test.tsx')
    
    if (fs.existsSync(testPath)) {
      const overwrite = await this.askQuestion(`测试文件已存在，是否覆盖? (y/n): `)
      if (overwrite.toLowerCase() !== 'y') {
        return
      }
    }

    const testContent = this.generateTestContent(componentName, componentPath)
    fs.writeFileSync(testPath, testContent)
    
    console.log(`✅ 测试文件已生成: ${testPath}`)
  }

  /**
   * 生成测试内容
   */
  generateTestContent(componentName, componentPath) {
    const relativePath = path.relative(path.dirname(componentPath), componentPath).replace(/\.(tsx|ts)$/, '')
    
    return `import React from 'react'
import { render, screen, fireEvent } from '@testing-library/react'
import { describe, it, expect, vi, beforeEach } from 'vitest'
import ${componentName} from './${relativePath}'

describe('${componentName} - shadcn重构测试', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('shadcn颜色系统使用验证', () => {
    it('应该使用shadcn的颜色系统', () => {
      render(<${componentName} />)
      
      // TODO: 根据实际组件调整选择器
      const element = screen.getByRole('button') // 或其他适当的角色
      
      // 验证使用了shadcn颜色类
      expect(element).toHaveClass('bg-secondary')
      expect(element).toHaveClass('text-secondary-foreground')
      expect(element).toHaveClass('border-border')
    })

    it('应该不再使用旧的颜色类', () => {
      render(<${componentName} />)
      
      const element = screen.getByRole('button') // 或其他适当的角色
      
      // 验证不再使用旧颜色类
      expect(element).not.toHaveClass('bg-gray-100')
      expect(element).not.toHaveClass('text-gray-700')
      expect(element).not.toHaveClass('border-gray-300')
    })
  })

  describe('功能验证', () => {
    it('应该正确渲染组件', () => {
      render(<${componentName} />)
      
      // TODO: 添加具体的功能测试
      expect(screen.getByRole('button')).toBeInTheDocument()
    })

    it('应该正确处理用户交互', () => {
      const mockHandler = vi.fn()
      render(<${componentName} onClick={mockHandler} />)
      
      // TODO: 根据实际组件调整交互测试
      const element = screen.getByRole('button')
      fireEvent.click(element)
      
      expect(mockHandler).toHaveBeenCalledTimes(1)
    })
  })

  describe('无障碍性验证', () => {
    it('应该支持键盘导航', () => {
      render(<${componentName} />)
      
      const element = screen.getByRole('button')
      
      // 验证可以获得焦点
      element.focus()
      expect(element).toHaveFocus()
      
      // TODO: 添加键盘事件测试
    })

    it('应该有正确的ARIA标签', () => {
      render(<${componentName} />)
      
      const element = screen.getByRole('button')
      
      // TODO: 根据实际组件验证ARIA属性
      expect(element).toHaveAttribute('aria-label')
    })
  })

  describe('性能优化验证', () => {
    it('应该正确使用性能优化', () => {
      // TODO: 如果组件使用了useMemo/useCallback，添加相应测试
      render(<${componentName} />)
      
      // 验证组件正常渲染
      expect(screen.getByRole('button')).toBeInTheDocument()
    })
  })
})
`
  }

  /**
   * 验证迁移质量
   */
  async validateMigration() {
    console.log('🔍 运行迁移质量检查...')
    
    try {
      const { spawn } = require('child_process')
      const checker = spawn('node', ['scripts/shadcn-migration-checker.js'], {
        stdio: 'inherit'
      })
      
      checker.on('close', (code) => {
        if (code === 0) {
          console.log('\n✅ 迁移质量检查完成')
        } else {
          console.log('\n❌ 迁移质量检查发现问题')
        }
      })
    } catch (error) {
      console.log('❌ 无法运行检查工具:', error.message)
    }
  }

  /**
   * 显示迁移指南
   */
  async showMigrationGuide() {
    console.log('\n📚 shadcn迁移指南')
    console.log('=' .repeat(30))
    
    console.log('\n🎨 常用颜色映射:')
    Object.entries(COLOR_MAPPINGS).slice(0, 10).forEach(([old, new_]) => {
      console.log(`  ${old} → ${new_}`)
    })
    
    console.log('\n🚫 需要移除的深色模式类:')
    DARK_MODE_CLASSES_TO_REMOVE.slice(0, 5).forEach(cls => {
      console.log(`  ${cls}`)
    })
    
    console.log('\n📋 迁移步骤:')
    console.log('  1. 备份原文件')
    console.log('  2. 替换颜色类')
    console.log('  3. 移除深色模式类')
    console.log('  4. 添加性能优化')
    console.log('  5. 编写测试用例')
    console.log('  6. 验证迁移质量')
    
    console.log('\n📖 更多信息请查看:')
    console.log('  - docs/shadcn-ui-migration-guidelines.md')
    console.log('  - docs/shadcn-quick-reference.md')
  }

  /**
   * 查找TSX文件
   */
  findTSXFiles(dir) {
    const files = []
    
    const scan = (currentDir) => {
      const items = fs.readdirSync(currentDir)
      
      items.forEach(item => {
        const fullPath = path.join(currentDir, item)
        const stat = fs.statSync(fullPath)
        
        if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
          scan(fullPath)
        } else if (stat.isFile() && (item.endsWith('.tsx') || item.endsWith('.ts')) && !item.includes('.test.')) {
          files.push(fullPath)
        }
      })
    }
    
    scan(dir)
    return files
  }

  /**
   * 询问问题
   */
  askQuestion(question) {
    return new Promise((resolve) => {
      this.rl.question(question, (answer) => {
        resolve(answer.trim())
      })
    })
  }
}

// 运行助手
if (require.main === module) {
  const assistant = new ShadcnMigrationAssistant()
  assistant.run().catch(console.error)
}

module.exports = ShadcnMigrationAssistant