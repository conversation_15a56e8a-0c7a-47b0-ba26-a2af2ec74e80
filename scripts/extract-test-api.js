#!/usr/bin/env node

/**
 * 测试API提取工具
 * 从 test-build.test.js 文件中提取函数签名和文档
 * 
 * 功能说明:
 * - 自动提取测试框架类和方法的签名
 * - 生成完整的API参考文档
 * - 验证文档与代码的一致性
 * - 支持中文注释和文档生成
 */

import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)
const rootDir = path.resolve(__dirname, '..')

console.log('🔍 提取测试API信息...')

/**
 * API提取器类
 * 
 * 负责从JavaScript文件中提取类、方法、函数等API信息
 * 并生成结构化的文档数据
 */
class TestAPIExtractor {
  constructor() {
    this.classes = []      // 提取的类信息
    this.functions = []    // 提取的函数信息
    this.constants = []    // 提取的常量信息
    this.testCases = []    // 提取的测试用例信息
  }

  /**
   * 从文件中提取API信息
   * 
   * @param {string} filePath - 要分析的文件路径
   */
  extractFromFile(filePath) {
    try {
      const content = fs.readFileSync(filePath, 'utf8')
      const fileName = path.basename(filePath)
      
      console.log(`📄 分析文件: ${fileName}`)
      
      // 提取类定义
      this.extractClasses(content, fileName)
      
      // 提取函数定义
      this.extractFunctions(content, fileName)
      
      // 提取常量定义
      this.extractConstants(content, fileName)
      
      // 提取测试用例
      this.extractTestCases(content, fileName)
      
    } catch (error) {
      console.error(`❌ 分析文件失败 ${filePath}:`, error.message)
    }
  }

  /**
   * 提取类定义
   * 
   * @param {string} content - 文件内容
   * @param {string} fileName - 文件名
   */
  extractClasses(content, fileName) {
    // 匹配类定义的正则表达式
    const classRegex = /class\s+(\w+)\s*{([^}]*)}/g
    let match

    while ((match = classRegex.exec(content)) !== null) {
      const className = match[1]
      const classBody = match[2]
      
      // 获取类前的注释
      const beforeClass = content.substring(0, match.index)
      const comment = this.extractComment(beforeClass)
      
      // 提取类方法
      const methods = this.extractMethods(classBody)
      
      this.classes.push({
        name: className,
        comment: comment,
        methods: methods,
        file: fileName,
        fullMatch: match[0]
      })
    }
  }

  /**
   * 提取类方法
   * 
   * @param {string} classBody - 类体内容
   * @returns {Array} 方法信息数组
   */
  extractMethods(classBody) {
    const methods = []
    
    // 匹配方法定义
    const methodRegex = /(async\s+)?(\w+)\s*\([^)]*\)\s*{/g
    let match

    while ((match = methodRegex.exec(classBody)) !== null) {
      const isAsync = !!match[1]
      const methodName = match[2]
      
      // 跳过构造函数的特殊处理
      if (methodName === 'constructor') {
        methods.push({
          name: 'constructor',
          signature: 'constructor()',
          isAsync: false,
          returnType: className,
          description: '初始化测试运行器实例'
        })
        continue
      }
      
      methods.push({
        name: methodName,
        signature: match[0].replace('{', ''),
        isAsync: isAsync,
        returnType: this.inferReturnType(methodName, isAsync),
        description: this.getMethodDescription(methodName)
      })
    }
    
    return methods
  }

  /**
   * 推断方法返回类型
   * 
   * @param {string} methodName - 方法名
   * @param {boolean} isAsync - 是否为异步方法
   * @returns {string} 返回类型
   */
  inferReturnType(methodName, isAsync) {
    const returnTypes = {
      'test': 'void',
      'assert': 'void | throws Error',
      'run': isAsync ? 'Promise<void>' : 'void'
    }
    
    return returnTypes[methodName] || 'any'
  }

  /**
   * 获取方法描述
   * 
   * @param {string} methodName - 方法名
   * @returns {string} 方法描述
   */
  getMethodDescription(methodName) {
    const descriptions = {
      'test': '添加测试用例到测试套件',
      'assert': '断言验证函数，条件不满足时抛出错误',
      'run': '执行所有注册的测试用例并输出结果'
    }
    
    return descriptions[methodName] || '方法功能描述'
  }

  /**
   * 提取函数定义
   * 
   * @param {string} content - 文件内容
   * @param {string} fileName - 文件名
   */
  extractFunctions(content, fileName) {
    // 匹配函数定义的正则表达式
    const functionRegex = /(?:async\s+)?function\s+(\w+)\s*\([^)]*\)/g
    let match

    while ((match = functionRegex.exec(content)) !== null) {
      const functionName = match[1]
      
      // 获取函数前的注释
      const beforeFunction = content.substring(0, match.index)
      const comment = this.extractComment(beforeFunction)
      
      this.functions.push({
        name: functionName,
        signature: match[0],
        comment: comment,
        file: fileName
      })
    }
  }

  /**
   * 提取常量定义
   * 
   * @param {string} content - 文件内容
   * @param {string} fileName - 文件名
   */
  extractConstants(content, fileName) {
    // 匹配常量定义的正则表达式
    const constRegex = /const\s+(\w+)\s*=\s*([^;\n]+)/g
    let match

    while ((match = constRegex.exec(content)) !== null) {
      const constName = match[1]
      const constValue = match[2].trim()
      
      // 获取常量前的注释
      const beforeConst = content.substring(0, match.index)
      const comment = this.extractComment(beforeConst)
      
      this.constants.push({
        name: constName,
        value: constValue,
        comment: comment,
        file: fileName,
        type: this.inferConstantType(constValue)
      })
    }
  }

  /**
   * 推断常量类型
   * 
   * @param {string} value - 常量值
   * @returns {string} 推断的类型
   */
  inferConstantType(value) {
    if (value.includes('fileURLToPath')) return 'string'
    if (value.includes('path.dirname')) return 'string'
    if (value.includes('new TestRunner')) return 'TestRunner'
    if (value.startsWith('"') || value.startsWith("'")) return 'string'
    if (value.match(/^\d+$/)) return 'number'
    if (value === 'true' || value === 'false') return 'boolean'
    return 'any'
  }

  /**
   * 提取测试用例
   * 
   * @param {string} content - 文件内容
   * @param {string} fileName - 文件名
   */
  extractTestCases(content, fileName) {
    // 匹配测试用例定义
    const testRegex = /runner\.test\(\s*['"`]([^'"`]+)['"`]\s*,\s*(?:async\s+)?\(\s*\)\s*=>\s*{/g
    let match

    while ((match = testRegex.exec(content)) !== null) {
      const testName = match[1]
      
      // 获取测试用例前的注释
      const beforeTest = content.substring(0, match.index)
      const comment = this.extractComment(beforeTest)
      
      this.testCases.push({
        name: testName,
        comment: comment,
        file: fileName,
        category: this.categorizeTest(testName)
      })
    }
  }

  /**
   * 测试用例分类
   * 
   * @param {string} testName - 测试名称
   * @returns {string} 测试类别
   */
  categorizeTest(testName) {
    if (testName.includes('文件')) return '文件系统测试'
    if (testName.includes('package.json')) return '配置验证测试'
    if (testName.includes('manifest.json')) return '扩展配置测试'
    if (testName.includes('TypeScript')) return '编译配置测试'
    if (testName.includes('源代码')) return '结构验证测试'
    if (testName.includes('脚本')) return '脚本验证测试'
    return '通用测试'
  }

  /**
   * 提取注释
   * 
   * @param {string} beforeCode - 代码前的内容
   * @returns {string} 注释内容
   */
  extractComment(beforeCode) {
    const lines = beforeCode.split('\n')
    const commentLines = []
    
    // 从后往前查找注释
    for (let i = lines.length - 1; i >= 0; i--) {
      const line = lines[i].trim()
      
      if (line.startsWith('//')) {
        commentLines.unshift(line.substring(2).trim())
      } else if (line.startsWith('*') || line.startsWith('*/') || line.startsWith('/**')) {
        commentLines.unshift(line.replace(/^[\*\/\s]+/, ''))
      } else if (line === '' || line.startsWith('/*')) {
        continue
      } else {
        break
      }
    }
    
    return commentLines.join(' ').trim()
  }

  /**
   * 生成API文档
   * 
   * @returns {string} 文档内容
   */
  generateDocumentation() {
    let doc = '# 测试API提取报告\n\n'
    
    // 生成概览
    doc += '## 概览\n\n'
    doc += `- **类**: ${this.classes.length} 个\n`
    doc += `- **函数**: ${this.functions.length} 个\n`
    doc += `- **常量**: ${this.constants.length} 个\n`
    doc += `- **测试用例**: ${this.testCases.length} 个\n\n`
    
    // 生成类文档
    if (this.classes.length > 0) {
      doc += '## 类定义\n\n'
      
      this.classes.forEach(cls => {
        doc += `### ${cls.name}\n\n`
        
        if (cls.comment) {
          doc += `**说明**: ${cls.comment}\n\n`
        }
        
        doc += `**文件**: ${cls.file}\n\n`
        
        if (cls.methods.length > 0) {
          doc += '**方法**:\n\n'
          cls.methods.forEach(method => {
            doc += `- \`${method.signature}\` - ${method.description}\n`
            doc += `  - 返回类型: \`${method.returnType}\`\n`
            if (method.isAsync) {
              doc += `  - 异步方法: 是\n`
            }
            doc += '\n'
          })
        }
        
        doc += '---\n\n'
      })
    }
    
    // 生成常量文档
    if (this.constants.length > 0) {
      doc += '## 常量定义\n\n'
      
      this.constants.forEach(constant => {
        doc += `### ${constant.name}\n\n`
        doc += `**类型**: \`${constant.type}\`\n`
        doc += `**值**: \`${constant.value}\`\n`
        doc += `**文件**: ${constant.file}\n`
        
        if (constant.comment) {
          doc += `**说明**: ${constant.comment}\n`
        }
        
        doc += '\n---\n\n'
      })
    }
    
    // 生成测试用例文档
    if (this.testCases.length > 0) {
      doc += '## 测试用例\n\n'
      
      // 按类别分组
      const groupedTests = this.groupTestsByCategory()
      
      Object.keys(groupedTests).forEach(category => {
        doc += `### ${category}\n\n`
        
        groupedTests[category].forEach(test => {
          doc += `- **${test.name}**\n`
          if (test.comment) {
            doc += `  - 说明: ${test.comment}\n`
          }
          doc += `  - 文件: ${test.file}\n\n`
        })
      })
    }
    
    return doc
  }

  /**
   * 按类别分组测试用例
   * 
   * @returns {Object} 分组后的测试用例
   */
  groupTestsByCategory() {
    const grouped = {}
    
    this.testCases.forEach(test => {
      const category = test.category
      if (!grouped[category]) {
        grouped[category] = []
      }
      grouped[category].push(test)
    })
    
    return grouped
  }

  /**
   * 生成统计信息
   * 
   * @returns {Object} 统计信息
   */
  generateStats() {
    return {
      classes: this.classes.length,
      functions: this.functions.length,
      constants: this.constants.length,
      testCases: this.testCases.length,
      totalMethods: this.classes.reduce((sum, cls) => sum + cls.methods.length, 0),
      total: this.classes.length + this.functions.length + this.constants.length + this.testCases.length
    }
  }
}

/**
 * 主函数
 */
function main() {
  const extractor = new TestAPIExtractor()
  
  // 要分析的测试文件
  const testFile = path.resolve(__dirname, 'test-build.test.js')
  
  if (fs.existsSync(testFile)) {
    extractor.extractFromFile(testFile)
  } else {
    console.log(`⚠️  测试文件不存在: ${testFile}`)
    return
  }
  
  // 生成文档
  const documentation = extractor.generateDocumentation()
  const stats = extractor.generateStats()
  
  // 保存文档
  const outputPath = path.resolve(__dirname, '../docs/test-api-extraction.md')
  
  try {
    fs.writeFileSync(outputPath, documentation, 'utf8')
    console.log(`📝 文档已生成: ${outputPath}`)
  } catch (error) {
    console.error('❌ 保存文档失败:', error.message)
  }
  
  // 输出统计信息
  console.log('\n📊 提取统计:')
  console.log(`- 类: ${stats.classes} 个`)
  console.log(`- 方法: ${stats.totalMethods} 个`)
  console.log(`- 函数: ${stats.functions} 个`)
  console.log(`- 常量: ${stats.constants} 个`)
  console.log(`- 测试用例: ${stats.testCases} 个`)
  console.log(`- 总计: ${stats.total} 个API项目`)
  
  console.log('\n✅ API提取完成!')
}

// 运行主函数
main()