#!/usr/bin/env node

/**
 * shadcn主题一致性验证脚本
 * 检查项目中shadcn CSS变量的使用情况和主题一致性
 */

const fs = require('fs')
const path = require('path')
const { execSync } = require('child_process')

// 颜色常量
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
}

// 日志函数
const log = {
  info: (msg) => console.log(`${colors.blue}ℹ${colors.reset} ${msg}`),
  success: (msg) => console.log(`${colors.green}✓${colors.reset} ${msg}`),
  warning: (msg) => console.log(`${colors.yellow}⚠${colors.reset} ${msg}`),
  error: (msg) => console.log(`${colors.red}✗${colors.reset} ${msg}`),
  section: (msg) => console.log(`\n${colors.cyan}${msg}${colors.reset}`)
}

// shadcn CSS变量定义
const shadcnVariables = [
  'background', 'foreground', 'card', 'card-foreground', 'popover', 'popover-foreground',
  'primary', 'primary-foreground', 'secondary', 'secondary-foreground',
  'muted', 'muted-foreground', 'accent', 'accent-foreground',
  'destructive', 'destructive-foreground', 'border', 'input', 'ring'
]

// 需要检查的文件扩展名
const fileExtensions = ['.tsx', '.ts', '.css', '.scss']

// 需要检查的目录
const checkDirectories = ['src', 'tests']

/**
 * 递归获取目录下的所有文件
 */
function getAllFiles(dir, extensions = fileExtensions) {
  const files = []

  function traverse(currentDir) {
    if (!fs.existsSync(currentDir)) {
      return
    }

    const items = fs.readdirSync(currentDir)

    for (const item of items) {
      const fullPath = path.join(currentDir, item)
      const stat = fs.statSync(fullPath)

      if (stat.isDirectory()) {
        // 跳过node_modules等目录
        if (!['node_modules', '.git', 'dist', 'build'].includes(item)) {
          traverse(fullPath)
        }
      } else if (extensions.some(ext => item.endsWith(ext))) {
        files.push(fullPath)
      }
    }
  }

  traverse(dir)
  return files
}

/**
 * 检查文件中的硬编码颜色
 */
function checkHardcodedColors(filePath) {
  const content = fs.readFileSync(filePath, 'utf8')
  const issues = []

  // 检查常见的硬编码颜色模式
  const colorPatterns = [
    /bg-gray-\d+/g,
    /text-gray-\d+/g,
    /border-gray-\d+/g,
    /bg-blue-\d+/g,
    /text-blue-\d+/g,
    /border-blue-\d+/g,
    /bg-red-\d+/g,
    /text-red-\d+/g,
    /border-red-\d+/g,
    /bg-green-\d+/g,
    /text-green-\d+/g,
    /border-green-\d+/g,
    /bg-yellow-\d+/g,
    /text-yellow-\d+/g,
    /border-yellow-\d+/g,
    /bg-white\b/g,
    /text-black\b/g
  ]

  colorPatterns.forEach(pattern => {
    const matches = content.match(pattern)
    if (matches) {
      issues.push({
        type: 'hardcoded-color',
        pattern: pattern.source,
        matches: [...new Set(matches)],
        file: filePath
      })
    }
  })

  return issues
}

/**
 * 检查shadcn变量的使用
 */
function checkShadcnVariableUsage(filePath) {
  const content = fs.readFileSync(filePath, 'utf8')
  const usedVariables = []

  shadcnVariables.forEach(variable => {
    const patterns = [
      new RegExp(`hsl\\(var\\(--${variable}\\)\\)`, 'g'),
      new RegExp(`bg-${variable.replace('-', '-')}\\b`, 'g'),
      new RegExp(`text-${variable.replace('-', '-')}\\b`, 'g'),
      new RegExp(`border-${variable.replace('-', '-')}\\b`, 'g')
    ]

    patterns.forEach(pattern => {
      if (pattern.test(content)) {
        usedVariables.push(variable)
      }
    })
  })

  return [...new Set(usedVariables)]
}

/**
 * 验证globals.css中的CSS变量定义
 */
function validateCSSVariables() {
  const globalsPath = path.join(process.cwd(), 'src/styles/globals.css')

  if (!fs.existsSync(globalsPath)) {
    log.error('globals.css文件不存在')
    return false
  }

  const content = fs.readFileSync(globalsPath, 'utf8')
  const missingVariables = []

  shadcnVariables.forEach(variable => {
    const pattern = new RegExp(`--${variable}:`, 'g')
    if (!pattern.test(content)) {
      missingVariables.push(variable)
    }
  })

  if (missingVariables.length > 0) {
    log.error(`globals.css中缺少以下CSS变量: ${missingVariables.join(', ')}`)
    return false
  }

  log.success('所有必需的shadcn CSS变量都已定义')
  return true
}

/**
 * 验证tailwind.config.js配置
 */
function validateTailwindConfig() {
  const configPath = path.join(process.cwd(), 'tailwind.config.js')

  if (!fs.existsSync(configPath)) {
    log.error('tailwind.config.js文件不存在')
    return false
  }

  const content = fs.readFileSync(configPath, 'utf8')

  // 检查是否包含shadcn颜色配置
  const requiredConfigs = [
    'hsl(var(--background))',
    'hsl(var(--foreground))',
    'hsl(var(--primary))',
    'hsl(var(--secondary))'
  ]

  const missingConfigs = requiredConfigs.filter(config => !content.includes(config))

  if (missingConfigs.length > 0) {
    log.error(`tailwind.config.js中缺少shadcn颜色配置`)
    return false
  }

  log.success('Tailwind配置包含所有必需的shadcn颜色')
  return true
}

/**
 * 检查组件中的shadcn组件使用
 */
function checkShadcnComponentUsage() {
  const componentFiles = getAllFiles('src', ['.tsx', '.ts'])
  const shadcnComponents = []
  const customComponents = []

  componentFiles.forEach(file => {
    const content = fs.readFileSync(file, 'utf8')

    // 检查shadcn组件导入
    const shadcnImports = content.match(/from ['"](\.\/ui\/|@\/components\/ui\/)[^'"]+['"]/g)
    if (shadcnImports) {
      shadcnComponents.push({
        file,
        imports: shadcnImports
      })
    }

    // 检查自定义样式类的使用
    const customClasses = content.match(/className=['"](.*?)['"]/g)
    if (customClasses) {
      customComponents.push({
        file,
        classes: customClasses
      })
    }
  })

  log.info(`发现 ${shadcnComponents.length} 个文件使用shadcn组件`)
  log.info(`发现 ${customComponents.length} 个文件使用自定义样式类`)

  return { shadcnComponents, customComponents }
}

/**
 * 生成主题一致性报告
 */
function generateThemeReport() {
  const report = {
    timestamp: new Date().toISOString(),
    cssVariables: validateCSSVariables(),
    tailwindConfig: validateTailwindConfig(),
    hardcodedColors: [],
    shadcnUsage: [],
    recommendations: []
  }

  // 检查所有文件
  const allFiles = []
  checkDirectories.forEach(dir => {
    if (fs.existsSync(dir)) {
      allFiles.push(...getAllFiles(dir))
    }
  })

  log.info(`检查 ${allFiles.length} 个文件...`)

  allFiles.forEach(file => {
    const hardcodedIssues = checkHardcodedColors(file)
    const usedVariables = checkShadcnVariableUsage(file)

    if (hardcodedIssues.length > 0) {
      report.hardcodedColors.push(...hardcodedIssues)
    }

    if (usedVariables.length > 0) {
      report.shadcnUsage.push({
        file,
        variables: usedVariables
      })
    }
  })

  // 生成建议
  if (report.hardcodedColors.length > 0) {
    report.recommendations.push('将硬编码颜色替换为shadcn CSS变量')
  }

  if (report.shadcnUsage.length < allFiles.length * 0.5) {
    report.recommendations.push('增加shadcn组件的使用率')
  }

  return report
}

/**
 * 主函数
 */
function main() {
  log.section('🎨 shadcn主题一致性验证')

  try {
    const report = generateThemeReport()

    // 显示结果
    log.section('📊 验证结果')

    if (report.cssVariables) {
      log.success('CSS变量定义完整')
    } else {
      log.error('CSS变量定义不完整')
    }

    if (report.tailwindConfig) {
      log.success('Tailwind配置正确')
    } else {
      log.error('Tailwind配置有问题')
    }

    if (report.hardcodedColors.length > 0) {
      log.warning(`发现 ${report.hardcodedColors.length} 个硬编码颜色问题`)
      report.hardcodedColors.slice(0, 5).forEach(issue => {
        log.error(`${issue.file}: ${issue.matches.join(', ')}`)
      })
      if (report.hardcodedColors.length > 5) {
        log.info(`... 还有 ${report.hardcodedColors.length - 5} 个问题`)
      }
    } else {
      log.success('没有发现硬编码颜色问题')
    }

    log.info(`${report.shadcnUsage.length} 个文件使用了shadcn变量`)

    // 显示建议
    if (report.recommendations.length > 0) {
      log.section('💡 改进建议')
      report.recommendations.forEach(rec => {
        log.warning(rec)
      })
    }

    // 保存报告
    const reportPath = path.join(process.cwd(), 'docs/shadcn-theme-consistency-report.json')
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2))
    log.success(`详细报告已保存到: ${reportPath}`)

    // 检查组件使用情况
    log.section('🧩 组件使用情况')
    const componentUsage = checkShadcnComponentUsage()

    // 返回退出码
    const hasIssues = !report.cssVariables || !report.tailwindConfig || report.hardcodedColors.length > 0
    process.exit(hasIssues ? 1 : 0)

  } catch (error) {
    log.error(`验证过程中发生错误: ${error.message}`)
    process.exit(1)
  }
}

// 运行主函数
if (require.main === module) {
  main()
}

module.exports = {
  validateCSSVariables,
  validateTailwindConfig,
  checkHardcodedColors,
  checkShadcnVariableUsage,
  generateThemeReport
}