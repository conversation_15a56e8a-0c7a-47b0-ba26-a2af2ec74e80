#!/usr/bin/env node

/**
 * 验证 shadcn/ui 高级交互组件安装脚本
 * 检查 DropdownMenu、Badge、Select、Tooltip 组件的安装状态
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log('🔍 验证 shadcn/ui 高级交互组件安装状态...\n');

// 需要检查的组件文件
const componentsToCheck = [
  {
    name: 'DropdownMenu',
    path: 'src/components/ui/dropdown-menu.tsx',
    description: '下拉菜单组件'
  },
  {
    name: 'Badge',
    path: 'src/components/ui/badge.tsx',
    description: '标签组件'
  },
  {
    name: 'Select',
    path: 'src/components/ui/select.tsx',
    description: '选择器组件'
  },
  {
    name: 'Tooltip',
    path: 'src/components/ui/tooltip.tsx',
    description: '提示组件'
  }
];

let allComponentsInstalled = true;

// 检查每个组件文件
componentsToCheck.forEach(component => {
  const fullPath = path.join(path.dirname(__dirname), component.path);
  
  if (fs.existsSync(fullPath)) {
    console.log(`✅ ${component.name} (${component.description}) - 已安装`);
    
    // 检查文件内容是否包含预期的导出
    const content = fs.readFileSync(fullPath, 'utf8');
    
    // 基本的内容验证
    if (content.includes('React.forwardRef') || content.includes('export')) {
      console.log(`   📄 文件内容验证通过`);
    } else {
      console.log(`   ⚠️  文件内容可能不完整`);
    }
  } else {
    console.log(`❌ ${component.name} (${component.description}) - 未找到文件: ${component.path}`);
    allComponentsInstalled = false;
  }
  console.log('');
});

// 检查演示文件
const demoPath = path.join(path.dirname(__dirname), 'src/components/examples/AdvancedComponentsDemo.tsx');
if (fs.existsSync(demoPath)) {
  console.log('✅ 高级组件演示文件 - 已创建');
  
  // 检查演示文件是否导入了所有组件
  const demoContent = fs.readFileSync(demoPath, 'utf8');
  const expectedImports = [
    'DropdownMenu',
    'Badge',
    'Select',
    'Tooltip'
  ];
  
  const missingImports = expectedImports.filter(imp => !demoContent.includes(imp));
  
  if (missingImports.length === 0) {
    console.log('   📦 所有组件导入验证通过');
  } else {
    console.log(`   ⚠️  缺少导入: ${missingImports.join(', ')}`);
  }
} else {
  console.log('❌ 高级组件演示文件 - 未找到');
  allComponentsInstalled = false;
}

console.log('');

// 检查测试文件
const testPath = path.join(path.dirname(__dirname), 'tests/shadcn-advanced-components.test.tsx');
if (fs.existsSync(testPath)) {
  console.log('✅ 高级组件测试文件 - 已创建');
} else {
  console.log('❌ 高级组件测试文件 - 未找到');
}

console.log('\n' + '='.repeat(50));

if (allComponentsInstalled) {
  console.log('🎉 所有高级交互组件安装验证通过！');
  console.log('\n📋 已安装的组件:');
  componentsToCheck.forEach(component => {
    console.log(`   • ${component.name} - ${component.description}`);
  });
  
  console.log('\n🚀 可以开始使用这些组件进行开发了！');
  console.log('\n💡 使用建议:');
  console.log('   • 查看 src/components/examples/AdvancedComponentsDemo.tsx 了解使用示例');
  console.log('   • 运行 npm test tests/shadcn-advanced-components.test.tsx 进行测试');
  console.log('   • 参考 shadcn/ui 官方文档获取更多使用方法');
  
  process.exit(0);
} else {
  console.log('❌ 部分组件安装不完整，请检查上述错误信息');
  process.exit(1);
}