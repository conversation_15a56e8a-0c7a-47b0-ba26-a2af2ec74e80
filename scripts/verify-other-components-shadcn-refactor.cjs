// 验证其他页面组件shadcn重构的脚本

const fs = require('fs')
const path = require('path')

/**
 * 验证其他页面组件的shadcn重构结果
 */
function verifyOtherComponentsShadcnRefactor() {
  console.log('🔍 开始验证其他页面组件的shadcn重构结果...\n')

  const results = {
    passed: 0,
    failed: 0,
    warnings: 0,
    details: []
  }

  // 要验证的组件列表
  const componentsToVerify = [
    {
      name: 'CategoryManagementTab',
      path: 'src/components/CategoryManagementTab.tsx',
      requiredImports: [
        'Button',
        'Card',
        'CardContent',
        'CardDescription',
        'CardHeader',
        'CardTitle'
      ]
    },
    {
      name: 'TagsTab',
      path: 'src/components/TagsTab.tsx',
      requiredImports: [
        'Button',
        'Card',
        'CardContent',
        'CardDescription',
        'CardHeader',
        'CardTitle',
        'Alert',
        'AlertDescription'
      ]
    },
    {
      name: 'ImportExportTab',
      path: 'src/components/ImportExportTab.tsx',
      requiredImports: [
        'Button',
        'Card',
        'CardContent',
        'CardDescription',
        'CardHeader',
        'CardTitle',
        'Alert',
        'AlertDescription',
        'Input',
        'Label',
        'Checkbox',
        'Select',
        'SelectContent',
        'SelectItem',
        'SelectTrigger',
        'SelectValue',
        'Progress'
      ]
    },
    {
      name: 'AboutTab',
      path: 'src/options/components/AboutTab.tsx',
      requiredImports: [
        'Card',
        'CardContent',
        'CardDescription',
        'CardHeader',
        'CardTitle',
        'Badge'
      ]
    },
    {
      name: 'HelpCenterTab',
      path: 'src/options/components/HelpCenterTab.tsx',
      requiredImports: [
        'Button',
        'Card',
        'CardContent',
        'CardDescription',
        'CardHeader',
        'CardTitle',
        'Badge',
        'Alert',
        'AlertDescription'
      ]
    }
  ]

  // 验证每个组件
  componentsToVerify.forEach(component => {
    console.log(`📋 验证 ${component.name} 组件...`)
    
    const componentResult = verifyComponent(component)
    results.details.push(componentResult)
    
    if (componentResult.status === 'passed') {
      results.passed++
      console.log(`✅ ${component.name} 验证通过`)
    } else if (componentResult.status === 'warning') {
      results.warnings++
      console.log(`⚠️  ${component.name} 验证有警告`)
    } else {
      results.failed++
      console.log(`❌ ${component.name} 验证失败`)
    }
    
    // 显示详细信息
    componentResult.issues.forEach(issue => {
      console.log(`   ${issue}`)
    })
    
    console.log('')
  })

  // 输出总结
  console.log('📊 验证结果总结:')
  console.log(`✅ 通过: ${results.passed}`)
  console.log(`⚠️  警告: ${results.warnings}`)
  console.log(`❌ 失败: ${results.failed}`)
  console.log(`📝 总计: ${componentsToVerify.length}`)

  if (results.failed === 0) {
    console.log('\n🎉 所有组件的shadcn重构验证通过！')
    return true
  } else {
    console.log('\n🚨 部分组件的shadcn重构验证失败，请检查上述问题。')
    return false
  }
}

/**
 * 验证单个组件
 */
function verifyComponent(component) {
  const result = {
    name: component.name,
    status: 'passed',
    issues: []
  }

  try {
    // 检查文件是否存在
    if (!fs.existsSync(component.path)) {
      result.status = 'failed'
      result.issues.push(`❌ 文件不存在: ${component.path}`)
      return result
    }

    // 读取文件内容
    const content = fs.readFileSync(component.path, 'utf8')

    // 检查shadcn组件导入
    const missingImports = []
    component.requiredImports.forEach(importName => {
      const importPattern = new RegExp(`import.*{[^}]*\\b${importName}\\b[^}]*}.*from.*['"].*ui/`, 'g')
      if (!importPattern.test(content)) {
        missingImports.push(importName)
      }
    })

    if (missingImports.length > 0) {
      result.status = 'failed'
      result.issues.push(`❌ 缺少shadcn组件导入: ${missingImports.join(', ')}`)
    }

    // 检查是否使用了shadcn组件
    const shadcnUsagePattern = /className="[^"]*\b(bg-background|text-foreground|border|rounded-md|shadow-sm|hover:|focus:)\b[^"]*"/g
    const shadcnMatches = content.match(shadcnUsagePattern)
    
    if (!shadcnMatches || shadcnMatches.length === 0) {
      result.status = 'warning'
      result.issues.push(`⚠️  未检测到shadcn样式类的使用`)
    }

    // 检查是否还有自定义CSS类（可能需要清理）
    const customCssPattern = /className="[^"]*\b(bg-gray-|text-gray-|border-gray-)\b[^"]*"/g
    const customMatches = content.match(customCssPattern)
    
    if (customMatches && customMatches.length > 0) {
      result.status = 'warning'
      result.issues.push(`⚠️  仍使用自定义颜色类，建议使用shadcn颜色系统: ${customMatches.slice(0, 3).join(', ')}${customMatches.length > 3 ? '...' : ''}`)
    }

    // 检查是否有中文注释
    const chineseCommentPattern = /\/\/.*[\u4e00-\u9fa5]/g
    const chineseComments = content.match(chineseCommentPattern)
    
    if (!chineseComments || chineseComments.length === 0) {
      result.status = 'warning'
      result.issues.push(`⚠️  建议添加中文注释说明shadcn重构`)
    }

    if (result.issues.length === 0) {
      result.issues.push(`✅ 所有检查项通过`)
    }

  } catch (error) {
    result.status = 'failed'
    result.issues.push(`❌ 验证过程中出错: ${error.message}`)
  }

  return result
}

// 运行验证
if (require.main === module) {
  const success = verifyOtherComponentsShadcnRefactor()
  process.exit(success ? 0 : 1)
}

module.exports = { verifyOtherComponentsShadcnRefactor }